package com.happyfresh.fulfillment.user.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.batch.service.BatchSndService;
import com.happyfresh.fulfillment.batch.service.ShopperAutoAssignmentService;
import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;
import com.happyfresh.fulfillment.common.exception.type.ClockInException;
import com.happyfresh.fulfillment.common.exception.type.HasOnDemandBatchAssignedException;
import com.happyfresh.fulfillment.common.exception.type.InvalidAgentStateException;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.service.JedisCacheService;
import com.happyfresh.fulfillment.common.service.JedisLockService;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.common.util.UserRoleUtil;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.shift.service.ShiftService;
import com.happyfresh.fulfillment.slot.bean.SlotOptimizationContext;
import com.happyfresh.fulfillment.slot.presenter.SlotOptimizationEvent;
import com.happyfresh.fulfillment.slot.service.SlotOptimizationService;
import com.happyfresh.fulfillment.user.mapper.AgentMapper;
import com.happyfresh.fulfillment.user.mapper.AgentShiftMapper;
import com.happyfresh.fulfillment.user.presenter.AgentShiftPresenter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

import static java.util.Arrays.asList;

@Service
public class AgentService {

    @Autowired
    UserRepository userRepository;

    @Autowired
    StockLocationRepository stockLocationRepository;

    @Autowired
    AgentRepository agentRepository;

    @Autowired
    BatchRepository batchRepository;

    @Autowired
    TenantRepository tenantRepository;

    @Autowired
    private JedisCacheService jedisCacheService;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private AgentMapper agentMapper;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private BatchSndService batchSndService;

    @Autowired
    private AgentInGeofenceService agentInGeofenceService;

    @Lazy
    @Autowired
    private AgentClockInActivityService agentClockInActivityService;

    @Autowired
    private ShiftRepository shiftRepository;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private ShiftService shiftService;
    
    @Autowired
    private AgentShiftMapper agentShiftMapper;

    @Autowired
    private SlotRepository slotRepository;

    @Lazy
    @Autowired
    private SlotOptimizationService slotOptimizationService;

    @Lazy
    @Autowired
    private ShopperAutoAssignmentService shopperAutoAssignmentService;

    public static final String AGENT_CACHE_KEY_PREFIX = "agent";

    public static final int AGENT_CACHE_EXPIRY_IN_SECONDS = 600;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private Map<String, BiFunction<User, Agent, Agent>> events = new HashMap<>();

    @PostConstruct
    private void init() {
        events.put("WORK", (x, y) -> work(x, y));
        events.put("FINISH", (x, y) -> finish(x, y));
        events.put("PAUSE", (x, y) -> pause(x, y));
        events.put("RESUME", (x, y) -> resume(x, y));
    }

    @Transactional
    public Agent transformAgent(User user, String state, Agent agent) {
        BiFunction<User, Agent, Agent> function = events.get(state);

        if (function == null) {
            throw new IllegalArgumentException("Invalid state: " + agent.getState());
        }

        if (agent == null) {
            return user.getAgent();
        }

        return function.apply(user, agent);
    }

    @Transactional(readOnly = true)
    public AgentShiftPresenter getAgentInformation(User user) {
        final String key = getAgentCacheKey(user);
        AgentShiftPresenter agentPresenter = null;
        try {
            Optional<String> agentPresenterCache = jedisCacheService.get(key);
            if (agentPresenterCache.isPresent()) {
                String cachedAgentPresenter = agentPresenterCache.get();
                agentPresenter = mapper.readValue(cachedAgentPresenter, new TypeReference<AgentShiftPresenter>() {});
            } else {
                Agent agent = user.getAgent();
                AgentClockInActivity clockInActivity = agentClockInActivityService.getLastAgentClockInActivity(agent);
                agentPresenter = agentShiftMapper.agentToAgentPresenter(agent, clockInActivity);

                String cachedAgentPresenter = mapper.writeValueAsString(agentPresenter);
                jedisCacheService.setWithExpiry(key, cachedAgentPresenter, AGENT_CACHE_EXPIRY_IN_SECONDS);
            }
        } catch (Exception e) {
            Agent agent = user.getAgent();
            AgentClockInActivity clockInActivity = agentClockInActivityService.getLastAgentClockInActivity(agent);
            agentPresenter = agentShiftMapper.agentToAgentPresenter(agent, clockInActivity);
            String errorMessage = String.format("[GetAgentInformationError] Parsing error for user %d. %s", user.getId(), e.toString());
            logger.error(errorMessage);
        }
        return agentPresenter;
    }

    public void invalidateAgentInformationCache(User user) {
        final String key = getAgentCacheKey(user);
        jedisCacheService.del(key);
    }

    private String getAgentCacheKey(User user) {
        return AGENT_CACHE_KEY_PREFIX + ":user_id:" + user.getId();
    }

    private Agent work(User user, Agent agent) {
        Agent currentAgent = user.getAgent();
        if (currentAgent == null) {
            currentAgent = agent;
            currentAgent.setUser(user);
        }

        StockLocation stockLocation = agent.getStockLocation();
        currentAgent.setState(Agent.State.WORKING);
        currentAgent.setStockLocation(stockLocation);
        currentAgent.setLat(agent.getLat());
        currentAgent.setLon(agent.getLon());
        currentAgent.setDeviceId(agent.getDeviceId());
        currentAgent = saveAndDontCreateAgentActivity(currentAgent);

        return currentAgent;
    }

    private Agent pause(User user, Agent agent) {

        if (agent.getState() == Agent.State.FINISH) {
            throw new InvalidAgentStateException("Could not transition from finish to paused");
        }

        return changeAgentState(user, agent, Agent.State.PAUSED);

    }

    private Agent resume(User user, Agent agent) {
        if (agent.getState() == Agent.State.FINISH) {
            throw new InvalidAgentStateException("Could not transition from finish to resume");
        }

        return changeAgentState(user, agent, Agent.State.WORKING);

    }

    private Agent finish(User user, Agent agent) {
        if (UserRoleUtil.isOnDemandRanger(user)
                && LocalDateTime.now().isBefore(agent.getClockOutTime())
                && batchSndService.hasOnDemandBatchAssigned(user))
            throw new HasOnDemandBatchAssignedException();

        agentInGeofenceService.deleteAgentInGeofence(agent, agent.getStockLocation());

        return changeAgentState(user, agent, Agent.State.FINISH);
    }

    private Agent changeAgentState(User user, Agent agent, Agent.State state) {
        agent.setState(state);
        agentRepository.save(agent);
        return user.getAgent();
    }

    @Async
    @Transactional
    public void updateAgentLocation(Agent agent, Double lat, Double lon){
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format("update_agent_location_%s", agent.getId()), 1)) {
                agent.setLat(lat);
                agent.setLon(lon);
                agent.setUpdateLocation(true);
                agent = saveAndDontCreateAgentActivity(agent);
            }
        } catch (Exception e) {
            logger.error("Update agent location async error: " + agent.getId() , e);
        } finally {
            jedisLockService.unlock();
        }
    }

    public void updateLastActivityTime(Agent agent, LocalDateTime lastActivityTime) {
        if(agent!=null) {
            agent.setLastActivityTime(lastActivityTime);
            saveAndDontCreateAgentActivity(agent);
        }
    }

    public List<Agent> getAvailableShopperAgents(StockLocation stockLocation) {
        LocalDateTime oneWeekAgo = LocalDateTime.now().minusWeeks(1L);
        //exclude shoppers almost clock out, clock_out_time - 30 minutes
        return agentRepository.getIdleWorkingShopperAgentsOnStockLocation(stockLocation.getId(), oneWeekAgo);
    }

    @Transactional
    public AgentShiftPresenter processClockIn(String state, User user, Agent agent, Integer clockInDuration) {
        if(agent != null && agent.getShiftId() != null && !agent.getShiftId().equals(0L)) {

            if("WORK".equals(state))
                return processClockInWithShift(state, user, agent);
            else return processWithHour(state, user, agent, clockInDuration);
        }else{
            return processWithHour(state, user, agent, clockInDuration);
        }
    }

    private void validateClockIn(Agent agent, Integer clockInDuration) {
        // Calculate clock out time
        LocalDateTime clockOutTime = LocalDateTime.now().withMinute(0).withSecond(0).withNano(0).plusHours(clockInDuration);

        // Get End Shift by Stock Location and role is Shopper
        Shift.Type  shiftType = Shift.Type.SHOPPER;
        List<Shift> shifts = shiftRepository.findAllByEndTime(agent.getStockLocation(), clockOutTime, shiftType);
        if (shifts.isEmpty()) {
            // throws error if a shopper clock in not based on their shift
            throw new UnprocessableEntityException("Silahkan pilih waktu sesuai shift kamu.");
        }
    }

    private void validateClockOut(Agent agent) {
        // Calculate clock out time
        LocalDateTime clockOutTime = agent.getClockOutTime();
        if (LocalDateTime.now().isBefore(clockOutTime)) {
            throw new UnprocessableEntityException("Silahkan clock out sesuai waktu shift");
        }
    }

    private AgentShiftPresenter processWithHour(String state, User user, Agent agent, Integer clockInDuration) {
        Agent result = transformAgent(user, state, agent);
        if (agent.getStockLocation().enableClockInValidation() && Boolean.TRUE.equals(UserRoleUtil.isShopper(user))) {
            if ("WORK".equals(state)) {
                validateClockIn(agent, clockInDuration);
            }

            if ("FINISH".equals(state)) {
                validateClockOut(agent);
            }
        }

        if (clockInDuration == null)
            clockInDuration = Agent.DEFAULT_CLOCK_IN_DURATION;

        if (clockInDuration >= 1 && result != null) {
            agentClockInActivityService.createAgentClockInActivity(result, clockInDuration);
            agentInGeofenceService.createOrUpdateAgentInGeofence(result);
        }

        List<String> stateTriggerAutoAssignment = Lists.newArrayList("WORK", "RESUME");
        if (result != null && stateTriggerAutoAssignment.contains(state)
                && Boolean.TRUE.equals(UserRoleUtil.isShopper(user))
                && user.isAllowedForAutoAssignment()
        ) {
            shopperAutoAssignmentService.publishAutoAssignmentEvent(
                    result.getStockLocation().getCluster().getId().toString(),
                    "", result.getStockLocation().getId(),
                    state.equals("WORK") ? SlotOptimizationEvent.AutoAssignmentTriggerEvent.CLOCK_IN : SlotOptimizationEvent.AutoAssignmentTriggerEvent.RESUME
            );
        }
        processClockOutWithShift(state, result);
        AgentClockInActivity agentClockInActivity = null;

        if(result!= null)
            agentClockInActivity = agentClockInActivityService.getLastAgentClockInActivity(result);

        return agentShiftMapper.agentToAgentPresenter(result, agentClockInActivity);
    }

    private void processClockOutWithShift(String state, Agent agent) {
        LocalDateTime now = LocalDateTime.now();
        if ("FINISH".equals(state) && agent != null) {
            LocalDateTime startOfDay = DateTimeUtil.getTodayStartOfTheDayInUTC(agent.getStockLocation());
            Optional<Shift> optionalShift = shiftRepository.findLatestTodayShiftByAgentId(agent.getId(), startOfDay);

            if(optionalShift.isPresent()) {
                User user = agent.getUser();
                Shift shift = optionalShift.get();
                Long shiftId = shift.getId();
                LocalDateTime shiftStartTime = shift.getStartTime();
                updateShiftCount(now, shift, user);

                publishSlotOptimization(agent, now, user, shiftId, shiftStartTime);
            }
        }
    }

    private void publishSlotOptimization(Agent agent, LocalDateTime now, User user, Long shiftId, LocalDateTime shiftStartTime) {
        List<Object[]> objectSlots = slotRepository.findLastSlotAndRelatedShopperShiftByDriverShiftId(shiftId);
        if(!objectSlots.isEmpty() && now.isAfter(shiftStartTime)
                && user.isAllowedForAutoAssignment()
                && Boolean.TRUE.equals(UserRoleUtil.isDriver(user))) {
            Object[] objectSlot = objectSlots.get(0);
            long lastSlotId = ((BigInteger) objectSlot[0]).longValue();
            long driverShiftId = ((BigInteger) objectSlot[1]).longValue();
            long shopperShiftId = ((BigInteger) objectSlot[2]).longValue();
            LocalDateTime lastSlotStartTime = ((Timestamp) objectSlot[3]).toLocalDateTime();
            StockLocation stockLocation = agent.getStockLocation();
            Cluster cluster = stockLocation.getCluster();
            String timeZone = stockLocation.getState().getTimeZone();

            SlotOptimizationContext context = new SlotOptimizationContext(
                    lastSlotId, shopperShiftId, cluster.getId(),
                    driverShiftId, lastSlotStartTime,
                    false, true, timeZone,
                    cluster.getTenant().isEnableSlotOptimizationEventDeduplication());
            slotOptimizationService.publishSlotOptimization(context);
        }
    }

    private AgentShiftPresenter processClockInWithShift(String state, User user, Agent agent) {
        LocalDateTime now = LocalDateTime.now();
        Long shiftId = agent.getShiftId();
        Shift shift = validateShift(user, shiftId, now);

        Agent result = transformAgent(user, state, agent);
        if ("WORK".equals(state) && result != null) {
            int duration = getDuration(shift, now);
            agentClockInActivityService.createAgentClockInActivity(result, shift, duration);
            agentInGeofenceService.createOrUpdateAgentInGeofence(result);
            updateShiftCount(now, shift, user);
        }
        AgentClockInActivity agentClockInActivity = null;

        if(result!= null)
            agentClockInActivity = agentClockInActivityService.getLastAgentClockInActivity(result);

        publishSlotOptimization(agent, now, user, shiftId, shift.getStartTime());

        return agentShiftMapper.agentToAgentPresenter(result, agentClockInActivity);
    }

    private int getDuration(Shift shift, LocalDateTime now) {
        now = now.withMinute(0).withSecond(0).withNano(0);
        LocalDateTime startWorkingTime = now.isAfter(shift.getStartTime()) ? now : shift.getStartTime();
        int duration = (int) Duration.between(startWorkingTime, shift.getEndTime()).toHours();
        if (duration < 1)
            duration = 1;
        return duration;
    }

    private Shift validateShift(User user, Long shiftId, LocalDateTime now) {
        Optional<Shift> optionalShift = shiftRepository.findById(shiftId);

        if (optionalShift.isEmpty())
            throw new ClockInException(ClockInException.SHIFT_NOT_FOUND);

        Shift shift = optionalShift.get();
        if (now.isAfter(shift.getEndTime()))
            throw new ClockInException(ClockInException.INVALID_SHIFT_TIME);

        if((shift.getType().equals(Shift.Type.SHOPPER) && !UserRoleUtil.isShopper(user)) ||
                (shift.getType().equals(Shift.Type.DRIVER) && !UserRoleUtil.isDriver(user))
        )
            throw new ClockInException(ClockInException.INVALID_SHIFT_USER_ROLE);

        return shift;
    }

    private void updateShiftCount(LocalDateTime now, Shift shift, User user) {
        if(now.isAfter(shift.getStartTime())
                && user.isAllowedForAutoAssignment()
                && Boolean.TRUE.equals(UserRoleUtil.isDriver(user)))
            shiftService.updateShiftCountFromClockedInFleet(shift);
    }

    public List<Agent> getAvailableDriverAgentsByShiftId(Shift shift){
        LocalDateTime minStartTime = LocalDateTime.now().minusWeeks(1L);
        LocalDateTime maxStartTime = DateTimeUtil.getTodayEndOfTheDayInUTC(shift.getStockLocation());

        List<Integer> batchTypesValue = new ArrayList<>(asList(Batch.Type.DELIVERY.getValue(), Batch.Type.RANGER.getValue()));
        return agentRepository.getAllWorkingAgentOnSameShiftId(Role.Name.DRIVER.toString(), shift.getId(),
                batchTypesValue, Job.getInactiveStates(), minStartTime, maxStartTime);
    }

    public List<Agent> getAvailableDriverAgentsByStockLocations(List<StockLocation> stockLocations){
        LocalDateTime minStartTime = LocalDateTime.now().minusWeeks(1L);
        LocalDateTime maxStartTime = DateTimeUtil.getTodayEndOfTheDayInUTC(stockLocations.get(0));

        List<Long> stockLocationIds = stockLocations.stream().map(StockLocation::getId).collect(Collectors.toList());
        List<Integer> batchTypesValue = new ArrayList<>(asList(Batch.Type.DELIVERY.getValue(), Batch.Type.RANGER.getValue()));
        return agentRepository.getAllWorkingAgentOnSameStockLocation(Role.Name.DRIVER.toString(), stockLocationIds,
                batchTypesValue, Job.getInactiveStates(), minStartTime, maxStartTime);
    }

    /**
     * Get available driver agents ordered by last activity time (least recently active first)
     * This ensures drivers who have been idle longer get priority for new assignments
     */
    public List<Agent> getAvailableDriverAgentsByStockLocationsOrderedByLastActivity(List<StockLocation> stockLocations){
        LocalDateTime minStartTime = LocalDateTime.now().minusWeeks(1L);
        LocalDateTime maxStartTime = DateTimeUtil.getTodayEndOfTheDayInUTC(stockLocations.get(0));

        List<Long> stockLocationIds = stockLocations.stream().map(StockLocation::getId).collect(Collectors.toList());
        List<Integer> batchTypesValue = new ArrayList<>(asList(Batch.Type.DELIVERY.getValue(), Batch.Type.RANGER.getValue()));
        return agentRepository.getAllWorkingAgentOnSameStockLocationOrderedByLastActivity(Role.Name.DRIVER.toString(), stockLocationIds,
                batchTypesValue, Job.getInactiveStates(), minStartTime, maxStartTime);
    }

    public Agent saveAndDontCreateAgentActivity(Agent agent) {
        agent = agentRepository.save(agent);
        agent.setShouldNotCreateAgentActivity(true);
        return agent;
    }
}
