package com.happyfresh.fulfillment.user.service;

import com.happyfresh.fulfillment.entity.Agent;
import com.happyfresh.fulfillment.entity.AgentActivity;
import com.happyfresh.fulfillment.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class AgentActivityService {

    @Autowired
    AgentActivityRepository agentActivityRepository;

    @Transactional
    public void createAgentActivity(Agent agent) {
       AgentActivity agentActivity = new AgentActivity();
       agentActivity.setUserId(agent.getUser().getId());
       agentActivity.setAgentId(agent.getId());
       agentActivity.setState(agent.getState());
       agentActivity.setStockLocationId(agent.getStockLocation().getId());
       agentActivity.setLat(agent.getLat());
       agentActivity.setLon(agent.getLon());
       agentActivity.setAccuracy(agent.getAccuracy());
       agentActivity.setCreatedBy(agent.getCreatedBy());
       agentActivity.setUpdatedBy(agent.getUpdatedBy());
       agentActivity.setTenant(agent.getTenant());
       agentActivityRepository.save(agentActivity);
    }

}
