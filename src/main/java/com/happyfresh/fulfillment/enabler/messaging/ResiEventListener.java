package com.happyfresh.fulfillment.enabler.messaging;

import com.happyfresh.fulfillment.common.presenter.ResiEvent;
import com.happyfresh.fulfillment.enabler.service.JubelioService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ResiEventListener {

    @Autowired
    private JubelioService jubelioService;

    private final Logger LOG = LoggerFactory.getLogger(ResiEventListener.class);

//    @KafkaListener(topics = KafkaTopicConfig.RESI_PROCESSING_TOPIC,
//                   containerFactory = "resiKafkaListenerContainerFactory")
    public void listen(ResiEvent event) {
        jubelioService.handleResiEvent(event);
    }

}
