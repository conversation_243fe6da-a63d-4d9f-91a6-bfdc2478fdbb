package com.happyfresh.fulfillment.enabler.controller;

import com.happyfresh.fulfillment.common.annotation.ResponseWrapper;
import com.happyfresh.fulfillment.enabler.form.EnablerReceiptForm;
import com.happyfresh.fulfillment.enabler.form.EnablerWebhookForm;
import com.happyfresh.fulfillment.enabler.presenter.EnablerOrderPresenter;
import com.happyfresh.fulfillment.enabler.presenter.EnablerReceiptPresenter;
import com.happyfresh.fulfillment.enabler.presenter.EnablerStorePresenter;
import com.happyfresh.fulfillment.enabler.service.EnablerService;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.sayurbox.form.SayurboxWebhookForm;
import com.happyfresh.fulfillment.sayurbox.service.SayurboxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/enablers")
public class EnablerController {

    @Autowired
    private EnablerService enablerService;

    @Autowired
    private SayurboxService sayurboxService;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @GetMapping(value = "/{enabler}/stores")
    @PreAuthorize("isEnablerAdminAuthenticated(#enabler)")
    @ResponseWrapper(rootName = "stores")
    public List<EnablerStorePresenter> getEnablerStores(@PathVariable String enabler) {
        return enablerService.getStores(enabler);
    }

    @GetMapping(value = "/{enabler}/stores/{externalId}/orders")
    @PreAuthorize("isEnablerAdminAuthenticated(#enabler)")
    @ResponseWrapper(rootName = "orders")
    public List<EnablerOrderPresenter> getOrders(@PathVariable String enabler, @PathVariable Long externalId) {
        return enablerService.getOrders(enabler, externalId);
    }

    @PostMapping(value = "/{enabler}/orders/{orderNumber}/receipts")
    @PreAuthorize("isEnablerAdminAuthenticated(#enabler)")
    @ResponseWrapper(ignore = true)
    public EnablerReceiptPresenter createReceipt(@PathVariable String enabler,
                                                 @PathVariable String orderNumber,
                                                 @Valid @RequestBody EnablerReceiptForm receiptForm) {
        return enablerService.createReceipt(enabler, orderNumber, receiptForm);
    }


    @PutMapping(value = "/{enabler}/orders")
    @PreAuthorize("isEnablerAdminAuthenticated(#enabler)")
    @ResponseWrapper(rootName = "order")
    public EnablerOrderPresenter updateItemStatus(@PathVariable String enabler, @Validated @RequestBody EnablerWebhookForm enablerWebhookForm) {
        return enablerService.updateItemStatus(enabler, enablerWebhookForm);
    }

    @PostMapping(value = "/{enabler}/notification")
    @PreAuthorize("isExternalSystemAuthenticated()")
    public ResponseEntity<Map<String, String>> updateSayurboxOrderStatus(@PathVariable String enabler, @Validated @RequestBody SayurboxWebhookForm sayurboxWebhookForm) {
        if(!enabler.equalsIgnoreCase(StockLocation.Enabler.SAYURBOX.toString())) {
            return new ResponseEntity<>(HttpStatus.OK);
        }
        Shipment shipment = shipmentRepository.findByOrderNumber(sayurboxWebhookForm.getOrderNumber());
        if (shipment == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        sayurboxService.sanitizeAndUpdateOrderStatus(shipment, sayurboxWebhookForm);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PutMapping(value = "/{enabler}/orders/{orderNumber}")
    @PreAuthorize("isExternalSystemAuthenticated()")
    public ResponseEntity<Map<String, String>> updateOrderStatus(@PathVariable String enabler, @PathVariable String orderNumber) {
        if(!enabler.equalsIgnoreCase(StockLocation.Enabler.SAYURBOX.toString())) {
            return new ResponseEntity<>(HttpStatus.OK);
        }
        Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
        if (shipment == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        sayurboxService.fetchSayurboxOrderDetailAndUpdateOrderStatus(shipment);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
