package com.happyfresh.fulfillment.enabler.presenter;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class JubelioSalesOrderPresenter {

    public enum ChannelStatus {
        Pending, Processing, Paid, Returned, Failed, Completed, Cancelled;

        public String toString() {
            return name();
        }
    }

    private List<JubelioSalesOrderItemPresenter> items;

    private Long salesorderId;

    private String salesorderNo;

    private Integer contactId;

    private String customerName;

    private LocalDateTime transactionDate;

    private LocalDateTime createdDate;

    private Boolean isTaxIncluded;

    private String note;

    private String subTotal;

    private String totalDisc;

    private String totalTax;

    private String grandTotal;

    private String refNo;

    private String paymentMethod;

    private Integer locationId;

    private Boolean isCanceled;

    private String cancelReason;

    private Integer source;

    private String cancelReasonDetail;

    private Boolean isPaid;

    @Enumerated(EnumType.STRING)
    private ChannelStatus channelStatus;

    private String shippingCost;

    private String insuranceCost;

    private String shippingFullName;

    private String shippingAddress;

    private String shippingArea;

    private String shippingCity;

    private String shippingProvince;

    private String shippingPostCode;

    private String shippingCountry;

    private LocalDateTime lastModified;

    private String registerSessionId;

    private String userName;

    private String storeId;

    private String markedAsComplete;

    private Boolean isTracked;

    private String storeSoNumber;

    private Boolean isDeletedFromPicklist;

    private String dropshipper;

    private String dueDate;

    private String dropshipperNote;

    private String dropshipperAddress;

    private Boolean isShipped;

    private String receivedDate;

    private String salesmenId;

    private String shippingPhone;

    private String escrowAmount;

    private Boolean isAcknowledge;

    private String acknowledgeStatus;

    private String deletedFromPicklistBy;

    private String addDisc;

    private String addFee;

    private Boolean isLabelPrinted;

    private Boolean isInvoicePrinted;

    private String pickedIn;

    private String totalAmountMp;

    private String internalDoNumber;

    private String internalSoNumber;

    private String trackingNumber;

    private String courier;

    private String closureId;

    private String username;

    private Boolean isPo;

    private Long invoiceId;

    private String invoiceNo;

    private String salesmenName;

    private String trackingNo;

    private String sourceName;

    private String storeName;

    private String locationName;

    private String shipper;

}
