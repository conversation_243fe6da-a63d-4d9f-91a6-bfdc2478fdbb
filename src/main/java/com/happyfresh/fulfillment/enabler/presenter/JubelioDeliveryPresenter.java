package com.happyfresh.fulfillment.enabler.presenter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class JubelioDeliveryPresenter {

    private Long id;

    private Long batchId;

    private String shipmentNumber;

    private String orderNumber;

    private String state;

    private String salesOrderId;

    @JsonIgnore
    private LocalDateTime latestStateChangedAt;

    @JsonProperty("latest_state_changed_at")
    public String getLatestStateChangedAt() {
        if (latestStateChangedAt != null)
            return latestStateChangedAt.toString();
        return null;
    }

    @JsonIgnore
    private LocalDateTime slotStartTime;

    @JsonProperty("slot_start_time")
    public String getSlotStartTime() {
        return slotStartTime.toString();
    }

    @JsonIgnore
    private LocalDateTime slotEndTime;

    @JsonProperty("slot_end_time")
    public String getSlotEndTime() {
        return slotEndTime.toString();
    }

    @JsonIgnore
    private LocalDateTime createdAt;

    @JsonProperty("created_at")
    public String getCreatedAt() {
        return createdAt.toString();
    }

}
