package com.happyfresh.fulfillment.enabler.form;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class JubelioCreateSalesOrderItemForm {

    @NotNull
    private Long itemId;

    private String serialNo;

    private String description;

    private Integer taxId;

    private Double price;

    private String unit;

    private Integer qtyInBase;

    private Double disc;

    private Double discAmount;

    private Double taxAmount;

    private Double amount;

    private Integer locationId;

    private String shipper;

}
