package com.happyfresh.fulfillment.enabler.form;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ResiTrackingResult {

    private String status;

    private ResiTrackingSummary summary;

    private ResiTrackingDetail detail;

    private List<ResiTrackingHistory> history;

}