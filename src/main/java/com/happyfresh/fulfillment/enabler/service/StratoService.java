package com.happyfresh.fulfillment.enabler.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.admin.mapper.AdminBatchMapper;
import com.happyfresh.fulfillment.batch.mapper.BatchMapper;
import com.happyfresh.fulfillment.batch.service.JobSndService;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.exception.ApiError;
import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;
import com.happyfresh.fulfillment.common.exception.type.*;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.model.CustomRequestScopeAttr;
import com.happyfresh.fulfillment.common.presenter.StratoEvent;
import com.happyfresh.fulfillment.common.service.WebhookCustomPublisherService;
import com.happyfresh.fulfillment.common.service.radar.RadarApiService;
import com.happyfresh.fulfillment.common.util.ApplicationUtil;
import com.happyfresh.fulfillment.enabler.form.*;
import com.happyfresh.fulfillment.enabler.model.StratoShopperCapacity;
import com.happyfresh.fulfillment.enabler.presenter.StratoBookShopperCapacityPresenter;
import com.happyfresh.fulfillment.enabler.presenter.StratoExpressCapacityPresenter;
import com.happyfresh.fulfillment.enabler.presenter.StratoGetShopperCapacityPresenter;
import com.happyfresh.fulfillment.enabler.service.api.StratoApiService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressService;
import com.happyfresh.fulfillment.lalamove.service.LalamoveService;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.shipment.form.ItemForm;
import com.happyfresh.fulfillment.shipment.form.PackagingReplacementForm;
import com.happyfresh.fulfillment.shipment.mapper.ItemMapper;
import com.happyfresh.fulfillment.shipment.service.CatalogService;
import com.happyfresh.fulfillment.shipment.service.ItemService;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import com.happyfresh.fulfillment.shipment.service.ShipmentService;
import com.happyfresh.fulfillment.slot.service.SlotOptimizationService;
import com.happyfresh.fulfillment.tpl.delyva.service.DelyvaService;
import com.happyfresh.fulfillment.user.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.persistence.EntityNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class StratoService {

    @Autowired
    private CatalogService catalogService;

    @Autowired
    private JobSndService jobSndService;

    @Autowired
    private ItemService itemService;

    @Autowired
    private ItemMapper itemMapper;

    @Autowired
    private GrabExpressDeliveryRepository grabExpressDeliveryRepository;

    @Autowired
    private ItemRepository itemRepository;

    @Autowired
    private WebhookCustomPublisherService webhookCustomPublisherService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private OrderService orderService;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private PackagingRepository packagingRepository;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private BatchMapper batchMapper;

    @Autowired
    private StratoApiService stratoApiService;

    @Autowired
    private UserService userService;

    @Autowired
    private ShipmentService shipmentService;

    @Autowired
    private LalamoveService lalamoveService;

    @Autowired
    private DelyvaService delyvaService;

    @Autowired
    private RadarApiService radarApiService;

    @Autowired
    private GrabExpressService grabExpressService;

    @Autowired
    private StratoWebhookService stratoWebhookService;

    @Autowired
    private DriverReassignmentService driverReassignmentService;

    @Autowired
    private AdminBatchMapper adminBatchMapper;

    @Lazy
    @Autowired
    private SlotOptimizationService slotOptimizationService;

    private final Logger logger = LoggerFactory.getLogger(StratoService.class);

    @Transactional
    public void handleEvent(StratoEvent stratoEvent) {
        StratoWebhookForm form;
        try {
            form = mapper.readValue(stratoEvent.getPayload(), StratoWebhookForm.class);
        } catch (IOException e) {
            logger.error("Failed mapping StratoEvent payload to StratoWebhookForm", e);
            return;
        }

        Shipment shipment = shipmentRepository.findByOrderNumber(form.getOrderNumber());
        if (shipment == null)
            return;

        if ((form.getStatus().equals(StratoEvent.Status.ORDER_ITEM_PICKED.toString()) || form.getStatus().equals(StratoEvent.Status.ORDER_ITEM_PACKED.toString()))
                && shipment.getState().equals(Shipment.State.PENDING))
            return;

        if (!shipment.getSlot().getStockLocation().isFulfilledByStrato())
            return;


        RequestContextHolder.setRequestAttributes(new CustomRequestScopeAttr());
        RequestContextHolder.currentRequestAttributes().setAttribute(ApplicationUtil.LOCALE, "en", RequestAttributes.SCOPE_REQUEST);
        processShipment(shipment, form);
        RequestContextHolder.resetRequestAttributes();
    }

    @Transactional
    public void processShipment(Shipment shipment, StratoWebhookForm form) {
        Job shoppingJob = getShoppingJob(shipment);
        if (shoppingJob.getState().equals(Job.State.FINISHED))
            return;

        AbstractAuthenticationToken authenticationToken = getSystemAdminAuthToken(shipment);
        SecurityContextHolder.getContext().setAuthentication(authenticationToken);

        StockLocation stockLocation = shipment.getSlot().getStockLocation();
        if (!stockLocation.isFulfilledByStrato())
            return;

        Role shopperRole = roleRepository.findByName(Role.Name.STRATO_SHOPPER);
        User shopper = userRepository.findByRolesContainingAndTenantId(shopperRole, shipment.getTenant().getId());

        // FIRST ORDER ITEM PICKED
        if (form.getStatus().equalsIgnoreCase(StratoEvent.Status.ORDER_ITEM_PICKED.toString())) {
            List<ApiError> itemPickedExceptions = new ArrayList<>();
            for (StratoItemForm stratoItemForm : form.getItems()) {
                Item item = itemRepository.findByShipmentIdAndReplacedItemIdAndSku(shipment.getId(), null, stratoItemForm.getSku());
                if (item == null)
                    continue;

                if (stratoItemForm.getFoundQty() == 0 && stratoItemForm.getOosQty() == 0) {
                    // The state in Strato has changed to order item picked but actually picking not started
                    continue;
                }

                item.setFoundQty(stratoItemForm.getFoundQty());
                item.setOosQty(stratoItemForm.getOosQty());
                item.setOosType(stratoItemForm.getOosType());
                item.setOosDetail(stratoItemForm.getOosDetail());
                item.setShopperNotesFulfilled(stratoItemForm.isShopperNotesFulfilled());
                if (item.getAverageWeight() != null) {
                    item.setActualWeight(stratoItemForm.getWeight());
                }

                if (item.getRequestedQty() != item.getFoundQty() + item.getOosQty()) {
                    itemPickedExceptions.add(new ApiError(InvalidItemQtyException.class.getSimpleName(), "Found quantity or oos quantity is not valid."));
                    continue;
                }

                itemRepository.save(item);
            }

            if (itemPickedExceptions.isEmpty() && shoppingJob.getState().equals(Job.State.INITIAL)) {
                Batch shoppingBatch = shoppingJob.getBatch();
                transitionToStartShopping(shopper, shoppingJob);

                webhookCustomPublisherService.publishWebhookForBatch(WebhookType.START_BATCH, shoppingBatch);
            }

            if (!itemPickedExceptions.isEmpty()) {
                logExceptions("OrderItemPicked", form.getOrderNumber(), itemPickedExceptions);
            }
        }

        // ORDER ITEM PACKED
        if (form.getStatus().equalsIgnoreCase(StratoEvent.Status.ORDER_ITEM_PACKED.toString())) {
            // Sync All Items First and Finalize Batch
            List<ApiError> exceptions = finalizeShipment(shipment, form);

            if (exceptions.isEmpty()) {
                if (shoppingJob.getState().equals(Job.State.INITIAL)) {
                    transitionToStartShopping(shopper, shoppingJob);
                }
                jobSndService.changeJobState(shoppingJob, Job.State.FINALIZING);

                // Calculate order total for every shipment in a batch
                Double newOrderTotal = orderService.getOrderTotal(shipment.getNumber());
                BigDecimal orderTotal = BigDecimal.valueOf(newOrderTotal);
                shipment.setOrderTotal(orderTotal);
                shipmentRepository.save(shipment);

                // Finish Batch
                jobSndService.changeJobState(shoppingJob, Job.State.FINISHED);

                // Publish slot optimization after shopping is finished
                slotOptimizationService.publishSlotOptimizationAndAutoAssignments(shipment);

                grabExpressService.bookByShipment(stockLocation, shipment);

                Batch shoppingBatch = shoppingJob.getBatch();
                webhookCustomPublisherService.publishWebhookForBatch(WebhookType.FINALIZE_BATCH, shoppingBatch);
                webhookCustomPublisherService.publishWebhookForBatchShipment(WebhookType.PAY_SHIPMENT, shoppingBatch, shipment);

                Country country = stockLocation.getState().getCountry();
                Optional<Job> deliveryOrRanger = shipment.getDeliveryOrRangerJob();
                Job deliveryOrRangerJob = null;
                if (deliveryOrRanger.isPresent() && deliveryOrRanger.get().getBatch().getTplType() == null) {
                    // Create geofence for fleet tracking
                    deliveryOrRangerJob = deliveryOrRanger.get();
                    // Radar service disabled
//                    radarApiService.createDeliveryGeofence(shipment, country);
                }

                doDriverReassignment(stockLocation, shipment, shoppingJob);
                sendStratoDeliveryBooked(stockLocation, deliveryOrRangerJob);
            } else {
                logExceptions("OrderItemPacked", form.getOrderNumber(), exceptions);
            }
        }
    }

    public List<ApiError> validateItemsAndPackagings(Shipment shipment, StratoWebhookForm form) {
        List<ApiError> errors = new ArrayList<>();

        String webhookStatus = form.getStatus();

        List<StratoItemForm> formItems = form.getItems();
        List<Item> items = itemRepository.findAllByShipmentId(shipment.getId());

        String itemExceptionName = InvalidItemQtyException.class.getSimpleName();

        for (StratoItemForm itemForm : formItems) {
            String sku = itemForm.getSku();
            Optional<Item> optionalItem = items.stream()
                    .filter(i -> (i.getReplacedItem() == null) && i.getSku().equalsIgnoreCase(sku))
                    .findFirst();

            if (optionalItem.isEmpty()) {
                errors.add(new ApiError(itemExceptionName, "Item not found.", sku));
                continue;
            }

            Item item = optionalItem.get();
            if (item.getRequestedQty() != itemForm.getFoundQty() + itemForm.getOosQty())
                errors.add(new ApiError(itemExceptionName, "Found quantity or OOS quantity is not valid.", sku));
        }

        List<String> dbSKUs = items.stream().map(Item::getSku).sorted().collect(Collectors.toList());
        List<String> formSKUs = formItems.stream().map(StratoItemForm::getSku).sorted().collect(Collectors.toList());
        List<String> missingSKUs = new ArrayList<>(dbSKUs);
        missingSKUs.removeAll(formSKUs);

        boolean isOrderItemPackedEvent = webhookStatus.equalsIgnoreCase(StratoEvent.Status.ORDER_ITEM_PACKED.toString());

        if (!missingSKUs.isEmpty() && isOrderItemPackedEvent) {
            String commaSeparatedSKUs = String.join(", ", missingSKUs);
            ApiError e = new ApiError(itemExceptionName, "Missing finalize item(s).", commaSeparatedSKUs);
            errors.add(e);
        }

        if (!form.getPackagings().isEmpty()) {
            String formPackagingId = form.getPackagings().get(0).getPackagingId();
            Optional<Packaging> optionalPackaging = packagingRepository.findByIdAndStockLocationId(Long.parseLong(formPackagingId), shipment.getSlot().getStockLocation().getId());
            if (optionalPackaging.isEmpty()) {
                ApiError e = new ApiError("InvalidPackagingException", "Unknown packaging id.", formPackagingId);
                errors.add(e);
            }
        }

        return errors;
    }

    private void finalizePackaging(Shipment shipment, StratoWebhookForm form) throws Exception {
        if (form.getPackagings() != null && !form.getPackagings().isEmpty() && form.getPackagings().get(0).getReplacement() != null) {
            StratoPackagingForm replacementForm = form.getPackagings().get(0).getReplacement();

            PackagingReplacementForm packagingReplacementForm = new PackagingReplacementForm();
            packagingReplacementForm.setPackagingId(Long.parseLong(replacementForm.getPackagingId()));
            packagingReplacementForm.setReplacementReason("OOS");
            shipmentService.replacePackaging(shipment.getNumber(), packagingReplacementForm);
        }
    }

    private List<ApiError> finalizeShipment(Shipment shipment, StratoWebhookForm form) {
        List<StratoItemForm> itemsForm = form.getItems();
        StockLocation stockLocation = shipment.getSlot().getStockLocation();
        Country country = stockLocation.getState().getCountry();
        List<ApiError> exceptions = new ArrayList<>();

        /*** Should empty all replacements, due to optimistic calling API on client*/
        transactionHelper.withNewTransaction(() -> {
            itemRepository.deleteAllReplacementByShipment(shipment);
        });

        List<String> skus = itemsForm.stream()
                .filter(i -> i.getReplacement() != null)
                .map(i -> i.getReplacement().getSku()).collect(Collectors.toList());
        List<ItemForm> itemsBySku = new ArrayList<>();
        if (!skus.isEmpty()) {
            try {
                itemsBySku = catalogService.getItemsFromCatalogService(skus, stockLocation.getExternalId(), shipment);
            } catch (Exception exception) {
                exceptions.add(new ApiError(exception.getClass().getSimpleName(), exception.getMessage()));
                return exceptions;
            }
        }

        for (StratoItemForm itemForm : itemsForm) {
            try {
                finalizeItem(itemForm, shipment, country, stockLocation, itemsBySku);
            } catch (Exception exception) {
                exceptions.add(new ApiError(exception.getClass().getSimpleName(), exception.getMessage(), itemForm.getSku()));
            }
        }

        List<String> itemFormSkus = new ArrayList<>();
        for (StratoItemForm itemForm : itemsForm) {
            itemFormSkus.add(itemForm.getSku());
        }

        List<String> itemSkus = new ArrayList<>();
        for (Item item : shipment.getItems()) {
            if (item.getReplacedItem() == null) {
                itemSkus.add(item.getSku());
            }
        }

        itemSkus.removeAll(itemFormSkus);
        if (!itemSkus.isEmpty()) {
            exceptions.add(new ApiError(InvalidItemQtyException.class.getSimpleName(), "Missing finalize item(s)", itemSkus.get(0)));
        }

        // Sync packaging and replacement
        try {
            finalizePackaging(shipment, form);
        } catch (Exception e) {
            exceptions.add(new ApiError(UnprocessableEntityException.class.getSimpleName(), "Unable to proceed to finalize packaging"));
        }

        return exceptions;
    }

    private void finalizeItem(StratoItemForm form, Shipment shipment, Country country, StockLocation stockLocation, List<ItemForm> itemsBySku) {
        Item item = itemRepository.findByShipmentIdAndReplacedItemIdAndSku(shipment.getId(), null, form.getSku());
        item.setFoundQty(form.getFoundQty());
        item.setOosQty(form.getOosQty());
        item.setOosType(form.getOosType());
        item.setOosDetail(form.getOosDetail());
        item.setShopperNotesFulfilled(form.isShopperNotesFulfilled());

        if (item.getAverageWeight() != null) {
            // Strato currently not activating weighted item, switch back if they already do
            item.setActualWeight(item.getAverageWeight() * item.getFoundQty());
            //item.setActualWeight(form.getWeight());
        }

        if (item.getRequestedQty() != item.getFoundQty() + item.getOosQty())
            throw new InvalidItemQtyException("Finalize quantity is not valid.");

        validateItemWeightAdjustment(item, country);
        itemService.save(stockLocation, item, country.getIsoName());

        // Replacement
        if (form.getReplacement() != null) {
            StratoItemReplacementForm replacementForm = form.getReplacement();
            Item replacementItem = findBySku(replacementForm.getSku(), itemsBySku);
            replacementItem.setShipment(item.getShipment());
            replacementItem.setRequestedQty(replacementForm.getFoundQty());
            replacementItem.setFoundQty(replacementForm.getFoundQty());
            if (replacementItem.getAverageWeight() != null)
                replacementItem.setActualWeight(replacementForm.getWeight());

            replacementItem.setReplacedItem(item);

            validateReplacementItem(replacementItem, item);
            itemService.save(stockLocation, replacementItem, country.getIsoName());
        }
    }

    private void validateReplacementItem(Item replacementItem, Item parentItem) {
        if (replacementItem.getFoundQty() <= 0)
            throw new InvalidReplacementQtyException("Found quantity should not zero.");

        if (parentItem.getReplacements().size() > 1)
            throw new InvalidReplacementItemException("Total replacements should not more than one.");

        int allowedReplacementQty = parentItem.getRequestedQty() - parentItem.getBundleQty();
        if (allowedReplacementQty <= 0 || allowedReplacementQty <= parentItem.getFoundQty())
            throw new InvalidReplacementItemException("Item replacement is not allowed.");
    }

    private void validateItemWeightAdjustment(Item item, Country country) {
        int foundQuantity = item.getFoundQty();
        if (foundQuantity == 0)
            return;

        Double averageWeight = item.getAverageWeight();
        if (averageWeight == null)
            return;

        Double actualWeight = item.getActualWeight();
        if (actualWeight == null)
            throw new InvalidItemWeightException();

        double upperAdjustment = (double) country.getUpperWeightAdjustmentThreshold() / 100;
        double lowerAdjustment = (double) country.getLowerWeightAdjustmentThreshold() / 100;
        double foundAverageWeight = foundQuantity * averageWeight;

        if (actualWeight < foundAverageWeight * (1 + lowerAdjustment))
            throw new InvalidItemWeightException();
        if (actualWeight > foundAverageWeight * (1 + upperAdjustment))
            throw new InvalidItemWeightException();
    }

    private Item findBySku(String sku, List<ItemForm> itemsBySku) {
        Optional<ItemForm> itemForm = itemsBySku.stream().filter(i -> StringUtils.equals(i.getSku(), sku)).findAny();
        if (!itemForm.isPresent())
            throw new EntityNotFoundException();

        return itemMapper.itemFormToItem(itemForm.get());
    }

    private void transitionToStartShopping(User user, Job job) {
        Batch batch = job.getBatch();
        if (batch.getUser() == null) {
            batch.setUser(user);
            batchRepository.save(batch);
        }

        job.setState(Job.State.STARTED);
        jobRepository.save(job);

    }

    private Job getShoppingJob(Shipment shipment) {
        Optional<Job> shoppingJob = shipment.getJobs().stream().filter(job -> job.isShopping() || job.isOnDemandShopping() || job.isRanger() || job.isOnDemandRanger()).findAny();

        if (!shoppingJob.isPresent())
            throw new EntityNotFoundException();

        return shoppingJob.get();
    }

    private void logExceptions(String eventName, String orderNumber, List<ApiError> exceptions) {
        try {
            logger.error("[StratoWebhookLog] Failed to process " + eventName + " for order " + orderNumber + ". Exceptions: " + mapper.writeValueAsString(exceptions));
        } catch (Exception e) {
            logger.error("[StratoWebhookLog] Failed to process " + eventName + " for order " + orderNumber + " and failed mapping exceptions to string", e);
        }
    }

    private void doDriverReassignment(StockLocation stockLocation, Shipment shipment,
                                      Job shoppingJob){
        try {
            driverReassignmentService.onDemandDriverReassignment(stockLocation, shipment, shoppingJob);
        } catch (Exception e){
            logger.warn("onDemandDriverReassignment for order "+shipment.getOrderNumber()+" failed ", e);
        }
    }

    @Transactional
    public void checkOrderStatus(String orderNumber) {
        Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
        if (shipment == null)
            return;

        List<ApiError> exceptions = new ArrayList<>();
        Optional<StratoWebhookForm> optionalOrderDetail = Optional.empty();
        try {
            // Hit Api and check order status
            optionalOrderDetail = stratoApiService.getOrderDetail(shipment.getOrderNumber());
        } catch (Exception exception) {
            exceptions.add(new ApiError(UnprocessableEntityException.class.getSimpleName(), "Cannot get order detail from strato. Message: " + exception.getMessage()));
        }

        if (exceptions.isEmpty() && optionalOrderDetail.isPresent()) {
            StratoWebhookForm orderDetail = optionalOrderDetail.get();
            RequestContextHolder.setRequestAttributes(new CustomRequestScopeAttr());
            RequestContextHolder.currentRequestAttributes().setAttribute(ApplicationUtil.LOCALE, "en", RequestAttributes.SCOPE_REQUEST);
            processShipment(shipment, orderDetail);
            RequestContextHolder.resetRequestAttributes();
        } else {
            logExceptions("CheckOrderStatus", orderNumber, exceptions);
        }
    }

    public List<StratoShopperCapacity> getShopperCapacity(String orderNumber,
                                                          Long externalStockLocationId,
                                                          int totalLineItems,
                                                          List<Slot> deliverySlots) {
        // initiate default response object, with all slots set to unavailable.
        List<StratoShopperCapacity> resultCapacities = deliverySlots.stream()
                .map(dSlot -> new StratoShopperCapacity(dSlot.getId(), dSlot.getStartTime(), false))
                .collect(Collectors.toList());

        List<StratoGetShopperCapacityPresenter> apiResponse = new ArrayList<>();
        try {
            List<LocalDateTime> deliveryStartTimes = deliverySlots.stream().map(Slot::getStartTime).collect(Collectors.toList());
            StratoGetShopperCapacityForm form = new StratoGetShopperCapacityForm(orderNumber, totalLineItems, deliveryStartTimes);
            apiResponse = stratoApiService.getShopperCapacity(externalStockLocationId, form);
        } catch (Exception e) {
            logger.error("[StratoService] Cannot get shopper capacity. Message: " + e.getMessage(), e);
        }

        if (!apiResponse.isEmpty()) {
            for (StratoShopperCapacity cap : resultCapacities) {
                Optional<StratoGetShopperCapacityPresenter> optMatchedResponse = apiResponse.stream()
                        .filter(sCap -> sCap.getDeliverySlot().equals(cap.getDeliverySlotStart()))
                        .findFirst();

                if (optMatchedResponse.isPresent()) {
                    cap.setAvailable(optMatchedResponse.get().getBookAvailable());
                } else {
                    cap.setAvailable(false);
                }
            }
        }

        return resultCapacities;
    }

    public List<StratoShopperCapacity> getShopperCapacityV2(String orderNumber,
                                                            Long externalStockLocationId,
                                                            List<Item> items,
                                                            List<Slot> deliverySlots) {
        List<StratoShopperCapacity> resultCapacities = deliverySlots.stream()
                .map(dSlot -> new StratoShopperCapacity(dSlot.getId(), dSlot.getStartTime(), false))
                .collect(Collectors.toList());

        List<StratoGetShopperCapacityPresenter> apiResponse = new ArrayList<>();
        try {
            List<LocalDateTime> deliveryStartTimes = deliverySlots.stream().map(Slot::getStartTime).collect(Collectors.toList());
            List<StratoOrderItemForm> orderItemsForm = items.stream().map(i -> new StratoOrderItemForm(i.getSku(), i.getRequestedQty())).collect(Collectors.toList());
            StratoGetShopperCapacityV2Form form = new StratoGetShopperCapacityV2Form(orderNumber, orderItemsForm, deliveryStartTimes);
            apiResponse = stratoApiService.getShopperCapacityV2(externalStockLocationId, form);
        } catch (Exception e) {
            logger.error("[StratoService] Cannot get shopper capacity. Message: " + e.getMessage(), e);
        }

        if (!apiResponse.isEmpty()) {
            for (StratoShopperCapacity cap : resultCapacities) {
                Optional<StratoGetShopperCapacityPresenter> optMatchedResponse = apiResponse.stream()
                        .filter(sCap -> sCap.getDeliverySlot().equals(cap.getDeliverySlotStart()))
                        .findFirst();

                if (optMatchedResponse.isPresent()) {
                    cap.setAvailable(optMatchedResponse.get().getBookAvailable());
                } else {
                    cap.setAvailable(false);
                }
            }
        }

        return resultCapacities;
    }

    public boolean bookShopperCapacity(String orderNumber, Long externalStockLocationId, int itemCounts, Slot slot) {
        List<ApiError> exceptions = new ArrayList<>();
        boolean isBooked = false;
        Optional<StratoBookShopperCapacityPresenter> optionalPresenter;
        StratoBookShopperCapacityForm form = new StratoBookShopperCapacityForm(orderNumber, itemCounts, slot.getStartTime());
        try {
            optionalPresenter = stratoApiService.bookShopperCapacity(externalStockLocationId, form);
        } catch (Exception exception) {
            exceptions.add(new ApiError(UnprocessableEntityException.class.getSimpleName(), "Cannot book shopper capacity to strato. Message: " + exception.getMessage()));
            return isBooked;
        }

        if (optionalPresenter.isPresent() && exceptions.isEmpty()) {
            StratoBookShopperCapacityPresenter response = optionalPresenter.get();
            isBooked = response.getBookAvailable();
        }
        return isBooked;
    }

    public boolean bookShopperCapacityV2(String orderNumber, Long externalStockLocationId, List<Item> items, Slot slot) {
        List<ApiError> exceptions = new ArrayList<>();
        boolean isBooked = false;
        Optional<StratoBookShopperCapacityPresenter> optionalPresenter;
        List<StratoOrderItemForm> orderItemsForm = items.stream().map(i -> new StratoOrderItemForm(i.getSku(), i.getRequestedQty())).collect(Collectors.toList());
        StratoBookShopperCapacityV2Form form = new StratoBookShopperCapacityV2Form(orderNumber, orderItemsForm, slot.getStartTime());
        try {
            optionalPresenter = stratoApiService.bookShopperCapacityV2(externalStockLocationId, form);
        } catch (Exception exception) {
            exceptions.add(new ApiError(UnprocessableEntityException.class.getSimpleName(), "Cannot book shopper capacity to strato. Message: " + exception.getMessage()));
            return isBooked;
        }

        if (optionalPresenter.isPresent() && exceptions.isEmpty()) {
            StratoBookShopperCapacityPresenter response = optionalPresenter.get();
            isBooked = response.getBookAvailable();
        }
        return isBooked;
    }

    public LocalDateTime checkExpressEarliestPickup(String orderNumber, Long externalStockLocationId, int totalLineItems, int bufferInMinutes) {
        LocalDateTime earliestPickup = LocalDateTime.now().plusDays(1);
        StratoExpressCapacityPresenter response = getExpressCapacity(orderNumber, externalStockLocationId, totalLineItems, bufferInMinutes);
        if (response != null)
            earliestPickup = response.getEarliestPickup();

        return earliestPickup;
    }

    public StratoExpressCapacityPresenter getExpressCapacity(String orderNumber, Long externalStockLocationId, int totalLineItems, int bufferInMinutes) {
        try {
            StratoExpressCapacityForm form = new StratoExpressCapacityForm(orderNumber, totalLineItems, bufferInMinutes);
            Optional<StratoExpressCapacityPresenter> optionalPresenter = stratoApiService.getExpressCapacity(externalStockLocationId, form);
            return optionalPresenter.orElse(null);
        } catch (Exception e) {
            logger.error("[StratoService] Cannot get express capacity. Message: " + e.getMessage(), e);
        }

        return null;
    }

    public StratoExpressCapacityPresenter bookExpressCapacity(String orderNumber, Long externalStockLocationId, int totalLineItems, int bufferInMinutes) {
        try {
            StratoBookExpressCapacityForm form = new StratoBookExpressCapacityForm(orderNumber, totalLineItems, bufferInMinutes);
            Optional<StratoExpressCapacityPresenter> optionalPresenter = stratoApiService.bookExpressCapacity(externalStockLocationId, form);
            return optionalPresenter.orElse(null);
        } catch (Exception e) {
            logger.error("[StratoService] Failed to book express capacity " + orderNumber + ". Message: " + e.getMessage(), e);
        }
        return null;
    }

    public void releaseExpressCapacity(String orderNumber, Long externalStockLocationId) {
        stratoApiService.releaseExpressCapacityWithRetry(orderNumber, externalStockLocationId);
    }

    @Transactional
    public void triggerPlaceOrderTPL(StratoEvent event) {
        Optional<StratoWebhookForm> optionalForm = getStratoWebhookForm(event);
        optionalForm.ifPresent(form -> {
            String orderNumber = form.getOrderNumber();
            triggerPlaceOrderTPL(orderNumber);
        });
    }

    @Transactional
    public void triggerPlaceOrderTPL(String orderNumber) {
        Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
        if (shipment == null)
            return;

        Job shoppingJob = getShoppingJob(shipment);
        if (!shoppingJob.getState().equals(Job.State.FINISHED))
            return;

        delyvaService.placeOrderInOneBatchAsync(shoppingJob.getBatch(), getSystemAdminAuthToken(shipment));
        lalamoveService.placeOrderInOneBatchAsync(shoppingJob.getBatch(), getSystemAdminAuthToken(shipment));
    }

    private Optional<StratoWebhookForm> getStratoWebhookForm(StratoEvent stratoEvent) {
        try {
            StratoWebhookForm form = mapper.readValue(stratoEvent.getPayload(), StratoWebhookForm.class);
            return Optional.of(form);
        } catch (IOException e) {
            logger.error("Failed mapping StratoEvent payload to StratoWebhookForm", e);
            return Optional.empty();
        }
    }

    private AbstractAuthenticationToken getSystemAdminAuthToken(Shipment shipment) {
        Tenant tenant = shipment.getTenant();
        Role role = roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, tenant.getId());
        User user = userRepository.findByRolesContainingAndTenantId(role, tenant.getId());

        AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), user.getTenant().getToken());
        SecurityContextHolder.getContext().setAuthentication(authenticationToken);
        return authenticationToken;
    }

    public void sendStratoDeliveryBooked(StockLocation stockLocation, Job job) {
        if (!stockLocation.isFulfilledByStrato() || job == null)
            return;

        Batch deliveryBatch = job.getBatch();
        if ((deliveryBatch.getType().equals(Batch.Type.DELIVERY) || deliveryBatch.getType().equals(Batch.Type.ON_DEMAND_DELIVERY))
                && deliveryBatch.getUser() != null) {
            stratoWebhookService.sendDeliveryBookedWebhookByBatch(adminBatchMapper.toAdminBatchPresenter(deliveryBatch));
        }
    }

    public LocalDateTime getSlotStartTime(StockLocation stockLocation, int onDemandBufferTime) {
        LocalDateTime now = LocalDateTime.now();
        Tenant tenant = stockLocation.getTenant();
        if(
                Boolean.TRUE.equals(tenant.isEnabledExpressHfsEtaAdjustment()) &&
                        stockLocation.isFulfilledByStrato() &&
                        stockLocation.enableExpressHfsLogic()

        ){
            now = now.plusMinutes(onDemandBufferTime);
        }
        return now;
    }

}
