package com.happyfresh.fulfillment.enabler.mapper;

import com.happyfresh.fulfillment.enabler.form.EnablerReceiptForm;
import com.happyfresh.fulfillment.enabler.form.EnablerReceiptItemForm;
import com.happyfresh.fulfillment.enabler.presenter.*;
import com.happyfresh.fulfillment.entity.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EnablerReceiptMapper {

    @Mapping(source = "receiptNumber", target = "number")
    Receipt toReceipt(EnablerReceiptForm form);

    @Mapping(source = "itemSku", target = "sku")
    ReceiptItem toReceiptItem(EnablerReceiptItemForm form);

    @Mapping(source = "shipment.orderNumber", target = "orderNumber")
    @Mapping(source = "number", target = "receiptNumber")
    @Mapping(source = "receiptItems", target = "items")
    EnablerReceiptPresenter toEnablerReceiptPresenter(Receipt receipt);

    @Mapping(source = "sku", target = "itemSku")
    EnablerReceiptItemPresenter toReceiptItemPresenter(ReceiptItem receiptItem);

    List<EnablerReceiptItemPresenter> toReceiptItemPresenters(List<ReceiptItem> receiptItems);

}
