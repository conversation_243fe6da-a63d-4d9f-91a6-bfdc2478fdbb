package com.happyfresh.fulfillment.tpl.delyva.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.JsonObject;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.service.WebhookPublisherService;
import com.happyfresh.fulfillment.entity.DelyvaDelivery;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.Tenant;
import com.happyfresh.fulfillment.repository.DelyvaDeliveryRepository;
import com.happyfresh.fulfillment.tpl.delyva.mapper.DelyvaDeliveryMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;

@Service
public class DelyvaDeliveryMessagingService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private WebhookPublisherService webhookPublisherService;

    @Autowired
    private DelyvaDeliveryRepository delyvaDeliveryRepository;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private DelyvaDeliveryMapper delyvaDeliveryMapper;

    @Transactional
    public void publishFailedBookingWebhook(Shipment shipment, String errorMessage) {
        ObjectNode objectNode = getWebhookErrorObjectResponse(errorMessage);

        String path = String.format("/shipments/%s/", shipment.getNumber());
        Tenant tenant = shipment.getTenant();
        delyvaDeliveryRepository.findByShipment(shipment).ifPresent(
                delyvaDelivery -> delyvaDeliveryRepository.delete(delyvaDelivery.getId())
        );
        WebhookType webhookType = WebhookType.DELYVA_BOOKING_ERROR_SWITCH_TO_HF;
        webhookPublisherService.publish(webhookType, objectNode, path, tenant.getId());
    }

    @Transactional
    public void publishSynchronizeWebhook(DelyvaDelivery delivery) {
        String path = String.format("/shipments/%s/", delivery.getShipment().getNumber());
        Tenant tenant = delivery.getTenant();
        WebhookType webhookType = WebhookType.DELYVA_SYNCHRONIZE;
        String rootName = "delyva_delivery";
        Object body = delyvaDeliveryMapper.toDelyvaDeliveryPresenter(delivery);
        ObjectNode wrapperNode = JsonNodeFactory.instance.objectNode();
        JsonNode bodyNode = mapper.convertValue(body, JsonNode.class);
        wrapperNode.set(rootName, bodyNode);

        webhookPublisherService.publish(webhookType, wrapperNode, path, tenant.getId());
    }

    private ObjectNode getWebhookErrorObjectResponse(String errorMessage) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("error", errorMessage);
        ObjectNode objectNode = null;
        try {
            objectNode = (ObjectNode) mapper.readTree(jsonObject.toString());
        } catch (IOException e) {
            logger.error("Webhook Error: {}", errorMessage);
        }
        return objectNode;
    }
}
