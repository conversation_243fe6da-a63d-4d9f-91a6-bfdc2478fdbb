package com.happyfresh.fulfillment.tpl.delyva.presenter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class DelyvaDeliveryPresenter {

    private Long id;

    private Long batchId;

    private String shipmentNumber;

    private String orderNumber;

    private String status;

    private String externalOrderId;

    private String driverName;

    private String driverPhone;

    private String trackingUrl;

    private String serviceCode;

    private String driverPhotoUrl;

    private String driverPlateNumber;

    private String serviceName;

    private String serviceDescription;
    
    private boolean enableSwitchHF;

    @JsonIgnore
    private LocalDateTime latestStatusChangedAt;

    @JsonProperty("latest_status_changed_at")
    public String getLatestStatusChangedAtString() {
        if (latestStatusChangedAt != null)
            return latestStatusChangedAt.toString();
        return null;
    }

    public void setLatestStatusChangedAtString(String latestStatusChangedAtString) {
        latestStatusChangedAt = DateTimeUtil.nullableStringToLocalDateTime(latestStatusChangedAtString);
    }

    @JsonIgnore
    private LocalDateTime scheduledAt;

    @JsonProperty("scheduled_at")
    public String getScheduledAtString() {
        return scheduledAt.toString();
    }

    public void setScheduledAtString(String scheduledAtString) {
        scheduledAt = DateTimeUtil.nullableStringToLocalDateTime(scheduledAtString);
    }

    @JsonIgnore
    private LocalDateTime slotStartTime;

    @JsonProperty("slot_start_time")
    public String getSlotStartTimeString() {
        return slotStartTime.toString();
    }

    public void setSlotStartTimeString(String slotStartTimeString) {
        slotStartTime = DateTimeUtil.nullableStringToLocalDateTime(slotStartTimeString);
    }

    @JsonIgnore
    private LocalDateTime slotEndTime;

    @JsonProperty("slot_end_time")
    public String getSlotEndTimeString() {
        return slotEndTime.toString();
    }

    public void setSlotEndTimeString(String slotEndTimeString) {
        slotEndTime = DateTimeUtil.nullableStringToLocalDateTime(slotEndTimeString);
    }

    @JsonIgnore
    private LocalDateTime createdAt;

    @JsonProperty("created_at")
    public String getCreatedAtString() {
        return createdAt.toString();
    }

    public void setCreatedAtString(String createdAtString) {
        createdAt = DateTimeUtil.nullableStringToLocalDateTime(createdAtString);
    }
}
