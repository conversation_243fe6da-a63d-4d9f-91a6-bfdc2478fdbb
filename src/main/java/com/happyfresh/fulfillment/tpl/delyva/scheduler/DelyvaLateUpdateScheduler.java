package com.happyfresh.fulfillment.tpl.delyva.scheduler;

import com.happyfresh.fulfillment.tpl.delyva.service.DelyvaDeliveryUpdateSchedulerService;
import net.javacrumbs.shedlock.core.LockAssert;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(name = "scheduler.enabled", matchIfMissing = true)
public class DelyvaLateUpdateScheduler {

    @Autowired
    private DelyvaDeliveryUpdateSchedulerService delyvaDeliveryUpdateSchedulerService;

    @Scheduled(cron = "0 30 15 * * *")
    @SchedulerLock(name = "delyvaLateUpdateScheduler", lockAtMostFor = "9m", lockAtLeastFor = "9m")
    public void delyvaLateUpdateScheduler() {
        LockAssert.assertLocked();
        delyvaDeliveryUpdateSchedulerService.lateUpdateScheduler();
    }

}
