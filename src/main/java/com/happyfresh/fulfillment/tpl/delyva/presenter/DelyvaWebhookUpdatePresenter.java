package com.happyfresh.fulfillment.tpl.delyva.presenter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.tpl.delyva.model.DelyvaCoord;
import com.happyfresh.fulfillment.tpl.delyva.model.DelyvaPersonnel;
import com.happyfresh.fulfillment.tpl.delyva.model.DelyvaStatusCode;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DelyvaWebhookUpdatePresenter {
    private String companyId;
    private String orderId;
    private int customerId;
    private String userId;
    private String consignmentNo;
    private DelyvaStatusCode statusCode;
    private String statusText;
    private String description;
    private String location;
    private Integer driverId;
    private int taskId;
    private String podSign;
    private String podName;
    private String podMobile;
    private String podNRIC;
    private Metadata metadata;
    private int id;
    private DelyvaCoord coord;
    private DelyvaPersonnel personnel;
    private Arrival arrival;
    private String invoiceId;
    private String serviceCode;
    private int cancelCount;

    public void setStatusCode(int statusCode) {
        this.statusCode = DelyvaStatusCode.forValues(statusCode);
    }

    @JsonIgnore
    private LocalDateTime createdAt;

    @JsonProperty("createdAt")
    public String getCreatedAtString() {
        if (createdAt != null)
            return DateTimeUtil.localDateTimeToSpreeStringDateTime(createdAt);
        return null;
    }

    public void setCreatedAtString(String createdAtString) {
        if (createdAtString == null)
            createdAt = null;
        else
            createdAt = DateTimeUtil.spreeStringToLocalDateTime(createdAtString);
    }

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Metadata {
        private DelyvaPersonnel driver;
    }

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Distance {
        private int value;
        private String unit;
    }

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Duration {
        private int value;
        private String unit;
    }

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Arrival {
        private Distance distance;
        private Duration duration;
        private int accuracy;
    }
}
