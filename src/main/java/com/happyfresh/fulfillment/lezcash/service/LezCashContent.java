package com.happyfresh.fulfillment.lezcash.service;

import com.happyfresh.fulfillment.entity.Batch;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.lezcash.model.LezCashItemPayload;
import com.happyfresh.fulfillment.lezcash.model.LezCashPayload;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.List;

@Service
public class LezCashContent {

    @Transactional(readOnly = true)
    public LezCashPayload constructContent(Batch batch) {
        LezCashPayload payload = new LezCashPayload();
        payload.setReqId(batch.getId().toString());
        payload.setDstId(batch.getUser().getLezCashDestinationId());

        List<LezCashItemPayload> itemsPayload = new ArrayList<>();
        for (Shipment shipment : batch.getShipmentsForLezCash()) {
            LezCashItemPayload itemPayload = new LezCashItemPayload();
            itemPayload.setItemId(shipment.getOrderNumber());
            itemPayload.setCurrency("IDR");
            itemPayload.setAmount(shipment.getOrderTotal().intValue());
            itemsPayload.add(itemPayload);
        }
        payload.setItems(itemsPayload);
        return payload;
    }
}
