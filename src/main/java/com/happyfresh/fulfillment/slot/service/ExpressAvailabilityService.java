package com.happyfresh.fulfillment.slot.service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.exception.type.SlotUnavailableException;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.service.DistanceService;
import com.happyfresh.fulfillment.common.service.JedisLockService;
import com.happyfresh.fulfillment.common.util.I18nUtil;
import com.happyfresh.fulfillment.common.util.LocaleUtil;
import com.happyfresh.fulfillment.common.util.WebRequestLogger;
import com.happyfresh.fulfillment.enabler.service.StratoService;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.Slot;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.entity.Tenant;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.DDFMatrixRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.repository.StockLocationRepository;
import com.happyfresh.fulfillment.shipment.form.ShipmentForm;
import com.happyfresh.fulfillment.shipment.service.ShipmentService;
import com.happyfresh.fulfillment.slot.bean.AppliedFee;
import com.happyfresh.fulfillment.slot.bean.DDFCalculationContext;
import com.happyfresh.fulfillment.slot.bean.SlotAvailabilityBuilder;
import com.happyfresh.fulfillment.slot.bean.SlotAvailabilityContext;
import com.happyfresh.fulfillment.slot.bean.availability.AbstractAvailabilityChain;
import com.happyfresh.fulfillment.slot.mappper.ExpressMapper;
import com.happyfresh.fulfillment.slot.mappper.SlotAvailabilityContextMapper;
import com.happyfresh.fulfillment.slot.presenter.ExpressAvailabilityPresenter;
import com.happyfresh.fulfillment.slot.presenter.FeePresenter;
import com.happyfresh.fulfillment.slot.service.ddf.*;
import com.happyfresh.fulfillment.tpl.delyva.service.DelyvaService;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Service
public class ExpressAvailabilityService {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private StockLocationRepository stockLocationRepository;

    @Autowired
    private ShipmentService shipmentService;

    @Autowired
    private OnDemandAvailabilityService onDemandAvailabilityService;

    @Autowired
    private SlotAvailabilityService slotAvailabilityService;

    @Autowired
    private ExpressMapper expressMapper;

    @Autowired
    private DistanceService distanceService;

    @Autowired
    private DDFMatrixRepository ddfMatrixRepository;

    @Autowired
    private NextHourSlotService nextHourSlotService;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private SlotAvailabilityContextMapper slotAvailabilityContextMapper;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private DelyvaService delyvaService;

    @Lazy
    @Autowired
    private StratoService stratoService;

    private final String MAP_KEY_ON_DEMAND = "onDemand";

    private final String MAP_KEY_NEXT_HOUR = "nextHour";

    @Transactional(readOnly = true)
    public ExpressAvailabilityPresenter checkExpressAvailability(ShipmentForm shipmentForm) throws Exception {
        StockLocation stockLocation = stockLocationRepository.findByExternalId(shipmentForm.getStockLocationId());
        // Check on demand enable and next hour enable, if false return disabled
        if (checkExpressDisabled(stockLocation))
            return getExpressPresenter(null, null, null, false, false, false, null);

        // Get next hour slot
        Slot slot = nextHourSlotService.getNextHourSlot(stockLocation, Lists.newArrayList(stockLocation.getCluster().getSlotType()));

        // Build context normal slot
        shipmentService.clearShipment(shipmentForm, stockLocation, false, true);
        Shipment shipment = shipmentService.convertShipmentFormToShipment(shipmentForm, stockLocation);
        shipment.setIsExpress(true);
        Locale locale = LocaleUtil.findLocaleByCountry(stockLocation.getState().getCountry().getIsoName());
        Map<String, SlotAvailabilityContext> contextList = new HashMap<>();

        // Check available
        boolean isOnDemandRanger = false;
        boolean isAvailable = false;

        if (eligibleForNextHourSlotChecking(stockLocation)) {
            isAvailable = isNextSlotAvailable(stockLocation, shipment, slot, contextList, false);
            LOGGER.info("[Availability] isNextSlotAvailable: {}", isAvailable);
        }

        // Check if orders is big volume or should be delivered by car
        if (isAvailable) {
            boolean isBigVolumeOrDeliveredByCar = shipmentService.isBigVolumeOrDeliveredByCar(shipment);
            LOGGER.info("[Availability] Express isBigVolumeOrDeliveredByCar: {}", isBigVolumeOrDeliveredByCar);

            if (isBigVolumeOrDeliveredByCar) {
                isAvailable =  false;
            }
        }

        if (!isAvailable && eligibleForOnDemandSlotChecking(stockLocation)) {
            // If not available build context on demand slot
            isOnDemandRanger = true;
            isAvailable = isOnDemandAvailable(stockLocation, shipment, slot, contextList, false);
            LOGGER.info("[Availability] isOnDemandAvailable: {}", isAvailable);
        }

        SlotAvailabilityContext slotAvailabilityContext = isOnDemandRanger ? contextList.get(MAP_KEY_ON_DEMAND) : contextList.get(MAP_KEY_NEXT_HOUR);

        // Check on demand fee
        DDFCalculationContext ddfCalculationContext = new DDFCalculationContext.Builder()
                .withStockLocation(stockLocation)
                .withShipmentContext(shipment, distanceService, 0, true)
                .withDDFMatrixConfig(ddfMatrixRepository)
                .withBatchCounts(slotAvailabilityContext.getBatchCounts())
                .withSlotsInCluster(slotAvailabilityContext.getClusteredSlots())
                .withNoOfShipmentsPerSlot(getNoOfShipmentsPerSlot(stockLocation.getId(), LocalDate.now(), shipmentForm.getOrderNumber()))
                .build();

        DDFCalculationServiceChain.Builder ddfServiceChainBuilder =  new DDFCalculationServiceChain.Builder();
        Tenant tenant = stockLocation.getTenant();
                    
        if (tenant.isEnableExpressAdditionalFee() && slot != null) {
            ddfServiceChainBuilder.addServiceChain(BikeFeeService.class)
                    .addServiceChain(CarFeeService.class)
                    .addServiceChain(PeakHourOnDemandFeeService.class)
                    .addServiceChain(VanFeeService.class)
                    .addServiceChain(ExpressAdditionalFeeService.class);
        } else {
            ddfServiceChainBuilder.addServiceChain(OnDemandFeeService.class);
        }

        DDFCalculationServiceChain ddfServiceChain = ddfServiceChainBuilder.build(stockLocation.getDdfType());
        AppliedFee appliedFee = ddfServiceChain.calculateFee(ddfCalculationContext, slot);
        // HC and High Order Volume get Van price + express fee
        if (shipment.getOrderCompanyId() != null && shipment.getIsHighOrderVolume()) {
            appliedFee.setTotal(appliedFee.getTotalHighVolumeOrder());
        } else {
            appliedFee.setTotal(appliedFee.getTotalTwoWheel());
        }
        if (locale.toString().equalsIgnoreCase("in_id"))
            appliedFee.setTotal(I18nUtil.roundToNearestThousand(appliedFee.getTotal()));

        String availabilityReason = "default";
        ExpressAvailabilityPresenter expressPresenter = getExpressPresenter(slot, appliedFee, locale, true, isAvailable, isOnDemandRanger, slotAvailabilityContext);
        expressPresenter.setAvailabilityReason(availabilityReason);

        return expressPresenter;
    }

    private boolean eligibleForNextHourSlotChecking(StockLocation sl) {
        if (sl.getEnabler() != null && sl.getEnabler().equals(StockLocation.Enabler.HYPERMART))
            return false;
        if (sl.getEnabler() != null && sl.getEnabler().equals(StockLocation.Enabler.SAYURBOX))
            return false;
        if (sl.getEnabler() != null && sl.getEnabler().equals(StockLocation.Enabler.SAYURBOX_INSTANT))
            return false;
        if (sl.isFulfilledByStrato())
            return !sl.enableExpressHfsLogic();

        return true;
    }

    private boolean eligibleForOnDemandSlotChecking(StockLocation sl) {
        if (sl.getEnabler() != null && sl.getEnabler().equals(StockLocation.Enabler.HYPERMART))
            return false;
        if (sl.getEnabler() != null && sl.getEnabler().equals(StockLocation.Enabler.SAYURBOX))
            return false;
        if (sl.isFulfilledByStrato())
            return sl.enableExpressHfsLogic();

        return true;
    }

    private ExpressAvailabilityPresenter getExpressPresenter(Slot slot, AppliedFee appliedFee, Locale locale, boolean isEnabled, boolean isAvailable, boolean isOnDemandRanger, SlotAvailabilityContext context) {
        ExpressAvailabilityPresenter presenter;

        if (!isEnabled) {
            presenter = new ExpressAvailabilityPresenter();
        } else {
            presenter = expressMapper.toExpressAvailabilityPresenter(context.getStockLocation(), appliedFee, locale);
            // Hack: To always return normal price level
            FeePresenter feePresenter = presenter.getFee();
            feePresenter.setLevel(FeePresenter.Level.NORMAL);
            presenter.setFee(feePresenter);
        }
        LocalDateTime slotStartTime = LocalDateTime.now();

        presenter.setContainsAlcohol(false);
        presenter.setStartTime(slotStartTime);
        presenter.setEndTime(slotStartTime);
        presenter.setAvailable(isAvailable);
        presenter.setVisible(isAvailable);
        presenter.setEnable(isEnabled);
        presenter.setOnDemandSlot(isOnDemandRanger);
        presenter.setSlotType(Slot.Type.ON_DEMAND.toString());

        if (!isEnabled)
            return presenter;

        if (isOnDemandRanger) {
            presenter.setFleetType(Slot.FleetType.HF);
            presenter.setAllowCod(true);
            presenter.setAllowEwallet(true);
        } else if (slot != null) {
            presenter.setFleetType(slot.getFleetType());
            presenter.setFleetName(slot.getFleetName());
            presenter.setAllowCod(slot.isAllowCod());
            presenter.setAllowEwallet(slot.isAllowEwallet());
        }

        if (context != null) {
            presenter.setContainsAlcohol(context.isContainsAlcohol());
            presenter.setUnavailableReason(context.getUnavailableReason());

            StockLocation stockLocation = context.getStockLocation();
            int onDemandBufferTime = (int) stockLocation.getOnDemandBufferTime();
            slotStartTime = stratoService.getSlotStartTime(stockLocation, onDemandBufferTime);
            presenter.setStartTime(slotStartTime);
            presenter.setEndTime(slotStartTime.plusMinutes(stockLocation.getOnDemandDeliveryTime()));
        }

        return presenter;
    }

    private boolean checkExpressDisabled(StockLocation stockLocation) {
        return !stockLocation.isEnableOnDemandDelivery();
    }

    @Transactional
    public Shipment reservedSlot(ShipmentForm shipmentForm) throws Exception {
        StockLocation stockLocation = stockLocationRepository.findByExternalId(shipmentForm.getStockLocationId());

        String lockKey = String.format("stock_location_id_%d_slot_on_demand", stockLocation.getId());
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(lockKey)) {
                Shipment shipment = shipmentRepository.findByNumber(shipmentForm.getNumber());

                if (shipment != null && !shipment.getJobs().isEmpty() && shipment.getSlot().getType() == Slot.Type.ON_DEMAND) {
                    return shipment;
                }
                shipmentService.clearShipment(shipmentForm, stockLocation, true, true);
                shipment = shipmentService.convertShipmentFormToShipment(shipmentForm, stockLocation);

                return reservedSlot(shipment, stockLocation);
            } else {
                final ImmutableMap<String, String> additionalInfo = ImmutableMap.of(
                        "SHIPMENT_NUMBER", shipmentForm.getNumber(),
                        "LOCK_KEY", lockKey
                );
                WebRequestLogger webRequestLogger = applicationContext.getBean(WebRequestLogger.class);
                webRequestLogger.error(LOGGER, "Acquire redis lock timeout", additionalInfo);
                throw new SlotUnavailableException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    private Shipment reservedSlot(Shipment shipment, StockLocation stockLocation) throws Exception {
        // Check on demand enable and next hour enable, if false return disabled
        if (checkExpressDisabled(stockLocation))
            throw new SlotUnavailableException();
        // Get next hour slot
        Slot slot = nextHourSlotService.getNextHourSlot(stockLocation, Lists.newArrayList(stockLocation.getCluster().getSlotType()));

        shipment.setIsExpress(true);
        shipment = shipmentService.saveShipment(shipment, slot, stockLocation);

        Locale locale = LocaleUtil.findLocaleByCountry(stockLocation.getState().getCountry().getIsoName());
        Map<String, SlotAvailabilityContext> contextList = new HashMap<>();
        boolean isAvailable = false;
        boolean isOnDemandRanger = false;
        if (stockLocation.getExpressSetSlotPrioritizeOnDemand() && eligibleForOnDemandSlotChecking(stockLocation)) {
            isOnDemandRanger = true;
            isAvailable = isOnDemandAvailable(stockLocation, shipment, slot, contextList, true);
            if (!isAvailable &&  eligibleForNextHourSlotChecking(stockLocation)) {
                isOnDemandRanger = false;
                isAvailable = isNextSlotAvailable(stockLocation, shipment, slot, contextList, true);
            }
        } else {
            if (eligibleForNextHourSlotChecking(stockLocation))
                isAvailable = isNextSlotAvailable(stockLocation, shipment, slot, contextList, true);

            if (!isAvailable && eligibleForOnDemandSlotChecking(stockLocation)) {
                isOnDemandRanger = true;
                isAvailable = isOnDemandAvailable(stockLocation, shipment, slot, contextList, true);
            }
        }

        SlotAvailabilityContext slotAvailabilityContext = isOnDemandRanger ? contextList.get(MAP_KEY_ON_DEMAND) : contextList.get(MAP_KEY_NEXT_HOUR);

        if (isAvailable) {
            // Check on demand fee
            DDFCalculationContext ddfCalculationContext = new DDFCalculationContext.Builder()
                    .withStockLocation(stockLocation)
                    .withShipmentContext(shipment, distanceService, 0, true)
                    .withDDFMatrixConfig(ddfMatrixRepository)
                    .withBatchCounts(slotAvailabilityContext.getBatchCounts())
                    .withSlotsInCluster(slotAvailabilityContext.getClusteredSlots())
                    .withNoOfShipmentsPerSlot(getNoOfShipmentsPerSlot(stockLocation.getId(), LocalDate.now(), shipment.getOrderNumber()))
                    .build();


            DDFCalculationServiceChain.Builder ddfServiceChainBuilder =  new DDFCalculationServiceChain.Builder();
            Tenant tenant = stockLocation.getTenant();
            if (tenant.isEnableExpressAdditionalFee() && slot != null) {
                ddfServiceChainBuilder.addServiceChain(BikeFeeService.class)
                        .addServiceChain(CarFeeService.class)
                        .addServiceChain(PeakHourOnDemandFeeService.class)
                        .addServiceChain(VanFeeService.class)
                        .addServiceChain(ExpressAdditionalFeeService.class);
            } else {
                ddfServiceChainBuilder.addServiceChain(OnDemandFeeService.class);
            }

            DDFCalculationServiceChain ddfServiceChain = ddfServiceChainBuilder.build(stockLocation.getDdfType());
            AppliedFee appliedFee = ddfServiceChain.calculateFee(ddfCalculationContext, slot);
            // HC and High Order Volume get Van price + express fee
            if (shipment.getOrderCompanyId() != null && shipment.getIsHighOrderVolume()) {
                appliedFee.setTotal(appliedFee.getTotalHighVolumeOrder());
            } else {
                appliedFee.setTotal(appliedFee.getTotalTwoWheel());
            }
            if (locale.toString().equalsIgnoreCase("in_id"))
                appliedFee.setTotal(I18nUtil.roundToNearestThousand(appliedFee.getTotal()));

            if (delyvaService.isSlotDelyva(stockLocation, slot)) {
                delyvaService.createInitialDelyvaDelivery(shipment, appliedFee, slotAvailabilityContext.getSelectedDelyvaService());
            }

            shipment.setCost(appliedFee.getTotal());
            shipment = shipmentRepository.save(shipment);
            return shipment;
        } else {
            throw new SlotUnavailableException();
        }
    }

    private Map<Long, Long> getNoOfShipmentsPerSlot(Long stockLocationId, LocalDate date, String orderNumber) {
        if (date == null) {
            date = LocalDate.now();
        }
        List<Object[]> listShipmentsPerSlot = shipmentRepository.getNoOfShipmentsPerSlot(stockLocationId, date.minusDays(1).atStartOfDay(), date.plusDays(2).atStartOfDay(), orderNumber);
        Map<Long, Long> noOfShipmentsPerSlot = new HashMap<>();
        for (Object[] obj : listShipmentsPerSlot) {
            noOfShipmentsPerSlot.put(((BigInteger) obj[0]).longValue(),  ((BigInteger) obj[1]).longValue());
        }
        return noOfShipmentsPerSlot;
    }

    private boolean isNextSlotAvailable(StockLocation stockLocation, Shipment shipment, Slot slot, Map<String, SlotAvailabilityContext> contextList, boolean isReservedSlot) {
        if (slot != null) {
            LOGGER.info("[Availability] isNextSlotAvailable slot start time {}", slot.getStartTime());
            LOGGER.info("[Availability] isNextSlotAvailable stockLocation.getExpressDeliveryOperationalCutoffTime {}", stockLocation.getExpressDeliveryOperationalCutoffTime());
            if (LocalDateTime.now().plusMinutes(stockLocation.getExpressDeliveryOperationalCutoffTime()).isAfter(slot.getStartTime())) {
                return false;
            }
        }

        LOGGER.info("[Availability] isNextSlotAvailable shipment.isExpressDelivery() {}", shipment.isExpressDelivery());

        Slot.Type slotType = stockLocation.getCluster().getSlotType();
        Pair<LocalDateTime, LocalDateTime> dateFilter = slotAvailabilityService.getStartAndEndTime(stockLocation, LocalDateTime.now().toLocalDate());
        SlotAvailabilityBuilder slotAvailabilityBuilder = slotAvailabilityService.buildSlotAvailabilityBuilder(slotType, stockLocation, shipment, dateFilter.getKey(), dateFilter.getValue(), isReservedSlot);
        SlotAvailabilityContext slotAvailabilityContext;
        if (isReservedSlot) {
            slotAvailabilityContext = slotType == Slot.Type.LONGER_DELIVERY ?
                    slotAvailabilityBuilder.buildLongerDeliveryReservedSlotContext() : slotAvailabilityBuilder.buildReservedSlotContext();
        } else {
            slotAvailabilityContext = slotType == Slot.Type.LONGER_DELIVERY ?
                    slotAvailabilityBuilder.buildLongerDeliveryAvailabilityContext() : slotAvailabilityBuilder.buildContext();
        }
        slotAvailabilityContext.setSlotTrackingId(generateTrackingId());

        contextList.put(MAP_KEY_NEXT_HOUR, slotAvailabilityContext);
        AbstractAvailabilityChain slotAvailability = slotAvailabilityService.buildSlotAvailabilityChain(slotType, stockLocation, slotAvailabilityBuilder, slotAvailabilityContext);
        if (slot != null) {
            slot.setFleetType(Slot.FleetType.HF);
            slot.setVehicleType(Slot.FleetVehicleType.TWO_WHEELS);
            slot.setAllowCod(true);
            slot.setAllowEwallet(true);
            return slotAvailability.isAvailable(slot);
        }
        return false;
    }

    private boolean isOnDemandAvailable(StockLocation stockLocation, Shipment shipment, Slot slot, Map<String, SlotAvailabilityContext> contextList, boolean isReservedSlot) {
        SlotAvailabilityBuilder slotAvailabilityBuilder = onDemandAvailabilityService.buildOnDemandSlotAvailabilityBuilder(stockLocation, shipment);
        SlotAvailabilityContext slotAvailabilityContext = isReservedSlot ? slotAvailabilityBuilder.buildOnDemandDeliveryReservedSlotContext() : slotAvailabilityBuilder.buildOnDemandDeliveryAvailabilityContext();
        slotAvailabilityContext.setOnDemandEndTime(getOnDemandEndTime(slot, stockLocation));
        slotAvailabilityContext.setSlotTrackingId(generateTrackingId());

        contextList.put(MAP_KEY_ON_DEMAND, slotAvailabilityContext);
        AbstractAvailabilityChain slotAvailability;
        if (stockLocation.isFulfilledByStrato()) {
            slotAvailability = slotAvailabilityBuilder.buildOnDemandStratoAvailabilityChain(slotAvailabilityContext);
        } else if (stockLocation.isFulfilledBySayurboxInstant()) {
            slotAvailability = slotAvailabilityBuilder.buildSayurboxInstantAvailabilityChain(slotAvailabilityContext);
        } else {
            slotAvailability = slotAvailabilityBuilder.buildOnDemandAvailabilityChain(slotAvailabilityContext);
        }
        return slotAvailability.isAvailable(null);
    }

    private LocalDateTime getOnDemandEndTime(Slot nextHourSlot, StockLocation stockLocation) {
        if (nextHourSlot == null)
            return null;

        LocalDateTime nextHourEnd = nextHourSlot.getEndTime();
        long onDemandDeliveryTime = stockLocation.getOnDemandDeliveryTime();
        LocalDateTime nowPlusODSLA = LocalDateTime.now().plusMinutes(onDemandDeliveryTime);

        return Collections.max(Arrays.asList(nextHourEnd, nowPlusODSLA));
    }

    private String generateTrackingId() {
        return "EXP" + System.currentTimeMillis() + RandomStringUtils.random(4, false, true);
    }
}
