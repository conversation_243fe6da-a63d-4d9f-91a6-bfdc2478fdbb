package com.happyfresh.fulfillment.slot.service.ddf;

import com.happyfresh.fulfillment.entity.DdfVolumeDistance;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.Slot;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.slot.bean.AppliedFee;
import com.happyfresh.fulfillment.slot.bean.DDFCalculationContext;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

public class VolumeDistanceFeeService extends DDFCalculationServiceChain {

    @Override
    protected AppliedFee doCalculate(DDFCalculationContext ddfCalculationContext, Slot slot, AppliedFee chainingResultFee) {
        Optional<DdfVolumeDistance> ddfVolumeDistance = getDdfVolumeDistanceBasedOnVolume(ddfCalculationContext);

        if (!ddfVolumeDistance.isPresent() || ddfVolumeDistance.get().isStoreDdf()) {
            return callNextChainIfExist(ddfCalculationContext, slot, chainingResultFee);
        } else {
            chainingResultFee.setTotal(calculateTotalFee(ddfCalculationContext, ddfVolumeDistance.get()));
            return chainingResultFee;
        }
    }

    private Optional<DdfVolumeDistance> getDdfVolumeDistanceBasedOnVolume(DDFCalculationContext context) {
        StockLocation stockLocation = context.getStockLocation();
        Shipment shipment = context.getShipment();
        int totalVolumeInL = (int) Math.ceil(shipment.getTotalVolumeInML() / 1000);
        List<DdfVolumeDistance> ddfVolumeDistanceList = stockLocation.getDdfVolumeDistanceList();

        ddfVolumeDistanceList.sort(Comparator.comparing(DdfVolumeDistance::getLowerVolumeLimit));
        for (DdfVolumeDistance d : ddfVolumeDistanceList) {
            if (totalVolumeInL >= d.getLowerVolumeLimit() && totalVolumeInL <= d.getUpperVolumeLimit()) {
                return Optional.of(d);
            }
        }

        return Optional.empty();
    }

    private BigDecimal calculateTotalFee(DDFCalculationContext context, DdfVolumeDistance ddfVolumeDistance) {
        BigDecimal minimumFee = ddfVolumeDistance.getMinimumFee();
        BigDecimal fixedFee = ddfVolumeDistance.getFixedFee();
        BigDecimal costPerKm = ddfVolumeDistance.getCostPerKm();
        Double minimumFeeKm = ddfVolumeDistance.getMinimumFeeKm();
        Double shippingDistance = context.getShippingDistance();

        // minimumFee + fixedFee + (costPerKm * max(0, (ceiling(distance store to delivery address - minimumFeeKm)))
        BigDecimal remainingDistance = BigDecimal.valueOf(
            Math.max(0, Math.ceil(shippingDistance - minimumFeeKm))
        );
        return minimumFee.add(fixedFee).add(costPerKm.multiply(remainingDistance));
    }
}
