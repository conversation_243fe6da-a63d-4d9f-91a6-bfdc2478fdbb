package com.happyfresh.fulfillment.slot.service.ddf;

import com.happyfresh.fulfillment.entity.Company;
import com.happyfresh.fulfillment.entity.DdfCompany;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.Slot;
import com.happyfresh.fulfillment.slot.bean.AppliedFee;
import com.happyfresh.fulfillment.slot.bean.DDFCalculationContext;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import static java.util.Objects.nonNull;

public class CompanyFeeService extends DDFCalculationServiceChain {

    @Override
    protected AppliedFee doCalculate(DDFCalculationContext ddfCalculationContext, Slot slot, AppliedFee chainingResultFee) {

        if (ddfCalculationContext.getStockLocation().hasEnabler())
            return callNextChainIfExist(ddfCalculationContext, slot, chainingResultFee);

        if (nonNull(ddfCalculationContext.getCompany())) {
            Optional<DdfCompany> ddfCompany = getDdfCompanyBasedOnVolume(ddfCalculationContext);
            if (!ddfCompany.isPresent())
                return callNextChainIfExist(ddfCalculationContext, slot, chainingResultFee); // Skip company ddf and use store ddf chain as fallback
            if (ddfCompany.get().isStoreDdf())
                return callNextChainIfExist(ddfCalculationContext, slot, chainingResultFee); // Pass to next chain

            chainingResultFee.setTotal(calculateTotalFee(ddfCalculationContext, ddfCompany.get()));

            return chainingResultFee;
        }

        return callNextChainIfExist(ddfCalculationContext, slot, chainingResultFee);
    }

    private Optional<DdfCompany> getDdfCompanyBasedOnVolume(DDFCalculationContext context) {
        Shipment shipment = context.getShipment();
        Company company = context.getCompany();
        int totalVolumeInL = (int) Math.ceil(shipment.getTotalVolumeInML() / 1000);

        List<DdfCompany> ddfCompanyList = company.getDdfCompanyList();
        ddfCompanyList.sort(Comparator.comparing(DdfCompany::getLowerVolumeLimit));
        for (DdfCompany d : ddfCompanyList) {
            if (totalVolumeInL >= d.getLowerVolumeLimit() && totalVolumeInL <= d.getUpperVolumeLimit()) {
                return Optional.of(d);
            }
        }

        return Optional.empty();
    }

    private BigDecimal calculateTotalFee(DDFCalculationContext context, DdfCompany ddfCompany) {
        BigDecimal minimumFee = ddfCompany.getMinimumFee();
        BigDecimal fixedFee = ddfCompany.getFixedFee();
        BigDecimal costPerKm = ddfCompany.getCostPerKm();
        Double minimumFeeKm = ddfCompany.getMinimumFeeKm();
        Double shippingDistance = context.getShippingDistance();

        // HappyCorporate Delivery Fee:
        // minimumFee + fixedFee + (costPerKm * max(0, (ceiling(distance store to delivery address - minimumFeeKm)))
        BigDecimal remainingDistance = BigDecimal.valueOf(
            Math.max(0, Math.ceil(shippingDistance - minimumFeeKm))
        );
        return minimumFee.add(fixedFee).add(costPerKm.multiply(remainingDistance));
    }

}
