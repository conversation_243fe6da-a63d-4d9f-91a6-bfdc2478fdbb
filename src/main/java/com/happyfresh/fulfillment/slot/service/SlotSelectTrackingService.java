package com.happyfresh.fulfillment.slot.service;

import com.happyfresh.fulfillment.common.tracking.EventTrackingService;
import com.happyfresh.fulfillment.common.tracking.lambda.LambdaEvent;
import com.happyfresh.fulfillment.slot.presenter.tracking.selection.SlotPayload;
import com.happyfresh.fulfillment.slot.presenter.tracking.selection.SlotSelectPayload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class SlotSelectTrackingService {

    private static final String EVENT_NAME = "SET_SLOT";

    private EventTrackingService eventTrackingService;

    @Autowired
    public SlotSelectTrackingService(EventTrackingService eventTrackingService) {
        this.eventTrackingService = eventTrackingService;
    }

    @Async
    public void track(SlotPayload payload, String orderNumber) {
        SlotSelectPayload trackingPayload = new SlotSelectPayload(LocalDateTime.now(), orderNumber, payload);
        LambdaEvent event = new LambdaEvent<>(SlotSelectTrackingService.EVENT_NAME, trackingPayload);
        event.setUniqueName(orderNumber);
        eventTrackingService.track(event);
    }

}
