package com.happyfresh.fulfillment.slot.bean.availability;

import com.happyfresh.fulfillment.common.tracking.on_demand.OnDemandRangerAvailabilityTracker;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.Slot;
import com.happyfresh.fulfillment.sayurbox.model.SayurboxCapacity;
import com.happyfresh.fulfillment.sayurbox.service.SayurboxService;
import com.happyfresh.fulfillment.slot.bean.SlotAvailabilityContext;
import com.happyfresh.fulfillment.slot.service.SlotReservedService;
import com.happyfresh.fulfillment.slot.tracking.SlotAvailabilityTracker;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;

public class SayurboxInstantAvailability extends AbstractAvailabilityChain {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private final String DELIVERY_TYPE = "INSTANT";

    private SayurboxService sayurboxService;

    private SlotReservedService slotReservedService;

    private OnDemandRangerAvailabilityTracker currentAvailabilityTracking;

    public SayurboxInstantAvailability(
            SlotAvailabilityContext context,
            SayurboxService sayurboxService,
            SlotReservedService slotReservedService
    ) {
        super(context);
        this.slotReservedService = slotReservedService;
        this.sayurboxService = sayurboxService;
    }

    @Override
    public boolean isAvailable(Slot slot, SlotAvailabilityTracker tracker) {
        if (context.getStockLocation().isEnableOnDemandDelivery() && context.getStockLocation().isFulfilledBySayurboxInstant()) {
            try {
                boolean result = checkSayurboxAvailability();
                setAndTrackAvailabilityChain(slot, tracker, result);
                return result;
            } catch (Exception e) {
                ThreadContext.put("SHIPMENT_NUMBER", context.getShipment().getNumber());
                ThreadContext.put("ORDER_NUMBER", context.getShipment().getOrderNumber());
                LOGGER.error("Failed on sayurbox instant availability", e);
                ThreadContext.clearAll();

                setAndTrackAvailabilityChain(slot, tracker, false);
                return false;
            }
        } else {
            setAndTrackAvailabilityChain(slot, tracker, false);
            return false;
        }
    }

    private boolean checkSayurboxAvailability() {
        Shipment shipment = context.getShipment();
        List<SayurboxCapacity> sayurboxCapacities = sayurboxService.getTimeslots(
                DELIVERY_TYPE, shipment.getAddressLat(), shipment.getAddressLon(), shipment.getOrderNumber(), shipment.getOrderExternalCustomerId());
        boolean isAvailable = false;
        for (SayurboxCapacity sayurboxCapacity : sayurboxCapacities) {
            isAvailable = sayurboxCapacity.isAvailable();
        }

        if (!isAvailable) {
            return false;
        } else {
            if(context.isReservedSlot()) {
                slotReservedService.sayurboxInstantSlot(context.getShipment(), context.getStockLocation(), LocalDateTime.now(), LocalDateTime.now().plusHours(2));
            }
            return true;
        }
    }

}
