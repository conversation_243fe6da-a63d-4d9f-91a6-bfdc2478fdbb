package com.happyfresh.fulfillment.slot.scheduler;

import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.user.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.jobrunr.jobs.annotations.Recurring;
import org.jobrunr.jobs.context.JobRunrDashboardLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

@Component
@ConditionalOnProperty(name = "scheduler.enabled", matchIfMissing = true)
public class LongerDeliverySlotAndShiftGeneratorScheduler {

    @Autowired
    private ApplicationContext applicationContext;

    private static final Logger logger = new JobRunrDashboardLogger(LoggerFactory.getLogger(LongerDeliverySlotAndShiftGeneratorScheduler.class));

    @Autowired
    private StockLocationRepository stockLocationRepository;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private SlotRepository slotRepository;

    @Autowired
    private ShiftRepository shiftRepository;

    @Autowired
    private SchedulerLogRepository schedulerLogRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private TransactionHelper transactionHelper;

    @Recurring(id = "generate-slots-and-shift-job", cron = "30 4 * * *")
    @Transactional
    public void generateLongerDeliverySlotAndShift() {
        generateSlotsAndShiftsForAllTenant();
    }

    private void generateSlotsAndShiftsForAllTenant() {
        List<String> errorMessages = new ArrayList<>();
        LocalDateTime executionTime = LocalDateTime.now();

        for (Tenant tenant : tenantRepository.findAll()) {
            Role role = roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, tenant.getId());
            if (role != null) {
                User user = userRepository.findByRolesContainingAndTenantId(role, tenant.getId());

                AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), tenant.getToken());
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                List<StockLocation> stockLocations = stockLocationRepository.findAll();
                try {
                    logger.info("[LongerDeliverySlotAndShiftGenerator tenantId={}] START process", tenant.getId());
                    transactionHelper.withNewTransaction(() -> generateSlotsForOneTenant(stockLocations));
                    transactionHelper.withNewTransaction(() -> generateShiftsForOneTenant(stockLocations));
                } catch (Exception ex) {
                    String errMsg = "[LongerDeliverySlotAndShiftGenerator tenantId=" + tenant.getId() + "] ERROR message: " + ex.getMessage();
                    logger.info(errMsg);
                    errorMessages.add(errMsg);
                } finally {
                    logger.info("[LongerDeliverySlotAndShiftGenerator tenantId={}] END process", tenant.getId());
                }
                SecurityContextHolder.clearContext();
            }
        }

        saveSchedulerLog(errorMessages, executionTime);
    }

    private void generateSlotsForOneTenant(List<StockLocation> stockLocations) {
        LocalDateTime now = LocalDateTime.now(); // 19:00 UTC, means 02:00 WIB, means yesterday
        List<Slot> slotsToUpdate = new ArrayList<>();

        for (StockLocation stockLocation : stockLocations) {
            if (stockLocation.isFulfilledBySayurbox())
                continue;

            if (stockLocation.getCluster().getSlotType() != Slot.Type.LONGER_DELIVERY)
                continue;

            ZoneId UTCZone = ZoneId.of("UTC");
            ZoneId storeZone = ZoneId.of(stockLocation.getState().getTimeZone());
            LocalDateTime startOfDay = now.atZone(UTCZone)
                .withZoneSameInstant(storeZone)
                .toLocalDate()
                .atStartOfDay()
                .atZone(storeZone)
                .withZoneSameInstant(UTCZone).toLocalDateTime();

            LocalDateTime startOfDayPlus1 = startOfDay.plusDays(1);
            List<Slot> slotsInOneDay = slotRepository.findAllBetweenDate(stockLocation, startOfDay, startOfDayPlus1, Slot.Type.LONGER_DELIVERY);

            List<Slot> slotsPerStockLocation = new ArrayList<>();
            for (Slot slotToCopy : slotsInOneDay) {
                if (slotToCopy != null) {
                    LocalDateTime nextWeekStartTime = slotToCopy.getStartTime().plusWeeks(1);
                    LocalDateTime nextWeekEndTime = slotToCopy.getEndTime().plusWeeks(1);

                    // Do not override next week existing slot
                    List<Slot> existingSlots = slotRepository.findAllByUniqueConstraints(stockLocation, nextWeekStartTime, nextWeekEndTime);
                    if (existingSlots.isEmpty()) {
                        Slot slot = new Slot(stockLocation, nextWeekStartTime, nextWeekEndTime, Slot.Type.LONGER_DELIVERY);
                        slot.setShopperCount(slotToCopy.getShopperCount());
                        slot.setDriverCount(slotToCopy.getDriverCount());
                        slotsPerStockLocation.add(slot);
                    }
                }
            }
            slotsToUpdate.addAll(slotsPerStockLocation);
            logger.info("[LongerDeliverySlotAndShiftGenerator stockLocationId={}] slot to update on {} : {}", stockLocation.getId(), LocalDate.now().plusWeeks(1), slotsPerStockLocation.size());
        }

        if (!slotsToUpdate.isEmpty())
            slotRepository.saveAll(slotsToUpdate);
    }

    private void generateShiftsForOneTenant(List<StockLocation> stockLocations) {
        LocalDateTime now = LocalDateTime.now(); // 19:00 UTC, means 02:00 WIB, means yesterday
        List<Shift> shiftsToUpdate = new ArrayList<>();

        for (StockLocation stockLocation : stockLocations) {
            if (stockLocation.isFulfilledBySayurbox())
                continue;

            if (stockLocation.getCluster().getSlotType() != Slot.Type.LONGER_DELIVERY)
                continue;

            ZoneId UTCZone = ZoneId.of("UTC");
            ZoneId storeZone = ZoneId.of(stockLocation.getState().getTimeZone());
            LocalDateTime startOfDay = now.atZone(UTCZone)
                .withZoneSameInstant(storeZone)
                .toLocalDate()
                .atStartOfDay()
                .atZone(storeZone)
                .withZoneSameInstant(UTCZone).toLocalDateTime();
            LocalDateTime startOfDayPlus1 = startOfDay.plusDays(1);
            List<Shift> shiftsInOneDay = shiftRepository.findAllBetweenDate(stockLocation, startOfDay, startOfDayPlus1);

            List<Shift> shiftsPerStockLocation = new ArrayList<>();
            for (Shift shiftToCopy : shiftsInOneDay) {
                if (shiftToCopy != null) {
                    LocalDateTime nextWeekStartTime = shiftToCopy.getStartTime().plusWeeks(1);
                    LocalDateTime nextWeekEndTime = shiftToCopy.getEndTime().plusWeeks(1);
                    Shift.Type shiftType = shiftToCopy.getType();

                    // Do not override next week existing shift
                    List<Shift> existingShifts = shiftRepository.findAllByUniqueConstraints(stockLocation, nextWeekStartTime, nextWeekEndTime, shiftType);
                    if (existingShifts.isEmpty()) {
                        Shift shift = new Shift(stockLocation, nextWeekStartTime, nextWeekEndTime, shiftType, shiftToCopy.getCount());
                        shiftsPerStockLocation.add(shift);
                    }
                }
            }
            shiftsToUpdate.addAll(shiftsPerStockLocation);
            logger.info("[LongerDeliverySlotAndShiftGenerator stockLocationId={}] shift to update on {} : {}", stockLocation.getId(), LocalDate.now().plusWeeks(1), shiftsPerStockLocation.size());
        }

        if (!shiftsToUpdate.isEmpty())
            shiftRepository.saveAll(shiftsToUpdate);
    }

    private void saveSchedulerLog(List<String> errorMessages, LocalDateTime executionTime) {
        LocalDateTime completedAt = LocalDateTime.now();
        String message = String.join(";", errorMessages);

        if (StringUtils.isNotEmpty(message))
            logger.error(message);

        SchedulerLog log = new SchedulerLog(getActionClass(), message, executionTime, completedAt);
        schedulerLogRepository.save(log);
    }

    private boolean shouldRunScheduler() {
        LocalDateTime startOfToday = LocalDate.now().atStartOfDay();
        LocalDateTime startOfTomorrow = startOfToday.plusDays(1L);
        List<SchedulerLog> schedulerToday = schedulerLogRepository.findAllSchedulerAlreadyCompletedToday(getActionClass(), startOfToday, startOfTomorrow);
        return schedulerToday.isEmpty();
    }

    private String getActionClass() {
        return this.getClass().getSimpleName();
    }

}
