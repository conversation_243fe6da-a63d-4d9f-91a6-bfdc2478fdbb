package com.happyfresh.fulfillment.slot.scheduler;

import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.util.UserInfoFetcher;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.user.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.jobrunr.jobs.annotations.Recurring;
import org.jobrunr.jobs.context.JobRunrDashboardLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@ConditionalOnProperty(name = "scheduler.enabled", matchIfMissing = true)
public class SlotGeneratorScheduler {

    @Autowired
    private ApplicationContext applicationContext;

    private static final Logger logger = new JobRunrDashboardLogger(LoggerFactory.getLogger(SlotGeneratorScheduler.class));

    @Autowired
    private StockLocationRepository stockLocationRepository;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private SlotRepository slotRepository;

    @Autowired
    private SchedulerLogRepository schedulerLogRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private UserInfoFetcher userInfoFetcher;

    @Recurring(id = "generate-slots-job", cron = "0 5 * * *")
    @Transactional
    public void generateSlotsForAllTenants() {
        List<String> errorMessages = new ArrayList<>();
        LocalDateTime executionTime = LocalDateTime.now();
        List<Tenant> tenants = tenantRepository.findAll();

        for (Tenant tenant : tenants) {
            Role role = roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, tenant.getId());
            if (role != null) {
                User user = userRepository.findByRolesContainingAndTenantId(role, tenant.getId());

                AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), tenant.getToken());
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);

                try {
                    logger.info(String.format("[SlotGenerator tenantId=%s] START process ", userInfoFetcher.getTenantId()));
                    transactionHelper.withNewTransaction(() -> generateSlotsForTenant());
                } catch (Exception ex) {
                    logger.info(String.format("[SlotGenerator tenantId=%s] ERROR message : %s ", userInfoFetcher.getTenantId(), ex.getMessage()));
                    String message = "[Tenant ID = " + tenant.getId() + "] message: " + ex.getMessage();
                    errorMessages.add(message);
                } finally {
                    logger.info(String.format("[SlotGenerator tenantId=%s] END process ", userInfoFetcher.getTenantId()));
                }

                SecurityContextHolder.clearContext();
            }
        }
        saveSchedulerLog(errorMessages, executionTime);
    }

    private void saveSchedulerLog(List<String> errorMessages, LocalDateTime executionTime) {
        LocalDateTime completedAt = LocalDateTime.now();
        String message = String.join(";", errorMessages);

        if (StringUtils.isNotEmpty(message)) {
            logger.error(message);
        }

        SchedulerLog log = new SchedulerLog(getActionClass(), message, executionTime, completedAt);
        schedulerLogRepository.save(log);
    }

    private boolean shouldRunScheduler() {
        LocalDateTime beginOfTodayDateTime = LocalDate.now().atStartOfDay();
        LocalDateTime endOfTodayDateTime = beginOfTodayDateTime.plusDays(1L);
        List<SchedulerLog> schedulerToday = schedulerLogRepository.findAllSchedulerAlreadyCompletedToday(getActionClass(), beginOfTodayDateTime, endOfTodayDateTime);
        return schedulerToday.isEmpty();
    }

    private void generateSlotsForTenant() {
        /** We should get yesterday date and copy yesterday's slot to next week slots
         * because cron running on UTC every 18.00, so it's already "yesterday" slots */
        LocalDateTime now = LocalDateTime.now();
        List<Slot> slotsToUpdate = new ArrayList<>();

        for (StockLocation stockLocation : stockLocationRepository.findAll()) {
            Map<Pair<Slot.Type, LocalDateTime>, Slot> slots = getSlotsByDate(stockLocation, now);

            Cluster cluster = stockLocation.getCluster();
            Slot.Type slotType = cluster.getSlotType();
            if (slotType == Slot.Type.LONGER_DELIVERY)
                continue;

            long duration = stockLocation.getSlotDuration();

            LocalDateTime utcOpenAt = LocalDateTime.of(now.toLocalDate(), stockLocation.getOpenAt());
            LocalDateTime utcCloseAt = LocalDateTime.of(now.toLocalDate(), stockLocation.getCloseAt());
            LocalDateTime startTime = utcOpenAt;

            List<Slot> slotsPerStockLocation = new ArrayList<>();
            while (startTime.isBefore(utcCloseAt) || startTime.isEqual(utcCloseAt)) {
                Pair<Slot.Type, LocalDateTime> slotLookupKey = new ImmutablePair<>(slotType, startTime);
                Slot slotToCopy = slots.get(slotLookupKey);
                if (slotToCopy != null) {
                    LocalDateTime nextWeekStartTime = slotToCopy.getStartTime().plusWeeks(1);
                    LocalDateTime nextWeekEndTime = nextWeekStartTime.plusHours(duration);

                    Slot slot = slotRepository.findByStockLocationAndStartTime(stockLocation, nextWeekStartTime);
                    if (slot == null) {
                        slot = new Slot(stockLocation, nextWeekStartTime, nextWeekEndTime, slotType);
                        slot.setShopperCount(slotToCopy.getShopperCount());
                        slot.setDriverCount(slotToCopy.getDriverCount());
                    }
                    slotsPerStockLocation.add(slot);
                }
                startTime = startTime.plusHours(duration);
            }
            logger.info(String.format("[SlotGenerator stockLocationId=%s] slot to update on %s : %s", stockLocation.getId(), LocalDate.now().plusWeeks(1), slotsPerStockLocation.size()));
            slotsToUpdate.addAll(slotsPerStockLocation);
        }

        if (slotsToUpdate.size() > 0)
            slotRepository.saveAll(slotsToUpdate);
    }

    private Map<Pair<Slot.Type, LocalDateTime>, Slot> getSlotsByDate(StockLocation stockLocation, LocalDateTime date) {
        LocalDateTime utcStartSlot = date.toLocalDate().atStartOfDay();
        LocalDateTime utcEndSlot = utcStartSlot.plusDays(1);

        List<Slot> slots = slotRepository.findAllBetweenDate(stockLocation, utcStartSlot, utcEndSlot, PageRequest.of(0, Integer.MAX_VALUE));

        Map<Pair<Slot.Type, LocalDateTime>, Slot> todaySlots = new HashMap();
        if (slots.size() > 0) {
            slots.forEach(slot -> todaySlots.put(constructLookupKey(slot), slot));
        }

        return todaySlots;
    }

    private String getActionClass() {
        return this.getClass().getSimpleName();
    }

    private Pair<Slot.Type, LocalDateTime> constructLookupKey(Slot slot) {
        Pair<Slot.Type, LocalDateTime> lookupKey = new ImmutablePair(slot.getType(), slot.getStartTime());
        return lookupKey;
    }

}
