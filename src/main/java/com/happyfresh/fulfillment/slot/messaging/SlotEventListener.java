package com.happyfresh.fulfillment.slot.messaging;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.batch.service.DriverAutoAssignmentService;
import com.happyfresh.fulfillment.batch.service.ShopperAutoAssignmentService;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaTopicConfig;
import com.happyfresh.fulfillment.slot.presenter.SlotOptimizationEvent;
import com.happyfresh.fulfillment.slot.service.SlotOptimizationEventPublisherService;
import com.happyfresh.fulfillment.slot.service.SlotOptimizationService;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.jobrunr.scheduling.BackgroundJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
public class SlotEventListener {

    private final Logger logger = LoggerFactory.getLogger(SlotEventListener.class);

    @Autowired
    private SlotOptimizationService slotOptimizationService;

    @Autowired
    private ShopperAutoAssignmentService shopperAutoAssignmentService;

    @Autowired
    private DriverAutoAssignmentService driverAutoAssignmentService;

    @Autowired
    private SlotOptimizationEventPublisherService slotOptimizationEventPublisher;

    @Autowired
    private ObjectMapper mapper;

    @KafkaListener(topics = KafkaTopicConfig.SLOT_OPTIMIZATION_PROCESSING_TOPIC, containerFactory = "slotOptimizationKafkaListenerContainerFactory")
    public void listen(SlotOptimizationEvent event, ConsumerRecord<String, String> consumerRecord) {

        log(event, consumerRecord);

        event.setPartitionAsText(String.valueOf(consumerRecord.partition()));
        event.setOffsetAsText(String.valueOf(consumerRecord.offset()));

        if (event.getEventType() == null || event.getEventType().isEmpty()) {
            // support backward compatibility
            BackgroundJob.enqueue(() -> slotOptimizationService.handleEventOptimization(event));
        } else {
            if (event.getEventType().equalsIgnoreCase(SlotOptimizationEvent.SLOT_OPTIMIZATION_EVENT)) {
                BackgroundJob.enqueue(() -> slotOptimizationService.handleEventOptimization(event));
            } else if (event.getEventType().equalsIgnoreCase(SlotOptimizationEvent.AUTO_ASSIGNMENT_SHOPPER_EVENT) ||
                    event.getEventType().equalsIgnoreCase(SlotOptimizationEvent.AUTO_ASSIGNMENT_EVENT) // to support backward compatibility
            ) {
                shopperAutoAssignmentService.handleEvent(event);
            } else if (event.getEventType().equalsIgnoreCase(SlotOptimizationEvent.AUTO_ASSIGNMENT_DRIVER_EVENT)) {
                driverAutoAssignmentService.handleEvent(event);
            }
        }

        slotOptimizationEventPublisher.consumeFinished(event);
    }

    private void log(SlotOptimizationEvent event, ConsumerRecord<String, String> consumerRecord) {
        try {
            long offset = consumerRecord.offset();
            int partition = consumerRecord.partition();
            String key = consumerRecord.key();
            String message = mapper.writeValueAsString(event);
            String slotDate = event.getSlotStartTimeAsText();
            logger.info("[Slot Event Listener] slot_date= {} partition={} offset={} key={} message={}", slotDate, partition, offset, key, message);
        } catch (JsonProcessingException e) {
            // do nothing
        }
    }

    @KafkaListener(topics = KafkaTopicConfig.TOMORROW_SLOT_OPTIMIZATION_PROCESSING_TOPIC, containerFactory = "slotOptimizationTomorrowKafkaListenerContainerFactory")
    public void listenTomorrowSlotOptimization(SlotOptimizationEvent event, ConsumerRecord<String, String> consumerRecord) {
        if (!event.isSlotStartTimeTomorrowOnward())
            return;

        log(event, consumerRecord);

        event.setPartitionAsText(String.valueOf(consumerRecord.partition()));
        event.setOffsetAsText(String.valueOf(consumerRecord.offset()));

        if (event.getEventType().equalsIgnoreCase(SlotOptimizationEvent.SLOT_OPTIMIZATION_EVENT)) {
            slotOptimizationService.handleEventOptimization(event);
        }
    }

}
