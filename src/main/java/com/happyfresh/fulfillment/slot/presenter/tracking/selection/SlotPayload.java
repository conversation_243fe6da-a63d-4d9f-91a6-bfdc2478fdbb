package com.happyfresh.fulfillment.slot.presenter.tracking.selection;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.entity.ddf.RemainingTimeMultiplierMatrix;
import com.happyfresh.fulfillment.entity.ddf.SlotUtilizationMultiplierMatrix;
import com.happyfresh.fulfillment.slot.presenter.FeePresenter;
import lombok.Getter;
import lombok.Setter;

import java.time.format.DateTimeFormatter;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SlotPayload {

    private static DateTimeFormatter TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");

    private Long id;

    private Long stockLocationId;

    private Long externalStockLocationId;

    private String startTime;

    private String endTime;

    private Long shippingMethodId;

    private Double cost;

    private String displayCost;

    private Boolean available;

    private Boolean containsAlcohol;

    private FeePresenter.Level priceLevel;

    private FeePresenter.Level trackingLevel;

    private SlotUtilizationMultiplierMatrix.Type utilization;

    private Integer remainingMinutes;

    private Integer driverCapacityInCluster;

    private Integer usedCapacityInCluster;

    private RemainingTimeMultiplierMatrix.Type remainingMultiplierType;

}
