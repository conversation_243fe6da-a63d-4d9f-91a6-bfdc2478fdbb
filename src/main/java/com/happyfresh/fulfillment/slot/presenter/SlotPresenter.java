package com.happyfresh.fulfillment.slot.presenter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.google.common.base.CaseFormat;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.entity.Slot;
import com.happyfresh.fulfillment.stockLocation.presenter.StockLocationPresenter;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SlotPresenter {

    private Long id;

    @JsonIgnore
    private LocalDateTime startTime;

    @JsonIgnore
    private LocalDateTime endTime;

    private Boolean containsAlcohol;

    private StockLocationPresenter stockLocation;

    private String externalSlotId;

    @JsonIgnore
    private Slot.Type type;

    @JsonProperty("start_time")
    public String getStartTimeString() {
        return DateTimeUtil.localDateTimeToStringSlotDateTime(startTime);
    }

    public void setStartTimeString(String startTimeString) {
        startTime = LocalDateTime.parse(startTimeString);
    }

    @JsonProperty("end_time")
    public String getEndTimeString() {
        return DateTimeUtil.localDateTimeToStringSlotDateTime(endTime);
    }

    public void setEndTimeString(String endTimeString) {
        endTime = LocalDateTime.parse(endTimeString);
    }

    @JsonProperty("slot_type")
    public String getSlotType() {
        return type.toString();
    }

    public void setSlotType(String slotType) {
        type = Slot.Type.valueOf(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.UPPER_UNDERSCORE, slotType));
    }

}
