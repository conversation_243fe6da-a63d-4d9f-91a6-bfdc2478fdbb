package com.happyfresh.fulfillment.slot.presenter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.entity.Slot;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SimpleSlotPresenter {

    private Long id;

    @JsonIgnore
    private LocalDateTime startTime;

    @JsonIgnore
    private LocalDateTime endTime;

    private Integer shopperCount;

    private Integer driverCount;

    private Boolean containsAlcohol;

    @JsonIgnore
    private Slot.Type type;

    @JsonProperty("start_time")
    public String getStartTimeString() {
        return DateTimeUtil.localDateTimeToStringSlotDateTime(startTime);
    }

    @JsonProperty("end_time")
    public String getEndTimeString() {
        return DateTimeUtil.localDateTimeToStringSlotDateTime(endTime);
    }

    @JsonProperty("slot_type")
    public String getSlotType() {
        return type.toString();
    }

}
