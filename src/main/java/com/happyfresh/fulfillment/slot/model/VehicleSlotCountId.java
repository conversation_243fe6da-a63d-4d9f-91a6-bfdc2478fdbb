package com.happyfresh.fulfillment.slot.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class VehicleSlotCountId implements Serializable {

    @JsonIgnore
    private LocalDateTime slotStartTime;

    private Long shiftId;

    @JsonProperty("slot_start_time")
    public String getSlotStartTimeString() {
        return DateTimeUtil.localDateTimeToStringSlotDateTime(slotStartTime);
    }

}
