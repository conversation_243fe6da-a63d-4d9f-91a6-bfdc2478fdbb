package com.happyfresh.fulfillment.stockLocation.presenter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class BaseStockLocationPresenter {

    private Long id;

    private String name;

    private String address1;

    private String address2;

    private String code;

    private boolean active;

    private Double lat;

    private Double lon;

    private Long tenantId;

    @JsonProperty(value = "type")
    private String typeName;

}
