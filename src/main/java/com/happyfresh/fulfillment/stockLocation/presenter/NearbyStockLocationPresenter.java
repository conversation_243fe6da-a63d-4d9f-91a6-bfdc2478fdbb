package com.happyfresh.fulfillment.stockLocation.presenter;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.entity.Cluster;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class NearbyStockLocationPresenter extends StockLocationPresenter {

    private Cluster.ClockInType clockInType;

}
