package com.happyfresh.fulfillment.stockLocation.presenter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.entity.StockLocation;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalTime;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class StockLocationPresenter extends BaseStockLocationPresenter {

    private String clusterName;

    private String imageUrl;

    private StatePresenter state;

    private SupplierPresenter supplier;

    private ClusterPresenter cluster;

    private OnDemandClusterPresenter onDemandCluster;

    @JsonIgnore
    private LocalTime openAt;

    @JsonProperty("open_at")
    public String getOpenAtString() {
        return openAt.toString();
    }

    public void setOpenAtString(String openAtString) {
        openAt = LocalTime.parse(openAtString);
    }

    @JsonIgnore
    private LocalTime closeAt;

    @JsonProperty("close_at")
    public String getCloseAtString() {
        return closeAt.toString();
    }

    public void setCloseAtString(String closeAtString) {
        closeAt = LocalTime.parse(closeAtString);
    }

    private Boolean askReceiptNumber;

    private Long externalId;

    private Boolean enableGrabExpress;

    private Boolean enableGrabExpressCod;

    private Boolean enableChat;

    private boolean enableCutOff;

    private StockLocation.DDFType ddfType;

    private Integer grabExpressOffsetTime;

    private Integer grabExpressDelayTime;

    private String meetingPointName;

    private String meetingPointRemark;

    private Double meetingPointLat;

    private Double meetingPointLon;

    private double shopperAveragePickingTimePerUniqItem;

    private double shopperQueueReplacementTime;

    private double shopperHandoverToDriverTime;

    private int shoppingBatchNotifiedOffset;

    private int deliveryBatchNotifiedOffset;

    private boolean enableMultiBatchShopping;

    private int prepickingOffset;

    private boolean crossdayPrepickingEnabled;

    private int maxShoppingVolume;

    private int maxDeliveryVolume;

    private double maxDeliveryHandover;

    private boolean tplEnabled;

    private double shipDistanceThreshold;

    private boolean enableLalamove;

    private boolean enableLalamoveDeliveryFee;

    private double lalamoveFlatServiceFee;

    private boolean enableDelyva;

    private boolean enableDelyvaDeliveryFee;

    private double delyvaFlatServiceFee;

    private int ongoingSlotCutOff;

    private int cutOffOffsetDay;

    @JsonIgnore
    private LocalTime cutOffTime;

    @JsonProperty("cut_off_time")
    public String getCutOffTimeString() {
        if (cutOffTime != null)
            return cutOffTime.toString();

        return null;
    }

    public void setCutOffTimeString(String cutOffTimeString) {
        if (cutOffTimeString == null)
            cutOffTime = null;
        else
            cutOffTime = LocalTime.parse(cutOffTimeString);
    }

    private boolean enablePendingJobNotification;

    private boolean expressSetSlotPrioritizeOnDemand;

    private double maximumTraveledDistance;

    private boolean enableShopperAutoAssignment;

}
