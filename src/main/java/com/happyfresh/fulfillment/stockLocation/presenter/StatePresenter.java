package com.happyfresh.fulfillment.stockLocation.presenter;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class StatePresenter {

    private Long id;

    private String name;

    private String timeZone;

    private CountryPresenter country;

}
