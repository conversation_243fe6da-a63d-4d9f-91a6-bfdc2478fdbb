package com.happyfresh.fulfillment.stockLocation.mapper;

import com.happyfresh.fulfillment.entity.Role;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.stockLocation.presenter.BaseStockLocationPresenter;
import com.happyfresh.fulfillment.stockLocation.presenter.NearbyStockLocationPresenter;
import com.happyfresh.fulfillment.stockLocation.presenter.StockLocationMapperDecorator;
import com.happyfresh.fulfillment.stockLocation.presenter.StockLocationPresenter;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring", uses = CountryMapper.class)
@DecoratedWith(StockLocationMapperDecorator.class)
public interface StockLocationMapper {

    @Mapping(source = "tenant.id", target = "tenantId")
    @Mapping(source = "cluster.name", target = "clusterName")
    StockLocationPresenter stockLocationToStockLocationPresenter(StockLocation stockLocation);

    @Mapping(source = "stockLocation.tenant.id", target = "tenantId")
    @Mapping(source = "stockLocation.cluster.name", target = "clusterName")
    NearbyStockLocationPresenter stockLocationToNearbyStockLocationPresenter(StockLocation stockLocation, List<Role.Name> roles);

    @Mapping(source = "tenant.id", target = "tenantId")
    BaseStockLocationPresenter stockLocationToSimpleStockLocationPresenter(StockLocation stockLocation);

    List<StockLocationPresenter> toStockLocationPresenters(List<StockLocation> stockLocations);

}
