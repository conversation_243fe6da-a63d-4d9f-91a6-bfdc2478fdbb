package com.happyfresh.fulfillment.shipment.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.packaging.form.ShipmentPackagingForm;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ShipmentForm {

    @NotNull
    private Long stockLocationId;

    private Long slotId;

    @NotBlank
    private String state;

    @NotBlank
    private String orderNumber;

    @NotBlank
    private String number;

    @NotNull
    private Double orderTotal;

    @NotNull
    private Double adjustmentTotal;

    @NotBlank
    private String addressName;

    private String addressLine;

    private String addressNumber;

    private String addressDetail;

    private String addressPhone;

    @NotBlank
    private String addressCity;

    @NotNull
    private Double addressLat;

    @NotNull
    private Double addressLon;

    private String addressInstruction;

    @NotBlank
    private String orderCustomerEmail;

    private String orderCustomerFirstName;

    private String orderCustomerLastName;

    private String orderCustomerLanguage;

    private String orderCustomerPhone;

    @NotBlank
    private String orderCurrency;

    private String orderPaymentMethod;

    private String orderCompanyId;

    private String orderCompanyName;

    private Boolean orderEligibleForShoppingBag;

    private Boolean orderNewCustomer;

    private String orderClientType;

    private Long orderExternalCustomerId;

    private int customerAverageItem;

    private int stockLocationAverageItem;

    private Boolean isPaymentClear;

    private Boolean customerAllowGeDelivery;

    private List<@Valid ItemForm> items;

    private List<ShipmentPackagingForm> orderPackagings;

    private String orderCustomerMemberTier;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private LocalDateTime slotStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private LocalDateTime slotEndTime;

    public boolean isSlotTimeAdjusted(LocalDateTime slotStartTime){
        return this.getSlotStartTime() != null && this.getSlotStartTime().isAfter(slotStartTime);
    }

    private Boolean isAddressIncorrect;

}
