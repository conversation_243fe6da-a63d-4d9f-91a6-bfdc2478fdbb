
package com.happyfresh.fulfillment.shipment.service;

import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.happyfresh.fulfillment.common.exception.type.CapturePaymentFailedException;
import com.happyfresh.fulfillment.common.exception.type.CapturePaymentRetryException;
import com.happyfresh.fulfillment.common.exception.type.PaymentAlreadyCompletedException;
import com.happyfresh.fulfillment.common.exception.type.SwitchToCODFailedException;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.presenter.PaymentEvent;
import com.happyfresh.fulfillment.common.util.CurrencyUtil;
import com.happyfresh.fulfillment.common.util.UserInfoFetcher;
import com.happyfresh.fulfillment.common.util.UserRoleUtil;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.payment.model.OutstandingStatusResponse;
import com.happyfresh.fulfillment.payment.presenter.OutstandingPaymentPresenter;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.user.service.UserService;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.http.client.support.BasicAuthorizationInterceptor;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URI;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class PaymentService {

    @Autowired
    private UserInfoFetcher userInfoFetcher;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private ShipmentService shipmentService;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private PaymentRepository paymentRepository;

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Transactional(readOnly = true)
    public ResponseEntity<String> capturePayment(String shipmentNumber, String clientIPAddress, Boolean noSplitCod) {
        Tenant tenant = userInfoFetcher.getTenant();

        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getInterceptors().add(new BasicAuthorizationInterceptor(tenant.getOmsAuthUsername(), tenant.getOmsAuthPassword()));

        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);

        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("shipment_number", shipmentNumber);
        requestBody.put("shopper_ip", clientIPAddress);
        requestBody.put("no_split_cod", noSplitCod.toString());

        HttpEntity<Map> request = new HttpEntity<>(requestBody, requestHeaders);
        ResponseEntity<String> response = new ResponseEntity<>(HttpStatus.OK);
        try {
            response =  restTemplate.postForEntity(tenant.getOmsBaseUrl() + "/carbon/payments/capture", request, String.class);
        } catch (RestClientResponseException exception) {
            ParsedException parsedException = parseException(exception);
            if (!parsedException.type.equalsIgnoreCase("payment_already_completed")) {
                throw new CapturePaymentFailedException(parsedException.message);
            }
        }

        return response;
    }

    @Transactional(readOnly = true)
    public ResponseEntity<String> verifyPayment(String shipmentNumber, String clientIPAddress, String paymentType, String paymentName) {
        Tenant tenant = userInfoFetcher.getTenant();

        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getInterceptors().add(new BasicAuthorizationInterceptor(tenant.getOmsAuthUsername(), tenant.getOmsAuthPassword()));

        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);

        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("shipment_number", shipmentNumber);
        requestBody.put("shopper_ip", clientIPAddress);
        requestBody.put("payment_type", paymentType);
        requestBody.put("payment_name", paymentName);

        HttpEntity<Map> request = new HttpEntity<>(requestBody, requestHeaders);
        ResponseEntity<String> response = new ResponseEntity<>(HttpStatus.OK);
        try {
            response =  restTemplate.postForEntity(tenant.getOmsBaseUrl() + "/carbon/payments/verify", request, String.class);
        } catch (RestClientResponseException exception) {
            ParsedException parsedException = parseException(exception);
            if (!parsedException.type.equalsIgnoreCase("payment_already_completed")) {
                if (parsedException.message.equalsIgnoreCase("Retry again"))
                    throw new CapturePaymentRetryException(parsedException.message);
                throw new CapturePaymentFailedException(parsedException.message);
            }
        }

        return response;
    }

    @Transactional(readOnly = true)
    public void switchToCOD(Shipment shipment, String clientIPAddress) {
        Tenant tenant = userInfoFetcher.getTenant();

        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getInterceptors().add(new BasicAuthorizationInterceptor(tenant.getOmsAuthUsername(), tenant.getOmsAuthPassword()));

        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);

        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("shipment_number", shipment.getNumber());
        requestBody.put("shopper_ip", clientIPAddress);


        HttpEntity<Map> request = new HttpEntity<>(requestBody, requestHeaders);

        try {
            restTemplate.postForEntity(tenant.getOmsBaseUrl() + "/carbon/payments/switch_to_cod", request, String.class);
        } catch (RestClientResponseException exception) {
            ParsedException parsedException = parseException(exception);
            if (parsedException.type.equalsIgnoreCase("payment_already_completed")) {
                throw new PaymentAlreadyCompletedException(parsedException.message);
            } else {
                throw new SwitchToCODFailedException(parsedException.message);
            }
        }
    }

    private ParsedException parseException(RestClientResponseException exception) {
        JsonParser jsonParser = new JsonParser();
        JsonElement exceptionParsed = jsonParser.parse(exception.getResponseBodyAsString());
        String exceptionType = exceptionParsed.getAsJsonObject().get("type").getAsString();
        String exceptionMessage = exceptionParsed.getAsJsonObject().get("message").getAsString();
        return new ParsedException(exceptionType, exceptionMessage);
    }

    @Transactional
    public void handlePaymentEvent(PaymentEvent event) {
        String eventType = event.getType();
        String orderNumber = event.getData().getOrderNumber();
        LOGGER.info("[HandlePaymentEvent] " + eventType + " for order " + orderNumber);

        if (eventType.equalsIgnoreCase(PaymentEvent.Type.PAYMENT_EXPIRED.toString()))
            handlePaymentExpired(orderNumber);
    }

    private void handlePaymentExpired(String orderNumber) {
        List<Tenant> tenants = tenantRepository.findAll();
        for (Tenant tenant : tenants) {
            Role role = roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, tenant.getId());
            if (role != null) {
                User user = userRepository.findByRolesContainingAndTenantId(role, tenant.getId());

                AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), tenant.getToken());
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                Shipment shipment = shipmentRepository.findByOrderNumberAndTenantId(orderNumber, tenant.getId());
                if (shipment != null && shipment.getState().equals(Shipment.State.PENDING)) {
                    try {
                        transactionHelper.withNewTransaction(() -> shipmentService.destroy(shipment));
                    } catch (Exception e) {
                        ThreadContext.put("SHIPMENT_NUMBER", shipment.getNumber());
                        LOGGER.error("Failed to destroy shipment on payment expired", e);
                        ThreadContext.clearAll();
                    }
                }
                SecurityContextHolder.clearContext();
            }
        }
    }

    @Transactional(readOnly = true)
    public OutstandingStatusResponse[] getOutstandingStatus(String[] orderNumbers) {
        Tenant tenant = userInfoFetcher.getTenant();
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getInterceptors().add(new BasicAuthorizationInterceptor(tenant.getOmsAuthUsername(), tenant.getOmsAuthPassword()));
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        String url = tenant.getOmsBaseUrl() + "/carbon/outstanding_status";
        UriComponentsBuilder builder = UriComponentsBuilder
                .fromHttpUrl(url)
                .queryParam("order_number[]", orderNumbers);

        URI uri = builder.build().encode().toUri();

        try {
            OutstandingStatusResponse[] response = restTemplate.getForObject(uri, OutstandingStatusResponse[].class);
            return response;
        } catch (RestClientResponseException exception) {
            LOGGER.error("Failed to get outstanding job", exception);
            throw exception;
        }
    }

    @Transactional
    public List<OutstandingPaymentPresenter> getOutstandingPaymentsBy(User user) throws Exception {
        try {
            Long userId = user.getId();
            List<Object[]> sqlObjects;

            if (UserRoleUtil.isShopper(user)) {
                sqlObjects = paymentRepository.findShopperOutstandingPaymentsJobBy(userId);
            } else {
                sqlObjects = paymentRepository.findDriverOutstandingPaymentsJobBy(userId);
            }

            List<OutstandingPaymentPresenter> outstandingPayments = new ArrayList<>();
            for (Object[] obj : sqlObjects) {
                OutstandingPaymentPresenter outstanding = new OutstandingPaymentPresenter();
                String firstName = getString(obj, 0);
                String lastName = getString(obj, 1);
                String fullName = firstName + " " + lastName;
                outstanding.setCustomerName(fullName);
                outstanding.setCustomerPhone(getString(obj, 2));
                outstanding.setOrderNumber(getString(obj, 3));

                Double paidAmount = getDouble(obj, 4); // col 4 = cashless -> means paid amount
                outstanding.setPaymentPaidAmount(paidAmount);
                outstanding.setDisplayPaidAmount(getDisplayAmount(paidAmount));

                Double debtAmount = getDouble(obj, 5); // col 5 = cash -> means debt amount
                outstanding.setPaymentDebtAmount(debtAmount);
                outstanding.setDisplayDebtAmount(getDisplayAmount(debtAmount));

                Double orderTotalAmount = getDouble(obj, 6);
                outstanding.setOrderTotal(orderTotalAmount);
                outstanding.setDisplayOrderTotalAmount(getDisplayAmount(orderTotalAmount));

                String dateCreatedAt = getString(obj, 7);
                outstanding.setDueDate(setDueDatePlusOneSinceCreatedDate(dateCreatedAt));

                outstanding.setShipmentId(getLong(obj, 8));
                outstanding.setOutstandingStatus(getString(obj, 9));
                outstandingPayments.add(outstanding);
            }
            return outstandingPayments;
        } catch (Exception e) {
            throw e;
        }
    }

    private String getString(Object[] obj, int columnNo) {
        if (obj[columnNo] == null) {
            return "";
        }
        return obj[columnNo].toString();
    }

    private Long getLong(Object[] obj, int columnNo) {
        if (obj[columnNo] == null) {
            return 0L;
        }
        return ((BigInteger) obj[columnNo]).longValue();
    }

    private Double getDouble(Object[] obj, int columnNo) {
        if (obj[columnNo] == null) {
            return 0D;
        }
        return ((BigDecimal) obj[columnNo]).doubleValue();
    }

    public String getDisplayAmount(Double amount) {
        String countryISOName = "ID"; // hardcoded. TODO: get from hff_country when service scaled up
        return CurrencyUtil.format(BigDecimal.valueOf(amount), countryISOName);
    }

    private String setDueDatePlusOneSinceCreatedDate(String dateValue) throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = dateFormat.parse(dateValue);
        date.setDate(date.getDate() + 1);
        /**
         *  assume all shopper/driver more convenience to use local language
         *  if not TODO: adjust based on shopper/driver's selected language
         * **/
        Locale locale = new Locale("id", "ID");
        SimpleDateFormat dateFormatJ = new SimpleDateFormat("dd MMM yyyy", locale);
        TimeZone timezone = TimeZone.getTimeZone("Asia/Jakarta");
        dateFormatJ.setTimeZone(timezone);
        return dateFormatJ.format(date);
    }

    @Transactional
    public void updatePaymentOutstandingStatus(
            Long shipmentId,
            String outstandingStatus,
            Double paidAmount
    ) {
        Payment payment = paymentRepository.findByShipmentId(shipmentId);
        payment.setOutstandingStatus(outstandingStatus);
        payment.setCash(BigDecimal.valueOf(paidAmount));
        paymentRepository.save(payment);
    }

    private class ParsedException {
        String type;
        String message;

        private ParsedException(String type, String message) {
            this.type = type;
            this.message = message;
        }
    }
}
