
package com.happyfresh.fulfillment.shipment.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.util.StdDateFormat;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.batch.form.CancellationForm;
import com.happyfresh.fulfillment.batch.service.*;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.exception.BadRequestException;
import com.happyfresh.fulfillment.common.exception.ResourceNotFoundException;
import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;
import com.happyfresh.fulfillment.common.exception.type.*;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.service.DistanceService;
import com.happyfresh.fulfillment.common.service.FleetTrackingService;
import com.happyfresh.fulfillment.common.service.NotificationService;
import com.happyfresh.fulfillment.common.tracking.LongerDeliverySlotOptimizationEventTracker;
import com.happyfresh.fulfillment.common.util.DistanceUtil;
import com.happyfresh.fulfillment.common.util.UserInfoFetcher;
import com.happyfresh.fulfillment.enabler.service.EnablerService;
import com.happyfresh.fulfillment.enabler.service.JubelioService;
import com.happyfresh.fulfillment.enabler.service.api.StratoApiService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.gosend.service.GosendService;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressService;
import com.happyfresh.fulfillment.lalamove.model.LalamoveServiceTypeEnum;
import com.happyfresh.fulfillment.lalamove.service.LalamoveService;
import com.happyfresh.fulfillment.locus.service.LocusTaskService;
import com.happyfresh.fulfillment.packaging.form.ShipmentPackagingForm;
import com.happyfresh.fulfillment.packaging.mapper.ShipmentPackagingMapper;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.shipment.form.*;
import com.happyfresh.fulfillment.shipment.mapper.ItemMapper;
import com.happyfresh.fulfillment.shipment.mapper.ShipmentMapper;
import com.happyfresh.fulfillment.shipment.presenter.ItemPresenter;
import com.happyfresh.fulfillment.shipment.presenter.PaymentEligibilityPresenter;
import com.happyfresh.fulfillment.shipment.presenter.VirtualAccountEligibilityPresenter;
import com.happyfresh.fulfillment.slot.bean.SlotOptimizationContext;
import com.happyfresh.fulfillment.slot.service.BatchRecalculateService;
import com.happyfresh.fulfillment.slot.service.SlotOptimizationService;
import com.happyfresh.fulfillment.stockLocation.service.StockLocationService;
import com.happyfresh.fulfillment.tpl.delyva.presenter.DelyvaGetQuotationPresenter;
import com.happyfresh.fulfillment.tpl.delyva.service.DelyvaService;
import lombok.SneakyThrows;
import org.elasticsearch.common.geo.GeoPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityNotFoundException;
import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Objects.nonNull;

@Service
public class ShipmentService {

    private static final int TPL_VOLUME_THRESHOLD_TIER_1 = 30; // in Litre

    private static final int TPL_VOLUME_THRESHOLD_TIER_2 = 50; // in Litre

    private static final int TPL_VOLUME_THRESHOLD_TIER_3 = 80; // in Litre

    public static final int TPL_VOLUME_THRESHOLD_TIER_4 = 200; // in Litre

    private static final int GOSEND_ITEM_VOLUME_THRESHOLD = 6; // in Litre

    private static final int MAX_ORDER_MUTATION_AIR_DISTANCE_IN_KM = 14;

    private static final int HIGH_ORDER_VOLUME_THRESHOLD_IN_LITRE = 200;

    @Autowired
    private ItemService itemService;

    @Autowired
    private GosendService gosendService;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private ShipmentPackagingRepository shipmentPackagingRepo;

    @Autowired
    private StockLocationRepository stockLocationRepository;

    @Autowired
    private PackagingRepository packagingRepository;

    @Autowired
    private ShiftRepository shiftRepository;

    @Autowired
    private JobSlotRepository jobSlotRepository;

    @Autowired
    private BatchRecalculateService batchRecalculateService;

    @Lazy
    @Autowired
    private BatchService batchService;

    @Autowired
    private ItemRepository itemRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private ItemMapper itemMapper;

    @Autowired
    private UserInfoFetcher userInfoFetcher;

    @Autowired
    private ShipmentMapper shipmentMapper;

    @Autowired
    private ShipmentPackagingMapper shipmentPackagingMapper;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private GrabExpressDeliveryRepository grabExpressDeliveryRepository;

    @Autowired
    private ShipmentPackagingRepository shipmentPackagingRepository;

    @Lazy
    @Autowired
    private GrabExpressService grabExpressService;

    @Autowired
    private JubelioService jubelioService;

    @Autowired
    private JubelioDeliveryRepository jubelioDeliveryRepository;

    @Lazy
    @Autowired
    private BatchSndService batchSndService;

    @Autowired
    private LocusTaskService locusTaskService;

    @Lazy
    @Autowired
    private LalamoveService lalamoveService;

    @Autowired
    private LalamoveDeliveryRepository lalamoveDeliveryRepository;

    @Lazy
    @Autowired
    private SlotOptimizationService slotOptimizationService;

    @Autowired
    private ItemReplacementPreferenceService itemReplacementPreferenceService;

    @Autowired
    private LongerDeliverySlotOptimizationEventTracker ldsTracker;

    @Autowired
    private FleetTrackingService fleetTrackingService;

    @Autowired
    private DistanceService distanceService;

    @Lazy
    @Autowired
    private StockLocationService stockLocationService;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private SlotRepository slotRepository;

    @Autowired
    private StratoApiService stratoApiService;

    @Autowired
    private DelyvaDeliveryRepository delyvaDeliveryRepository;

    @Lazy
    @Autowired
    private DelyvaService delyvaService;

    @Autowired
    private ShopperAutoAssignmentService shopperAutoAssignmentService;

    @Lazy
    @Autowired
    private DriverAutoAssignmentService driverAutoAssignmentService; //

    @Autowired
    private FileUploaderService fileUploaderService;

    @Autowired
    private ChatService chatService;

    @Autowired
    private CancelInfoRepository cancelInfoRepository;

    @Autowired
    private EnablerService enablerService;

    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private UserRepository userRepository;

    private final Logger logger = LoggerFactory.getLogger(ShipmentService.class);

    public static final String LINEMAN = "lineman";
    public static final String GRABFRESH = "grabfresh";

    @Transactional(readOnly = true)
    public boolean isTplEligibled(Shipment shipment, StockLocation stockLocation) {
        final Double threshold = stockLocation.getShipDistanceThreshold();
        Double shipDistance = shipment.getShipDistance();

        return !stockLocation.isSpecial() && stockLocation.isTplEnabled()
                && (threshold != null) && (shipDistance != null)
                && (threshold > 0) && (shipDistance > threshold)
                && !isOrderClientTypeLineMan(shipment);
    }

    @Transactional(readOnly = true)
    public boolean isGoldTierEligible(Shipment shipment, StockLocation stockLocation, LocalDateTime slotEndTime, long shoppingAndDeliveryTime, boolean slotClosed) {
        return stockLocation.isSlotOpenForGoldTierCustomer() && "gold".equalsIgnoreCase(shipment.getOrderCustomerMemberTier())
                && LocalDateTime.now().plusMinutes(shoppingAndDeliveryTime).isBefore(slotEndTime) && !slotClosed;
    }

    @Transactional
    public Shipment updateTrackingUrl(String shipmentNumber, TrackingUrlForm trackingUrlForm) throws Exception {
        Shipment shipment = findByNumber(shipmentNumber);
        shipment.setTrackingUrl(trackingUrlForm.getTrackingUrl());

        return shipmentRepository.save(shipment);
    }

    @Transactional
    public Shipment adjustShipment(String shipmentNumber, ShipmentForm shipmentForm, Shipment.State shipmentState) throws Exception {
        updateItems(shipmentNumber, shipmentForm);
        return updateShipmentState(shipmentNumber, shipmentForm, shipmentState);
    }

    private Shipment updateShipmentState(String shipmentNumber, ShipmentForm shipmentForm, Shipment.State shipmentState) throws Exception {
        Shipment shipment = findByNumber(shipmentNumber);
        if (!shipment.getNumber().equals(shipmentForm.getNumber())) {
            throw new Exception();
        }

        BigDecimal orderTotal = BigDecimal.valueOf(shipmentForm.getOrderTotal());
        shipment.setOrderTotal(orderTotal);
        shipment.setOrderCustomerMemberTier(shipmentForm.getOrderCustomerMemberTier());

        adjustLocationCorrect(shipment, shipmentForm);

        if (shipment.getState() != Shipment.State.READY) {
            if (shipment.getJobs().isEmpty()) {
                throw new SlotUnavailableException();
            }

            if (shipmentForm.getOrderPaymentMethod() == null)
                throw new BadRequestException("Order payment method must not null");

            shipment.setState(shipmentState);

            shipment.setAddressName(shipmentForm.getAddressName());
            shipment.setAddressDetail(shipmentForm.getAddressDetail());
            shipment.setAddressNumber(shipmentForm.getAddressNumber());
            shipment.setAddressPhone(shipmentForm.getAddressPhone());
            shipment.setAddressInstruction(shipmentForm.getAddressInstruction());
            shipment.setOrderPaymentMethod(shipmentForm.getOrderPaymentMethod());

            LocalDateTime now = LocalDateTime.now();
            final Slot slot = shipment.getSlot();
            StockLocation stockLocation = slot.getStockLocation();
            shipment.setOrderCompletedAt(now);
            if (shipment.getIsExpress() != null && shipment.getIsExpress())
                shipment.setOrderExpressSla(now.plusMinutes(stockLocation.getOnDemandDeliveryTime()));

            Payment payment = shipment.getPayment();
            if (payment == null) {
                Payment newPayment = new Payment(shipment, shipment.getOrderPaymentMethod());
                shipment.setPayment(newPayment);
            } else {
                payment.updateType(shipment.getOrderPaymentMethod());
            }

            if (stockLocation.hasEnabler() &&
                    stockLocation.getEnablerPlatform() == StockLocation.EnablerPlatform.JUBELIO) {
                JubelioDelivery jubelioDelivery = jubelioDeliveryRepository.findByShipmentId(shipment.getId());
                jubelioService.createSalesOrderByScheduler(jubelioDelivery);
            }

            if (stockLocation.isFulfilledBySayurbox() || stockLocation.isFulfilledBySayurboxInstant()) {
                Role shopperRole = roleRepository.findByName(Role.Name.SAYURBOX_SHOPPER);
                User shopper = userRepository.findByRolesContainingAndTenantId(shopperRole, shipment.getTenant().getId());
                if (shopper != null) {
                    Batch shoppingBatch = shipment.getShoppingJob().get().getBatch();
                    enablerService.startBatch(shoppingBatch, shopper);
                    enablerService.publishWebhookForBatches(WebhookType.START_BATCH, Stream.of(shoppingBatch).collect(Collectors.toList()));
                }
            }

            // When Shipment's Slot.Type is ON_DEMAND,
            // set Job.State to STARTED
            if (slot.getType().equals(Slot.Type.ON_DEMAND)) {
                adjustEtaOnDemand(shipmentForm, slot, shipment);
                adjustOnDemandJob(shipment, stockLocation, slot);
            }

            stockLocationService.refreshCache(slot.getId());
        }

        return shipmentRepository.save(shipment);
    }

    private void adjustEtaOnDemand(ShipmentForm shipmentForm, Slot slot, Shipment shipment) {
        // for this case slot.start_time value is original slot.start_time + additional buffer time and compared with spree slot start end time
        // if spree slot.start_time is after fulfillment-service slot.start_time, use spree slot start and end time
        if (slot.getStockLocation().isFulfilledByStrato() &&
                Boolean.TRUE.equals(slot.getStockLocation().getTenant().isEnabledExpressHfsEtaAdjustment())) {
            if (shipmentForm.isSlotTimeAdjusted(slot.getStartTime())) {
                long gapInSec = ChronoUnit.SECONDS.between(slot.getStartTime(), shipmentForm.getSlotStartTime());
                slot.setStartTime(slot.getStartTime().plusSeconds(gapInSec));
                slot.setEndTime(slot.getEndTime().plusSeconds(gapInSec));
                slotRepository.save(slot);

                adjustBatchJob(shipment, gapInSec);
            }

            if (shipment.getIsExpress() != null && shipment.getIsExpress())
                shipment.setOrderExpressSla(slot.getEndTime());
        }
    }

    private void adjustBatchJob(Shipment shipment, long gapInSec){
        List<Batch> batches = new ArrayList<>();
        List<Job> jobs = new ArrayList<>();
        shipment.getJobs().stream().filter(job -> job.getType().equals(Job.Type.SHOPPER) || job.getType().equals(Job.Type.ON_DEMAND_DRIVER))
                .forEach(job -> {
                    Batch currentBatch = job.getBatch();
                    currentBatch.setStartTime(currentBatch.getStartTime().plusSeconds(gapInSec));
                    currentBatch.setEndTime(currentBatch.getEndTime().plusSeconds(gapInSec));
                    batches.add(currentBatch);

                    if (job.getType().equals(Job.Type.ON_DEMAND_DRIVER)) {
                        job.setDeliveryTime(job.getDeliveryTime().plusSeconds(gapInSec));
                        jobs.add(job);
                    }
                });
        if (!batches.isEmpty())
            batchRepository.saveAll(batches);
        if (!jobs.isEmpty())
            jobRepository.saveAll(jobs);
    }

    private void adjustOnDemandJob(Shipment shipment, StockLocation stockLocation, Slot slot){
        shipment.getJobs().forEach(currentJob -> {
            ZoneId zoneId = ZoneId.of(stockLocation.getState().getTimeZone());
            LocalDateTime startTime = ZonedDateTime.of(slot.getStartTime(), ZoneId.of("UTC"))
                    .withZoneSameInstant(zoneId).toLocalDateTime();
            LocalDateTime endTime = ZonedDateTime.of(slot.getEndTime(), ZoneId.of("UTC"))
                    .withZoneSameInstant(zoneId).toLocalDateTime();

            final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");

            if (currentJob.getType().equals(Job.Type.ON_DEMAND_SHOPPER)) {
                currentJob.setState(Job.State.STARTED);
                jobRepository.save(currentJob);

                notificationService.sendPushNotificationForNewOnDemandShoppingJob(currentJob.getBatch().getUser().getId(), shipment.getOrderNumber(), formatter.format(startTime), formatter.format(endTime), currentJob.getTenant().getId());
            } else {
                Batch currentBatch = currentJob.getBatch();
                List<Batch> batches = batchRepository.findActiveBatchesByTypesAndUser(Batch.Type.getOnDemandRangerBatchTypes(), currentBatch.getUser(), Job.getInactiveJobStates());
                Optional<Batch> otherBatch = batches.stream().filter(b -> !b.getId().equals(currentBatch.getId())).findFirst();
                Optional<Job> otherJob = otherBatch.map(value -> value.getJobs().get(0));

                if (!otherJob.isPresent() || (otherJob.isPresent() && otherJob.get().getState().equals(Job.State.INITIAL) && otherJob.get().getType().equals(Job.Type.ON_DEMAND_RANGER))) {
                    if (currentJob.getType().equals(Job.Type.ON_DEMAND_RANGER)) {
                        currentJob.setState(Job.State.STARTED);
                        jobRepository.save(currentJob);
                    }
                }

                if (currentJob.getType().equals(Job.Type.ON_DEMAND_RANGER) || currentJob.getType().equals(Job.Type.ON_DEMAND_DRIVER)) {
                    notificationService.sendPushNotificationForNewOnDemandJob(currentJob.getBatch().getUser().getId(), shipment.getOrderNumber(), formatter.format(startTime), formatter.format(endTime), currentJob.getTenant().getId());
                }
            }
        });
    }

    private void adjustLocationCorrect(Shipment shipment, ShipmentForm shipmentForm){
        if (shipmentForm.getIsAddressIncorrect() != null)
            shipment.updateFlags(Shipment.FLAG_IS_ADDRESS_INCORRECT, shipmentForm.getIsAddressIncorrect().toString());
    }

    @Transactional
    public List<Item> updateReplacementType(String number, List<ItemForm> itemForms) {
        Shipment shipment = findByNumber(number);
        HashMap<String, String> itemFormMap = new HashMap<>();
        HashMap<String, ItemReplacementPreferenceForm> replacementPreferenceFormMap = new HashMap<>();
        HashMap<String, String> itemsShopperNotes = new HashMap<>();
        itemForms.forEach(itemForm -> {
            itemFormMap.put(itemForm.getSku(), itemForm.getReplacementType());
            replacementPreferenceFormMap.put(itemForm.getSku(), itemForm.getReplacementPreference());
            itemsShopperNotes.put(itemForm.getSku(), itemForm.getShopperNotes());
        });

        List<Item> items = itemRepository.findAllByShipmentId(shipment.getId());
        items.forEach(item -> {
            String replacementType = itemFormMap.get(item.getSku());
            ItemReplacementPreferenceForm preferenceForm = replacementPreferenceFormMap.get(item.getSku());
            if (item.getReplacementType() == null || !item.getReplacementType().equalsIgnoreCase(replacementType)) {
                item.setReplacementType(replacementType);
            }
            updateItemReplacementPreference(preferenceForm, item);
            item.setShopperNotes(itemsShopperNotes.get(item.getSku()));
            itemRepository.save(item);
        });

        return items;
    }

    @Transactional
    public Item updateItem(String number, OrderTotalWithItemForm orderTotalWithItemForm) {
        Shipment shipment = findByNumber(number);

        validateUpdateItemOnComplete(shipment);

        Item item = updateItem(shipment, orderTotalWithItemForm.getItem());

        // Update shipment order total first
        shipment.setOrderTotal(BigDecimal.valueOf(orderTotalWithItemForm.getOrderTotal()));
        shipmentRepository.save(shipment);

        updateBatchDeliveryType(shipment, item);

        sendPushNotificationForUpdateItem(shipment);

        return item;
    }

    public int countFoundOrOosItems(Shipment shipment) {
        return itemRepository.countFoundOrOosByShipment(shipment);
    }

    public int countItems(Shipment shipment) {
        return itemRepository.countByShipment(shipment);
    }

    @Transactional
    public ShipmentPackaging updatePackaging(String number, ShipmentPackagingForm shipmentPackagingForm) {
        Shipment shipment = findByNumber(number);

        validateUpdateItemOnComplete(shipment);

        return updatePackaging(shipment, shipmentPackagingForm);
    }

    private ShipmentPackaging updatePackaging(Shipment shipment, ShipmentPackagingForm shipmentPackagingForm) {
        List<ShipmentPackaging> shipmentPackagings = shipment.getShipmentPackagings();
        shipmentPackagings.clear();

        ShipmentPackaging shipmentPackaging = shipmentPackagingMapper.shipmentPackagingFormToShipmentPackaging(shipmentPackagingForm);
        Packaging packaging = packagingRepository.findById(shipmentPackagingForm.getFulfillmentPackagingId()).orElse(null);
        if (packaging != null) {
            shipmentPackaging.setPackagingInternalName(packaging.getInternalName());
            shipmentPackaging.setPackagingClientNames(packaging.getPackagingType().getClientNames());
            shipmentPackaging.setPackagingSndNames(packaging.getPackagingType().getSndNames());
            shipmentPackaging.setPackagingImageUrl(packaging.getImageUrl());
            shipmentPackaging.setPackagingMaxQuantity(packaging.getMaxQuantity());
        }
        shipmentPackaging.setShipment(shipment);

        return shipmentPackagingRepository.save(shipmentPackaging);
    }

    @Transactional
    public Shipment updateItems(String number, ShipmentForm shipmentForm) {
        Shipment shipment = findByNumber(number);

        validateUpdateItemOnComplete(shipment);

        List<Item> shipmentItems = itemRepository.findAllByShipmentId(shipment.getId());

        List<String> updatedItems = new ArrayList<>();
        List<ItemForm> itemForms = shipmentForm.getItems();
        List<ItemForm> addedItems = new ArrayList<>();
        for (ItemForm itemForm : itemForms) {
            Optional<Item> shipmentItem = shipmentItems.stream().filter(_item -> _item.getSku().equalsIgnoreCase(itemForm.getSku())).findFirst();
            Item updatedItem = null;
            if (shipmentItem.isPresent()) {
                updatedItem = shipmentItem.get();
            } else {
                addedItems.add(itemForm);
            }
            updatedItem = updateItem(shipment, updatedItem, itemForm);
            if (updatedItem != null) {
                updatedItems.add(updatedItem.getSku());
            }
        }

        List<Item> deletedItems = shipmentItems
                .stream()
                .filter(item -> (!updatedItems.contains(item.getSku())) && !item.isReplacement())
                .collect(Collectors.toList());
        itemRepository.deleteAll(deletedItems);

        try {
            Slot slot = shipment.getSlot();
            if (slot.getType().equals(Slot.Type.LONGER_DELIVERY)) {
                ldsTracker.construct(LongerDeliverySlotOptimizationEventTracker.EVENT_LIVE_EDIT_ORDER, slot.getStockLocation().getCluster(), shipment, slot, batchRepository, null);
                ObjectMapper mapper = new ObjectMapper();
                mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
                mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
                mapper.setDateFormat(new StdDateFormat().withColonInTimeZone(true));

                List<ItemPresenter> deletedItemPresenters = itemMapper.toItemPresenters(deletedItems);
                String trackPayload = "Number of added items:" + addedItems.size() +
                        "<newline> Added items:" + mapper.writeValueAsString(addedItems) +
                        "<newline> Number of deleted items:" + deletedItems.size() +
                        "<newline> Deleted items:" + mapper.writeValueAsString(deletedItemPresenters);
                ldsTracker.setValue(trackPayload);
                ldsTracker.track();
            }
        } catch (Exception e) {
            logger.error(" Error when serialize items.", e);
        }

        sendPushNotificationForUpdateItem(shipment);

        return shipment;
    }

    @Transactional
    public Item updateItem(String number, ItemForm itemForm) {
        Shipment shipment = findByNumber(number);

        validateUpdateItemOnComplete(shipment);

        Item item = updateItem(shipment, itemForm);

        updateBatchDeliveryType(shipment, item);

        return item;
    }

    private Item updateItem(Shipment shipment, ItemForm itemForm) {
        Item item = itemRepository.findByShipmentIdAndSku(shipment.getId(), itemForm.getSku());

        return updateItem(shipment, item, itemForm);
    }

    private Item updateItem(Shipment shipment, Item currentItem, ItemForm itemForm) {
        Item item = currentItem;
        if (item == null) {
            if (itemForm.getRequestedQty() > 0) {
                item = itemMapper.itemFormToItem(itemForm);

                StockLocation stockLocation = shipment.getSlot().getStockLocation();
                String isoName = stockLocation.getState().getCountry().getIsoName();
                item.setCurrency(isoName);
                updateItemReplacementPreference(itemForm, item);
                item = itemService.save(shipment, stockLocation, item, isoName, false);
            }
        } else {
            if (itemForm.getRequestedQty() <= 0) {
                itemRepository.delete(item);
            } else {
                item.setShopperNotes(itemForm.getShopperNotes());
                item.setRequestedQty(itemForm.getRequestedQty());
                item.setBundleQty(itemForm.getBundleQty());
                item.setFreeQty(itemForm.getFreeQty());
                item.setRequestedBeforeShopperStartedQty(itemForm.getQuantityBeforeShopperStarted());
                item.setPromotionSource(itemForm.getPromotionSource());
                item.setReplacementType(itemForm.getReplacementType());
                updateItemReplacementPreference(itemForm, item);
                item = itemRepository.save(item);
            }
        }

        return item;
    }

    private void updateItemReplacementPreference(ItemForm itemForm, Item currentItem) {
        ItemReplacementPreferenceForm preferenceForm = itemForm.getReplacementPreference();
        updateItemReplacementPreference(preferenceForm, currentItem);
    }

    private void updateItemReplacementPreference(ItemReplacementPreferenceForm preferenceForm, Item currentItem) {
        if (preferenceForm != null && currentItem != null) {
            ItemReplacementPreference replacementPreference = itemReplacementPreferenceService.createOrUpdateBySku(currentItem, preferenceForm);
            if (replacementPreference != null) replacementPreference.setItem(currentItem);
            currentItem.setShopperPickType(preferenceForm.getShopperPickType());
            currentItem.setReplacementPreferenceType(preferenceForm.getReplacementPreferenceType());
        }
    }

    private void updateBatchDeliveryType(Shipment shipment, Item item) {
        if (item == null)
            return;

        // If item is Grab Express restricted product and current batch is GE batch,
        // we need to update to HF batch.
        Optional<Job> geDeliveryJob = shipment.getJobs().stream().filter(job -> (job.isDelivery() || job.isOnDemandDelivery()) && job.getBatch().getDeliveryType() == Batch.DeliveryType.TPL && job.getBatch().getTplType() == Batch.TplType.GRAB_EXPRESS).findFirst();
        if (!geDeliveryJob.isPresent()) {
            return;
        }

        Country country = shipment.getSlot().getStockLocation().getState().getCountry();

        if (item.isConsideredAsGeRestrictedProduct() ||
                (shipment.getOrderPaymentMethod().equals(Shipment.COD_PAYMENT_TYPE) && shipment.getOrderTotal().doubleValue() > country.getGrabExpressMaxCodAmount()) ||
                shipment.getTotalVolumeInML() > country.getGrabExpressMaxDeliveryVolumeInLitre() * 1000
        ) {
            Batch batch = geDeliveryJob.get().getBatch();
            batch.setDeliveryType(Batch.DeliveryType.NORMAL);
            batch.setTplType(null);
            batchRepository.save(batch);
        }
    }

    private void validateUpdateItemOnComplete(Shipment shipment) {
        Optional<Job> job = shipment.getShoppingOrRangerJob();
        if (!job.isPresent())
            throw new JobNotFoundException();

        Batch shoppingBatch = job.get().getBatch();
        if (shoppingBatch.getUser() != null && !shoppingBatch.getType().equals(Batch.Type.ON_DEMAND) && !shoppingBatch.getType().equals(Batch.Type.ON_DEMAND_SHOPPING))
            if (job.get().getState() != Job.State.INITIAL && job.get().getState() != Job.State.STARTED) {
                throw new CannotUpdateItemOnCompleteException();
            }
    }

    @Transactional
    public Shipment cancel(String number) throws Exception {
        Shipment shipment = findByNumber(number);

        if (shipment.getState() == Shipment.State.CANCELLED)
            return shipment;

        Long deliveryShiftId = getDeliveryShiftId(shipment);

        boolean shopperAutoAssignmentFlag = getShopperAutoAssignmentFlag(shipment);
        boolean driverAutoAssignmentFlag = getDriverAutoAssignmentFlag(shipment);

        cancelGrabExpress(shipment);
        cancelLalamove(shipment);
        cancelDelyva(shipment);
        Job deliveryJob = batchSndService.getDeliveryJob(number);

        List<Job> jobs = shipment.getJobs();
        List<Long> userIds = jobs.stream()
                .filter(job -> job.getBatch().getUser() != null)
                .map(job -> job.getBatch().getUser().getId())
                .collect(Collectors.toList());

        if (!userIds.isEmpty()) {
            notificationService.sendPushNotificationForOrderCancellation(userIds, shipment.getOrderNumber(), shipment.getTenant().getId());
        }

        List<Job> deletedJobs = new ArrayList<>();
        for (Job job : jobs) {
            if (Lists.newArrayList(Job.State.INITIAL, Job.State.STARTED).contains(job.getState())) {
                deletedJobs.add(job);
            } else {
                job.setState(Job.State.CANCELLED);
                jobRepository.save(job);
            }
        }

        Slot slot = shipment.getSlot();
        Cluster cluster = slot.getStockLocation().getCluster();
        Shift shoppingShift = null;
        Optional<Job> shoppingJob = shipment.getShoppingJob();
        if (shoppingJob.isPresent()) {
            shoppingShift = shoppingJob.get().getBatch().getShift();
        }

//        Job deletedDeliveryJob = deletedJobs.stream().filter(job -> job.isDelivery() || job.isRanger() || job.isOnDemandRanger() || job.isOnDemandDelivery())
//                .findFirst().orElse(null);
//        if (deletedDeliveryJob == null) {
//            fleetTrackingService.syncFleetTracking(number, FleetTrackingService.EventName.CANCEL_ORDER);
//        }

        for (Job job : deletedJobs) {
            Batch batch = job.getBatch();

            shipment.getJobs().remove(job);
            jobRepository.delete(job);

            try {
                if (slot.getType() != Slot.Type.LONGER_DELIVERY) {
                    batchRecalculateService.recalculateBatch(batch.getId());
                } else {
                    List<Job> jobsInTheBatch = jobRepository.findAllByBatchId(batch.getId());
                    if (jobsInTheBatch.isEmpty()) {
                        batchRepository.deleteById(batch.getId());
                    }
                }
            } catch (ReservedSlotException ex) {
                List<Job> jobsInTheBatch = jobRepository.findAllByBatchId(batch.getId());
                if (jobsInTheBatch.isEmpty()) {
                    batchRepository.deleteById(batch.getId());
                }
            }
        }

        shipment.setState(Shipment.State.CANCELLED);
        shipmentRepository.save(shipment);

        jubelioService.cancelSalesOrder(shipment);

        if (Job.Type.getOnDemandRangerJobTypes().contains(deliveryJob.getType())) {
            batchSndService.startNextOnDemandJob(deliveryJob);
        }

        if (cluster.isEnableLocus() && !shipment.getSlot().getType().equals(Slot.Type.ON_DEMAND)) {
            locusTaskService.cancelTask(shipment);
        }

        if (slot.getType().equals(Slot.Type.LONGER_DELIVERY)) {
            ldsTracker.construct(LongerDeliverySlotOptimizationEventTracker.EVENT_CANCEL_SHIPMENT, cluster, shipment, slot, batchRepository, null);
            ldsTracker.track();
        }

        StockLocation stockLocation = slot.getStockLocation();
        String timeZone = stockLocation.getState().getTimeZone();

        if (slot.getType() == Slot.Type.LONGER_DELIVERY) {
            Long shoppingShiftId = 0l;
            if (shoppingShift != null)
                shoppingShiftId = shoppingShift.getId();


            SlotOptimizationContext context = new SlotOptimizationContext(
                    slot.getId(), shoppingShiftId, cluster.getId(),
                    deliveryShiftId, slot.getStartTime(),
                    shopperAutoAssignmentFlag, driverAutoAssignmentFlag, timeZone,
                    cluster.getTenant().isEnableSlotOptimizationEventDeduplication());
            slotOptimizationService.publishSlotOptimization(context);
        }

        if (stockLocation.isFulfilledByStrato()) {
            if (slot.getType().equals(Slot.Type.ON_DEMAND)) {
                stratoApiService.releaseExpressCapacityWithRetry(shipment.getOrderNumber(), stockLocation.getExternalId());
            } else {
                stratoApiService.releaseCapacityWithRetry(shipment.getOrderNumber(), stockLocation.getExternalId());
            }
        }

        stockLocationService.refreshCache(slot.getId());

        return shipment;
    }

    public Long getDeliveryShiftId(Shipment shipment) {
        Long shiftId = 0L;
        Optional<Job> optRangerJob = shipment.getRangerJob();
        Optional<Job> optDeliveryJob = shipment.getDeliveryJob();
        if (optRangerJob.isPresent()) {
            shiftId = optRangerJob.get().getBatch().getShift() != null ? optRangerJob.get().getBatch().getShift().getId() : 0L;
        } else if (optDeliveryJob.isPresent()) {
            Job job = optDeliveryJob.get();
            if (job.getBatch().isHFDelivery()) {
                shiftId = job.getBatch().getShift() != null ? job.getBatch().getShift().getId() : 0L;
            }
        }
        return shiftId;
    }

    private boolean getShopperAutoAssignmentFlag(Shipment shipment) {
        return  (shipment.getShoppingJob().isPresent() &&
                shipment.getSlot().getStockLocation().isAllowedShopperAutoAssignment());
    }

    private boolean getDriverAutoAssignmentFlag(Shipment shipment) {
        Cluster cluster = shipment.getSlot().getStockLocation().getCluster();
        Optional<Job> optRangerOrDeliveryJob = shipment.getDeliveryOrRangerJob();

        return (optRangerOrDeliveryJob.isPresent() &&
                cluster.isEnableDriverAutoAssignment()
                && batchRepository.isTheLastShipmentInBatch(optRangerOrDeliveryJob.get().getBatch().getId(),
                shipment.getOrderNumber(), Job.getInactiveJobStates()));
    }

    @Transactional
    public void unassignedShipment(String number) {
        Shipment shipment = findByNumber(number);

        if (shipment.getState() == Shipment.State.READY)
            throw new UnassignedShipmentException();

        destroy(shipment);
    }

    @SneakyThrows
    @Transactional(propagation = Propagation.MANDATORY)
    public void destroy(Shipment shipment) {
        List<Batch> batches = shipment.getJobs().stream().map(job -> job.getBatch()).distinct().collect(Collectors.toList());
        List<Job> jobs = shipment.getJobs();
        List<Item> items = shipment.getItems();
        Slot slot = shipment.getSlot();
        StockLocation stockLocation = slot.getStockLocation();

        Optional<Job> deliveryJob = jobs.stream().filter(job -> job.isDelivery() || job.isRanger() || job.isOnDemandRanger() || job.isOnDemandDelivery()).findAny();
        if (deliveryJob.isPresent() && deliveryJob.get().isLocus())
            locusTaskService.cancelTask(shipment);

        jobRepository.deleteAll(jobs);
        itemRepository.deleteAll(items);
        shipmentRepository.delete(shipment);
        for (Batch batch : batches) {
            if (shipment.getSlot().getType().equals(Slot.Type.LONGER_DELIVERY))
                batchRecalculateService.rearrangeBatchesWithinVehicle(batch, slot, "SHIPMENT DESTROYED");
            else
                batchRecalculateService.recalculateBatch(batch.getId());
        }

        if (deliveryJob.isPresent() && Job.Type.getOnDemandRangerJobTypes().contains(deliveryJob.get().getType())) {
            batchSndService.startNextOnDemandJob(deliveryJob.get());
        }

        if (slot.getType().equals(Slot.Type.LONGER_DELIVERY)) {
            ldsTracker.construct(LongerDeliverySlotOptimizationEventTracker.EVENT_DESTROY_SHIPMENT, slot.getStockLocation().getCluster(), shipment, slot, batchRepository, null);
            ldsTracker.track();
        }

        if (slot.getType().equals(Slot.Type.ON_DEMAND))
            slotRepository.delete(slot);

        if (stockLocation.isFulfilledByStrato()) {
            if (slot.getType().equals(Slot.Type.ON_DEMAND)) {
                stratoApiService.releaseExpressCapacityWithRetry(shipment.getOrderNumber(), stockLocation.getExternalId());
            } else {
                stratoApiService.releaseCapacityWithRetry(shipment.getOrderNumber(), stockLocation.getExternalId());
            }
        }

    }

    @Transactional(readOnly = true)
    public Shipment findByNumber(String number) {
        Shipment shipment = shipmentRepository.findByNumber(number);

        if (shipment == null)
            throw new EntityNotFoundException();

        return shipment;
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForCODPayment(Shipment shipment) {
        Optional<Job> job = shipment.getDeliveryOrRangerJob();

        if (!job.isPresent())
            return true;

        if (job.get().getType().equals(Job.Type.RANGER))
            return true;

        Batch batch = job.get().getBatch();
        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        // Check for store that using enabler(brand store)
        if (stockLocation.hasEnabler() && stockLocation.isFulfilledByJubelio())
            return false;

        Country country = stockLocation.getState().getCountry();

        if (batch.isGrabExpress() && !isEligibleForGrabExpressCOD(shipment, stockLocation, country))
            return false;

        if (batch.isLalamove() && !isEligibleForLalamoveCod(shipment, stockLocation, country))
            return false;

        if (batch.isDelyva()) return false;

        return true;
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForEwalletPayment(Shipment shipment) {
        Optional<Job> job = shipment.getDeliveryOrRangerJob();

        if (job.isPresent()) {
            Batch batch = job.get().getBatch();

            if (batch.getDeliveryType().equals(Batch.DeliveryType.TPL)) {
                return shipment.getSlot().getStockLocation().getState().getCountry().getAllowEWalletForTPL();
            }
        }

        return true;
    }

    @Transactional(readOnly = true)
    public VirtualAccountEligibilityPresenter isEligibleForVirtualAccountPayment(Shipment shipment) {
        Slot slot = shipment.getSlot();
        StockLocation stockLocation = slot.getStockLocation();
        Country country = stockLocation.getState().getCountry();

        Boolean isNextHourOrOngoing = isNextHourSlot(slot) || isOnGoingSlot(slot);

        VirtualAccountEligibilityPresenter vaEligibilityPresenter = new VirtualAccountEligibilityPresenter();
        vaEligibilityPresenter.setIsNextHour(isNextHourOrOngoing);

        boolean eligible = true;
        if (!stockLocation.isEnableVirtualAccountPayment()) {
            eligible = false;
            vaEligibilityPresenter.setNotEligibleReason(VirtualAccountEligibilityPresenter.NotEligibleReason.STORE_NOT_ELIGIBLE);
        }

        if (eligible && isNextHourOrOngoing && !country.getAllowVAForExpress()) {
            eligible = false;
            vaEligibilityPresenter.setNotEligibleReason(VirtualAccountEligibilityPresenter.NotEligibleReason.EXPRESS_NOT_ELIGIBLE);
        }

        if (eligible && isTplShipment(shipment) && !country.getAllowVAForTPL()) {
            eligible = false;
            vaEligibilityPresenter.setNotEligibleReason(VirtualAccountEligibilityPresenter.NotEligibleReason.TPL_NOT_ELIGIBLE);
        }

        vaEligibilityPresenter.setEligible(eligible);
        return vaEligibilityPresenter;
    }

    @Transactional(readOnly = true)
    public PaymentEligibilityPresenter getPaymentEligibility(String shipmentNumber) {
        Shipment shipment = findByNumber(shipmentNumber);
        Optional<Job> optionalJob = shipment.getDeliveryOrRangerJob();
        boolean isEligibleForCODPayment = isEligibleForCODPayment(shipment);
        boolean isEligibleForEwallet = isEligibleForEwalletPayment(shipment);
        VirtualAccountEligibilityPresenter isEligibleForVA = isEligibleForVirtualAccountPayment(shipment);

        PaymentEligibilityPresenter presenter = new PaymentEligibilityPresenter();
        presenter.setEligibleForCod(isEligibleForCODPayment);
        presenter.setEligibleForCreditCard(true);
        presenter.setEligibleForEwallet(isEligibleForEwallet);
        presenter.setEligibleForVa(isEligibleForVA);
        if (optionalJob.isPresent()) {
            Job deliveryJob = optionalJob.get();
            String fleetType = getFleetType(deliveryJob.getBatch());
            presenter.setFleetType(fleetType);
        }
        return presenter;
    }

    private String getFleetType(Batch batch) {
        if (batch.getDeliveryType() != Batch.DeliveryType.TPL) {
            return Slot.FleetType.HF.toString();
        } else {
            if (batch.getTplType() == Batch.TplType.GRAB_EXPRESS) {
                return Slot.FleetType.GE.toString();
            } else if (batch.getTplType() == Batch.TplType.LALAMOVE) {
                return Slot.FleetType.LALAMOVE.toString();
            } else {
                return Slot.FleetType.TPL.toString();
            }
        }
    }

    private boolean isNextHourSlot(Slot slot) {
        StockLocation stockLocation = slot.getStockLocation();
        LocalDateTime now = LocalDateTime.now();
        final int instantDeliveryCutoffTime = stockLocation.getInstantDeliveryCutoffTime();
        LocalDateTime nextHourWindowEnd = now.plusMinutes(instantDeliveryCutoffTime);
        return slot.getStartTime().isAfter(now) && slot.getStartTime().isBefore(nextHourWindowEnd);
    }

    private boolean isOnGoingSlot(Slot slot) {
        LocalDateTime now = LocalDateTime.now();
        return slot.getStartTime().isBefore(now) || slot.getStartTime().isEqual(now);
    }

    private boolean isTplShipment(Shipment shipment) {
        Optional<Job> deliveryJob = shipment.getDeliveryOrRangerJob();
        if (deliveryJob.isPresent()) {
            Batch batch = deliveryJob.get().getBatch();
            return Batch.DeliveryType.TPL.equals(batch.getDeliveryType());
        } else {
            return false;
        }
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForGrabExpress(Shipment shipment, StockLocation stockLocation, Country country) {
        return stockLocation.isEnableGrabExpress()
                && shipment.isCustomerAllowGeDelivery()
                && shipment.getItems().stream().noneMatch(Item::isConsideredAsGeRestrictedProduct)
                && itemsDimensionEligibleForGrab(country, shipment)
                && shipment.getTotalVolumeInML() <= country.getGrabExpressMaxDeliveryVolumeInLitre() * 1000
                && !isOrderClientTypeLineMan(shipment)
                && shipment.getOrderCompanyId() == null;
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForGrabExpressCOD(Shipment shipment, StockLocation stockLocation, Country country) {
        return isEligibleForGrabExpress(shipment, stockLocation, country)
                && stockLocation.isEnableGrabExpressCod()
                && (shipment.getOrderTotal().doubleValue() <= country.getGrabExpressMaxCodAmount())
                && !isOrderClientTypeLineMan(shipment);
    }

    @Transactional
    public void replacePackaging(String shipmentNumber, PackagingReplacementForm form) throws Exception {
        Shipment shipment = shipmentRepository.findByNumber(shipmentNumber);
        if (shipment == null)
            throw new UnprocessableEntityException("Shipment with given number is not found.");

        StockLocation stockLocation = shipment.getSlot().getStockLocation();
        Packaging packaging = packagingRepository.findByIdAndStockLocationId(form.getPackagingId(), stockLocation.getId())
                .orElseThrow(() -> new UnprocessableEntityException("There's no packaging with given packaging_id in stock location."));

        // Delete previous replaced item, if exist
        shipmentPackagingRepo.findReplacedByShipmentId(shipment.getId())
                .ifPresent(existingReplacement -> shipmentPackagingRepo.delete(existingReplacement));

        // Create new replacement on ShipmentPackaging
        ShipmentPackaging sPackaging = new ShipmentPackaging();
        sPackaging.setShipment(shipment);
        sPackaging.setPackagingInternalName(packaging.getInternalName());
        sPackaging.setPackagingClientNames(packaging.getPackagingType().getClientNames());
        sPackaging.setPackagingSndNames(packaging.getPackagingType().getSndNames());
        sPackaging.setPackagingImageUrl(packaging.getPackagingType().getImageUrl());
        sPackaging.setPackagingPrice(packaging.getPrice());
        sPackaging.setPackagingMaxQuantity(packaging.getMaxQuantity());
        sPackaging.setReplacement(true);
        sPackaging.setFulfillmentPackagingId(packaging.getId());
        sPackaging.setReplacementReason(form.getReplacementReason());
        shipmentPackagingRepo.save(sPackaging);
    }

    @Transactional(readOnly = true)
    public boolean isEligibleToChangeSlotToGE(String number, StockLocation stockLocation) {
        Shipment shipment = shipmentRepository.findByNumber(number);

        if (shipment == null)
            return true;

        return !(shipment.isReady() && ((shipment.isCOD() && !stockLocation.isEnableGrabExpressCod()) || (shipment.isEWallet()) || (shipment.isQR())));
    }

    private boolean itemsDimensionEligibleForGrab(Country country, Shipment shipment) {
        boolean itemsDimensionEligibleForGrab;
        float MAX_LINE_ITEM_DIMENSION_FOR_GRAB = country.getGrabExpressMaxProductDimension();
        for (Item item : shipment.getItems()) {
            if (item.getBundleQty() > 0) {
                return false;
            } else {
                itemsDimensionEligibleForGrab = item.getHeight() <= MAX_LINE_ITEM_DIMENSION_FOR_GRAB && item.getDepth() <= MAX_LINE_ITEM_DIMENSION_FOR_GRAB && item.getWidth() <= MAX_LINE_ITEM_DIMENSION_FOR_GRAB;
                if (!itemsDimensionEligibleForGrab) return false;
            }
        }
        return true;
    }

    private void cancelGrabExpress(Shipment shipment) {
        // Cancel GE booking if batch Grab Express
        GrabExpressDelivery grabExpressDelivery = shipment.getCurrentGrabExpressDelivery();
        List<GrabExpressDelivery.Status> activeStatuses = Arrays.asList(GrabExpressDelivery.Status.ALLOCATING, GrabExpressDelivery.Status.PICKING_UP);
        if (grabExpressDelivery != null) {
            if (grabExpressDelivery.getStatus() == null) {
                grabExpressDeliveryRepository.delete(grabExpressDelivery.getId());
            } else if (activeStatuses.contains(grabExpressDelivery.getStatus())) {
                grabExpressService.cancelGrabExpressBooking(shipment.getNumber(), "CUSTOMER_ASKED_TO_CANCEL");
            }
        }
    }

    private void cancelLalamove(Shipment shipment) {
        LalamoveDelivery lalamoveDelivery = shipment.getCurrentLalamoveDelivery();
        if (lalamoveDelivery != null) {
            if (lalamoveDelivery.getStatus() == null) {
                lalamoveDeliveryRepository.delete(lalamoveDelivery.getId());
            } else {
                lalamoveService.cancelLalamoveBooking(shipment, lalamoveDelivery);
            }
        }
    }

    private void cancelDelyva(Shipment shipment) {
        DelyvaDelivery delyvaDelivery = shipment.getCurrentDelyvaDelivery();
        if (delyvaDelivery != null) {
            if (delyvaDelivery.getStatus() == null) {
                delyvaDeliveryRepository.delete(delyvaDelivery.getId());
            } else {
                delyvaService.cancelDelyvaBooking(shipment, delyvaDelivery);
            }
        }
    }

    private boolean isOrderClientTypeLineMan(Shipment shipment) {
        return LINEMAN.equalsIgnoreCase(shipment.getOrderClientType());
    }

    private boolean isOrderClientTypeGrabfresh(Shipment shipment) {
        return GRABFRESH.equalsIgnoreCase(shipment.getOrderClientType());
    }

    @Transactional(readOnly = true)
    public Optional<Job> getOnDemandShoppingJob(Shipment shipment) {
        List<Job> jobs = jobRepository.findAllByShipmentIdIn(Collections.singletonList(shipment.getId()));
        return jobs.stream().filter(Job::isOnDemandShopping).findAny();
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForLalamove(Shipment shipment, StockLocation stockLocation) {

        // Exclude HappyCorporate for LLM specific in country thailand
        if (stockLocation.getState().getCountry().getIsoName().equals("TH") && nonNull(shipment.getOrderCompanyId())){
            return false;
        }

        return stockLocation.isEnableLalamove()
                && !isOrderClientTypeLineMan(shipment);
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForLalamoveCod(Shipment shipment, StockLocation stockLocation, Country country) {
        return isEligibleForLalamove(shipment, stockLocation)
                && country.isEnableLalamoveCod();
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForLalamoveFourWheels(Shipment shipment, StockLocation stockLocation) {
        if (!stockLocation.isEnableLalamoveFourWheels()
                || shipment.isExpressDelivery()
                || !isVolumeAndDistanceEligibleForTPLCar(shipment)
        ){
            return false;
        }

        return isEligibleForLalamoveFourWheelsDimensionAndService(shipment, stockLocation);
    }

    public boolean isEligibleForLalamoveFourWheelsDimensionAndService(Shipment shipment, StockLocation stockLocation) {
        LalamoveServiceType fourWheelsDelivery = lalamoveService.getFourWheelsServiceTypeByCountryV3(stockLocation);

        if (fourWheelsDelivery == null) {
            logger.info("[Availability] isEligibleForLalamoveFourWheelsDimensionAndService fourWheelsDelivery not found");
            return false;
        }

//        boolean shipmentDimensionEligibleForLalamove = shipmentDimensionEligibleForLalamove(fourWheelsDelivery, shipment);
//        boolean itemsDimensionEligibleForLalamove = itemsDimensionEligibleForLalamove(fourWheelsDelivery, shipment);

        logger.info("[Availability] isEligibleForLalamoveFourWheelsDimensionAndService fourWheelsDelivery {}", fourWheelsDelivery.getServiceTypeEnum().toString());
//        logger.info("[Availability] isEligibleForLalamoveFourWheelsDimensionAndService shipmentDimensionEligibleForLalamove {}", shipmentDimensionEligibleForLalamove);
//        logger.info("[Availability] isEligibleForLalamoveFourWheelsDimensionAndService itemsDimensionEligibleForLalamove {}", itemsDimensionEligibleForLalamove);

        return true;
//        return shipmentDimensionEligibleForLalamove && itemsDimensionEligibleForLalamove;
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForLalamoveVan(Shipment shipment, StockLocation stockLocation) {
        if (!stockLocation.isEnableLalamoveVan()) {
            return false;
        }

        double totalVolumeInLitre = shipment.getTotalVolumeInLitre();

        if (totalVolumeInLitre <= TPL_VOLUME_THRESHOLD_TIER_4) {
            logger.info("[Availability] isEligibleForLalamoveVan totalVolumeInLitre below threshold {}", TPL_VOLUME_THRESHOLD_TIER_4);
            return false;
        }

        LalamoveServiceType vanDelivery = lalamoveService.getVanServiceTypeByCountryV3(stockLocation);

        if (vanDelivery == null) {
            logger.info("[Availability] isEligibleForLalamoveVan vanDelivery not found");
            return false;
        }

//        boolean shipmentDimensionEligibleForLalamove = shipmentDimensionEligibleForLalamove(vanDelivery, shipment);
//        boolean itemsDimensionEligibleForLalamove = itemsDimensionEligibleForLalamove(vanDelivery, shipment);

        logger.info("[Availability] isEligibleForLalamoveVan vanDelivery {}", vanDelivery.getServiceTypeEnum().toString());
//        logger.info("[Availability] isEligibleForLalamoveVan shipmentDimensionEligibleForLalamove {}", shipmentDimensionEligibleForLalamove);
//        logger.info("[Availability] isEligibleForLalamoveVan itemsDimensionEligibleForLalamove {}", itemsDimensionEligibleForLalamove);

        return true;
//        return shipmentDimensionEligibleForLalamove && itemsDimensionEligibleForLalamove;
    }


//    TODO:: review this unused code
    @Transactional(readOnly = true)
    public boolean isEligibleToChangeSlotToLalamove(String number, Country country) {
        Shipment shipment = shipmentRepository.findByNumber(number);

        if (shipment == null)
            return true;

        return !(shipment.isReady() && ((shipment.isCOD() && !country.isEnableLalamoveCod()) || shipment.isEWallet() || shipment.isQR()));
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForLalamoveTwoWheels(Shipment shipment, StockLocation stockLocation, Country country) {
        if (!stockLocation.isEnableLalamoveTwoWheels()
                || !isVolumeAndDistanceEligibleForTPLBike(shipment)
        ) {
            return false;
        }

        return isEligibleForLalamoveTwoWheelsServiceAndDimension(shipment, stockLocation, country);
    }

    private boolean isEligibleForLalamoveTwoWheelsServiceAndDimension(Shipment shipment, StockLocation stockLocation, Country country) {
        LalamoveServiceType twoWheelsDelivery = lalamoveService.getServiceTypeByEnum(LalamoveServiceTypeEnum.MOTORCYCLE, country, stockLocation);

        if (twoWheelsDelivery == null) {
            logger.info("[Availability] isEligibleForLalamoveTwoWheelsServiceAndDimension twoWheelsDelivery not found");
            return false;
        }

//        boolean shipmentDimensionEligibleForLalamove = shipmentDimensionEligibleForLalamove(twoWheelsDelivery, shipment);
//        boolean itemsDimensionEligibleForLalamove = itemsDimensionEligibleForLalamove(twoWheelsDelivery, shipment);

        logger.info("[Availability] isEligibleForLalamoveTwoWheelsServiceAndDimension twoWheelsDelivery {}", twoWheelsDelivery.getServiceTypeEnum().toString());
//        logger.info("[Availability] isEligibleForLalamoveTwoWheelsServiceAndDimension shipmentDimensionEligibleForLalamove {}", shipmentDimensionEligibleForLalamove);
//        logger.info("[Availability] isEligibleForLalamoveTwoWheelsServiceAndDimension itemsDimensionEligibleForLalamove {}", itemsDimensionEligibleForLalamove);

//        notes ini gk usah di check karena udah di check via TPL dari logicnya team product
        return true;
//        return shipmentDimensionEligibleForLalamove && itemsDimensionEligibleForLalamove;
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForGosendBike(Shipment shipment, StockLocation stockLocation) {
        return !shipment.isExpressDelivery() && stockLocation.isEnableGosendBike() && isVolumeAndDistanceEligibleForTPLBike(shipment);
    }

    private boolean isVolumeAndDistanceEligibleForTPLBike(Shipment shipment) {
        double totalVolumeInLitre = shipment.getTotalVolumeInLitre();
        double maxItemVolumeInLitre = shipment.getMaxItemVolumeInLitre();

        logger.info("[Availability] isVolumeAndDistanceEligibleForTPLBike totalVolumeInLitre {}", totalVolumeInLitre);
        logger.info("[Availability] isVolumeAndDistanceEligibleForTPLBike maxItemVolumeInLitre {}", maxItemVolumeInLitre);
        logger.info("[Availability] isVolumeAndDistanceEligibleForTPLBike shipment.isExpressDelivery() {}", shipment.isExpressDelivery());

        return totalVolumeInLitre <= TPL_VOLUME_THRESHOLD_TIER_1 && maxItemVolumeInLitre < GOSEND_ITEM_VOLUME_THRESHOLD;
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForGosendBikeBackup(Shipment shipment, StockLocation stockLocation, int maxVolume) {
        boolean isEligibleForTPLBikeBackup = isEligibleForTPLBikeBackup(shipment, maxVolume);
        return !shipment.isExpressDelivery() && stockLocation.isEnableGosendBike() && isEligibleForTPLBikeBackup;
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForLalamoveTwoWheelsBackup(Shipment shipment, StockLocation stockLocation, Country country, int maxVolume) {
        return !shipment.isExpressDelivery()
                && stockLocation.isEnableLalamoveTwoWheels()
                && isEligibleForLalamoveTwoWheelsServiceAndDimension(shipment, stockLocation, country)
                && isEligibleForTPLBikeBackup(shipment, maxVolume);
    }

    public boolean isEligibleForTPLBikeBackup(Shipment shipment, int maxVolume) {
        double totalVolumeInLitre = shipment.getTotalVolumeInLitre();

        logger.info("[Availability] isEligibleForTPLBikeBackup totalVolumeInLitre {}", totalVolumeInLitre);

        return totalVolumeInLitre <= maxVolume;
    }

    @Transactional(readOnly = true)
    public boolean isEligibleToChangeSlotToGosendBike(Shipment shipment) {
        shipment = shipmentRepository.findByNumber(shipment.getNumber());

        if (shipment == null)
            return true;

        return !(shipment.isReady());
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForGosendCar(Shipment shipment, StockLocation stockLocation) {
        return !shipment.isExpressDelivery() && stockLocation.isEnableGosendCar() && isVolumeAndDistanceEligibleForTPLCar(shipment);
    }

    private boolean isVolumeAndDistanceEligibleForTPLCar(Shipment shipment) {
        double totalVolumeInLitre = shipment.getTotalVolumeInLitre();
        double maxItemVolumeInLitre = shipment.getMaxItemVolumeInLitre();

        logger.info("[Availability] isVolumeAndDistanceEligibleForTPLCar totalVolumeInLitre {}", totalVolumeInLitre);
        logger.info("[Availability] isVolumeAndDistanceEligibleForTPLCar maxItemVolumeInLitre {}", maxItemVolumeInLitre);
        logger.info("[Availability] isVolumeAndDistanceEligibleForTPLCar shipment.isExpressDelivery() {}", shipment.isExpressDelivery());

        boolean isMediumVolumeDistanceEligible = totalVolumeInLitre > TPL_VOLUME_THRESHOLD_TIER_2
                && totalVolumeInLitre <= TPL_VOLUME_THRESHOLD_TIER_3
                && maxItemVolumeInLitre >= GOSEND_ITEM_VOLUME_THRESHOLD;
        boolean isLargeVolumeDistanceEligible = totalVolumeInLitre > TPL_VOLUME_THRESHOLD_TIER_3
                && totalVolumeInLitre <= TPL_VOLUME_THRESHOLD_TIER_4;

        logger.info("[Availability] isVolumeAndDistanceEligibleForTPLCar isMediumVolumeDistanceEligible {}", isMediumVolumeDistanceEligible);
        logger.info("[Availability] isVolumeAndDistanceEligibleForTPLCar isLargeVolumeDistanceEligible {}", isLargeVolumeDistanceEligible);

        return isMediumVolumeDistanceEligible || isLargeVolumeDistanceEligible;
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForGosendCarBackup(Shipment shipment, StockLocation stockLocation, int volumeCriteria1, int volumeCriteria2, int itemVolumeCriteria) {
        return stockLocation.isEnableGosendCar() && isEligibleForTPLCarBackup(shipment, stockLocation, volumeCriteria1, volumeCriteria2, itemVolumeCriteria);
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForLalamoveFourWheelsBackup(Shipment shipment, StockLocation stockLocation, int volumeCriteria1, int volumeCriteria2, int itemVolumeCriteria) {
        return stockLocation.isEnableLalamoveFourWheels()
                && isEligibleForLalamoveFourWheelsDimensionAndService(shipment, stockLocation)
                && isEligibleForTPLCarBackup(shipment, stockLocation, volumeCriteria1, volumeCriteria2, itemVolumeCriteria);
    }

    private boolean isEligibleForTPLCarBackup(Shipment shipment, StockLocation stockLocation, int volumeCriteria1, int volumeCriteria2, int itemVolumeCriteria) {
        double totalVolumeInLitre = shipment.getTotalVolumeInLitre();
        double maxItemVolumeInLitre = shipment.getMaxItemVolumeInLitre();

        logger.info("[Availability] isEligibleForTPLCarBackup totalVolumeInLitre {}", totalVolumeInLitre);
        logger.info("[Availability] isEligibleForTPLCarBackup maxItemVolumeInLitre {}", maxItemVolumeInLitre);
        logger.info("[Availability] isEligibleForTPLCarBackup shipment.isExpressDelivery() {}", shipment.isExpressDelivery());

        return !shipment.isExpressDelivery() &&
                totalVolumeInLitre > volumeCriteria1 && totalVolumeInLitre <= volumeCriteria2 &&
                maxItemVolumeInLitre < itemVolumeCriteria;
    }

    @Transactional(readOnly = true)
    public boolean isEligibleToChangeSlotToGosendCar(Shipment shipment) {
        shipment = shipmentRepository.findByNumber(shipment.getNumber());

        if (shipment == null)
            return true;

        return !(shipment.isReady());
    }

    @Transactional(readOnly = true)
    public boolean isBigVolumeOrDeliveredByCar(Shipment shipment) {
        return isVolumeAndDistanceEligibleForTPLCar(shipment) || shipment.getTotalVolumeInLitre() > TPL_VOLUME_THRESHOLD_TIER_4;
    }

    @Transactional(readOnly = true)
    public long getShoppingBatchIdByShipmentNumber(String shipmentNumber) {
        long batchId;
        Shipment shipment = shipmentRepository.findByNumber(shipmentNumber);

        if (shipment == null)
            throw new EntityNotFoundException("Shipment not found");

        Optional<Job> shoppingJob = shipment.getShoppingOrRangerJob();
        if (shoppingJob.isPresent()) {
            Job job = shoppingJob.get();
            batchId = job.getBatch().getId();
        } else {
            throw new ShipmentCancelledException();
        }
        return batchId;
    }

    private boolean itemsDimensionEligibleForLalamove(LalamoveServiceType serviceType, Shipment shipment) {
        boolean itemsDimensionEligible;
        for (Item item : shipment.getItems()) {
            if (item.getBundleQty() > 0) {
                return false;
            } else {
                itemsDimensionEligible = item.getHeight() <= serviceType.getMaxHeight() && item.getDepth() <= serviceType.getMaxLength() && item.getWidth() <= serviceType.getMaxWidth();
                if (!itemsDimensionEligible) return false;
            }
        }
        return true;
    }

    private boolean shipmentDimensionEligibleForLalamove(LalamoveServiceType serviceType, Shipment shipment) {
        return shipment.getTotalVolumeInLitre() <= lalamoveService.getServiceVolumeInLitre(serviceType)
                && shipment.getTotalWeightInKG() <= serviceType.getMaxWeight();
    }

    private void sendPushNotificationForUpdateItem(Shipment shipment) {
        Optional<Job> job = shipment.getShoppingOrRangerJob();
        if (job.isPresent() && job.get().getState() != Job.State.INITIAL) {
            Batch batch = job.get().getBatch();
            User user = batch.getUser();
            if (user != null) {
                List<Long> userIds = Lists.newArrayList(user.getId());
                notificationService.sendPushNotificationForUpdateItem(userIds, shipment.getOrderNumber(), batch.getId(), shipment.getTenant().getId());
            }
        }
    }

    public double calculateShoppingDurationInMinutes(Shipment shipment, StockLocation stockLocation, boolean isDeliveryChecker) {
        int totalItem = shipment.getTotalItemCount(isDeliveryChecker);

        return calculateShoppingDurationInMinutes(stockLocation, totalItem);
    }

    public double calculateShoppingDurationInMinutes(StockLocation stockLocation, int totalItem) {
        return totalItem * stockLocation.getShopperAveragePickingTimePerUniqItem() +
                stockLocation.getShopperQueueReplacementTime() +
                stockLocation.getShopperHandoverToDriverTime();
    }

    @Transactional(readOnly = true)
    public SlotOptimizationContext getContextForSlotOptimization(String shipmentNumber) {
        SlotOptimizationContext slotOptimizationContext = new SlotOptimizationContext();
        Shipment shipment = shipmentRepository.findByNumber(shipmentNumber);
        Slot slot = shipment.getSlot();
        StockLocation stockLocation = slot.getStockLocation();
        slotOptimizationContext.setSlotId(slot.getId());
        slotOptimizationContext.setSlotStartTime(slot.getStartTime());
        slotOptimizationContext.setStoreZoneId(stockLocation.getState().getTimeZone());
        slotOptimizationContext.setClusterId(stockLocation.getCluster().getId());
        slotOptimizationContext.setPublishShopperAutoAssignment(true);
        slotOptimizationContext.setPublishDriverAutoAssignment(true);
        slotOptimizationContext.setEnableSlotOptimizationEventDeduplication(stockLocation.getTenant().isEnableSlotOptimizationEventDeduplication());

        Optional<Job> shoppingJobOptional = shipment.getShoppingOrRangerJob();
        if (shoppingJobOptional.isPresent() && !stockLocation.isSpecial()) {
            if (stockLocation.isFulfilledByStrato()) {
                slotOptimizationContext.setShiftId(0L);
            } else {
                slotOptimizationContext.setShiftId(shoppingJobOptional.get().getBatch().getShift().getId());
            }
        } else {
            slotOptimizationContext.setShiftId(0L);
        }

        Optional<Job> deliveryRangerJob = shipment.getDeliveryOrRangerJob();
        if (deliveryRangerJob.isPresent()) {
            Job job = deliveryRangerJob.get();
            if ((job.isDelivery() || job.isRanger()) && job.getBatch().getShift() != null) {
                slotOptimizationContext.setDeliveryShiftId(job.getBatch().getShift().getId());
            } else {
                slotOptimizationContext.setDeliveryShiftId(0L);
            }
        } else {
            slotOptimizationContext.setDeliveryShiftId(0L);
        }

        return slotOptimizationContext;
    }

    public Shipment convertShipmentFormToShipment(ShipmentForm shipmentForm, StockLocation stockLocation) {
        Shipment shipment = shipmentRepository.findByOrderNumberFetchItems(shipmentForm.getOrderNumber());

        if (shipment == null)
            shipment = shipmentMapper.shipmentFormToShipment(shipmentForm);
        else
            shipment = shipmentMapper.shipmentFormToShipment(shipmentForm, shipment);

        Double shipDistance = distanceService.getDistance(shipment, stockLocation, true, false);
        shipment.setShipDistance(shipDistance);

        boolean highOrderVolume = shipment.getTotalVolumeInLitre() > HIGH_ORDER_VOLUME_THRESHOLD_IN_LITRE;
        shipment.setIsHighOrderVolume(highOrderVolume);

        if (shipmentForm.getOrderPackagings() != null) {
            List<ShipmentPackaging> shipmentPackagings = shipment.getShipmentPackagings();
            shipmentPackagings.clear();

            for (ShipmentPackagingForm shipmentPackagingForm : shipmentForm.getOrderPackagings()) {
                ShipmentPackaging shipmentPackaging = shipmentPackagingMapper.shipmentPackagingFormToShipmentPackaging(shipmentPackagingForm);
                Packaging packaging = packagingRepository.findById(shipmentPackagingForm.getFulfillmentPackagingId()).orElse(null);
                if (packaging != null) {
                    shipmentPackaging.setPackagingInternalName(packaging.getInternalName());
                    shipmentPackaging.setPackagingClientNames(packaging.getPackagingType().getClientNames());
                    shipmentPackaging.setPackagingSndNames(packaging.getPackagingType().getSndNames());
                    shipmentPackaging.setPackagingImageUrl(packaging.getImageUrl());
                    shipmentPackaging.setPackagingMaxQuantity(packaging.getMaxQuantity());
                }
                shipmentPackaging.setShipment(shipment);
                shipmentPackagings.add(shipmentPackaging);
            }
            shipment.setShipmentPackagings(shipmentPackagings);
        }

        return shipment;
    }

    public Shipment setSlotToShipment(Shipment shipment, Slot slot) {
        if (slot != null)
            shipment.setSlot(slot);
        shipment = shipmentRepository.save(shipment);
        shipment.setNewObject(true);

        return shipment;
    }

    public Shipment saveShipment(Shipment shipment, Slot slot, StockLocation stockLocation) {
        if (shipment.getId() != null)
            itemRepository.deleteByShipmentId(shipment.getId());

        List<Item> items = Lists.newArrayList(shipment.getItems());
        setSlotToShipment(shipment, slot);

        //TODO: need revisit, using locale or currency or iso?
        String isoName = stockLocation.getState().getCountry().getIsoName();
        for (Item item : items) {
            itemService.save(shipment, stockLocation, item, isoName, false);
        }

        shipment.setItems(items);
        return shipment;
    }

    @Transactional
    public void clearShipment(ShipmentForm shipmentForm, StockLocation stockLocation, boolean isSetSlot, boolean isOnDemandRequest) {
        Shipment shipment = shipmentRepository.findByOrderNumber(shipmentForm.getOrderNumber());
        clearShipment(shipment, stockLocation, isSetSlot, isOnDemandRequest);
    }

    @Transactional
    public void clearShipment(Shipment shipment, StockLocation stockLocation, boolean isSetSlot, boolean isOnDemandRequest) {
        if (shipment == null)
            return;

        Slot oldSlot = shipment.getSlot();
        if (!isOnDemandRequest && oldSlot.getType() == Slot.Type.ON_DEMAND && isSetSlot) {
            if (shipment.getState().equals(Shipment.State.READY))
                throw new SlotUnavailableException("Cannot switch slot from on demand slot to normal slot after shipment ready.");
            else
                transactionHelper.withNewTransaction(() -> destroy(shipment));
        } else if (isOnDemandRequest && oldSlot.getType() != Slot.Type.ON_DEMAND && isSetSlot) {
            if (shipment.getState().equals(Shipment.State.READY))
                throw new SlotUnavailableException("Cannot switch slot from normal slot to on demand slot after shipment ready.");
            else
                transactionHelper.withNewTransaction(() -> destroy(shipment));
        } else if (!shipment.getSlot().getStockLocation().getId().equals(stockLocation.getId())) {
            transactionHelper.withNewTransaction(() -> destroy(shipment));
        }

        if (!isOnDemandRequest) {
            if (stockLocation.getCluster().isEnableLocus() && isSetSlot)
                locusTaskService.cancelTask(shipment);

            Optional<Job> optionalShoppingJob = shipment.getShoppingOrRangerJob();
            if (optionalShoppingJob.isPresent() && optionalShoppingJob.get().getState().equals(Job.State.STARTED) && isSetSlot)
                throw new JobAlreadyStartedException();
        }

        if (oldSlot != null && oldSlot.getType() != Slot.Type.ON_DEMAND &&
                stockLocation.isFulfilledByStrato() && isSetSlot) {
            stratoApiService.releaseCapacityWithRetry(shipment.getOrderNumber(), stockLocation.getExternalId());
        }

        if (isSetSlot && stockLocation.isEnableDelyva()) {
            DelyvaDelivery delyvaDelivery = shipment.getCurrentDelyvaDelivery();
            if (delyvaDelivery != null)
                delyvaDeliveryRepository.delete(delyvaDelivery.getId());
        }
    }

    // TODO handle multiple tenant
    public boolean maskShipmentForDeletedUser(Long userId) {
        List<Shipment> shipments = shipmentRepository.findAllByOrderExternalCustomerId(userId);
        if (shipments.stream().count() == 0)
            return true;

        String addressName = "deleted";
        String addressPhone = "deleted";
        String addressLine = "deleted";
        String addressNumber = "deleted";
        String addressDetail = "deleted";
        double addressLat = 16.7713828;
        double addressLon = -3.025489;
        String orderCustomerEmail = "deleted";

        shipments.forEach(shipment -> {
            shipment.setAddressName(addressName);
            shipment.setAddressPhone(addressPhone);
            shipment.setAddressLine(addressLine);
            shipment.setAddressNumber(addressNumber);
            shipment.setAddressDetail(addressDetail);
            shipment.setAddressLat(addressLat);
            shipment.setAddressLon(addressLon);
            shipment.setOrderCustomerEmail(orderCustomerEmail);
        });

        shipmentRepository.saveAll(shipments);

        return true;
    }

    @Transactional(readOnly = true)
    public boolean isEligibleForDelyva(Shipment shipment, StockLocation stockLocation, DelyvaGetQuotationPresenter.Service delyvaService) {
        return stockLocation.isEnableDelyva()
                && shipment.getItems().stream().noneMatch(Item::isConsideredAsGeRestrictedProduct)
                && delyvaService != null
                && !isOrderClientTypeLineMan(shipment);
    }

    @Transactional(readOnly = true)
    public boolean isEligibleToCheckDelyvaQuotation(Shipment shipment, StockLocation stockLocation) {
        return stockLocation.isEnableDelyva()
                && shipment.getItems().stream().noneMatch(Item::isConsideredAsGeRestrictedProduct)
                && !isOrderClientTypeLineMan(shipment);
    }

    @Transactional(readOnly = true)
    public boolean isEligibleToChangeSlotToDelyva(String number, Country country) {
        Shipment shipment = shipmentRepository.findByNumber(number);

        if (shipment == null)
            return true;

        return !(shipment.isReady() && ((shipment.isCOD() || shipment.isQR()) || (shipment.isEWallet() && !country.getAllowEWalletForTPL())));
    }

    @Transactional
    public void cancelByShopper(Shipment shipment, CancellationForm cancellationForm, String cancelReason) throws Exception {
        String url = "";
        String reason = cancelReason;
        if (cancellationForm != null) {
            url = fileUploaderService.uploadAttachment(cancellationForm.getEvidence());
            reason = cancellationForm.getReason();
        }

        CancelInfo cancelInfo = new CancelInfo();
        cancelInfo.setEvidenceUrl(url);
        cancelInfo.setShipment(shipment);
        cancelInfo.setReason(reason);
        cancelInfoRepository.save(cancelInfo);

        if (shipment.getChat() != null) {
            chatService.closeChat(shipment.getNumber());
        }
        cancel(shipment.getNumber());
    }

    @Transactional
    public Shipment resetToStarting(String number) {
        Shipment shipment = shipmentRepository.findByNumber(number);

        if (shipment == null)
            throw new ResourceNotFoundException();

        if (shipment.isPaymentClear()) {
            throw new UnprocessableEntityException("Payment has been made");
        }

        Optional<Job> optionalJob = shipment.getShoppingOrRangerJob();
        if (optionalJob.isEmpty()) {
            throw new ResourceNotFoundException();
        }

        Job shoppingOrRangerJob = optionalJob.get();
        List<Job.State> doNothingState = List.of(Job.State.INITIAL, Job.State.STARTED);
        if (!doNothingState.contains(shoppingOrRangerJob.getState())) {
            Map<String, String> jobStates = shoppingOrRangerJob.getJobStates();
            String started = Job.State.STARTED.toString();
            String startedTime = jobStates.get(started);
            jobStates.entrySet().removeIf(e -> !e.equals(started));
            LocalDateTime localDateTime = Instant.ofEpochMilli(Long.parseLong(startedTime)).atOffset(ZoneOffset.UTC).toLocalDateTime();

            shoppingOrRangerJob.setState(Job.State.STARTED, localDateTime);
            shoppingOrRangerJob.setJobStates(jobStates);
            jobRepository.save(shoppingOrRangerJob);
        }

        return shipment;
    }

    @Transactional
    public Shipment changeStockLocation(String number, Long stockLocationId, boolean removeDistanceRestriction) {
        Shipment shipment = shipmentRepository.findByNumber(number);

        if (shipment == null) {
            logger.info("[OrderMutation] " + shipment.getOrderNumber() + " No shipment with number " + number + " found");
            throw new ResourceNotFoundException();
        }

        Optional<StockLocation> optionalNewStockLocation = stockLocationRepository.findById(stockLocationId);
        if (optionalNewStockLocation.isEmpty()) {
            logger.info("[OrderMutation] " + shipment.getOrderNumber() + " No stock location with id " + stockLocationId + " found");
            throw new ResourceNotFoundException();
        }

        StockLocation newStockLocation = optionalNewStockLocation.get();

        if (!newStockLocation.getSupplier().equals(shipment.getSlot().getStockLocation().getSupplier())) {
            logger.info("[OrderMutation] " + shipment.getOrderNumber() + " Different supplier is not allowed");
            throw new UnprocessableEntityException("Different supplier is not allowed");
        }

        GeoPoint newLocation = new GeoPoint(newStockLocation.getLat(), newStockLocation.getLon());
        GeoPoint destLocation = new GeoPoint(shipment.getAddressLat(), shipment.getAddressLon());
        if (!removeDistanceRestriction && DistanceUtil.airDistanceInKM(newLocation, destLocation) > MAX_ORDER_MUTATION_AIR_DISTANCE_IN_KM) {
            logger.info("[OrderMutation] " + shipment.getOrderNumber() + " Target location is too far from destination");
            throw new UnprocessableEntityException("Target location is too far from destination");
        }

        List<Slot> slots = slotRepository.findSlotByTargetStockLocation(number, stockLocationId);
        if (slots.isEmpty()) {
            logger.info("[OrderMutation] " + shipment.getOrderNumber() + " No similar slot exists");
            throw new UnprocessableEntityException("No similar slot exists");
        }

        Slot slot = slots.get(0);

        shipment.setSlot(slot);
        shipmentRepository.save(shipment);

        Optional<Job> optionalShoppingJob = shipment.getShoppingJob();
        if (optionalShoppingJob.isPresent()) {
            Job shoppingJob = optionalShoppingJob.get();
            Batch shoppingBatch = shoppingJob.getBatch();
            if (shoppingBatch.getUser() != null) {
                logger.info("[OrderMutation] " + shipment.getOrderNumber() + " A shopper has already picked the job");
                throw new UnprocessableEntityException("A shopper has already picked the job");
            }

            if (shoppingBatch.getJobs().size() > 1) {
                shoppingBatch = cloneBatch(shoppingBatch);
            }

            List<Shift> shifts =  shiftRepository.findEquivalentShoppingShift(slot.getStartTime(), newStockLocation.getId());

            if (shifts.isEmpty()) {
                logger.info("[OrderMutation] " + shipment.getOrderNumber() + " Target shopping shift doesn't exist");
                throw new UnprocessableEntityException("Target shopping shift doesn't exist");
            }

            Shift shoppingShift = shifts.get(0);
            shoppingBatch.setShift(shoppingShift);
            shoppingBatch.setStockLocation(newStockLocation);
            shoppingBatch = batchRepository.save(shoppingBatch);

            // Ensure Job set to correct batch
            shoppingJob.setBatch(shoppingBatch);
            jobRepository.save(shoppingJob);

            List<JobSlot> jobSlots = shoppingJob.getJobSlots();
            for(JobSlot jobSlot: jobSlots) {
                jobSlot.setSlot(slot);
                jobSlotRepository.save(jobSlot);
            }
        }

        Optional<Job> optionalDeliveryJob = shipment.getDeliveryOrRangerJob();
        if (optionalDeliveryJob.isPresent()) {
            Job deliveryJob = optionalDeliveryJob.get();
            Batch deliveryBatch = deliveryJob.getBatch();
            if (deliveryBatch.getUser() != null) {
                logger.info("[OrderMutation] " + shipment.getOrderNumber() + " A driver or ranger has already picked the job");
                throw new UnprocessableEntityException("A driver or ranger has already picked the job");
            }

            if (deliveryBatch.getJobs().size() > 1) {
                deliveryBatch = cloneBatch(deliveryBatch);
            }

            List<Shift> shifts =  shiftRepository.findEquivalentDeliveryShift(slot.getStartTime(), slot.getEndTime(), newStockLocation.getCluster().getId());
            if (shifts.isEmpty()) {
                logger.info("[OrderMutation] " + shipment.getOrderNumber() + " Target delivery shift doesn't exist");
                throw new UnprocessableEntityException("Target delivery shift doesn't exist");
            }

            Shift deliveryShift = shifts.get(0);
            deliveryBatch.setShift(deliveryShift);
            deliveryBatch.setStockLocation(newStockLocation);
            deliveryBatch = batchRepository.save(deliveryBatch);

            // Ensure Job set to correct batch
            deliveryJob.setBatch(deliveryBatch);
            jobRepository.save(deliveryJob);

            List<JobSlot> jobSlots = deliveryJob.getJobSlots();
            for(JobSlot jobSlot: jobSlots) {
                jobSlot.setSlot(slot);
                jobSlotRepository.save(jobSlot);
            }
        }

        return shipment;
    }

    private Batch cloneBatch(Batch batch) {
        Batch cloneBatch = new Batch();
        cloneBatch.setType(batch.getType());
        cloneBatch.setDeliveryType(batch.getDeliveryType());
        cloneBatch.setTplType(batch.getTplType());
        cloneBatch.setVehicle(batch.getVehicle());
        cloneBatch.setShift(batch.getShift());
        cloneBatch.setStockLocation(batch.getStockLocation());
        cloneBatch.setStartTime(batch.getStartTime());
        cloneBatch.setEndTime(batch.getEndTime());
        cloneBatch.setHandoverTime(batch.getHandoverTime());
        cloneBatch.setType(batch.getType());
        cloneBatch.setTenant(batch.getTenant());
        cloneBatch.setCreatedBy(batch.getCreatedBy());

        return cloneBatch;
    }
}
