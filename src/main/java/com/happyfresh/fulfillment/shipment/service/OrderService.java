
package com.happyfresh.fulfillment.shipment.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.exception.type.OrderTotalCalculationException;
import com.happyfresh.fulfillment.common.util.UserInfoFetcher;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.Tenant;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.shipment.mapper.ShipmentMapper;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentItemPresenter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.support.BasicAuthorizationInterceptor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Service
public class OrderService {

    @Autowired
    private UserInfoFetcher userInfoFetcher;

    @Autowired
    private ShipmentMapper shipmentMapper;

    @Autowired
    private ShipmentRepository shipmentRepository;

    private Tenant tenant;

    @Transactional(readOnly = true)
    public Double getOrderTotal(String shipmentNumber) {
        Shipment shipment = shipmentRepository.findByNumber(shipmentNumber);
        Tenant requestTenant = this.tenant;
        if (userInfoFetcher.getTenant() != null)
            requestTenant = userInfoFetcher.getTenant();

        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getInterceptors().add(new BasicAuthorizationInterceptor(requestTenant.getOmsAuthUsername(), requestTenant.getOmsAuthPassword()));

        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);

        ShipmentItemPresenter shipmentForm = shipmentMapper.shipmentToShipmentItemPresenter(shipment);
        Map<String, ShipmentItemPresenter> requestBody = new HashMap<>();
        requestBody.put("shipment", shipmentForm);

        HttpEntity<Map> request = new HttpEntity<>(requestBody, requestHeaders);

        Double orderTotal = 0.0;
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(requestTenant.getOmsBaseUrl() + "/carbon/calculate_order_total", request, String.class);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode actualObj = mapper.readTree(response.getBody());
            JsonNode orderTotalObj = actualObj.get("order_total");
            orderTotal = orderTotalObj.asDouble();
        } catch (RestClientException | IOException exception) {
            throw new OrderTotalCalculationException(exception.getMessage());
        }

        return orderTotal;
    }

    @Transactional(readOnly = true)
    public Double getOrderTotal(String shipmentNumber, Tenant tenant) {
        this.tenant = tenant;
        return getOrderTotal(shipmentNumber);
    }

}
