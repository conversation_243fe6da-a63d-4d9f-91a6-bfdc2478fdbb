package com.happyfresh.fulfillment.shipment.presenter;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.entity.Receipt;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ReceiptPresenter {

    private Long id;

    private String number;

    private Double total;

    private Double tax;

    private Boolean completed;

    private Receipt.PaymentMethod paymentMethod;

    private List<ReceiptImagePresenter> receiptImages;
}
