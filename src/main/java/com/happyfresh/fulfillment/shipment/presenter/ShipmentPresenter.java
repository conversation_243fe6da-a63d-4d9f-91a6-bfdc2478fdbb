package com.happyfresh.fulfillment.shipment.presenter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.gosend.presenter.GosendDeliveryPresenter;
import com.happyfresh.fulfillment.grabExpress.presenter.GrabExpressDeliveryPresenter;
import com.happyfresh.fulfillment.lalamove.presenter.LalamoveDeliveryPresenter;
import com.happyfresh.fulfillment.packaging.presenter.ShipmentPackagingPresenter;
import com.happyfresh.fulfillment.slot.presenter.SlotPresenter;
import com.happyfresh.fulfillment.tpl.delyva.presenter.DelyvaDeliveryPresenter;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ShipmentPresenter {

    protected Long id;

    protected Integer totalItems;

    protected String addressName;

    protected String addressLine;

    protected String addressNumber;

    protected String addressDetail;

    protected String addressPhone;

    protected String addressCity;

    protected Double addressLat;

    protected Double addressLon;

    protected String addressInstruction;

    protected Double shipDistance;

    protected String orderNumber;

    protected String orderCustomerEmail;

    protected String orderCustomerFirstName;

    protected String orderCustomerLastName;

    protected String orderCustomerName;

    protected String orderCustomerLanguage;

    protected String orderCustomerPhone;

    protected String orderCustomerMemberTier;

    protected String orderCurrency;

    protected String orderPaymentMethod;

    protected boolean orderPaymentCompleted;

    protected Long orderCompanyId;

    protected String orderCompanyName;

    protected boolean orderEligibleForShoppingBag;

    protected boolean customerAllowGeDelivery;

    protected Long orderExternalCustomerId;

    protected boolean orderNewCustomer;

    protected String orderClientType;

    protected String fleetType;

    protected String deliveryType;

    protected BigDecimal orderTotal;

    protected String displayOrderTotal;

    protected BigDecimal cost;

    protected SlotPresenter slot;

    protected JobPresenter shoppingJob;

    protected JobPresenter deliveryJob;

    protected JobPresenter rangerJob;

    protected JobPresenter onDemandRangerJob;

    protected JobPresenter onDemandShoppingJob;

    protected JobPresenter onDemandDeliveryJob;

    protected List<ReceiptPresenter> receipts;

    protected DeliveryInfoPresenter deliveryInfo;

    protected String number;

    protected String state;

    protected boolean isPaymentClear;

    protected ChatPresenter chat;

    protected PaymentPresenter payment;

    protected List<ShipmentPackagingPresenter> packagings;

    protected GrabExpressDeliveryPresenter grabExpressDelivery;

    protected LalamoveDeliveryPresenter lalamoveDelivery;

    protected DelyvaDeliveryPresenter delyvaDelivery;

    protected GosendDeliveryPresenter gosendDelivery;

    protected boolean isAgeConsent;

    protected String trackingUrl;

    protected List<DeliveryPhotoPresenter> deliveryPhotos;

    protected boolean hasBeenPending;

    protected boolean hasBeenContinue;

    protected boolean isAddressIncorrect;

    protected LocalDateTime orderCompletedAt;

    @JsonProperty("is_address_incorrect")
    public boolean isAddressIncorrect() {
        return isAddressIncorrect;
    }

    protected List<String> incorrectAddressReasons;

    public boolean isExpressDelivery;

    @JsonProperty("is_express_delivery")
    public boolean isExpressDelivery() { return isExpressDelivery; };
}
