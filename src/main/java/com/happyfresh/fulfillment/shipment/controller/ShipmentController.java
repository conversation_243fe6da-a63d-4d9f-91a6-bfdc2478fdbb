package com.happyfresh.fulfillment.shipment.controller;

import com.happyfresh.fulfillment.batch.controller.BatchV3Controller;
import com.happyfresh.fulfillment.common.annotation.Publish;
import com.happyfresh.fulfillment.common.annotation.ResponseWrapper;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.exception.type.RedisLockTimeoutException;
import com.happyfresh.fulfillment.common.service.JedisLockService;
import com.happyfresh.fulfillment.common.util.ApplicationUtil;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.Slot;
import com.happyfresh.fulfillment.grabExpress.form.CancelReasonForm;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressService;
import com.happyfresh.fulfillment.packaging.form.ShipmentPackagingForm;
import com.happyfresh.fulfillment.packaging.mapper.ShipmentPackagingMapper;
import com.happyfresh.fulfillment.packaging.presenter.ShipmentPackagingPresenter;
import com.happyfresh.fulfillment.shipment.form.*;
import com.happyfresh.fulfillment.shipment.mapper.ItemMapper;
import com.happyfresh.fulfillment.shipment.mapper.ShipmentMapper;
import com.happyfresh.fulfillment.shipment.presenter.ItemPresenter;
import com.happyfresh.fulfillment.shipment.presenter.PaymentEligibilityPresenter;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentItemPresenter;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentPresenter;
import com.happyfresh.fulfillment.shipment.service.ChatService;
import com.happyfresh.fulfillment.shipment.service.ShipmentService;
import com.happyfresh.fulfillment.slot.bean.SlotOptimizationContext;
import com.happyfresh.fulfillment.slot.service.PublishSlotOptimizationService;
import com.happyfresh.fulfillment.slot.service.SlotAvailabilityService;
import com.happyfresh.fulfillment.slot.service.SlotOptimizationService;
import com.happyfresh.fulfillment.slot.service.SlotUtilizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.happyfresh.fulfillment.batch.controller.BatchController.BATCH_START_LOCK_KEY;

@RestController
@RequestMapping("/api/shipments")
public class ShipmentController {

    @Autowired
    private ShipmentService shipmentService;

    @Autowired
    private GrabExpressService grabExpressService;

    @Autowired
    private SlotAvailabilityService slotAvailabilityService;

    @Autowired
    private ShipmentMapper shipmentMapper;

    @Autowired
    private ItemMapper itemMapper;

    @Autowired
    private ShipmentPackagingMapper shipmentPackagingMapper;

    @Autowired
    private ChatService chatService;

    @Autowired
    private SlotOptimizationService slotOptimizationService;

    @Autowired
    private PublishSlotOptimizationService publishSlotOptimizationService;

    @Autowired
    private SlotUtilizationService slotUtilizationService;

    @Autowired
    private ApplicationContext applicationContext;

    @ResponseWrapper(rootName = "items")
    @PreAuthorize("isExternalSystemAuthenticated()")
    @PutMapping(value = "/{number}/update_replacement_type")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public List<ItemPresenter> updateReplacementType(@PathVariable String number, @Validated @RequestBody List<ItemForm> itemForms) throws InterruptedException {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format(ApplicationUtil.SHIPMENT_ITEMS_UPDATE_KEY, number))) {
                return itemMapper.toItemPresenters(shipmentService.updateReplacementType(number, itemForms));
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @ResponseWrapper(rootName = "shipment")
    @PreAuthorize("isExternalSystemAuthenticated()")
    @PutMapping(value = "/{number}/tracking_url")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Publish(WebhookType.UPDATE_TRACKING_URL)
    public ShipmentPresenter updateTrackingUrl(@PathVariable String number, @Validated @RequestBody TrackingUrlForm trackingUrlForm) throws Exception {
        return shipmentMapper.shipmentToShipmentPresenter(shipmentService.updateTrackingUrl(number, trackingUrlForm));
    }

    @ResponseWrapper(rootName = "item")
    @PreAuthorize("isUserAuthenticated()")
    @PutMapping(value = "/{number}/items")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public ItemPresenter updateItem(@PathVariable String number, @Validated @RequestBody ItemForm itemForm) throws Exception {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format(ApplicationUtil.SHIPMENT_ITEMS_UPDATE_KEY, number))) {
                return itemMapper.toItemPresenter(shipmentService.updateItem(number, itemForm));
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @ResponseWrapper(rootName = "shipment")
    @PreAuthorize("isExternalSystemAuthenticated()")
    @PutMapping(value = "/{number}/all_items")
    public ShipmentPresenter updateAllItems(@PathVariable String number, @Validated @RequestBody ShipmentForm shipmentForm) throws Exception {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format(ApplicationUtil.SHIPMENT_ITEMS_UPDATE_KEY, number))) {
                return shipmentMapper.shipmentToShipmentItemPresenter(shipmentService.updateItems(number, shipmentForm));
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @ResponseWrapper(rootName = "packaging")
    @PreAuthorize("isExternalSystemAuthenticated()")
    @PutMapping(value = "/{number}/packagings")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public ShipmentPackagingPresenter updatePackaging(@PathVariable String number, @Validated @RequestBody ShipmentPackagingForm shipmentPackagingForm) {
        return shipmentPackagingMapper.shipmentPackagingToShipmentPackagingPresenter(shipmentService.updatePackaging(number, shipmentPackagingForm));
    }

    @PreAuthorize("isExternalSystemAuthenticated()")
    @PutMapping(value = "/{number}/cancel")
    @ResponseWrapper(rootName = "shipment")
    public ShipmentPresenter cancel(@PathVariable String number) throws Exception {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        long batchId = shipmentService.getShoppingBatchIdByShipmentNumber(number);
        try {
            if (jedisLockService.lock(String.format(BatchV3Controller.BATCH_FINALIZE_LOCK_KEY, batchId))) {
                return shipmentMapper.shipmentToShipmentItemPresenter(shipmentService.cancel(number));
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isExternalSystemAuthenticated()")
    @DeleteMapping(value = "/{number}/unassigned")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void unassigned(@PathVariable String number) {
        shipmentService.unassignedShipment(number);
    }

    @PreAuthorize("isUserAuthenticated()")
    @GetMapping(value = "/{number}")
    @ResponseWrapper(rootName = "shipment")
    public ShipmentItemPresenter getShipment(@PathVariable String number) {
        return shipmentMapper.shipmentToShipmentItemPresenter(shipmentService.findByNumber(number));
    }

    @PreAuthorize("isExternalSystemAuthenticated()")
    @PutMapping(value = "/{number}/adjust")
    @ResponseWrapper(rootName = "shipment")
    public ShipmentPresenter adjustShipment(@PathVariable String number, @Validated @RequestBody ShipmentForm shipmentForm) throws Exception {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format(ApplicationUtil.SHIPMENT_ITEMS_UPDATE_KEY, number))) {
                Shipment shipment = shipmentService.adjustShipment(number, shipmentForm, Shipment.State.READY);
                publishSlotOptimizationService.publishSlotOptimizationAndAutoAssignments(shipment);
                return shipmentMapper.shipmentToShipmentItemPresenter(shipment);
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isExternalSystemAuthenticated()")
    @PutMapping(value = "/{number}/change_slot")
    @ResponseWrapper(rootName = "shipment")
    public ShipmentPresenter changeSlot(@PathVariable String number, @RequestParam("slot_id") long slotId) throws Exception {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        long batchId = shipmentService.getShoppingBatchIdByShipmentNumber(number);
        try {
            if (jedisLockService.lock(String.format(BATCH_START_LOCK_KEY, batchId))) {
                Shipment shipment = shipmentService.findByNumber(number);
                ShipmentPresenter shipmentPresenter;
                if (shipment.getSlot().getType().equals(Slot.Type.LONGER_DELIVERY)) {
                    SlotOptimizationContext oldContext = shipmentService.getContextForSlotOptimization(number);
                    shipmentPresenter = shipmentMapper.shipmentToShipmentPresenter(slotAvailabilityService.reservedSlot(number, slotId));
                    SlotOptimizationContext newContext = shipmentService.getContextForSlotOptimization(number);

                    slotOptimizationService.publishSlotOptimization(oldContext);
                    slotOptimizationService.publishSlotOptimization(newContext);
                } else {
                    shipmentPresenter = shipmentMapper.shipmentToShipmentPresenter(slotAvailabilityService.reservedSlot(number, slotId));
                }
                return shipmentPresenter;
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PostMapping(value = "/{number}/grab")
    @ResponseWrapper(ignore = true)
    public ResponseEntity bookGrabExpress(@PathVariable String number) {
        return grabExpressService.createGrabExpressBooking(number, false);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/{number}/grab")
    @ResponseWrapper(ignore = true)
    public ResponseEntity getGrabExpressBookingStatus(@PathVariable String number) {
        return grabExpressService.getGrabExpressBookingStatus(number);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/{number}/grab/cancel")
    @ResponseWrapper(ignore = true)
    public ResponseEntity cancelGrabExpressBooking(@PathVariable String number, @Validated @RequestBody CancelReasonForm cancelReasonForm) {
        return grabExpressService.cancelGrabExpressBooking(number, cancelReasonForm.getCancelReason());
    }

    @PreAuthorize("isExternalSystemAuthenticated()")
    @GetMapping(value = "/{number}/payment_eligibility")
    @ResponseWrapper(rootName = "shipment")
    public PaymentEligibilityPresenter getPaymentEligibility(@PathVariable String number) {
        return shipmentService.getPaymentEligibility(number);
    }

    @PreAuthorize("isAgentAuthenticated()")
    @PostMapping(value = "/{number}/chat")
    @ResponseWrapper(rootName = "shipment")
    @Publish(WebhookType.OPEN_CHAT_SHIPMENT)
    public ShipmentPresenter openChat(@PathVariable String number, @Validated @RequestBody ChatForm chatForm) throws Exception {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format("open_chat_%s", number))) {
                return shipmentMapper.shipmentToShipmentPresenter(chatService.openChat(number, chatForm));
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isAgentAuthenticated()")
    @DeleteMapping(value = "/{number}/chat")
    @ResponseWrapper(rootName = "shipment")
    @Publish(WebhookType.CLOSE_CHAT_SHIPMENT)
    public ShipmentPresenter closeChat(@PathVariable String number) throws Exception {
        return shipmentMapper.shipmentToShipmentPresenter(chatService.closeChat(number));
    }

    @PreAuthorize("isAgentAuthenticated()")
    @PutMapping(value = "/{number}/chat/close")
    @ResponseWrapper(rootName = "shipment")
    @Publish(WebhookType.CLOSE_CHAT_SHIPMENT)
    public ShipmentPresenter closeChatWithPayload(@PathVariable String number, @RequestBody ChatForm chatForm) throws Exception {
        return shipmentMapper.shipmentToShipmentPresenter(chatService.closeChatWithForm(number, chatForm));
    }

    @PreAuthorize("isAgentAuthenticated()")
    @PostMapping(value = "/{shipmentNumber}/packagings/replace")
    public ResponseEntity replacePackaging(@PathVariable String shipmentNumber, @Validated @RequestBody PackagingReplacementForm form) throws Exception {
        shipmentService.replacePackaging(shipmentNumber, form);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/{number}/change_stock_location")
    @ResponseWrapper(rootName = "shipment")
    public ShipmentPresenter changeStockLocation(@PathVariable String number, @Validated @RequestBody ShipmentStockLocationForm shipmentStockLocationForm) throws Exception {
        return shipmentMapper.shipmentToShipmentPresenter(shipmentService.changeStockLocation(number, shipmentStockLocationForm.getStockLocationId(), shipmentStockLocationForm.isRemoveDistanceRestriction()));
    }
}