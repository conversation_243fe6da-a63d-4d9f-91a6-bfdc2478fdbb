package com.happyfresh.fulfillment.shipment.mapper;

import com.happyfresh.fulfillment.common.exception.ApiError;
import com.happyfresh.fulfillment.common.util.CurrencyUtil;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.gosend.mapper.GosendDeliveryMapper;
import com.happyfresh.fulfillment.grabExpress.mapper.GrabExpressDeliveryMapper;
import com.happyfresh.fulfillment.lalamove.mapper.LalamoveDeliveryMapper;
import com.happyfresh.fulfillment.shipment.presenter.DeliveryInfoPresenter;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentItemPresenter;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentItemPresenterWithException;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentPresenter;
import com.happyfresh.fulfillment.slot.mappper.SlotMapper;
import com.happyfresh.fulfillment.tpl.delyva.mapper.DelyvaDeliveryMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public abstract class ShipmentMapperDecorator implements ShipmentMapper {

    @Autowired
    @Qualifier("delegate")
    private ShipmentMapper delegate;

    @Autowired
    private JobMapper jobMapper;

    @Autowired
    private SlotMapper slotMapper;

    @Autowired
    private DeliveryInfoMapper deliveryInfoMapper;

    @Autowired
    private ItemMapper itemMapper;

    @Autowired
    private ChatMapper chatMapper;

    @Autowired
    private GrabExpressDeliveryMapper grabExpressDeliveryMapper;

    @Autowired
    private LalamoveDeliveryMapper lalamoveDeliveryMapper;

    @Autowired
    private DelyvaDeliveryMapper delyvaDeliveryMapper;

    @Autowired
    private GosendDeliveryMapper gosendDeliveryMapper;

    @Override
    public ShipmentPresenter shipmentToShipmentPresenter(Shipment shipment) {
        ShipmentPresenter presenter = delegate.shipmentToShipmentPresenter(shipment);
        if (presenter != null)
            setShipmentDetail(presenter, shipment);

        return presenter;
    }

    @Override
    public ShipmentPresenter shipmentToBatchAvailableShipmentPresenter(Shipment shipment) {
        ShipmentPresenter presenter = delegate.shipmentToBatchAvailableShipmentPresenter(shipment);
        if (presenter != null) {
            setShipmentDetail(presenter, shipment);
            if (shipment.isExpressDelivery() && presenter.getSlot() != null) {
                presenter.getSlot().setStartTime(shipment.getOrderCompletedAt());
                presenter.getSlot().setEndTime(shipment.getOrderCompletedAt().plusMinutes(shipment.getSlot().getStockLocation().getOnDemandDeliveryTime()));
            }
        }

        return presenter;
    }

    @Override
    public ShipmentItemPresenter shipmentToShipmentItemPresenter(Shipment shipment) {
        ShipmentItemPresenter presenter = delegate.shipmentToShipmentItemPresenter(shipment);
        setShipmentDetail(presenter, shipment);
        presenter.setItems(itemMapper.toItemPresenters(shipment.getItems()));

        return presenter;
    }

    @Override
    public List<ShipmentItemPresenter> shipmentToShipmentItemPresenters(List<Shipment> shipments) {
        shipments = shipments.stream().filter(s -> s.getState() != Shipment.State.CANCELLED).collect(Collectors.toList());
        List<ShipmentItemPresenter> presenters = delegate.shipmentToShipmentItemPresenters(shipments);

        if (!CollectionUtils.isEmpty(presenters)) {
            for (ShipmentItemPresenter presenter : presenters) {
                Optional<Shipment> shipment = shipments.stream()
                        .filter(s -> s.getId().longValue() == presenter.getId().longValue())
                        .findAny();
                if (shipment.isPresent()) {
                    setShipmentDetail(presenter, shipment.get());
                    presenter.setItems(itemMapper.toItemPresenters(shipment.get().getItems()));
                }
            }
        }

        return presenters;
    }

    @Override
    public ShipmentItemPresenterWithException shipmentToShipmentItemPresenterWithException(Shipment shipment, List<ApiError> exceptions) {
        ShipmentItemPresenter shipmentItemPresenter = delegate.shipmentToShipmentItemPresenter(shipment);
        setShipmentDetail(shipmentItemPresenter, shipment);
        shipmentItemPresenter.setItems(itemMapper.toItemPresenters(shipment.getItems()));

        ShipmentItemPresenterWithException presenter = new ShipmentItemPresenterWithException();
        presenter.setShipmentItemPresenter(shipmentItemPresenter);
        presenter.setExceptions(exceptions);

        return presenter;
    }

    private void setShipmentDetail(ShipmentPresenter presenter, Shipment shipment) {
        List<Job> jobs = shipment.getJobs();
        Job shoppingJob = null;
        Job deliveryJob = null;
        Job rangerJob = null;
        Job onDemandRangerJob = null;
        Job onDemandShoppingJob = null;
        Job onDemandDeliveryJob = null;
        for (Job job : jobs) {
            if (shoppingJob == null && job.isShopping()) {
                shoppingJob = job;
            }
            if (deliveryJob == null && (job.isDelivery())) {
                deliveryJob = job;
            }
            if (rangerJob == null && job.isRanger()) {
                rangerJob = job;
            }
            if (onDemandRangerJob == null && job.isOnDemandRanger()) {
                onDemandRangerJob = job;
            }
            if (onDemandShoppingJob == null && job.isOnDemandShopping()) {
                onDemandShoppingJob = job;
            }
            if (onDemandDeliveryJob == null && job.isOnDemandDelivery()) {
                onDemandDeliveryJob = job;
            }

        }

        presenter.setShoppingJob(jobMapper.jobToJobPresenter(shoppingJob));
        presenter.setDeliveryJob(jobMapper.jobToJobPresenter(deliveryJob));
        presenter.setRangerJob(jobMapper.jobToJobPresenter(rangerJob));
        presenter.setOnDemandRangerJob(jobMapper.jobToJobPresenter(onDemandRangerJob));
        presenter.setOnDemandShoppingJob(jobMapper.jobToJobPresenter(onDemandShoppingJob));
        presenter.setOnDemandDeliveryJob(jobMapper.jobToJobPresenter(onDemandDeliveryJob));
        presenter.setSlot(slotMapper.toSlotPresenter(shipment.getSlot()));

        String currency = StringUtils.EMPTY;
        int totalItems = 0;
        for (Item item : shipment.getItems().stream().filter(item -> item.getReplacedItem() == null).collect(Collectors.toList())) {
            if (StringUtils.isEmpty(currency))
                currency = item.getCurrency();
            totalItems += item.getRequestedQty();
        }

        presenter.setTotalItems(totalItems);
        presenter.setDisplayOrderTotal(CurrencyUtil.format(shipment.getOrderTotal(), currency));

        Job job = deliveryJob == null ? rangerJob : deliveryJob;
        if (job != null) {
            Batch deliveryBatch = job.getBatch();
            String fleetType = setFleetType(deliveryBatch);
            presenter.setFleetType(fleetType);

            String deliveryType = setDeliveryType(deliveryBatch);
            presenter.setDeliveryType(deliveryType);
            presenter.setHasBeenPending(job.hasBeenPending());
            presenter.setHasBeenContinue(job.hasBeenContinue());
        }

        List<DeliveryInfo> deliveryInfos = shipment.getDeliveryInfos();
        if (!deliveryInfos.isEmpty()) {
            DeliveryInfoPresenter deliveryInfoPresenter = deliveryInfoMapper.deliveryInfoToDeliveryInfoPresenter(deliveryInfos.get(0));
            presenter.setDeliveryInfo(deliveryInfoPresenter);
        }

        GrabExpressDelivery grabExpressDelivery = shipment.getCurrentGrabExpressDelivery();
        if (grabExpressDelivery != null) {
            presenter.setGrabExpressDelivery(grabExpressDeliveryMapper.toGrabExpressDeliveryPresenter(grabExpressDelivery));
            presenter.setTrackingUrl(grabExpressDelivery.getTrackingUrl());
        }

        LalamoveDelivery lalamoveDelivery = shipment.getCurrentLalamoveDelivery();
        if (lalamoveDelivery != null) {
            presenter.setLalamoveDelivery(lalamoveDeliveryMapper.toLalamoveDeliveryPresenter(lalamoveDelivery));
            presenter.setTrackingUrl(lalamoveDelivery.getShareLink());
        }

        DelyvaDelivery delyvaDelivery = shipment.getCurrentDelyvaDelivery();
        if (delyvaDelivery != null) {
            presenter.setDelyvaDelivery(delyvaDeliveryMapper.toDelyvaDeliveryPresenter(delyvaDelivery));
            presenter.setTrackingUrl(delyvaDelivery.getTrackingUrl());
        }

        GosendDelivery gosendDelivery = shipment.getCurrentGosendDelivery();
        if (gosendDelivery != null) {
            presenter.setGosendDelivery(gosendDeliveryMapper.toGosendDeliveryPresenter(gosendDelivery));
            presenter.setTrackingUrl(gosendDelivery.getLiveTrackingUrl());
        }
    }

    private String setFleetType(Batch batch) {
        if (batch.getDeliveryType() != Batch.DeliveryType.TPL) {
            return "hf";
        } else {
            if (batch.isGrabExpress()) {
                return "ge";
            } else if (batch.isLalamove()) {
                return "lalamove";
            } else if (batch.isDelyva()) {
                return "delyva";
            } else if (batch.isGosendCar() || batch.isGosendBike()) {
                return "gosend";
            } else {
                return "tpl";
            }
        }
    }

    private String setDeliveryType(Batch batch) {
        return batch.getDeliveryType().name().toLowerCase();
    }

}
