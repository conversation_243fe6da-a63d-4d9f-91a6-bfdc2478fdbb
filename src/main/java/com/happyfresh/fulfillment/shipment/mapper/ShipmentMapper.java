package com.happyfresh.fulfillment.shipment.mapper;

import com.happyfresh.fulfillment.common.exception.ApiError;
import com.happyfresh.fulfillment.entity.Batch;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.packaging.mapper.ShipmentPackagingMapper;
import com.happyfresh.fulfillment.shipment.form.ShipmentForm;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentItemPresenterWithException;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentItemPresenter;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentPresenter;
import com.happyfresh.fulfillment.stockLocation.mapper.CountryMapper;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Mapper(componentModel = "spring", uses = {ItemMapper.class, ChatMapper.class, PaymentMapper.class, CountryMapper.class, ShipmentPackagingMapper.class})
@DecoratedWith(ShipmentMapperDecorator.class)
public interface ShipmentMapper {

    @Mappings({
            @Mapping(target = "shoppingJob", ignore = true),
            @Mapping(target = "deliveryJob", ignore = true),
            @Mapping(target = "rangerJob", ignore = true),
            @Mapping(target = "packagings", source = "shipmentPackagings"),
            @Mapping(target = "onDemandRangerJob", ignore = true),
            @Mapping(target = "orderCustomerName", source = ".", qualifiedByName = "toOrderCustomerName"),
            @Mapping(target = "addressInstruction", source = ".", qualifiedByName = "toAddressInstruction"),
            @Mapping(target = "incorrectAddressReasons", source = ".", qualifiedByName = "toIncorrectAddressReasons"),
            @Mapping(target = "isExpressDelivery", source = ".", qualifiedByName = "toIsExpressDelivery")
    })
    @Named(value = "shipment")
    ShipmentPresenter shipmentToShipmentPresenter(Shipment shipment);

    @Mappings({
            @Mapping(target = "shoppingJob", ignore = true),
            @Mapping(target = "deliveryJob", ignore = true),
            @Mapping(target = "rangerJob", ignore = true),
            @Mapping(target = "packagings", source = "shipmentPackagings"),
            @Mapping(target = "onDemandRangerJob", ignore = true),
            @Mapping(target = "orderCustomerName", source = ".", qualifiedByName = "toOrderCustomerName"),
            @Mapping(target = "addressInstruction", source = ".", qualifiedByName = "toAddressInstruction"),
            @Mapping(target = "incorrectAddressReasons", source = ".", qualifiedByName = "toIncorrectAddressReasons"),
            @Mapping(target = "isExpressDelivery", source = ".", qualifiedByName = "toIsExpressDelivery")
    })
    ShipmentPresenter shipmentToBatchAvailableShipmentPresenter(Shipment shipment);

    @IterableMapping(qualifiedByName = "shipment")
    List<ShipmentPresenter> shipmentsToShipmentPresenters(List<Shipment> shipments);

    Shipment shipmentFormToShipment(ShipmentForm shipmentForm);

    @Mapping(target = "state", source = "state")
    Shipment shipmentFormToShipment(ShipmentForm shipmentForm, @MappingTarget Shipment shipment);

    @Mappings({
            @Mapping(target = "shoppingJob", ignore = true),
            @Mapping(target = "deliveryJob", ignore = true),
            @Mapping(target = "rangerJob", ignore = true),
            @Mapping(target = "packagings", source = "shipmentPackagings"),
            @Mapping(target = "onDemandRangerJob", ignore = true),
            @Mapping(target = "orderCustomerName", source = ".", qualifiedByName = "toOrderCustomerName"),
            @Mapping(target = "addressInstruction", source = ".", qualifiedByName = "toAddressInstruction"),
            @Mapping(target = "incorrectAddressReasons", source = ".", qualifiedByName = "toIncorrectAddressReasons")
    })
    ShipmentItemPresenter shipmentToShipmentItemPresenter(Shipment shipment);

    List<ShipmentItemPresenter> shipmentToShipmentItemPresenters(List<Shipment> shipments);

    ShipmentItemPresenterWithException shipmentToShipmentItemPresenterWithException(Shipment shipment, List<ApiError> exceptions);

    @Named("toAddressInstruction")
    default String translateToAddressInstruction(Shipment shipment) {
        return shipment.getAddressDetail() + " " + shipment.getAddressInstruction();
    }

    @Named("toOrderCustomerName")
    default String translateToCustomerName(Shipment shipment) {
        return shipment.getOrderCustomerFirstName() + " " + shipment.getOrderCustomerLastName();
    }

    @Named("toIncorrectAddressReasons")
    default List<String> translateToIncorrectAddressReasons(Shipment shipment){
        if (shipment.getIncorrectLocationReasons().isEmpty()) {
            return Collections.emptyList();
        }
        return Stream.of(shipment.getIncorrectLocationReasons().split(",", -1))
                .collect(Collectors.toList());
    }

    @Named("toIsExpressDelivery")
    default boolean translateToIsExpressDelivery(Shipment shipment) {
        return shipment.isExpressDelivery();
    }
}
