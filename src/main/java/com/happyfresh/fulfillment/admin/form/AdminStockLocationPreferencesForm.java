package com.happyfresh.fulfillment.admin.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import java.time.LocalTime;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class AdminStockLocationPreferencesForm {

    @Min(0)
    private Integer grabExpressOffsetTime;

    @Min(0)
    private Integer grabExpressDelayTime;

    private String meetingPointName;

    private String meetingPointRemark;

    @Range(min = -90, max = 90)
    private Double meetingPointLat;

    @Range(min = -180, max = 180)
    private Double meetingPointLon;

    private Boolean enableLalamoveDeliveryFee;

    private Double lalamoveFlatServiceFee;

    private Boolean enableDelyvaDeliveryFee;

    private Double delyvaFlatServiceFee;

    @Min(0)
    private Integer ongoingSlotCutOff;

    @JsonFormat(pattern = "H:mm")
    private LocalTime cutOffTime;

    private Integer cutOffOffsetDay;

    private Boolean enablePendingJobNotification;

    private Boolean expressSetSlotPrioritizeOnDemand;

    private Integer maximumTraveledDistance;

}
