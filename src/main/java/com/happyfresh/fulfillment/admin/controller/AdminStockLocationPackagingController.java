package com.happyfresh.fulfillment.admin.controller;

import com.happyfresh.fulfillment.admin.form.AdminPackagingForm;
import com.happyfresh.fulfillment.admin.mapper.PackagingPageMapper;
import com.happyfresh.fulfillment.admin.presenter.PackagingPagePresenter;
import com.happyfresh.fulfillment.common.annotation.ResponseWrapper;
import com.happyfresh.fulfillment.entity.Packaging;
import com.happyfresh.fulfillment.packaging.mapper.PackagingMapper;
import com.happyfresh.fulfillment.packaging.presenter.PackagingPresenter;
import com.happyfresh.fulfillment.packaging.service.PackagingService;
import com.happyfresh.fulfillment.repository.PackagingRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/admin/stock_locations")
public class AdminStockLocationPackagingController {

    @Autowired
    private PackagingService packagingService;

    @Autowired
    private PackagingMapper packagingMapper;

    @Autowired
    private PackagingPageMapper packagingPageMapper;

    @Autowired
    private PackagingRepository packagingRepository;

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/{stockLocationId}/packagings")
    public PackagingPagePresenter listPackaging(@PathVariable Long stockLocationId,
                                                @RequestParam(name = "page", defaultValue = "1") int page,
                                                @RequestParam(name = "size", defaultValue = "20") int size) throws Exception {
        Page<Packaging> packaging = packagingService.paginateWithStockLocation(stockLocationId, page, size, Sort.Direction.ASC, "displaySequence" );
        return packagingPageMapper.packagingPageToPackagingPagePresenter(page, packaging);
    }

    @PreAuthorize(("isAdminAuthenticated()"))
    @GetMapping(value = "/{stockLocationId}/packagings/{id}")
    @ResponseWrapper(rootName = "packaging")
    public PackagingPresenter showPackaging(@PathVariable Long stockLocationId,
                                            @PathVariable Long id) throws Exception {
        Packaging packaging = packagingService.findOneWithStockLocation(stockLocationId, id);
        return packagingMapper.packagingToPackagingPresenter(packaging);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PostMapping(value = "/{stockLocationId}/packagings")
    @ResponseWrapper(rootName = "packaging")
    public PackagingPresenter createPackaging(@PathVariable Long stockLocationId,
                                              @Valid @RequestBody AdminPackagingForm packagingForm) throws Exception {
        Packaging packaging = packagingService.create(stockLocationId, packagingForm);
        return packagingMapper.packagingToPackagingPresenter(packaging);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/{stockLocationId}/packagings/{id}")
    @ResponseWrapper(rootName = "packaging")
    public PackagingPresenter updatePackaging(@PathVariable Long stockLocationId,
                                              @PathVariable Long id,
                                              @Valid @RequestBody AdminPackagingForm packagingForm) throws Exception {
        Packaging packaging = packagingService.updateWithStockLocation(stockLocationId, id, packagingForm);
        return packagingMapper.packagingToPackagingPresenter(packaging);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @DeleteMapping(value = "/{stockLocationId}/packagings/{id}")
    public void deletePackaging(@PathVariable Long stockLocationId,
                                @PathVariable Long id) throws Exception {
        packagingService.softDeleteWithStockLocation(stockLocationId, id);
    }
}
