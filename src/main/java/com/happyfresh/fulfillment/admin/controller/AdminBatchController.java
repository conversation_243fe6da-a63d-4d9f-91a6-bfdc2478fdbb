package com.happyfresh.fulfillment.admin.controller;

import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.admin.form.activebatch.AdminBatchAssignmentForm;
import com.happyfresh.fulfillment.admin.mapper.AdminBatchMapper;
import com.happyfresh.fulfillment.admin.mapper.activebatch.AdminActiveBatchMapper;
import com.happyfresh.fulfillment.admin.presenter.AdminAvailableUserAgentPresenter;
import com.happyfresh.fulfillment.admin.presenter.AdminBatchPresenter;
import com.happyfresh.fulfillment.admin.presenter.activebatch.AdminActiveBatchPresenter;
import com.happyfresh.fulfillment.admin.services.AdminBatchService;
import com.happyfresh.fulfillment.batch.service.BatchService;
import com.happyfresh.fulfillment.common.annotation.ResponseWrapper;
import com.happyfresh.fulfillment.common.exception.ApiError;
import com.happyfresh.fulfillment.entity.Batch;
import com.happyfresh.fulfillment.entity.Shipment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/admin/batches")
public class AdminBatchController {

    @Autowired
    private AdminBatchMapper adminBatchMapper;

    @Autowired
    private AdminActiveBatchMapper adminActiveBatchMapper;

    @Autowired
    private AdminBatchService adminBatchService;

    @Autowired
    private BatchService batchService;

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/by_cluster")
    @ResponseWrapper(rootName = "batches")
    public List<AdminBatchPresenter> getBatchesByCluster(
        @RequestParam String date,
        @RequestParam Long clusterId) {
        return adminBatchMapper.toAdminBatchPresenters(adminBatchService.getBatchesByClusterAndDate(clusterId, LocalDate.parse(date)));
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseWrapper(rootName = "errors")
    public ResponseEntity<List<ApiError>> onValidationError(Exception ex) {
        final ApiError apiError = new ApiError(ex.getClass().getSimpleName(), ex.getMessage());

        return new ResponseEntity<>(Lists.newArrayList(apiError), new HttpHeaders(), HttpStatus.UNPROCESSABLE_ENTITY);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping
    @ResponseWrapper(rootName = "batches")
    public List<AdminActiveBatchPresenter> getBatchesByOrderNumberAndUserEmail(
            @RequestParam(name = "order_number") String orderNumber,
            @RequestParam(name = "user_email") String userEmail) {

        List<Batch> batchesByOrderNumberOrUserEmail = adminBatchService.findAllBatchesByOrderNumberOrUserEmail(orderNumber, userEmail);

        return batchesByOrderNumberOrUserEmail
                .stream().map(batch -> {
                    List<Shipment> completedShipments = batchService.getAllCompletedShipments(batch);
                    return adminActiveBatchMapper.batchToBatchPresenter(batch, completedShipments);
                })
                .collect(Collectors.toList());
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/{batchId}/assign_user")
    @ResponseWrapper(rootName = "batches")
    public AdminActiveBatchPresenter assignBatch(@PathVariable Long batchId, @Valid @RequestBody AdminBatchAssignmentForm form) {
        Batch assignedBatch = adminBatchService.assignFleetToBatch(batchId, form);
        List<Shipment> completedShipments = batchService.getAllCompletedShipments(assignedBatch);
        return adminActiveBatchMapper.batchToBatchPresenter(assignedBatch, completedShipments);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/{batchId}/available_users")
    @ResponseWrapper(rootName = "users")
    public List<AdminAvailableUserAgentPresenter> getAvailableUserAgentByBatchId(@PathVariable Long batchId){
        return adminBatchService.getUsersAgentByBatchId(batchId);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping("/{batchId}/shipments/{shipmentNumber}/to_hf")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ResponseWrapper(ignore = true)
    public void changeTPLOrderToHF(@PathVariable Long batchId, @PathVariable String shipmentNumber) {
        adminBatchService.switchToHF(shipmentNumber);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping("/{batchId}/shipments/{shipmentNumber}/unpool")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ResponseWrapper(ignore = true)
    public void unpool(@PathVariable Long batchId, @PathVariable String shipmentNumber) {
        adminBatchService.unpool(batchId, shipmentNumber);
    }
}
