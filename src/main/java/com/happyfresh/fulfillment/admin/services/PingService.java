package com.happyfresh.fulfillment.admin.services;

import com.happyfresh.fulfillment.common.service.ESClientService;
import com.happyfresh.fulfillment.common.service.JedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;

@Component
public class PingService {

    @Autowired
    private JedisService jedisService;

    @Autowired
    private ESClientService jestClientService;

    public boolean ping()  {
        Jedis jedis  = null;
        try {
            jedis = jedisService.getJedis();

            return jestClientService.ping() && jedis.ping().equalsIgnoreCase("PONG");
        } catch(Exception e) {
          return false;
        } finally {
            if (jedis != null)
                jedis.close();
        }

    }
}
