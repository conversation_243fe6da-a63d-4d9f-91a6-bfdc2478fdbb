package com.happyfresh.fulfillment.admin.presenter;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ShiftPagePresenter extends BaseAdminPresenter {

    private List<AdminShiftPresenter> shifts;

}
