package com.happyfresh.fulfillment.admin.presenter;

import com.happyfresh.fulfillment.entity.Slot;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class ClusterSharedDriverPresenter {

    private Long clusterId;

    private Slot.Type type;

    private LocalDateTime startTime;

    private Long sharedDriverCount;

    public ClusterSharedDriverPresenter(Long clusterId, Slot.Type type, LocalDateTime startTime, Long sharedDriverCount) {
        this.clusterId = clusterId;
        this.type = type;
        this.startTime = startTime;
        this.sharedDriverCount = sharedDriverCount;
    }
}
