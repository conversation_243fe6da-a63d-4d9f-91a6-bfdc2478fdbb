package com.happyfresh.fulfillment.admin.presenter;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.slot.presenter.SlotPresenter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SlotPagePresenter extends BaseAdminPresenter {

    private List<SlotPresenter> slot;

}
