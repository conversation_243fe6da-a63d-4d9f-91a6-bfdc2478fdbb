package com.happyfresh.fulfillment.admin.mapper;

import com.happyfresh.fulfillment.admin.presenter.ClusterSharedDriverPresenter;
import com.happyfresh.fulfillment.admin.presenter.ClusterSlotPagePresenter;
import com.happyfresh.fulfillment.entity.Slot;
import com.happyfresh.fulfillment.slot.model.SlotShipmentCount;
import com.happyfresh.fulfillment.slot.presenter.ClusterSlotPresenter;
import org.mapstruct.*;
import org.springframework.data.domain.Page;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
@DecoratedWith(ClusterSlotMapperDecorator.class)
public interface ClusterSlotMapper {

    @Named(value="withUTC")
    @Mapping(source = "type", target = "slotType")
    ClusterSlotPresenter toClusterSlotPresenter(Slot slot);

    @IterableMapping(qualifiedByName = "withUTC")
    List<ClusterSlotPresenter> toClusterSlotPresenters(List<Slot> slots);

    ClusterSlotPagePresenter toClusterSlotPagePresenter(Integer page, Page<Slot> slotPage, List<ClusterSharedDriverPresenter> sharedDriverInCluster, List<SlotShipmentCount> slotShipmentCounts);

    @Named(value="withLocal")
    @Mapping(source = "type", target = "slotType")
    ClusterSlotPresenter toClusterSlotPresenterV2(Slot slot);

    @IterableMapping(qualifiedByName = "withLocal")
    List<ClusterSlotPresenter> toClusterSlotPresentersV2(List<Slot> slots);

    ClusterSlotPagePresenter toClusterSlotPagePresenterV2(Integer page, Page<Slot> paginatedSlots, List<ClusterSharedDriverPresenter> sharedDriverInCluster, List<SlotShipmentCount> slotShipmentCounts);
}
