package com.happyfresh.fulfillment.admin.mapper;

import com.happyfresh.fulfillment.admin.presenter.OnDemandClusterPagePresenter;
import com.happyfresh.fulfillment.entity.OnDemandCluster;
import com.happyfresh.fulfillment.stockLocation.mapper.OnDemandClusterMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;

public abstract class OnDemandClusterPageMapperDecorator implements OnDemandClusterPageMapper {

    @Autowired
    @Qualifier("delegate")
    private OnDemandClusterPageMapper delegate;

    @Autowired
    private OnDemandClusterMapper onDemandClusterMapper;

    @Override
    public OnDemandClusterPagePresenter onDemandClusterPageToOnDemandClusterPagePresenter(Integer page, Page<OnDemandCluster> odClusterPage) {
        OnDemandClusterPagePresenter presenter = delegate.onDemandClusterPageToOnDemandClusterPagePresenter(page, odClusterPage);

        if (presenter != null) {
            presenter.setTotalPages(odClusterPage.getTotalPages());
            presenter.setTotalElements(odClusterPage.getTotalElements());
            presenter.setOnDemandClusters(onDemandClusterMapper.toOnDemandClusterPresenters(odClusterPage.getContent()));
        }
        return presenter;
    }
}