package com.happyfresh.fulfillment.locus.service.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.property.LocusProperty;
import com.happyfresh.fulfillment.common.service.CoralogixAPIService;
import com.happyfresh.fulfillment.entity.Cluster;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.Slot;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.locus.form.*;
import com.happyfresh.fulfillment.locus.presenter.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
public class LocusApiService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private LocusProperty locusProperty;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private LocusApiFormService locusApiFormService;

    @Autowired
    private CoralogixAPIService coralogixAPIService;

    @Autowired
    private ApplicationContext applicationContext;

    public Optional<LocusTeamPresenter> createTeam(Cluster cluster) {
        LocusTeamForm form = locusApiFormService.createTeamForm(locusProperty.getClientId(), locusApiFormService.getTeamName(cluster.getId()));
        LocusApiCreateTeamRequest request = new LocusApiCreateTeamRequest(cluster, form, mapper);
        request.setLocusProperty(locusProperty);
        request.setRestTemplate(restTemplate);
        return request.makeRequest();
    }

    public LocusHomebasePresenter createHomebase(StockLocation stockLocation, List<String> locusTeamIds) {
        LocusHomebaseForm form = locusApiFormService
                .createLocusHomebaseForm(stockLocation, locusProperty.getClientId(), locusTeamIds);
        LocusApiCreateHomebaseRequest request = new LocusApiCreateHomebaseRequest(form, mapper);
        request.setLocusProperty(locusProperty);
        request.setRestTemplate(restTemplate);
        return request.makeRequest()
                .orElseThrow(() -> new LocusApiException("Error on Locus API when creating Homebase"));
    }

    public LocusEmptyBatchPresenter createBatch(String clusterId, String teamId, String homebaseId, LocalDate date) {
        LocusEmptyBatchForm form = locusApiFormService.createLocusEmptyBatchForm(teamId, homebaseId, date);
        LocusApiCreateBatchRequest request = new LocusApiCreateBatchRequest(form, clusterId, date, mapper);
        request.setLocusProperty(locusProperty);
        request.setRestTemplate(restTemplate);
        return request.makeRequest().orElse(null);
    }

    public LocusVehiclePresenter updateOrCreateVehicle(StockLocation stockLocation) {
        String vehicleId = locusApiFormService.getVehicleId(stockLocation);
        LocusVehicleForm form = locusApiFormService.createLocusVehicleForm(stockLocation, vehicleId);

        LocusApiCreateVehicleModelRequest request = new LocusApiCreateVehicleModelRequest(form, vehicleId, mapper);
        request.setLocusProperty(locusProperty);
        request.setRestTemplate(restTemplate);
        return request.makeRequest()
                .orElseThrow(() -> new LocusApiException("Error on Locus API when creating VehicleModel"));
    }

    public LocusPlanPresenter createPlan(Cluster cluster, LocalDateTime date, Map<StockLocation, List<Slot>> stockLocationSlots) {
        LocusPlanForm planForm = locusApiFormService.createLocusPlanForm(stockLocationSlots);
        LocusApiCreatePlanRequest request = new LocusApiCreatePlanRequest(planForm, getBatchId(date, cluster), getPlanId(date, cluster), mapper);
        request.setLocusProperty(locusProperty);
        request.setRestTemplate(restTemplate);
        return request.makeRequest().orElse(null);
    }

    public LocusVehicleTourPresenter createTask(Shipment shipment, StockLocation stockLocation, String batchId, String planId, Slot slot) {
        LocusCreateTaskForm form = locusApiFormService.createLocusCreateTaskForm(shipment, locusProperty.getClientId(), slot);
        LocusApiCreateTaskRequest request = new LocusApiCreateTaskRequest(form, batchId, planId, mapper);
        request.setLocusProperty(locusProperty);
        request.setRestTemplate(restTemplate);
        long startTime = System.nanoTime();
        LocusVehicleTourPresenter locusVehicleTourPresenter = request.makeRequest().orElse(null);
        long endTime = System.nanoTime();
        long duration = (endTime - startTime) / 1000000;
        track("Locus Create Task Log", shipment, stockLocation, null, form, locusVehicleTourPresenter, duration);
        return locusVehicleTourPresenter;
    }

    public void removeTask(Shipment shipment, StockLocation stockLocation, LocalDateTime slotStart, List<String> taskIds) {
        final Cluster cluster = stockLocation.getCluster();
        final String batchId = getBatchId(slotStart, cluster);
        final String planId = getPlanId(slotStart, cluster);
        LocusRemoveTaskForm form = new LocusRemoveTaskForm(taskIds);
        LocusApiRemoveTaskRequest request = new LocusApiRemoveTaskRequest(batchId, planId, form, mapper);
        request.setLocusProperty(locusProperty);
        request.setRestTemplate(restTemplate);
        long startTime = System.nanoTime();
        Object response = request.makeRequest().orElse(null);
        long endTime = System.nanoTime();
        long duration = (endTime - startTime) / 1000000;
        track("Locus Remove Task Log", shipment, stockLocation, null, form, response, duration);
    }

    @Transactional
    public List<LocusGetSlotPresenter> getSlot(Shipment shipment, StockLocation stockLocation, LocalDate from, LocalDate to) {
        long numOfDaysFromTo = ChronoUnit.DAYS.between(from, to) + 1;
        List<CompletableFuture<LocusGetSlotPresenter>> futures =
            IntStream.range(0, (int) numOfDaysFromTo)
                .mapToObj(i -> {
                    LocalDate d = from.plusDays(i);
                    return CompletableFuture.supplyAsync(() -> getSlotByDate(shipment, stockLocation, d));
                })
                .collect(Collectors.toList());

        List<LocusGetSlotPresenter> presenters = Lists.newArrayList();
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[]{}))
            .whenComplete( (v, throwable) ->
                futures.forEach( cf -> {
                    LocusGetSlotPresenter p = cf.getNow(null);
                    if (p != null) {
                        presenters.add(p);
                    }
                })
            );
        allOf.join();

        return presenters;
    }

    @Transactional
    public LocusGetSlotPresenter getSlotByDate(Shipment shipment, StockLocation stockLocation, LocalDate date) {
        Cluster cluster = stockLocation.getCluster();
        LocusGetSlotForm form = locusApiFormService.createGetSlotForm(shipment, stockLocation, date);
        LocusApiGetSlotRequest request = createGetSlotRequest(form, cluster, date);
        request.setLocusProperty(locusProperty);
        request.setRestTemplate(restTemplate);

        long startTime = System.nanoTime();
        LocusGetSlotPresenter locusGetSlotPresenter = request.makeRequest().orElse(null);
        long endTime = System.nanoTime();
        long duration = (endTime - startTime) / 1000000;
        track("Locus Get Slot Log", shipment, stockLocation, date, form, locusGetSlotPresenter, duration);
        return locusGetSlotPresenter;
    }

    private LocusApiGetSlotRequest createGetSlotRequest(LocusGetSlotForm form, Cluster cluster, LocalDate date) {
        String batchId = getBatchId(date.atStartOfDay(), cluster);
        String planId = getPlanId(date.atStartOfDay(), cluster);
        return new LocusApiGetSlotRequest(form, batchId, planId, mapper);
    }

    private String getBatchId(LocalDateTime date, Cluster cluster) {
        return locusApiFormService.getBatchId(date, cluster);
    }

    private String getPlanId(LocalDateTime date, Cluster cluster) {
        return locusApiFormService.getPlanId(date, cluster);
    }

    private void track(String eventName, Shipment shipment, StockLocation stockLocation, LocalDate date, Object form, Object response, long duration) {
        try {
            ImmutableMap.Builder<String, Object> builder = ImmutableMap.builder();
            putOrEmpty(builder, "request_time", LocalDateTime.now().toString());
            putOrEmpty(builder, "order_number", shipment.getOrderNumber());
            putOrEmpty(builder, "stock_location_id", stockLocation.getId());
            putOrEmpty(builder, "stock_location_name", stockLocation.getName());
            putOrEmpty(builder, "date", date == null? LocalDate.now().toString() : date.toString());
            putOrEmpty(builder, "request_body", mapper.writeValueAsString(form));
            putOrEmpty(builder, "response_body", mapper.writeValueAsString(response));
            putOrEmpty(builder, "duration", duration);
            ImmutableMap<String, Object> properties = builder.build();

            coralogixAPIService.sendLog(CoralogixAPIService.LogSeverity.INFO, null, eventName, getClass().getSimpleName(),
                    "track", properties);
        } catch (Exception e) {
            logger.error(eventName + " Error", e);
        }
    }

    private void putOrEmpty(ImmutableMap.Builder<String, Object> builder, String key, Object value) {
        builder.put(key, Optional.ofNullable(value).orElse(""));
    }

}
