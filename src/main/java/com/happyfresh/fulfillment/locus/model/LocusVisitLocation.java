package com.happyfresh.fulfillment.locus.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class LocusVisitLocation {

    private String id;

    private LocusGeometry geometry;

    private LocusTimeWindow timeWindow;

    private List<LocusTimeWindow> nonAvailableWindows;

    private LocusStructuredAddress locationAddress;

    private LocusContactPoint contact;

    private LocusGeocodingMetadata geocodingMetadata;

}
