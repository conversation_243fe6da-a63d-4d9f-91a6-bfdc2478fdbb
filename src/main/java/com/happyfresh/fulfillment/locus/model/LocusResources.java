package com.happyfresh.fulfillment.locus.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class LocusResources {

    private List<LocusResource> resources;

    @Getter
    @Setter
    public class LocusResource {
        /**
         * Name of a resource. This shouldsdfs be a unique value within a task.
         * Example values are - CASH.
         *
         */
        private String name;
        /**
         * Unit for a resource.
         * Allowed Values: COUNT, SECONDS, METERS, KG, UNIT. default: COUNT
         */
        private String unit;

        private double value;

        /**
         * Denotes the type of exchange that needs to happen for the resource, from delivery person's perspective.
         * Allowed Values: GIVE, COLLECT
         */
        private String exchangeType;
    }

}
