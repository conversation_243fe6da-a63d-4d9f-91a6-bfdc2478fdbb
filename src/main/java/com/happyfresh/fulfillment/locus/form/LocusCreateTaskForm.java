package com.happyfresh.fulfillment.locus.form;

import com.happyfresh.fulfillment.locus.model.LocusInputTask;
import com.happyfresh.fulfillment.locus.model.LocusPlanEdit;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class LocusCreateTaskForm {

    private List<LocusInputTask> inputTasks;

    private List<LocusPlanEdit> planEdits;

    private boolean ignoreConstraints;

}
