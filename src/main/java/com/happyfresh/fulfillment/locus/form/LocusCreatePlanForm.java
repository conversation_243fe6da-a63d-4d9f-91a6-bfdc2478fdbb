package com.happyfresh.fulfillment.locus.form;

import com.happyfresh.fulfillment.locus.model.LocusPlanCallback;
import com.happyfresh.fulfillment.locus.model.LocusPlanConfiguration;
import com.happyfresh.fulfillment.locus.model.LocusUserVehicle;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class LocusCreatePlanForm {

    private List<LocusUserVehicle> vehicles;

    private LocusPlanConfiguration configuration;

    private String serializedVaeConfiguration;

    private List<LocusPlanCallback> planCallbacks;

    private Map<String,String> customFields;

    private Boolean autoAssign;

}
