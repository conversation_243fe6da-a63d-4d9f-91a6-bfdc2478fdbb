package com.happyfresh.fulfillment.locus.presenter;

import com.happyfresh.fulfillment.locus.model.LocusOrderDetail;
import com.happyfresh.fulfillment.locus.model.LocusTaskGraph;
import com.happyfresh.fulfillment.locus.model.LocusTaskStatus;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
public class LocusTaskPresenter {

    @NotNull
    private String clientId;

    @NotNull
    private String taskId;

    private LocusTaskStatus status;

    /**
     * Order Id for which this task is being generated
     */
    private String sourceOrderId;

    private LocusOrderDetail orderDetail;

    /**
     * Time of order creation, in UTC using the standard ISO 8601 format
     */
    @NotNull
    private String creationTime;

    /**
     * Time when delivery or pickup is required to be scheduled, in UTC using the standard ISO 8601 format
     */
    @NotNull
    private String scheduledTime;

    /**
     * Time of task completion, in UTC using the standard ISO 8601 format
     */
    private String completionTime;

    /**
     * A DAG (directed acyclic graph) representation of the task.
     * Each node in the graph is a location of interest, that delivery person needs to visit.
     */
    private LocusTaskGraph taskGraph;

}
