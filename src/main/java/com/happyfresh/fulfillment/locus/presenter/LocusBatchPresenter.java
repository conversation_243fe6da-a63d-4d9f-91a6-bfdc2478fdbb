package com.happyfresh.fulfillment.locus.presenter;

import com.happyfresh.fulfillment.locus.model.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class LocusBatchPresenter {

    private List<LocusTaskPresenter> tasks;

    private List<LocusTaskPresenter> deletedTasks;

    private LocusBatchId batchId;

    private String createdOn;

    private String updatedOn;

    private String startedOn;

    private List<LocusBatchEdit> edits;

    private List<LocusGeocodeEdit> geocodeEdits;

    /**
     * Processing status of the batch
     * Allowed Values: RECEIVED, ASSIGNING, ASSIGNED, ERROR
     */
    private String status;

    private LocusBatchSummary batchSummary;

    @Getter
    @Setter
    public class LocusBatchEdit {

        private LocusTaskId taskId;

        /**
         * Denotes addition or removal of task
         * Allowed Values: ADD, DELETE, REPLACE
         */
        private String editType;

        private String editedOn;

    }

    @Getter
    @Setter
    public class LocusGeocodeEdit {

        private LocusVisitId visitId;

        private List<LocusLatLng> edits;

    }

}
