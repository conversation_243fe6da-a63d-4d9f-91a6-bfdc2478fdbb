package com.happyfresh.fulfillment.locus.presenter;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Combined Presenter for Locus Team, HomeBases, VehicleModels, Batches, Plans.
 */
@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LocusClusterActivationPresenter {

    private LocusTeamPresenter team;

    private List<LocusHomebasePresenter> homebases;

    private List<LocusVehiclePresenter> vehicleModels;

    private List<LocusBatchPresenter> batches;

    private List<LocusPlanPresenter> plans;

}
