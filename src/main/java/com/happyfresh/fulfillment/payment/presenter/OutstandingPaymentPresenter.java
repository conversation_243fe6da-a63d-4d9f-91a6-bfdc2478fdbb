package com.happyfresh.fulfillment.payment.presenter;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OutstandingPaymentPresenter {
    private Long shipmentId;

    private String customerName;

    private String customerPhone;

    private String orderNumber;

    private Double paymentPaidAmount;

    private String displayPaidAmount;

    private Double paymentDebtAmount;

    private String displayDebtAmount;

    private Double orderTotal;

    private String displayOrderTotalAmount;

    private String outstandingStatus;

    private String dueDate;
}
