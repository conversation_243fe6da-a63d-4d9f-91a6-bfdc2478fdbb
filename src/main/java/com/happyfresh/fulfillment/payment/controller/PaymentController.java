package com.happyfresh.fulfillment.payment.controller;

import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.payment.model.OutstandingStatusResponse;
import com.happyfresh.fulfillment.payment.presenter.OutstandingPaymentListPresenter;
import com.happyfresh.fulfillment.payment.presenter.OutstandingPaymentPresenter;
import com.happyfresh.fulfillment.payment.presenter.PaymentConfigurationPresenter;
import com.happyfresh.fulfillment.repository.UserRepository;
import com.happyfresh.fulfillment.shipment.service.PaymentService;
import com.happyfresh.fulfillment.stockLocation.service.StockLocationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api")
public class PaymentController {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private StockLocationService stockLocationService;

    @GetMapping("/payment/outstanding/should_automate/{stockLocationId}")
    public PaymentConfigurationPresenter shouldAutomateOutstandingPayment(@PathVariable Long stockLocationId) throws Exception {
        PaymentConfigurationPresenter presenter = new PaymentConfigurationPresenter();
        Boolean shouldAutomate = stockLocationService.shouldAutomateOutstandingPayment(stockLocationId);
        presenter.setShouldAutomateOutstandingPayment(shouldAutomate);
        return presenter;
    }
    @GetMapping("/payment/outstanding/job/{userId}")
    public OutstandingPaymentListPresenter getPackagingUsageJob(@PathVariable Long userId) throws Exception {
        Optional<User> user = userRepository.findById(userId);
        if (user.isEmpty()) {
            throw new Exception("user not found");
        }
        List<OutstandingPaymentPresenter> data = paymentService.getOutstandingPaymentsBy(user.get());
        ArrayList<String> orderNumbers = new ArrayList<>();
        data.forEach(outstandingPaymentPresenter -> {
            orderNumbers.add(outstandingPaymentPresenter.getOrderNumber());
        });

        OutstandingStatusResponse[] response = paymentService.getOutstandingStatus(orderNumbers.stream().toArray(String[]::new));
        for (int i = 0; i < data.size(); i++) {
            OutstandingPaymentPresenter dataValue = data.get(i);
            for (int j = 0; j < response.length; j++) {
                OutstandingStatusResponse outstandingStatus = response[j];
                if (dataValue.getOrderNumber().equals(outstandingStatus.getNumber())) {
                    String status = outstandingStatus.getState().toUpperCase();
                    Double debtAmount = outstandingStatus.getAmount();
                    String displayDebtAmount = paymentService.getDisplayAmount(debtAmount);
                    Double paidAmount = dataValue.getOrderTotal() - debtAmount;
                    String displayPaidAmount = paymentService.getDisplayAmount(paidAmount);
                    dataValue.setOutstandingStatus(status);
                    dataValue.setPaymentPaidAmount(paidAmount);
                    dataValue.setPaymentDebtAmount(debtAmount);
                    dataValue.setDisplayPaidAmount(displayPaidAmount);
                    dataValue.setDisplayDebtAmount(displayDebtAmount);
                    paymentService.updatePaymentOutstandingStatus(
                        dataValue.getShipmentId(),
                        status,
                        paidAmount
                    );
                }
            }
        }

        OutstandingPaymentListPresenter presenter = new OutstandingPaymentListPresenter();
        presenter.setOutstandingPayments(data);
        return presenter;
    }
}
