package com.happyfresh.fulfillment.receipt.presenter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.shipment.presenter.ReceiptPresenter;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PendingReceiptPresenter extends ReceiptPresenter {

    private String orderNumber;

    private String shipmentNumber;

    private Long batchId;

    private Boolean taxRequired;

    private String stockLocationName;

    @JsonIgnore
    private LocalDateTime dateTime;

    private String date;

    @JsonProperty("date")
    public String getDateString() {
        if (date != null)
            return date;
        if (dateTime == null)
            return null;
        return dateTime.toLocalDate().toString();
    }
}
