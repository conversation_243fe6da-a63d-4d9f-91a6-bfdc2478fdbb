package com.happyfresh.fulfillment.entity;

import com.happyfresh.fulfillment.common.jpa.BaseEntityWithAudit;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "hff_agent_in_geofence")
public class AgentInGeofence extends BaseEntityWithAudit {

    @Id
    @GeneratedValue(strategy= GenerationType.SEQUENCE, generator = "agent_in_geofence_id_seq")
    @SequenceGenerator(name = "agent_in_geofence_id_seq", sequenceName = "hff_agent_in_geofence_id_seq", allocationSize=1)
    private Long id;

    private Long agentId;

    private Long stockLocationId;

}
