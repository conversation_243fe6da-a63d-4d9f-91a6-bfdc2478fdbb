package com.happyfresh.fulfillment.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigInteger;
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PackagingNotFilled {

    private String shipmentNumber;

    private String orderNumber;

    private String customerName;

    private BigInteger batchId;
}
