package com.happyfresh.fulfillment.entity;

import com.happyfresh.fulfillment.common.jpa.BaseEntityWithAudit;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Filter;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.ParamDef;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import javax.annotation.Nullable;
import javax.persistence.*;
import javax.validation.constraints.Email;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@Entity
@Table(name = "hff_user")
@FilterDef(name = "tenantFilter", parameters = {@ParamDef(name = "tenantId", type = "long")})
@Filter(name = "tenantFilter", condition = "tenant_id = :tenantId")
public class User extends BaseEntityWithAudit {

    @Id
    @GeneratedValue(strategy=GenerationType.SEQUENCE, generator = "hff_user_id_seq")
    @SequenceGenerator(name = "hff_user_id_seq", sequenceName = "hff_user_id_seq", allocationSize=1)
    private Long id;

    @Email
    private String email;

    private String password;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

    private String phone;

    private String token;

    private Boolean isActive;

    private String profilePictureUrl;

    private LocalDateTime blockedUntil;

    @Transient
    private Boolean isNewlyCreated;

    @org.hibernate.annotations.Type(type = "hstore")
    private Map<String, String> preferences;

    @OneToMany(cascade = CascadeType.ALL,
            mappedBy = "user", orphanRemoval = true)
    private Set<UserRole> userRoles;

    @OneToMany(fetch = FetchType.LAZY,
            cascade = CascadeType.ALL,
            mappedBy = "user",
            orphanRemoval = true)
    private Set<UserEnabler> userEnablers;

    @ManyToMany
    @JoinTable(name = "hff_user_role",
            joinColumns = @JoinColumn(name = "user_id"),
            inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    public List<Role> roles;

    @OneToOne(fetch = FetchType.LAZY,
            cascade =  CascadeType.ALL,
            mappedBy = "user")
    private Agent agent;

    public List<GrantedAuthority> getAuthorities() {
        return this.getRoles().stream().map(
                role -> new SimpleGrantedAuthority(role.getName().toString())
        ).collect(Collectors.toList());
    }

    public String fullName() {
        return firstName.concat(lastName).trim();
    }

    public String getFullName() {
        return firstName + " " + lastName;
    }

    public Map<String, String> getPreferences() {
        if (preferences == null) {
            preferences = new HashMap<>();
        }
        return preferences;
    }

    public boolean isAllowedForOnDemand() {
        return Boolean.parseBoolean(this.getPreferences().getOrDefault("is_allowed_for_on_demand", "true"));
    }

    public boolean isAllowedForAutoAssignment() {
        return Boolean.parseBoolean(this.getPreferences().getOrDefault("is_allowed_for_auto_assignment", "true"));
    }

    public boolean useLezCash() {
        return this.getShoppingPayment().equals("lezcash");
    }

    public String getLezCashDestinationId() {
        String dstId = this.getPreferences().getOrDefault("lezcash_dst_id", "");
        if (dstId.equals("")) {
            return this.getId().toString();
        }
        return dstId;
    }

    public String getShoppingPayment() {
        return this.getPreferences().getOrDefault("shopping_payment", "");
    }

    public boolean isBlocked() {
        return (blockedUntil != null) && LocalDateTime.now().isBefore(blockedUntil);
    }

    public void safeClearUserRoles() {
        if(this.userRoles != null)
            this.userRoles.clear();
    }
}
