package com.happyfresh.fulfillment.entity;

import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.jpa.Addressable;
import com.happyfresh.fulfillment.common.jpa.BaseEntityWithAudit;
import com.happyfresh.fulfillment.common.model.AddressableHelper;
import com.happyfresh.fulfillment.common.service.GraphhopperApiService;
import com.happyfresh.fulfillment.locus.model.LocusVehicleModel;
import com.happyfresh.fulfillment.stockLocation.service.StockLocationEntityListener;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.geo.GeoPoint;
import org.javers.core.metamodel.annotation.ShallowReference;

import javax.persistence.*;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Setter
@Getter
@Entity
@EntityListeners(StockLocationEntityListener.class)
@Table(name = "hff_stock_location")
public class StockLocation extends BaseEntityWithAudit implements Addressable {

    public final static String HAPPY_HAMPERS_STORE = "HAPPY_HAMPERS_STORE";

    public final static String OKE_SHOP_STORE = "OKE_SHOP_STORE";

    public final static String ENABLE_LALAMOVE_TWO_WHEELS = "ENABLE_LALAMOVE_TWO_WHEELS";

    public final static String ENABLE_LALAMOVE_FOUR_WHEELS = "ENABLE_LALAMOVE_FOUR_WHEELS";

    public final static String ENABLE_LALAMOVE_VAN = "ENABLE_LALAMOVE_VAN";

    public enum Type {
        ORIGINAL(0),
        SPECIAL(1);

        private final int value;

        Type(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        @Override
        public String toString() {
            if (value == Type.ORIGINAL.value) {
                return "original";
            } else {
                return "special";
            }
        }
    }

    public enum DDFType {
        ORIGINAL(0),
        WEIGHT(1);

        private final int value;

        DDFType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    public enum Enabler {
        RALALI, HFC, HYPERMART, SAYURBOX, SAYURBOX_INSTANT;

        @Override
        public String toString() {
            return StringUtils.upperCase(name());
        }
    }

    public enum EnablerPlatform {
        JUBELIO, STRATO, SAYURBOX, SAYURBOX_INSTANT;

        @Override
        public String toString() {
            return StringUtils.upperCase(name());
        }
    }

    @Id
    @SequenceGenerator(name = "hff_stock_location_id_seq", sequenceName = "hff_stock_location_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "hff_stock_location_id_seq")
    private Long id;

    private String name;

    private String address1;

    private String address2;

    private String code;

    private boolean active;

    @ShallowReference
    @ManyToOne
    @JoinColumn(name = "state_id")
    private State state;

    @ShallowReference
    @ManyToOne
    @JoinColumn(name = "cluster_id")
    private Cluster cluster;

    @ShallowReference
    @ManyToOne
    @JoinColumn(name = "on_demand_cluster_id")
    private OnDemandCluster onDemandCluster;

    @ShallowReference
    @ManyToOne
    @JoinColumn(name = "supplier_id")
    private Supplier supplier;

    @ShallowReference
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "stockLocation", orphanRemoval = true)
    private List<DdfConfiguration> ddfConfigurations = new ArrayList<>();

    private Double lat;

    private Double lon;

    private LocalTime openAt;

    private LocalTime closeAt;

    private boolean tplEnabled;

    private String imageUrl;

    private Double shipDistanceThreshold;

    private int prepickingOffset;

    private boolean crossdayPrepickingEnabled;

    private int maxDeliveryVolume = 100;

    private int maxShoppingVolume = 100000;

    private double maxDeliveryHandover;

    private double shopperAveragePickingTimePerUniqItem;

    private double shopperQueueReplacementTime;

    private double shopperHandoverToDriverTime;

    private int shoppingBatchNotifiedOffset;

    private int deliveryBatchNotifiedOffset;

    private boolean askReceiptNumber;

    private boolean enableGrabExpress;

    private boolean enableGrabExpressCod;

    private boolean enableChat;

    private boolean enableCutOff;

    private boolean enableMultiBatchShopping;

    private boolean requireDeliveryPackaging;

    private boolean enableOnDemandDelivery;

    private boolean enableOnDemandSnd;

    private boolean enableGraphhopper;

    @org.hibernate.annotations.Type(type = "hstore")
    private Map<String, String> enableLalamoveConfigs;

    private boolean enableVirtualAccountPayment;

    private boolean enableDelyva;

    private boolean enableGosendBike;

    private boolean enableGosendCar;

    private double expressAdditionalFee = 0;

    @Enumerated(EnumType.STRING)
    private Enabler enabler;

    @Enumerated(EnumType.STRING)
    private EnablerPlatform enablerPlatform;

    private Type type;

    private DDFType ddfType;

    @ShallowReference
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "stockLocation", orphanRemoval = true)
    private List<Packaging> packagings = new ArrayList<>();

    @org.hibernate.annotations.Type(type = "hstore")
    private Map<String, String> preferences;

    private Long externalId;

    @ShallowReference
    @OneToMany(mappedBy = "stockLocation")
    private List<CategoryOrder> categoryOrders;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "stockLocation", orphanRemoval = true)
    private List<DdfVolumeDistance> ddfVolumeDistanceList = new ArrayList<>();

    public Double getMaxDeliveryHandoverInSeconds() {
        return getMaxDeliveryHandover() * 60;
    }

    public Double getShoppingServiceTime() {
        double serviceTime = 0.0;

        if (type == Type.ORIGINAL)
            serviceTime += shopperHandoverToDriverTime;
        serviceTime += shopperQueueReplacementTime;

        return serviceTime;
    }

    public void setGeoCoordinate(Double lat, Double lon) {
        this.lat = lat;
        this.lon = lon;
    }

    @Transient
    private AddressableHelper addressableHelper = new AddressableHelper(this);

    @Override
    public GeoPoint getLatLon() {
        double aLat = lat == null ? 0 : lat;
        double aLon = lon == null ? 0 : lon;
        return new GeoPoint(aLat, aLon);
    }

    @Override
    public String getComplexId() {
        return addressableHelper.getComplexId();
    }

    public boolean isSpecial() {
        return type == Type.SPECIAL;
    }

    public String getTypeName() {
        return type.toString();
    }

    public int getGrabExpressOffsetTime() {
        return Integer.parseInt(this.getPreferences().getOrDefault("grab_express_offset_time", "0"));
    }

    public int getGrabExpressDelayTime() {
        return Integer.parseInt(this.getPreferences().getOrDefault("grab_express_delay_time", "0"));
    }

    public String getMeetingPointName() {
        String meetingPointName = this.getPreferences().getOrDefault("meeting_point_name", name);
        if (StringUtils.isBlank(meetingPointName))
            return name;
        return meetingPointName;
    }

    public String getMeetingPointRemark() {
        return this.getPreferences().getOrDefault("meeting_point_remark", StringUtils.EMPTY);
    }

    public double getMeetingPointLat() {
        try {
            return Double.parseDouble(this.getPreferences().getOrDefault("meeting_point_lat", lat.toString()));
        } catch (NumberFormatException e) {
            return lat;
        }
    }

    public double getMeetingPointLon() {
        try {
            return Double.parseDouble(this.getPreferences().getOrDefault("meeting_point_lon", lon.toString()));
        } catch (NumberFormatException e) {
            return lon;
        }
    }

    public LocalTime getCutOffTime() {
        String cutOffTime = this.getPreferences().get("cut_off_time");
        if (StringUtils.isEmpty(cutOffTime))
            return null;

        try {
            return LocalTime.parse(cutOffTime);
        } catch (Exception exception) {
            return null;
        }
    }

    public int getCutOffOffsetDay() {
        return Integer.parseInt(this.getPreferences().getOrDefault("cut_off_offset_day", "0"));
    }

    // In Minutes
    public int getOngoingSlotCutOff() {
        return Integer.parseInt(this.getPreferences().getOrDefault("ongoing_slot_cut_off", "0"));
    }

    public long getOnDemandDeliveryTime() {
        return Long.parseLong(this.getPreferences().getOrDefault("on_demand_delivery_time","75"));
    }

    public long getOnDemandCutOffTimeFromCloseTime(){
        return Long.parseLong(this.getPreferences().getOrDefault("on_demand_delivery_cut_off_time_from_close_time","60"));
    }

    public long getOnDemandNearStoreThreshold(){
        return Long.parseLong(this.getPreferences().getOrDefault("on_demand_delivery_near_store_threshold","3"));
    }

    public long getOnDemandBufferTime(){
        return Long.parseLong(this.getPreferences().getOrDefault("on_demand_buffer_time","0"));
    }

    public Boolean getExpressSetSlotPrioritizeOnDemand() {
        return Boolean.parseBoolean(this.getPreferences().getOrDefault("express_set_slot_prioritize_on_demand","false"));
    }

    public Boolean isEnableLalamoveDeliveryFee() {
        return Boolean.parseBoolean(this.getPreferences().getOrDefault("enable_lalamove_delivery_fee", "false"));
    }

    public double getLalamoveFlatServiceFee() {
        double fee = Double.parseDouble(this.getPreferences().getOrDefault("lalamove_flat_service_fee", "0"));
        return fee < 0 ? 0 : fee;
    }

    public Boolean isEnableDelyvaDeliveryFee() {
        return Boolean.parseBoolean(this.getPreferences().getOrDefault("enable_delyva_delivery_fee", "false"));
    }

    public double getDelyvaFlatServiceFee() {
        double fee = Double.parseDouble(this.getPreferences().getOrDefault("delyva_flat_service_fee", "0"));
        return fee < 0 ? 0 : fee;
    }

    public int getMaximumDeliveryRadius(){
        return Integer.parseInt(this.getPreferences().getOrDefault("maximum_delivery_radius", "50"));
    }

    // In Minutes
    public int getMaximumShoppingTime(){
        return Integer.parseInt(this.getPreferences().getOrDefault("maximum_shopping_time", "60"));
    }

    // In Meter
    public double getMaximumTraveledDistance(){
        return Double.parseDouble(this.getPreferences().getOrDefault("maximum_traveled_distance", "100000"));
    }

    public boolean isEnablePendingJobNotification() {
        return Boolean.parseBoolean(this.getPreferences().getOrDefault("enable_pending_job_notification", "false"));
    }

    public LocalTime getBrandStoreCutOffTime(LocalDateTime date) {
        String operationalDaysString = getFullOperationalDays();
        Map<DayOfWeek,String> operationalDaysCutOff = new HashMap<>();

        Lists.newArrayList(StringUtils.split(operationalDaysString, "|"))
                .forEach(string -> {
                    try {
                        String[] daysHour = StringUtils.split(string,'-');
                        String day = daysHour[0];
                        String hour = daysHour[1];
                        operationalDaysCutOff.put(DayOfWeek.valueOf(StringUtils.upperCase(day)), hour);

                    } catch (Exception e) { /* Ignore invalid string */ }
                });

        DayOfWeek dayOfWeek = date.getDayOfWeek();
        String cutOffTime = operationalDaysCutOff.getOrDefault(dayOfWeek, "05:00");

        try {
            return LocalTime.parse(cutOffTime);
        } catch (Exception exception) {
            return null;
        }
    }

    public List<DayOfWeek> getOperationalDays() {
        String operationalDaysString = getFullOperationalDays();

        List<DayOfWeek> operationalDays = Lists.newArrayList();
        Lists.newArrayList(StringUtils.split(operationalDaysString, "|"))
                .forEach(string -> {
                    try {
                        string = StringUtils.split(string,'-')[0];
                        operationalDays.add(DayOfWeek.valueOf(StringUtils.upperCase(string)));
                    } catch (IllegalArgumentException e) { /* Ignore invalid string */ }
                });

        return operationalDays;
    }

    private String getFullOperationalDays() {
        return this.getPreferences().getOrDefault("operational_days", "MONDAY-05:00|TUESDAY-05:00|WEDNESDAY-05:00|THURSDAY-05:00|FRIDAY-05:00");
    }

    public String getJubelioEmail() {
        return this.getPreferences().getOrDefault("jubelio_email", StringUtils.EMPTY);
    }

    public String getEnablerCourierName() {
        return this.getPreferences().getOrDefault("enabler_courier_name", "JNE REG");
    }

    public GraphhopperApiService.VehicleType getGraphhopperVehicleType() {
        final String vehicleTypeString = this.getPreferences().get("graphhopper_vehicle_type");
        if (vehicleTypeString == null)
            return null;
        return GraphhopperApiService.VehicleType.fromString(vehicleTypeString);
    }

    public LocusVehicleModel.VehicleType getLocusVehicleType() {
        final LocusVehicleModel.VehicleType defaultType = LocusVehicleModel.VehicleType.BIKE;
        final String vehicleTypeString = this.getPreferences().getOrDefault("locus_vehicle_type", defaultType.toString());

        return LocusVehicleModel.VehicleType.fromString(vehicleTypeString)
                .orElse(defaultType);
    }

    public int getInstantDeliveryCutoffTime() {
        return Integer.parseInt(this.getPreferences().getOrDefault("instant_delivery_cutoff_time", "60"));
    }

    public boolean isEnableEarliestShoppingBatchVisibility() {
        return Boolean.parseBoolean(this.getPreferences().getOrDefault("enable_earliest_shopping_batch_visibility", "true"));
    }

    public Map<String, String> getPreferences() {
        if (preferences == null) {
            preferences = new HashMap<>();
        }
        return preferences;
    }

    public long getSlotDuration() {
        long duration = 0;
        switch (cluster.getSlotType()) {
            case ONE_HOUR:
                duration = 1;
                break;
            case TWO_HOUR:
                duration = 2;
                break;
            case ONE_DAY:
                duration = ChronoUnit.HOURS.between(openAt, closeAt);
                break;
            default:
                break;
        }

        return duration;
    }

    public boolean hasEnabler() {
        return this.getEnabler()!=null && this.getEnablerPlatform()!=null;
    }

    public boolean isFulfilledByJubelio() {
        return this.hasEnabler() && this.getEnablerPlatform() == EnablerPlatform.JUBELIO;
    }

    public boolean isFulfilledByStrato() {
        return this.hasEnabler() && this.getEnablerPlatform() == EnablerPlatform.STRATO;
    }

    public boolean isFulfilledBySayurbox() {
        return this.hasEnabler() && this.getEnablerPlatform() == EnablerPlatform.SAYURBOX;
    }

    public boolean isFulfilledBySayurboxInstant() {
        return this.hasEnabler() && this.getEnablerPlatform() == EnablerPlatform.SAYURBOX_INSTANT;
    }

    public boolean isFulfilledByThirdPartyEnabler() {
        return this.getEnabler() != null;
    }

    public boolean enableExpressHfsLogic() {
        return Boolean.parseBoolean(this.getPreferences().getOrDefault("enable_express_hfs_logic", "false"));
    }

    public String getOnDemandSLAImageUrl() {
        return this.getPreferences().getOrDefault("on_demand_sla_image_url", "");
    }

    public String getOnDemandUnavailableImageUrl() {
        return this.getPreferences().getOrDefault("on_demand_unavailable_image_url", "");
    }

    public Integer maxDaysSlotAvailability() {
        String maxDaysSlotAvailability = this.getPreferences().get("max_days_slot_availability");
        if (maxDaysSlotAvailability == null)
            return null;

        return Integer.parseInt(maxDaysSlotAvailability);
    }

    public boolean useStratoShopperCapacityV2Endpoint() {
        return Boolean.parseBoolean(this.getPreferences().getOrDefault("use_strato_shopper_capacity_v2", "false"));
    }

    public boolean isEnableShopperAutoAssignment() {
        return Boolean.parseBoolean(this.getPreferences().getOrDefault("enable_shopper_auto_assignment", "false"));
    }

    public boolean isAllowedShopperAutoAssignment(){
        return !this.isSpecial() &&
                this.getEnabler() == null;
    }

    public boolean isEnabledTplDelivery(){
        return this.isTplEnabled()
                || this.isEnableLalamove()
                || this.isEnableGrabExpress()
                || this.isEnableDelyva();
    }

    public boolean isEnableLalamove() {
        return this.isEnableLalamoveTwoWheels() || this.isEnableLalamoveFourWheels() || this.isEnableLalamoveVan();
    }

    public boolean isEnableLalamoveTwoWheels() {
        return Boolean.parseBoolean(this.getEnableLalamoveConfigs().get(ENABLE_LALAMOVE_TWO_WHEELS));
    }

    public boolean isEnableLalamoveFourWheels() {
        return Boolean.parseBoolean(this.getEnableLalamoveConfigs().get(ENABLE_LALAMOVE_FOUR_WHEELS));
    }

    public boolean isEnableLalamoveVan() {
        return Boolean.parseBoolean(this.getEnableLalamoveConfigs().get(ENABLE_LALAMOVE_VAN));
    }

    public void setEnableLalamove(boolean enable) {
        String value = enable ? "true" : "false";
        this.getEnableLalamoveConfigs().put(ENABLE_LALAMOVE_TWO_WHEELS, value);
    }

    public Map<String, String> getEnableLalamoveConfigs() {
        if (enableLalamoveConfigs == null) {
            enableLalamoveConfigs = new HashMap<>();
        }
        return enableLalamoveConfigs;
    }

    public String getContactNumber() {
        return this.getPreferences().getOrDefault("contact_number", StringUtils.EMPTY);
    }

    public String getShopperSlotOptimizationAlgorithm() {
        return this.getPreferences().getOrDefault("shopper_slot_optimization_algorithm", "vrp");
    }

    public boolean isSlotOpenForGoldTierCustomer(){
        return Boolean.valueOf(this.getPreferences().getOrDefault("is_slot_open_for_gold_tier_member", "false"));
    }

    public boolean isBlackListedForUsingLezCash(){
        return Boolean.valueOf(this.getPreferences().getOrDefault("is_black_listed_for_using_lezcash", "false"));
    }

    // Max X minutes before next hour slot in Express Delivery where express delivery is available
    public int getExpressDeliveryOperationalCutoffTime() {
        return Integer.parseInt(this.getPreferences().getOrDefault("express_delivery_operational_cutoff_time", "30"));
    }

    public boolean shouldAutomateVerificationForOutstandingPayment() {
        Boolean flagFromTenant = this.getTenant().shouldAutomateVerificationForOutstandingPayment();
        if (!flagFromTenant) {
            return Boolean.valueOf(this.getPreferences().getOrDefault("should_automate_verification_for_outstanding_payment", "true"));
        }
        return flagFromTenant;
    }

    public String getStoreTag() {
        return this.getPreferences().getOrDefault("store_tag", StringUtils.EMPTY);
    }

    public boolean isHappyHampersStore() {
        return getStoreTag().equalsIgnoreCase(HAPPY_HAMPERS_STORE);
    }

    public boolean isOkeShopStore() {
        return getStoreTag().equalsIgnoreCase(OKE_SHOP_STORE);
    }

    public boolean enableClockInValidation(){
        return Boolean.valueOf(this.getPreferences().getOrDefault("enable_clock_in_validation", "false"));
    }
}
