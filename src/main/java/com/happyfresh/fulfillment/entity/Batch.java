package com.happyfresh.fulfillment.entity;

import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.batch.service.BatchEntityListener;
import com.happyfresh.fulfillment.common.jpa.BaseEntityWithAudit;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import javax.validation.constraints.AssertTrue;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Setter
@Getter
@NoArgsConstructor
@Entity
@EntityListeners(BatchEntityListener.class)
@Table(name = "hff_batch")
public class Batch extends BaseEntityWithAudit {

    public static final String FLAG_UNASSIGN_JOB = "unassign_job";

    public static final String FLAG_OPTIMIZED = "optimized";

    public static final String FLAG_FULFILLED_BY_STRATO = "fulfilled_by_strato";

    public static final String FLAG_AUTO_ASSIGNED = "is_auto_assigned";

    public static final String FLAG_AUTO_ASSIGNMENT_TIMESTAMP = "auto_assignment_timestamp";

    public static final String FLAG_MANUAL_ASSIGNED = "is_manual_assigned";

    public static final String FLAG_REASSIGNED = "is_reassigned";

    public static final String FLAG_REASSIGNED_REASON = "reassignment_reason";

    public static final String FLAG_MANUAL_ASSIGNMENT_REASON = "manual_assignment_reason";

    public static final String FLAG_MANUAL_ASSIGNMENT_TIMESTAMP = "manual_assignment_timestamp";

    public enum Type {
        SHOPPING(0),
        DELIVERY(1),
        RANGER(2),
        ON_DEMAND(3),
        ON_DEMAND_SHOPPING(4),
        ON_DEMAND_DELIVERY(5);

        private int value;

        Type(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        public static List<Type> getOnDemandRangerBatchTypes() {
            return Lists.newArrayList(ON_DEMAND, ON_DEMAND_DELIVERY);
        }

        public static List<Type> getShopperBatchTypes() {
            return Lists.newArrayList(SHOPPING, ON_DEMAND_SHOPPING);
        }

        public static List<Type> getNormalDeliveryBatchTypes() {
            return Lists.newArrayList(DELIVERY, RANGER);
        }
    }

    public enum DeliveryType {
        NORMAL(0),
        LONG_HOUR(1),
        TPL(2);

        private final int value;

        DeliveryType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    public enum ShoppingType {
        NORMAL(0),
        TPL(2);

        private final int value;

        ShoppingType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /** Only filled if delivery type is tpl, normal means our current tpl */
    public enum TplType{
        DEFAULT(0),
        GRAB_EXPRESS(1),
        JUBELIO(2),
        LALAMOVE(3),
        DELYVA(4),
        GOSEND_BIKE(5),
        GOSEND_CAR(6),
        LALAMOVE_TWO_WHEELS(7),
        LALAMOVE_FOUR_WHEELS(8),
        LALAMOVE_VAN(9),
        ;

        private final int value;

        TplType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    public Batch(StockLocation stockLocation, Type type, DeliveryType deliveryType, LocalDateTime startTime, LocalDateTime endTime, LocalDateTime handoverTime) {
        this(stockLocation, type, deliveryType, null, startTime, endTime, handoverTime);
    }

    public Batch(StockLocation stockLocation, Type type, DeliveryType deliveryType, TplType tplType, LocalDateTime startTime, LocalDateTime endTime, LocalDateTime handoverTime) {
        this.stockLocation = stockLocation;
        this.type = type;
        this.deliveryType = deliveryType;
        this.tplType = tplType;
        this.startTime = startTime;
        this.endTime = endTime;
        this.handoverTime = handoverTime;
    }

    public Batch(StockLocation stockLocation, Type type, ShoppingType shoppingType, DeliveryType deliveryType, TplType tplType, LocalDateTime startTime, LocalDateTime endTime, LocalDateTime handoverTime) {
        this.stockLocation = stockLocation;
        this.type = type;
        this.shoppingType = shoppingType;
        this.deliveryType = deliveryType;
        this.tplType = tplType;
        this.startTime = startTime;
        this.endTime = endTime;
        this.handoverTime = handoverTime;
    }

    @Id
    @SequenceGenerator(name = "batch_id_seq", sequenceName = "hff_batch_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "batch_id_seq")
    private Long id;

    private Type type;

    private DeliveryType deliveryType;

    private ShoppingType shoppingType;

    private TplType tplType;

    @ManyToOne
    @JoinColumn(name = "user_id")
    private User user;

    @ManyToOne
    @JoinColumn(name = "stock_location_id")
    private StockLocation stockLocation;

    @ManyToOne
    @JoinColumn(name = "shift_id")
    private Shift shift;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private LocalDateTime handoverTime;

    private LocalDateTime driverNotifiedAt;

    private LocalDateTime driverSkippedAt;

    private Double totalDistance;

    private Integer vehicle;

    private boolean isSwitchedToHf;

    @org.hibernate.annotations.Type(type = "hstore")
    private Map<String, String> flags;

    public Map<String, String> getFlags() {
        if (flags == null)
            flags = new HashMap<>();

        return flags;
    }

    public void updateFlags(String key, String value) {
        getFlags().put(key, value);
    }

    @Transient
    private Double driverDistanceToStockLocation;

    @ManyToOne
    @JoinColumn(name = "notified_driver_id")
    private User notifiedUser;

    @OneToMany(mappedBy = "batch")
    @NotFound(action = NotFoundAction.IGNORE)
    private List<Job> jobs;

    public boolean isSpecial() {
        return type == Type.RANGER || type == Type.ON_DEMAND;
    }

    public boolean isDelivery() {
        return type == Type.DELIVERY || type == Type.ON_DEMAND_DELIVERY;
    }

    @AssertTrue
    private boolean isEligiblePersistToDB() {
        return deliveryType == Batch.DeliveryType.TPL ? tplType != null : tplType == null;
    }

    public boolean isGrabExpress() {
        return type == Type.DELIVERY
                && deliveryType == DeliveryType.TPL
                && tplType == TplType.GRAB_EXPRESS;
    }

    public boolean isLalamove() {
        return type == Type.DELIVERY
                && deliveryType == DeliveryType.TPL
                && (
                        tplType == TplType.LALAMOVE
                        || tplType == TplType.LALAMOVE_TWO_WHEELS
                        || tplType == TplType.LALAMOVE_FOUR_WHEELS
                        || tplType == TplType.LALAMOVE_VAN
        );
    }

    public boolean isDelyva() {
        return type == Type.DELIVERY
                && deliveryType == DeliveryType.TPL
                && tplType == TplType.DELYVA;
    }

    public boolean isGosend() {
        return type == Type.DELIVERY
                && deliveryType == DeliveryType.TPL
                && (
                        tplType == TplType.GOSEND_BIKE
                        || tplType == TplType.GOSEND_CAR
                );
    }
    public boolean isGosendBike() {
        return type == Type.DELIVERY
                && deliveryType == DeliveryType.TPL
                && tplType == TplType.GOSEND_BIKE;
    }

    public boolean isGosendCar() {
        return type == Type.DELIVERY
                && deliveryType == DeliveryType.TPL
                && tplType == TplType.GOSEND_CAR;
    }

    public boolean isHFDelivery() {
        return type == Type.DELIVERY
                && deliveryType != DeliveryType.TPL;
    }

    public boolean isFlagUnassignJob() {
        return  Boolean.parseBoolean(this.getFlags().getOrDefault(FLAG_UNASSIGN_JOB, "false"));
    }

    public boolean isFlagOptimized() {
        return  Boolean.parseBoolean(this.getFlags().getOrDefault(FLAG_OPTIMIZED, "false"));
    }

    public boolean isFulfilledByStrato() {
        return  Boolean.parseBoolean(this.getFlags().getOrDefault(FLAG_FULFILLED_BY_STRATO, "false"));
    }

    public boolean isManualAssigned() {
        return  Boolean.parseBoolean(this.getFlags().getOrDefault(FLAG_MANUAL_ASSIGNED, "false"));
    }

    public boolean isAutoAssigned() {
        return  Boolean.parseBoolean(this.getFlags().getOrDefault(FLAG_AUTO_ASSIGNED, "false"));
    }

    public void markAsAutoAssigned() {
        String nowEpoch = String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli());
        this.updateFlags(Batch.FLAG_AUTO_ASSIGNED, "true");
        this.updateFlags(Batch.FLAG_AUTO_ASSIGNMENT_TIMESTAMP, nowEpoch);
    }

    public void addJob(Job job) {
        if (jobs == null) {
            jobs = new ArrayList<>();
        }
        jobs.add(job);
    }

    public void removeJob(Job job) {
        if (jobs != null) {
            getJobs().remove(job);
        }
    }

    public void recalculateAffectedTimes() {
        calculateEndTimeByShoppingServiceTime();
        calculateBatchShoppingHandOverTime();
    }
    
    public void calculateEndTimeByShoppingServiceTime() {
        double totalItem = 0;
        for (Job job: getJobs()) {
            totalItem += job.getShipment().getTotalItemCount(false);
        }
        double shoppingTime = totalItem * stockLocation.getShopperAveragePickingTimePerUniqItem() + stockLocation.getShoppingServiceTime();
        long shoppingTimeMinutes = (long) shoppingTime;
        long shoppingTimeSec = DateTimeUtil.getSecondFromADecimal(shoppingTime);
        this.endTime = this.startTime.plusMinutes(shoppingTimeMinutes).plusSeconds(shoppingTimeSec);
    }

    public void calculateBatchShoppingHandOverTime() {
        long handoverTime = (long) stockLocation.getShopperHandoverToDriverTime();
        this.setHandoverTime(this.endTime.minusMinutes(handoverTime));
    }

    public boolean stillCanBePooled(Shipment shipment) {
        double totalDuration = 0;
        for (Job job: getJobs()) {
            totalDuration += job.getShipment().getPickingItemsDuration();
        }
        return totalDuration + shipment.getShoppingDuration() <= shipment.getSlot().getStockLocation().getMaximumShoppingTime();
    }

    public Long getDuration() {
        return ChronoUnit.MINUTES.between(getStartTime(), getEndTime());
    }

    public Long getSlotId() {
        if (jobs == null || jobs.isEmpty()) {
            return -1L;
        }
        return jobs.get(0).getShipment().getSlot().getId();
    }

    public LocalDateTime getSlotStartTime() {
        if (jobs == null || jobs.isEmpty()) {
            return null;
        }
        return jobs.get(0).getShipment().getSlot().getStartTime();
    }

    public boolean anyShoppingJob() {
        if (jobs == null) {
            return false;
        }
        return jobs.size() > 0;
    }

    public LocalDateTime getEarliestShipmentCreatedAt() {
        Collections.sort(jobs, (job1, job2) -> {
            if (job1.getShipment().getCreatedAt().isBefore(job2.getShipment().getCreatedAt())) {
                return -1;
            }
            return 1;
        });
        return jobs.get(0).getShipment().getCreatedAt();
    }

    public boolean shouldReadjustStartTime(LocalDateTime referenceTime) {
        LocalDateTime earliestShipmentCreatedAt = getEarliestShipmentCreatedAt();
        return isShipmentStartTimeValid() && earliestShipmentCreatedAt.isAfter(referenceTime);
    }

    public boolean isShipmentStartTimeValid() {
        LocalDateTime earliestShipmentCreatedAt = getEarliestShipmentCreatedAt();
        boolean isBeforeSlotStartTime = earliestShipmentCreatedAt.isBefore(getSlotStartTime()) || earliestShipmentCreatedAt.isEqual(getSlotStartTime());
        return (
            earliestShipmentCreatedAt.isAfter(shift.getStartTime()) &&
            earliestShipmentCreatedAt.isBefore(shift.getEndTime()) &&
            isBeforeSlotStartTime
        );
    }

    public boolean payUseLezCash() {
        return user.useLezCash();
    }

    public List<Shipment> getShipmentsForLezCash() {
        List<Shipment> shipments = new ArrayList<>();
        for (Job job : getJobs()) {
            shipments.add(job.getShipment());
        }
        return shipments;
    }

    public List<Shipment> getAllShipments() {
        List<Shipment> shipments = new ArrayList<>();
        for (Job job : getJobs()) {
            shipments.add(job.getShipment());
        }
        return shipments;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Batch that = (Batch) o;
        return Objects.equals(this.getId(), that.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId());
    }

}
