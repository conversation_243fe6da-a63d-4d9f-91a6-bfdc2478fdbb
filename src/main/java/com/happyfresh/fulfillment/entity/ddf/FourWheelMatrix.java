package com.happyfresh.fulfillment.entity.ddf;

import com.happyfresh.fulfillment.entity.StockLocation;
import lombok.Getter;
import lombok.NoArgsConstructor;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.math.BigDecimal;

@NoArgsConstructor
@Entity
@DiscriminatorValue("FourWheelMatrix")
public class FourWheelMatrix extends DDFMatrix {

    public FourWheelMatrix(FourWheelMatrix.Matrix matrix, BigDecimal value, StockLocation stockLocation) {
        super(matrix.getColumnIndex(), matrix.getRowIndex(), value, FourWheelMatrix.class.getSimpleName(), stockLocation);
    }

    public interface Matrix {
        int getRowIndex();

        int getColumnIndex();
    }

    @Getter
    public enum Type implements FourWheelMatrix.Matrix {
        CONSTANT_0(0), CONSTANT_1(1);

        private int columnIndex;

        private int rowIndex;

        Type(int columnIndex) {
            this.columnIndex = columnIndex;
            this.rowIndex = 0;
        }
    }

}