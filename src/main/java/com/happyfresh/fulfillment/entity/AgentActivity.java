package com.happyfresh.fulfillment.entity;

import com.happyfresh.fulfillment.common.jpa.BaseEntityWithAudit;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "hff_agent_activity")
@Inheritance(strategy = InheritanceType.JOINED)
public class AgentActivity extends BaseEntityWithAudit {

    @Id
    @GeneratedValue(strategy= GenerationType.SEQUENCE, generator = "agent_activity_id_seq")
    @SequenceGenerator(name = "agent_activity_id_seq", sequenceName = "hff_agent_activity_id_seq", allocationSize=1)
    private Long id;

    private Long userId;

    private Long agentId;

    @Enumerated(EnumType.ORDINAL)
    private Agent.State state;

    private Long stockLocationId;

    @Column(precision=9, scale=6)
    private Double lat;

    @Column(precision=9, scale=6)
    private Double lon;

    @Column(precision=8, scale=2)
    private Double accuracy;

}
