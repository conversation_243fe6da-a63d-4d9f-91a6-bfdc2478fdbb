package com.happyfresh.fulfillment.entity;

import com.google.common.base.CaseFormat;
import com.happyfresh.fulfillment.common.jpa.BaseEntityWithAudit;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.ShallowReference;

import javax.persistence.*;
import java.time.Clock;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Setter
@Getter
@NoArgsConstructor
@Entity
@Table(name = "hff_slot")
public class Slot extends BaseEntityWithAudit {

    public enum Type {
        ONE_HOUR(0), TWO_HOUR(1), ONE_DAY(2), ON_DEMAND(3), LONGER_DELIVERY(4);

        private int value;

        Type(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        @Override
        public String toString() {
            return CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_UNDERSCORE, name());
        }
    }

    public enum FleetType {
        HF, GE, TPL, LALAMOVE, PICKUPP, BORZO_CAR, BORZO_BIKE, DELYVA_TEST, GOSEND_BIKE, GOSEND_CAR, LALAMOVE_TWO_WHEELS, LALAMOVE_FOUR_WHEELS, LALAMOVE_VAN
    }

    private static final Map<FleetType, String> fleetTypeToFleetNameMap = new EnumMap<>(FleetType.class);
    static {
        // The value will be displayed as label on the delivery slots screen.
        // client won't display the label if fleet_name is null or "".
        fleetTypeToFleetNameMap.put(FleetType.HF, null);
        fleetTypeToFleetNameMap.put(FleetType.TPL, null);
        fleetTypeToFleetNameMap.put(FleetType.GE, null);
        fleetTypeToFleetNameMap.put(FleetType.LALAMOVE, null);
        fleetTypeToFleetNameMap.put(FleetType.PICKUPP, null);
        fleetTypeToFleetNameMap.put(FleetType.BORZO_CAR, null);
        fleetTypeToFleetNameMap.put(FleetType.BORZO_BIKE, null);
        fleetTypeToFleetNameMap.put(FleetType.DELYVA_TEST, null);
        fleetTypeToFleetNameMap.put(FleetType.GOSEND_BIKE, null);
        fleetTypeToFleetNameMap.put(FleetType.GOSEND_CAR, null);
        fleetTypeToFleetNameMap.put(FleetType.LALAMOVE_TWO_WHEELS, null);
        fleetTypeToFleetNameMap.put(FleetType.LALAMOVE_FOUR_WHEELS, null);
        fleetTypeToFleetNameMap.put(FleetType.LALAMOVE_VAN, null);
    }

    public enum FleetVehicleType {
        FOUR_WHEELS, TWO_WHEELS
    }

    public enum UnavailableReason {
        NO_DRIVER, DRIVER_BUSY, CLOSE_SOON, STORE_CLOSED, BIG_EFFORT, ROUTE_NOT_FOUND, OTHERS,
        FAILED_CHAIN,
        CONTAINS_ALCOHOL_ITEM,
        EXCEEDS_ONGOING_SLOT_CUTOFF,
        NO_SHOPPER_CAPACITY,
        OVER_MAX_DELIVERY_RADIUS,
        SHIPMENT_BELOW_MAX_DELIVERY_VOLUME
    }

    public Slot(StockLocation stockLocation, LocalDateTime startTime, LocalDateTime endTime, Type type) {
        this.stockLocation = stockLocation;
        this.startTime = startTime;
        this.endTime = endTime;
        this.type = type;
    }

    @Id
    @SequenceGenerator(name = "hff_slot_id_seq", sequenceName = "hff_slot_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "hff_slot_id_seq")
    private Long id;

    @ShallowReference
    @ManyToOne
    @JoinColumn(name = "stock_location_id")
    private StockLocation stockLocation;

    private Type type;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private int shopperCount;

    private int driverCount;

    @Transient
    private FleetType fleetType;

    @Transient
    private FleetVehicleType vehicleType;

    @Transient
    private boolean allowCod;

    @Transient
    private boolean allowEwallet;

    @Transient
    private boolean visible = true;

    @Transient
    private LocalDateTime earliestShoppingEndTime;

    @DiffIgnore
    @OneToMany(mappedBy = "slot")
    private Set<JobSlot> jobSlots;

    @ShallowReference
    @OneToMany(mappedBy = "slot")
    private List<Shipment> shipments;

    @Transient
    private boolean skipGoldTierChecking = false;

    public int durationInHour() {
        if (type == Type.ONE_HOUR) {
            return 1;
        } else {
            return 2;
        }
    }

    public int durationInMinutes() {
        if (type == Type.LONGER_DELIVERY) {
            return (int) ChronoUnit.MINUTES.between(startTime, endTime);
        } else {
            return durationInHour() * 60;
        }
    }

    public int durationInSeconds() {
        return durationInMinutes() * 60;
    }

    public boolean isNext(Clock clock) {
        return this.startTime.isBefore(LocalDateTime.now(clock)
                .plusHours(ChronoUnit.HOURS.between(this.startTime, this.endTime)));
    }

    public boolean isNext() {
        return this.isNext(Clock.systemUTC());
    }

    public boolean isNextX(int x, Clock clock) {
        return this.startTime.isBefore(LocalDateTime.now(clock)
                .plusHours(ChronoUnit.HOURS.between(this.startTime, this.endTime) * x));
    }

    public String getFleetName() {
        return fleetTypeToFleetNameMap.get(fleetType);
    }

    public boolean isClosed() {
        return shopperCount < 0 || driverCount < 0;
    }

}
