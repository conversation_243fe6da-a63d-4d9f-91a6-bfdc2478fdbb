package com.happyfresh.fulfillment.entity;

import com.happyfresh.fulfillment.common.jpa.BaseEntityWithCreateAudit;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;

@Setter
@Getter
@NoArgsConstructor
@Entity
@Table(name = "hff_receipt_item")
public class ReceiptItem extends BaseEntityWithCreateAudit {

    @Id
    @SequenceGenerator(name = "receipt_item_id_seq", sequenceName = "hff_receipt_item_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "receipt_item_id_seq")
    private Long id;

    @ManyToOne
    @JoinColumn(name = "receipt_id")
    private Receipt receipt;

    private String sku;

    private BigDecimal amount;

}
