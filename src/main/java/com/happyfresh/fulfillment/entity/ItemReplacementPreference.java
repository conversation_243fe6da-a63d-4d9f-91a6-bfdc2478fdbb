package com.happyfresh.fulfillment.entity;

import com.happyfresh.fulfillment.common.jpa.BaseEntityWithAudit;
import com.happyfresh.fulfillment.common.util.CurrencyUtil;
import com.happyfresh.fulfillment.common.util.I18nUtil;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.*;

@Setter
@Getter
@Entity
@Table(name = "hff_item_replacement_preference")
public class ItemReplacementPreference extends BaseEntityWithAudit {

    @Id
    @SequenceGenerator(name = "item_replacement_preference_id_seq", sequenceName = "hff_item_replacement_preference_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "item_replacement_preference_id_seq")
    private Long id;

    private Long variantId;

    private String sku;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "item_id", nullable = false)
    private Item item;

    @Type(type = "hstore")
    private Map<String, String> translationNames;

    @Type(type = "hstore")
    private Map<String, String> translationDescriptions;

    @Type(type = "hstore")
    private Map<String, String> imageUrl;

    private BigDecimal costPrice;

    private BigDecimal price;

    private BigDecimal normalPrice;

    private BigDecimal normalCostPrice;

    private BigDecimal unitPrice;

    private BigDecimal supermarketUnitCostPrice;

    private Double height;

    private Double width;

    private Double depth;

    private Double weight;

    private Double averageWeight;

    private String unit;

    private String supermarketUnit;

    private String currency;

    private boolean consideredAsAlcohol;

    private boolean consideredAsGeRestrictedProduct;

    private Integer maximumOrderQuantity;

    private Integer maximumPromoQuantity;

    public String getName() {
        return I18nUtil.getDefaultMessage(translationNames);
    }

    public String getDescription() {
        return I18nUtil.getDefaultMessage(translationDescriptions);
    }

    public List<String> getImages() {
        List<String> images = new ArrayList<>();
        imageUrl.keySet().stream().sorted().forEach(key -> images.add(imageUrl.get(key)));
        return images;
    }

    public String getDisplayPrice() {
        return CurrencyUtil.format(price, currency);
    }

    public String getDisplayCostPrice() {
        return CurrencyUtil.format(costPrice, currency);
    }

    public String getDisplayNormalPrice() {
        if (normalPrice == null) {
            return null;
        }
        return CurrencyUtil.format(normalPrice, currency);
    }

    public String getDisplayNormalCostPrice() {
        if (normalCostPrice == null) {
            return null;
        }
        return CurrencyUtil.format(normalCostPrice, currency);
    }

    public String getDisplayUnitPrice() {
        if (unitPrice == null) {
            return null;
        }
        return CurrencyUtil.format(unitPrice, currency);
    }

    public String getDisplaySupermarketUnitCostPrice() {
        if (supermarketUnitCostPrice == null)
            return null;
        return CurrencyUtil.format(supermarketUnitCostPrice, currency);
    }

    public String getDisplayAverageWeight() {
        if (averageWeight != null)
            return "~ " + averageWeight + " " + supermarketUnit;
        return null;
    }

    public void setItem(Item item) {
        if (!Objects.equals(this.item, item)) {
            Item oldItem = this.item;
            this.item = item;
            if (oldItem != null) oldItem.removeReplacementPreference(this);
            if (item != null) item.addReplacementPreference(this);
        }
    }

}
