package com.happyfresh.fulfillment.entity;

import com.happyfresh.fulfillment.common.jpa.BaseEntityWithAudit;
import com.happyfresh.fulfillment.common.util.CurrencyUtil;
import com.happyfresh.fulfillment.common.util.I18nUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.http.util.TextUtils;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "hff_packaging")
public class Packaging extends BaseEntityWithAudit {

    @Id
    @SequenceGenerator(name = "packaging_id_seq", sequenceName = "hff_packaging_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "packaging_id_seq")
    private Long id;

    @ManyToOne
    @JoinColumn(name = "packaging_type_id")
    private PackagingType packagingType;

    @ManyToOne
    @JoinColumn(name = "stock_location_id")
    private StockLocation stockLocation;

    private BigDecimal price;

    private int maxQuantity;

    private int displaySequence;

    private boolean isDefault;

    private LocalDateTime deletedAt;

    public String getInternalName() {
        return packagingType.getInternalName();
    }

    public String getImageUrl() {
        return packagingType.getImageUrl();
    }

    public boolean isActive() {
        return packagingType.isActive();
    }

    public String getClientName() {
        String name = I18nUtil.getDefaultMessage(packagingType.getClientNames());
        if (TextUtils.isEmpty(name)) {
            return packagingType.getInternalName(); // Fallback to internal_name
        }
        return name;
    }

    public String getSndName() {
        String name = I18nUtil.getDefaultMessage(packagingType.getSndNames());
        if (TextUtils.isEmpty(name)) {
            return packagingType.getInternalName(); // Fallback to internal_name
        }
        return name;
    }

    public String getDisplayPrice() {
        // Based on stock_location's country currency
        return CurrencyUtil.format(price, stockLocation.getState().getCountry().getIsoName());
    }
}
