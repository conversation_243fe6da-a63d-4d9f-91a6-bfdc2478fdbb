package com.happyfresh.fulfillment.gosend.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.happyfresh.fulfillment.gosend.form.GosendWebhookForm;
import com.happyfresh.fulfillment.gosend.service.GosendUpdateStatusService;
import com.happyfresh.fulfillment.gosend.service.GosendWebhookService;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.shipment.mapper.ShipmentMapper;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentPresenter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Enumeration;
import java.util.Map;

@RestController
@RequestMapping("/api/gosend")
public class GosendController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private ShipmentMapper shipmentMapper;

    @Autowired
    private GosendWebhookService gosendWebhookService;

    @Autowired
    private GosendUpdateStatusService gosendUpdateStatusService;

    @PostMapping(path = "/webhook")
    public ResponseEntity<Map<String, String>> webhook(@RequestHeader(value = "authorization", defaultValue = "") String gosendToken,
                                                       @Valid @RequestBody GosendWebhookForm gosendWebhookFrom,
                                                       HttpServletRequest request) {
        String headers = "";
        Enumeration headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = (String) headerNames.nextElement();
            headers = headers + headerName + " : " + request.getHeader(headerName) + "\n";
        }
        logger.info("[Gosend] Webhook request. Headers: {}", headers);

        if (gosendWebhookService.isValidSignature(gosendToken)) {
            gosendWebhookService.processData(gosendWebhookFrom);
            return new ResponseEntity<>(HttpStatus.OK);
        }
        return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
    }

    @PreAuthorize("isExternalSystemAuthenticated()")
    @PutMapping(path = "/orders/{orderNumber}")
    public ShipmentPresenter updateOrder(@PathVariable String orderNumber) throws JsonProcessingException {
        return shipmentMapper.shipmentToShipmentPresenter(gosendUpdateStatusService.updateBookingStatus(orderNumber));
    }


}
