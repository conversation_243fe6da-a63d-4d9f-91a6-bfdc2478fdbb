package com.happyfresh.fulfillment.gosend.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
public class GosendCreateBookingForm {

    @NotNull
    private int paymentType;

    @NotNull
    @JsonProperty("shipment_method")
    private String shipmentMethod;

    @NotEmpty
    private List<Route> routes;

    @Getter
    @Setter
    public static class Route {
        @NotNull
        private String originName;

        @NotNull
        private String originNote;

        @NotNull
        private String originContactName;

        @NotNull
        private String originContactPhone;

        @NotNull
        private String originLatLong;

        @NotNull
        private String originAddress;

        @NotNull
        private String destinationName;

        @NotNull
        private String destinationNote;

        @NotNull
        private String destinationContactName;

        @NotNull
        private String destinationContactPhone;

        @NotNull
        private String destinationLatLong;

        @NotNull
        private String destinationAddress;

        @NotNull
        private String item;

        @NotNull
        private String storeOrderId;

        @NotNull
        private InsuranceDetail insuranceDetail;

        @Getter
        @Setter
        public static class InsuranceDetail {

            @NotNull
            private boolean applied;

            @NotNull
            private String fee;

            @NotNull
            private String productDescription;

            @NotNull
            private String productPrice;
        }
    }
}
