package com.happyfresh.fulfillment.gosend.mapper;

import com.happyfresh.fulfillment.entity.GosendDelivery;
import com.happyfresh.fulfillment.gosend.presenter.GosendDeliveryPresenter;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
@DecoratedWith(GosendDeliveryMapperDecorator.class)
public interface GosendDeliveryMapper {

    GosendDeliveryPresenter toGosendDeliveryPresenter(GosendDelivery gosendDelivery);

    List<GosendDeliveryPresenter> toGosendDeliveryPresenters(List<GosendDelivery> gosendDeliveries);
}
