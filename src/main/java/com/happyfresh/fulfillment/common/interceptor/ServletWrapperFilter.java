package com.happyfresh.fulfillment.common.interceptor;

import com.happyfresh.fulfillment.common.util.ApplicationUtil;
import com.happyfresh.fulfillment.common.util.ServletRequestWrapper;
import com.happyfresh.fulfillment.common.util.ServletResponseWrapper;
import com.happyfresh.fulfillment.common.util.UserInfoFetcher;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Instant;
import java.util.UUID;

@Component
@Order(1)
public class ServletWrapperFilter implements Filter {

    private final Logger LOGGER = LoggerFactory.getLogger(ServletWrapperFilter.class);

    @Autowired
    private UserInfoFetcher userInfoFetcher;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        Long startTime = Instant.now().toEpochMilli();

        ServletResponseWrapper responseWrapper = new ServletResponseWrapper((HttpServletResponse) response);
        ServletRequestWrapper requestWrapper = new ServletRequestWrapper((HttpServletRequest) request);

        setRequestAttribute(requestWrapper);
        chain.doFilter(requestWrapper, responseWrapper);

        String responseBody = manipulateResponseBody(startTime, responseWrapper, response);
        int responseStatus = ((HttpServletResponse) response).getStatus();
        sendLogMessage(responseStatus, responseBody, requestWrapper);
    }

    @Override
    public void destroy() {
    }

    private void setRequestAttribute(ServletRequestWrapper requestWrapper) throws IOException {
        StringBuilder url = new StringBuilder();
        url.append(requestWrapper.getRequestURL());
        if (StringUtils.isNotEmpty(requestWrapper.getQueryString())) {
            url.append('?');
            url.append(requestWrapper.getQueryString());
        }

        RequestContextHolder.currentRequestAttributes().setAttribute(ApplicationUtil.REQUEST_UUID, UUID.randomUUID().toString(), RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.currentRequestAttributes().setAttribute(ApplicationUtil.REQUEST_URL, url.toString(), RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.currentRequestAttributes().setAttribute(ApplicationUtil.REQUEST_BODY, requestWrapper.getAsString(), RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.currentRequestAttributes().setAttribute(ApplicationUtil.REQUEST_METHOD, requestWrapper.getMethod(), RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.currentRequestAttributes().setAttribute(ApplicationUtil.SND_VERSION_CODE, requestWrapper.getHeader("X-Snd-Version-Code"), RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.currentRequestAttributes().setAttribute(ApplicationUtil.X_FULFILLMENT_USER_TOKEN, requestWrapper.getHeader("X-Fulfillment-User-Token"), RequestAttributes.SCOPE_REQUEST);
    }

    private String manipulateResponseBody(Long startTime, ServletResponseWrapper responseWrapper, ServletResponse response) throws IOException {
        Long processInMillisecond = Instant.now().toEpochMilli() - startTime;
        String responseBody = responseWrapper.getAsString();
        responseBody = StringUtils.replaceOnce(responseBody, "{took}", processInMillisecond.toString());
        response.getWriter().write(responseBody);

        return responseBody;
    }

    private void sendLogMessage(int responseStatus, String responseBody, ServletRequestWrapper requestWrapper) {
        String requestUrl = (String) RequestContextHolder.currentRequestAttributes().getAttribute(ApplicationUtil.REQUEST_URL, RequestAttributes.SCOPE_REQUEST);
        if (!StringUtils.contains(requestUrl, "actuator/health")) {
            String requestUUID = (String) RequestContextHolder.currentRequestAttributes().getAttribute(ApplicationUtil.REQUEST_UUID, RequestAttributes.SCOPE_REQUEST);
            String requestBody = (String) RequestContextHolder.currentRequestAttributes().getAttribute(ApplicationUtil.REQUEST_BODY, RequestAttributes.SCOPE_REQUEST);
            String requestMethod = (String) RequestContextHolder.currentRequestAttributes().getAttribute(ApplicationUtil.REQUEST_METHOD, RequestAttributes.SCOPE_REQUEST);
            String sndVersionCode = (String) RequestContextHolder.currentRequestAttributes().getAttribute(ApplicationUtil.SND_VERSION_CODE, RequestAttributes.SCOPE_REQUEST);
            String userTokenHeader = (String) RequestContextHolder.currentRequestAttributes().getAttribute(ApplicationUtil.X_FULFILLMENT_USER_TOKEN, RequestAttributes.SCOPE_REQUEST);
            String locale = (String) RequestContextHolder.currentRequestAttributes().getAttribute(ApplicationUtil.LOCALE, RequestAttributes.SCOPE_REQUEST);

            ThreadContext.put("requestUUID", "[" + requestUUID + "]");
            ThreadContext.put("requestBody", "[" + requestBody + "]");
            ThreadContext.put("responseBody", "[" + responseBody + "]");
            ThreadContext.put("sndVersionCode", sndVersionCode);
            ThreadContext.put("locale", locale);
            ThreadContext.put("requestUserToken", userInfoFetcher.getFormattedRequestUserToken(userTokenHeader));
            String message = requestMethod + " " + requestUrl + " " + responseStatus;
            LOGGER.info(message);
            ThreadContext.clearAll();
        }
    }
}
