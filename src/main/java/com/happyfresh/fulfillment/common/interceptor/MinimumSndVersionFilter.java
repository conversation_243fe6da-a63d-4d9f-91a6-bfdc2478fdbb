package com.happyfresh.fulfillment.common.interceptor;

import com.happyfresh.fulfillment.common.property.AppProperty;
import com.happyfresh.fulfillment.entity.Role;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
@Order(Integer.MAX_VALUE-1)
public class MinimumSndVersionFilter extends OncePerRequestFilter {

    private final Logger LOGGER = LoggerFactory.getLogger(MinimumSndVersionFilter.class);

    @Autowired
    private AppProperty appProperty;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            boolean isShopperOrDriver = authentication.getAuthorities().stream().anyMatch(authority -> StringUtils.equals(Role.Name.SHOPPER.toString(), authority.getAuthority())) ||
                    authentication.getAuthorities().stream().anyMatch(authority -> StringUtils.equals(Role.Name.DRIVER.toString(), authority.getAuthority()));
            if (isShopperOrDriver) {
                String sndVersionCode = request.getHeader("X-Snd-Version-Code");
                if (sndVersionCode == null || Integer.parseInt(sndVersionCode) < Integer.parseInt(appProperty.getMinSndVersionCodeToTakeJob())) {
                    response.sendError(HttpServletResponse.SC_FORBIDDEN);
                } else {
                    filterChain.doFilter(request, response);
                }
            } else {
                filterChain.doFilter(request, response);
            }
        } else {
            filterChain.doFilter(request, response);
        }
    }

}
