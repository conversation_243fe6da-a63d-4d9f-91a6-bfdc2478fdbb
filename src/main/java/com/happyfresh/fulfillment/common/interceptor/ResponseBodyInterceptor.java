package com.happyfresh.fulfillment.common.interceptor;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.happyfresh.fulfillment.common.annotation.Publish;
import com.happyfresh.fulfillment.common.annotation.ResponseWrapper;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.service.WebhookPublisherService;
import com.happyfresh.fulfillment.common.util.ApplicationUtil;
import com.happyfresh.fulfillment.common.util.UserInfoFetcher;
import com.happyfresh.fulfillment.entity.Tenant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@RestControllerAdvice
public class ResponseBodyInterceptor implements ResponseBodyAdvice {

    Logger LOG = LoggerFactory.getLogger(ResponseBodyInterceptor.class);

    @Autowired
    private WebhookPublisherService webhookPublisherService;

    @Autowired
    private UserInfoFetcher userInfoFetcher;

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        if (ApplicationUtil.isDocumentationResource(request.getURI().toString()))
            return body;

        ResponseWrapper responseWrapper = returnType.getMethodAnnotation(ResponseWrapper.class);
        if (responseWrapper != null && responseWrapper.ignore())
            return body;

        ObjectNode wrapperNode = JsonNodeFactory.instance.objectNode();
        wrapperNode.put("took", "{took}");

        if (body != null) {
            String rootName = responseWrapper != null ? responseWrapper.rootName() : "response";
            ObjectMapper mapper = new ObjectMapper();
            JsonNode bodyNode = mapper.convertValue(body, JsonNode.class);
            wrapperNode.set(rootName, bodyNode);

            Publish publish = returnType.getMethodAnnotation(Publish.class);
            if (publish != null && ((ServletServerHttpResponse) response).getServletResponse().getStatus() < 207) {
                publishWebhook(publish, bodyNode, request.getURI().getPath(), rootName);
            }
        }

        return wrapperNode;
    }

    private void publishWebhook(Publish publish, JsonNode bodyNode, String path, String rootName) {
        ObjectNode wrapperNode = JsonNodeFactory.instance.objectNode();
        wrapperNode.set(rootName, bodyNode);

        Tenant tenant = userInfoFetcher.getTenant();
        WebhookType webhookType = publish.value();
        LOG.info(String.format("Calling %s webhook", webhookType.toString()));
        LOG.info("tenant = " + (tenant != null ? tenant.getId().toString() : "empty"));

        webhookPublisherService.publish(webhookType, wrapperNode, path, tenant.getId());
    }

}
