package com.happyfresh.fulfillment.common.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.enabler.form.JubelioCreateSalesOrderForm;
import com.happyfresh.fulfillment.enabler.form.JubelioUpdateSalesOrderForm;
import com.happyfresh.fulfillment.enabler.presenter.JubelioInventoriesPresenter;
import com.happyfresh.fulfillment.enabler.presenter.JubelioSalesOrderPresenter;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.stockLocation.service.StockLocationService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

import javax.validation.Valid;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
public class JubelioApiService {

    @Lazy
    @Autowired
    private StockLocationService stockLocationService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper mapper;

    protected final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private static final String JUBELIO_API_BASE_URL = "https://api.jubelio.com";

    public JubelioInventoriesPresenter getInventories(StockLocation stockLocation) {
        HttpHeaders authHeader = buildAuthHeader(stockLocation);
        if (authHeader == null)
            return null;

        try {
            return callGetInventoriesApi(authHeader);
        } catch (RestClientResponseException restErr) {
            if (restErr.getRawStatusCode() == 401) { // Handle expired token
                try {
                    stockLocation = stockLocationService.clearJubelioLastTokenUsed(stockLocation);
                    return callGetInventoriesApi(buildAuthHeader(stockLocation));
                } catch (RestClientResponseException e) {
                    return null;
                }
            }

            return null;
        }
    }

    private JubelioInventoriesPresenter callGetInventoriesApi(HttpHeaders headers) {
        return this.restTemplate.exchange(
                JUBELIO_API_BASE_URL + "/inventory/",
                HttpMethod.GET,
                new HttpEntity<>(headers),
                JubelioInventoriesPresenter.class
        ).getBody();
    }

    public Optional<JubelioSalesOrderPresenter> getSalesOrder(Long salesOrderId, StockLocation stockLocation) {
        HttpHeaders authHeader = buildAuthHeader(stockLocation);
        if (authHeader == null)
            return Optional.empty();

        JubelioSalesOrderPresenter result;
        try {
            result = callGetSalesOrderApi(salesOrderId, authHeader);
            return Optional.of(result);
        } catch (RestClientResponseException restErr) {
            if (restErr.getRawStatusCode() == 401) { // Handle expired token
                try {
                    stockLocation = stockLocationService.clearJubelioLastTokenUsed(stockLocation);
                    result = callGetSalesOrderApi(salesOrderId, buildAuthHeader(stockLocation));
                    return Optional.of(result);
                } catch (RestClientResponseException e) {
                    return Optional.empty();
                }
            }

            return Optional.empty();
        }
    }

    private JubelioSalesOrderPresenter callGetSalesOrderApi(Long salesOrderId, HttpHeaders headers) {
        return this.restTemplate.exchange(
                JUBELIO_API_BASE_URL + "/sales/orders/" + salesOrderId,
                HttpMethod.GET,
                new HttpEntity<>(headers),
                JubelioSalesOrderPresenter.class
        ).getBody();
    }

    public Long createSalesOrder(@Valid JubelioCreateSalesOrderForm form, StockLocation stockLocation) {
        HttpHeaders header = buildAuthHeader(stockLocation);
        if (header == null)
            return null;

        String body;
        try {
            body = mapper.writer().writeValueAsString(form);
        } catch (IOException e) {
            LOGGER.error(e.getMessage(), e);
            return null;
        }

        try {
            return callCreateSalesOrderApi(body, header);
        } catch (RestClientResponseException restErr) {
            if (restErr.getRawStatusCode() == 401) { // Handle expired token
                try {
                    stockLocation = stockLocationService.clearJubelioLastTokenUsed(stockLocation);
                    return callCreateSalesOrderApi(body, buildAuthHeader(stockLocation));
                } catch (RestClientResponseException rErr) {
                    return null;
                }
            }

            return null;
        }
    }

    private Long callCreateSalesOrderApi(String body, HttpHeaders header) {
        ResponseEntity<String> response = this.restTemplate.exchange(
                JUBELIO_API_BASE_URL + "/sales/orders/",
                HttpMethod.POST,
                new HttpEntity<>(body, header),
                String.class
        );

        try {
            return mapper.readTree(response.getBody()).get("id").asLong();
        } catch (IOException e) {
            LOGGER.error(e.getMessage(), e);
            return null;
        }
    }

    public Boolean editSalesOrder(Long salesOrderId, @Valid JubelioUpdateSalesOrderForm form, StockLocation stockLocation) {
        form.setSalesorderId(salesOrderId);

        HttpHeaders header = buildAuthHeader(stockLocation);
        if (header == null)
            return false;

        String body;
        try {
            body = mapper.writer().writeValueAsString(form);
        } catch (IOException e) {
            LOGGER.error(e.getMessage(), e);
            return false;
        }

        try {
            return callEditSalesOrderApi(salesOrderId, body, header);
        } catch (RestClientResponseException restErr) {
            if (restErr.getRawStatusCode() == 401) { // Handle expired token
                try {
                    stockLocation = stockLocationService.clearJubelioLastTokenUsed(stockLocation);
                    return callEditSalesOrderApi(salesOrderId, body, buildAuthHeader(stockLocation));
                } catch (RestClientResponseException rErr) {
                    return false;
                }
            }

            return false;
        }
    }

    private Boolean callEditSalesOrderApi(Long salesOrderId, String body, HttpHeaders header) {
        ResponseEntity<String> response = this.restTemplate.exchange(
                JUBELIO_API_BASE_URL + "/sales/orders/",
                HttpMethod.POST,
                new HttpEntity<>(body, header),
                String.class
        );

        try {
            return mapper.readTree(response.getBody()).get("id").asLong() == salesOrderId;
        } catch (IOException e) {
            LOGGER.error(e.getMessage(), e);
            return false;
        }
    }

    public Boolean markSalesOrderAsComplete(Long salesOrderId, StockLocation stockLocation) {
        HttpHeaders header = buildAuthHeader(stockLocation);
        if (header == null)
            return false;

        String body = "{\"ids\": [\"" + salesOrderId + "\"]}";
        try {
            return callMarkSalesOrderAsCompleteApi(body, header);
        } catch (RestClientResponseException restErr) {
            if (restErr.getRawStatusCode() == 401) { // Handle expired token
                try {
                    stockLocation = stockLocationService.clearJubelioLastTokenUsed(stockLocation);
                    return callMarkSalesOrderAsCompleteApi(body, buildAuthHeader(stockLocation));
                } catch (RestClientResponseException rErr) {
                    return false;
                }
            }

            return false;
        }
    }

    private Boolean callMarkSalesOrderAsCompleteApi(String body, HttpHeaders headers) {
        ResponseEntity<String> response = this.restTemplate.exchange(
                JUBELIO_API_BASE_URL + "/sales/orders/mark-as-complete",
                HttpMethod.POST,
                new HttpEntity<>(body, headers),
                String.class
        );

        return response.getStatusCode().is2xxSuccessful();
    }

    private HttpHeaders buildAuthHeader(StockLocation stockLocation) {
        if (stockLocation == null)
            throw new IllegalArgumentException("Stock location cannot be null");

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("authorization", getTokenOrLogin(stockLocation));
            return headers;
        } catch (JubelioAuthException e) {
            LOGGER.error(e.getMessage(), e);
            return null;
        }
    }

    private String getTokenOrLogin(StockLocation stockLocation) throws JubelioAuthException {
        String token = stockLocationService.getJubelioLastTokenUsed(stockLocation);
        if (!StringUtils.isBlank(token))
            return token;

        String email = stockLocation.getJubelioEmail();
        String password = stockLocationService.getJubelioPassword(stockLocation);
        if (StringUtils.isBlank(email) || StringUtils.isBlank(password))
            throw new IllegalStateException("Jubelio email & password cannot be empty");

        token = login(email, password);

        try {
            stockLocationService.setAndSaveJubelioLastTokenUsed(stockLocation, token);
        } catch (Exception e) {
            LOGGER.error("Failed to save Jubelio token. " + e.getMessage(), e);
        }
        return token;
    }

    private String login(String email, String password) throws JubelioAuthException {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        Map<String, String> body = new HashMap<>();
        body.put("email", email);
        body.put("password", password);

        try {
            ResponseEntity<String> response = this.restTemplate.postForEntity(
                    JUBELIO_API_BASE_URL + "/login",
                    new HttpEntity<>(body, headers),
                    String.class
            );
            return mapper.readTree(response.getBody()).get("token").asText();
        } catch (RestClientResponseException e1) {
            throw new JubelioAuthException("Login failed " + e1.getRawStatusCode() + ": " + e1.getResponseBodyAsString());
        } catch (IOException e2) {
            throw new JubelioAuthException("Login API returns unknown response body, " + e2.getMessage(), e2);
        }
    }

    private static class JubelioAuthException extends Exception {
        private JubelioAuthException(String errMessage, Throwable err) {
            super(errMessage, err);
        }
        private JubelioAuthException(String errMessage) {
            super(errMessage);
        }
    }
}
