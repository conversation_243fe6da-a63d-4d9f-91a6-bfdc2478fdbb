package com.happyfresh.fulfillment.common.service;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;
import com.happyfresh.fulfillment.common.mapper.FleetTrackingMessageDataMapper;
import com.happyfresh.fulfillment.common.service.bean.FleetTrackingMessage;
import com.happyfresh.fulfillment.common.service.bean.FleetTrackingMessageData;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import org.jobrunr.scheduling.JobScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Optional;

@Service
public class FleetTrackingService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private FleetTrackingMessageDataMapper dataMapper;

    @Autowired
    private ShipmentRepository shipmentRepository;
    
    @Autowired
    private JobScheduler jobScheduler;
    
    @Autowired
    private ObjectMapper objectMapper;

    public enum EventName {
        @JsonProperty("delivering")
        DELIVERING,
        @JsonProperty("found_address")
        FOUND_ADDRESS,
        @JsonProperty("failed_delivery")
        FAILED_DELIVERY,
        @JsonProperty("cancel_order")
        CANCEL_ORDER;
    }

    @Transactional
    public void syncFleetTracking(String shipmentNumber, EventName eventName) {
        try {
            FleetTrackingMessage message = createSyncMessage(shipmentNumber, eventName);
            publishFleetSyncMessage(message);
        } catch (JsonProcessingException | UnprocessableEntityException e) {
            logger.error("Failed to publish fleet tracking sync", e);
        }
    }

    private FleetTrackingMessage createSyncMessage(String shipmentNumber, EventName eventName) {
        Shipment shipment = shipmentRepository.findByNumber(shipmentNumber);

        Optional<Job> optionalDeliveryJob = shipment.getDeliveryOrRangerJob();
        if (!optionalDeliveryJob.isPresent()) throw new UnprocessableEntityException("Delivery job not found");

        Batch deliveryBatch = optionalDeliveryJob.get().getBatch();
        User driver = deliveryBatch.getUser();
        if (driver == null) throw new UnprocessableEntityException("Driver not found");

        Optional<Role> optionalRole = driver.getRoles().stream()
            .filter( r -> Arrays.asList(Role.Name.ON_DEMAND_RANGER, Role.Name.DRIVER).contains(r.getName()))
            .findFirst();
        if (!optionalRole.isPresent()) throw new UnprocessableEntityException("Batch not assigned to driver or ranger role");
        Role role = optionalRole.get();

        Slot slot = shipment.getSlot();
        StockLocation stockLocation = slot.getStockLocation();

        FleetTrackingMessageData data = dataMapper.from(shipment, deliveryBatch, slot, stockLocation, driver, role);

        Agent driverAgent = driver.getAgent();
        if (driverAgent == null) throw new UnprocessableEntityException("No agent for driver");

        FleetTrackingMessage message = new FleetTrackingMessage();
        message.setData(data);
        message.setUserId(driver.getId());
        message.setDeviceId(driverAgent.getDeviceId());
        message.setEventName(eventName);

        return message;
    }

    private void publishFleetSyncMessage(FleetTrackingMessage message) throws JsonProcessingException {
        // Replace ActiveMQ with JobRunr
        String serializedMessage = objectMapper.writeValueAsString(message);
        jobScheduler.enqueue(() -> processFleetTrackingMessage(serializedMessage));
        logger.info("Scheduled fleet tracking sync job with JobRunr for user ID: {}", message.getUserId());
    }
    
    public void processFleetTrackingMessage(String serializedMessage) {
        try {
            // Process the fleet tracking message
            // This is where you would implement the actual processing logic
            // that was previously handled by the ActiveMQ consumer
            logger.info("Processing fleet tracking message: {}", serializedMessage);
            
            // Example implementation:
            FleetTrackingMessage message = objectMapper.readValue(serializedMessage, FleetTrackingMessage.class);
            
            // Add your processing logic here
            // For example, sending to an external fleet tracking system
            
            logger.info("Successfully processed fleet tracking message for user ID: {}", message.getUserId());
        } catch (Exception e) {
            logger.error("Error processing fleet tracking message", e);
        }
    }
}
