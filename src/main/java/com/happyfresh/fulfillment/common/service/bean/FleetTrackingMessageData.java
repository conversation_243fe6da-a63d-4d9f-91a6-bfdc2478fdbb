package com.happyfresh.fulfillment.common.service.bean;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class FleetTrackingMessageData {

    private String addressCity;

    private String addressDetail;

    private String addressInstruction;

    private String addressLine;

    private String addressName;

    private String addressNumber;

    private String addressPhone;

    private Long batchId;

    private String fleetEmail;

    private String fleetFirstName;

    private String fleetLastName;

    private String fleetName;

    private String fleetPhoneNumber;

    private String fleetRoles;

    private String orderNumber;

    private Long shipmentId;

    private String shipmentNumber;

    private String slotEndTime;

    private String slotStartTime;

    private Double addressLat;

    private Double addressLon;

    private Double stockLocationLat;

    private Double stockLocationLon;

    private String fleetProfilePictureUrl;
}
