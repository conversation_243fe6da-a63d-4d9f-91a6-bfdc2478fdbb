package com.happyfresh.fulfillment.common.service;

import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.jpa.Addressable;
import com.happyfresh.fulfillment.common.model.RoundTripDistance;
import com.happyfresh.fulfillment.common.model.RoundTripTime;
import com.happyfresh.fulfillment.common.util.ApplicationUtil;
import com.happyfresh.fulfillment.common.util.DistanceUtil;
import com.happyfresh.fulfillment.entity.LalamoveServiceType;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.entity.es.SimpleRouteEdge;
import com.happyfresh.fulfillment.gosend.presenter.GosendGetPriceEstimatePresenter;
import com.happyfresh.fulfillment.gosend.service.api.GosendApiFormService;
import com.happyfresh.fulfillment.gosend.service.api.GosendApiService;
import com.happyfresh.fulfillment.lalamove.model.LalamoveServiceTypeEnum;
import com.happyfresh.fulfillment.lalamove.presenter.v3.LalamoveV3GetQuotationPresenter;
import com.happyfresh.fulfillment.lalamove.service.LalamoveService;
import com.happyfresh.fulfillment.lalamove.service.api.v3.LalamoveApiV3Service;
import io.searchbox.core.SearchResult;
import org.elasticsearch.common.geo.GeoPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class DistanceService {

    private static final Logger LOG = LoggerFactory.getLogger(DistanceService.class);

    @Autowired
    private GoogleApiService googleApiService;

    @Autowired
    private GraphhopperApiService graphhopperApiService;

    @Autowired
    private ESClientService esClientService;

    @Autowired
    private TemplateEngine templateEngine;

    @Autowired
    private GosendApiService gosendApiService;

    @Autowired
    private GosendApiFormService gosendApiFormService;

    @Autowired
    private LalamoveService lalamoveService;

    @Autowired
    private LalamoveApiV3Service lalamoveApiV3Service;

    @Autowired
    private ApplicationContext applicationContext;

    @Value("${ENABLE_GDM:false}")
    private boolean enableGDM;

    public Double getDistance(Shipment shipment, StockLocation stockLocation, boolean inKM, boolean isOnDemandRequest) {
        LOG.info("[Cache] getDistance 61");
        double distance;
        if (stockLocation.getCluster().isEnableLocus() && !isOnDemandRequest) {
            distance = DistanceUtil.airDistanceInKM(stockLocation.getLatLon(), shipment.getLatLon());
        } else {
            distance = getDistance(stockLocation.getLatLon(), shipment.getLatLon(), inKM, stockLocation, shipment.getOrderNumber());
        }
        return distance;
    }

    private Double getDistance(GeoPoint origin, GeoPoint destination, boolean inKM, StockLocation stockLocation, String orderNumber) {
        LOG.info("[Cache] getDistance 72");
        double distance = getDistance(origin.getLat(), origin.getLon(), destination.getLat(), destination.getLon(), stockLocation, orderNumber);
        if (inKM) {
            distance = distance / 1000;
        }
        return distance;
    }

    private double getDistance(double originLat, double originLon, double destinationLat, double destinationLon, StockLocation stockLocation, String orderNumber) {
        LOG.info("[Cache] getDistance 82");
        return getDistance(originLat, originLon, destinationLat, destinationLon, true, stockLocation, false, orderNumber);
    }

    private double getDistance(double originLat, double originLon, double destinationLat, double destinationLon, boolean avoidTolls, StockLocation stockLocation, boolean isOnDemandRequest, String orderNumber) {
        Double distance = null;
        try {
            distance = getDistanceFromGosend(originLat, originLon, destinationLat, destinationLon);
            LOG.info("[Cache] getDistance 91 {}", distance);
        } catch(Exception e) {
            LOG.error("Price Estimate Error - switch to lalamove distance for order number {} {}", orderNumber, e);
        }

        if (distance == null) {
            try {
                distance = getDistanceFromLalamove(originLat, originLon, destinationLat, destinationLon, stockLocation);
            } catch (Exception e) {
                LOG.error("Price Estimate Error - switch to air distance for order number {} {}", orderNumber, e);
            }
        }

        if (distance == null) {
            distance = DistanceUtil.distance(new GeoPoint(originLat, originLon), new GeoPoint(destinationLat, destinationLon));
        }

        if ((distance/1000) > stockLocation.getMaximumDeliveryRadius()) {
            return distance;
        }

        GraphhopperApiService.VehicleType graphhopperVehicleType = stockLocation.getGraphhopperVehicleType();
        // Try fetch from ES
        SimpleRouteEdge simpleRouteEdge = fetchFromElastic(
                new GeoPoint(originLat, originLon),
                new GeoPoint(destinationLat, destinationLon),
                true,
                graphhopperVehicleType,
                orderNumber
        );
        if (simpleRouteEdge != null)
            return simpleRouteEdge.getDistance();

        // fallback
        return distance;
    }

    private Double getDistanceFromGosend(double originLat, double originLon, double destinationLat, double destinationLon) {
        GosendApiFormService.GosendOriginAndDestination gosendOriginAndDestination = gosendApiFormService.getOriginAndDestination( originLat, originLon, destinationLat, destinationLon);
        GosendGetPriceEstimatePresenter gosendGetPriceEstimatePresenter = gosendApiService.getPriceEstimate(gosendOriginAndDestination);

        if (gosendGetPriceEstimatePresenter == null) {
            return null;
        }

        LOG.info("[Distance] Distance from Gosend {}", gosendGetPriceEstimatePresenter.getInstant().getDistance());

        return BigDecimal.valueOf(gosendGetPriceEstimatePresenter.getInstant().getDistance()).multiply(BigDecimal.valueOf(1000)).doubleValue();
    }

    private Double getDistanceFromLalamove(double originLat, double originLon, double destinationLat, double destinationLon, StockLocation stockLocation) {
        LalamoveServiceType serviceType = lalamoveService.getServiceTypeByEnum(LalamoveServiceTypeEnum.MOTORCYCLE,null, stockLocation);
        String[] availableSpecialRequests =  serviceType.getAvailableSpecialRequests();;

        LalamoveV3GetQuotationPresenter presenter = lalamoveApiV3Service.getQuotationWithCoordinates(LalamoveServiceTypeEnum.MOTORCYCLE, availableSpecialRequests, LocalDateTime.now().plusHours(1), stockLocation,
                originLat, originLon, destinationLat, destinationLon);

        if (presenter == null) {
            return null;
        }

        LalamoveV3GetQuotationPresenter.Distance distance = presenter.getData().getDistance();
        LOG.info("[Distance] Distance from Lalamove {} {}", distance.getValue(), distance.getUnit());

        if (distance.getUnit() == "km")
            return Double.valueOf(distance.getValue()) * 1000;
        else
            return Double.valueOf(distance.getValue());
    }

    public long getDurationInMinutes(double originLat, double originLon, double destinationLat, double destinationLon, boolean avoidTolls, StockLocation stockLocation, String orderNumber) {
        return getDuration(originLat, originLon, destinationLat, destinationLon, avoidTolls, stockLocation, orderNumber) / 60;
    }

    private long getDuration(double originLat, double originLon, double destinationLat, double destinationLon, boolean avoidTolls, StockLocation stockLocation, String orderNumber) {
        GeoPoint origin = new GeoPoint(originLat, originLon);
        GeoPoint destination = new GeoPoint(destinationLat, destinationLon);

        double distance = DistanceUtil.distance(origin,destination);
        if ((distance/1000) > stockLocation.getMaximumDeliveryRadius()) {
            return Long.MAX_VALUE;
        }

        // Try fetch from ES
        SimpleRouteEdge simpleRouteEdge = fetchFromElastic(origin, destination, stockLocation.isEnableGraphhopper(), stockLocation.getGraphhopperVehicleType(), orderNumber);
        if (simpleRouteEdge != null)
            return simpleRouteEdge.getDurationInTraffic();

        // We're assuming duration in seconds would be 24 minutes
        long durationInSeconds = 24*60;
        return durationInSeconds;
    }

    public RoundTripDistance getRoundTripDistance(Shipment shipment, StockLocation stockLocation, boolean avoidTolls) {
        LOG.info("[Cache] getRoundTripDistance");
        return new RoundTripDistance(shipment.getShipDistance(), shipment.getShipDistance());
    }

    public double getDistance(GeoPoint origin, GeoPoint destination, StockLocation stockLocation) {
        LOG.info("[Cache] getDistance 197");
        return getDistance(origin.getLat(), origin.getLon(), destination.getLat(), destination.getLon(), false, stockLocation, false, null);
    }

    public RoundTripTime getRoundTripTime(Shipment shipment, StockLocation stockLocation, boolean avoidTolls) {
        LOG.info("[Cache] getRoundTripTime");
        GeoPoint origin = stockLocation.getLatLon();
        GeoPoint destination = shipment.getLatLon();

        long leaveTime = getDuration(origin.getLat(), origin.getLon(), destination.getLat(), destination.getLon(), avoidTolls, stockLocation, shipment.getOrderNumber());
        long returnTime = leaveTime;

        return new RoundTripTime(leaveTime, returnTime);
    }

    public List<SimpleRouteEdge> fetchAndSaveToElastic(List<Addressable> origins, List<Addressable> destinations, boolean avoidTolls, StockLocation stockLocation) {
        LOG.info("[Cache] fetchAndSaveToElastic");
        List<SimpleRouteEdge> simpleRouteEdges = Lists.newArrayList();

        final GraphhopperApiService.VehicleType vehicleType = stockLocation.getGraphhopperVehicleType();
        for (int i = 0; i < origins.size(); i++) {
            for (int j = 0; j < destinations.size(); j++) {
                // TODO Change this from value in cluster
                long durationInSecond = 24*60; //We're assuming distance always 24 minutes
                final SimpleRouteEdge simpleRouteEdge = new SimpleRouteEdge(
                        origins.get(i).getLatLon(),
                        destinations.get(j).getLatLon(),
                        durationInSecond,
                        durationInSecond,
                        (long) 2000, //We're assuming distance always 2kms
                        SimpleRouteEdge.Source.GRAPHHOPPER.toString(),
                        vehicleType == null ? null : vehicleType.toString()
                );
                simpleRouteEdges.add(simpleRouteEdge);
            }
        }

        return simpleRouteEdges;
    }

    public SimpleRouteEdge fetchFromElastic(GeoPoint origin, GeoPoint destination, boolean graphhopperEnabled, GraphhopperApiService.VehicleType graphhopperVehicleType, String orderNumber) {
        try {
            Map<String, Object> queryInfo = new HashMap<>();
            queryInfo.put("distance", ApplicationUtil.GEO_SEARCH_RANGE);
            queryInfo.put("origin", origin);
            queryInfo.put("destination", destination);
            queryInfo.put("source", graphhopperEnabled ? SimpleRouteEdge.Source.GRAPHHOPPER.toString() : SimpleRouteEdge.Source.GOOGLE.toString());
            queryInfo.put("graphhopper_vehicle_type", graphhopperVehicleType == null ? null : graphhopperVehicleType.toString());
            queryInfo.put("timestamp", LocalDateTime.now().minusMonths(ApplicationUtil.GEO_SEARCH_RANGE_TIMESTAMP).atZone(ZoneId.of("UTC")).toEpochSecond());

            final String queryString = templateEngine.getTemplateString(ApplicationUtil.ROUTE_EDGE_QUERY, queryInfo);

            SearchResult result = esClientService.searchDocument(queryString, SimpleRouteEdge.class);
            List<SimpleRouteEdge> results = Collections.EMPTY_LIST;
            if (result.isSucceeded()) {
                results = result.getSourceAsObjectList(SimpleRouteEdge.class, false);
            }

            if (results.isEmpty()) {
                return null;
            } else {
                return results.get(results.size()-1);
            }
        } catch (Exception ex) {
            LOG.error("Failed fetch from elastic ", ex);
            return null;
        }
    }
}
