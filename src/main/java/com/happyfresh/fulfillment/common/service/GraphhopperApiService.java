package com.happyfresh.fulfillment.common.service;

import com.graphhopper.api.*;
import com.graphhopper.util.shapes.GHPoint;
import com.happyfresh.fulfillment.common.jpa.Addressable;
import com.happyfresh.fulfillment.common.property.GraphhopperProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
public class GraphhopperApiService {

    private static final Logger LOG = LoggerFactory.getLogger(GraphhopperApiService.class);

    private GraphHopperMatrixWeb asyncClient;

    private GraphHopperMatrixWeb syncClient;

    @Autowired
    private GraphhopperProperty property;

    public enum VehicleType {
        CAR("car"),
        RACING_BIKE("racingbike");

        private String type;
        VehicleType(String vehicleType) {
            this.type = vehicleType;
        }

        @Override
        public String toString() {
            return type;
        }

        public static VehicleType fromString(String vehicleType) {
            return Arrays.stream(values())
                    .filter(value -> value.type.equals(vehicleType))
                    .findFirst().orElse(null);
        }
    }

    public static VehicleType getDefaultVehicleType() {
        return VehicleType.RACING_BIKE;
    }

    public double getDistanceInMeter(double originLat, double originLon, double destinationLat, double destinationLon, VehicleType vehicleType) throws Exception {
        LOG.info("[cache] getDistanceInMeter");
        LOG.info("[GH] getDistanceInMeter Vehicle Type {} ", vehicleType.toString());
        MatrixResponse matrixResponse = fetchDistanceMatrix(originLat, originLon, destinationLat, destinationLon, vehicleType);
        return matrixResponse.getDistance(0, 0);
    }

    @Cacheable(cacheNames = "expireAfterAccess10m")
    public MatrixResponse fetchDistanceMatrix(double originLat, double originLon, double destinationLat, double destinationLon, VehicleType vehicleType) throws Exception {
        GHMRequest ghmRequest = buildRequest(vehicleType);

        LOG.info("[GH] 1 fetchDistanceMatrix from Lat {}, Lon {}", originLat,  originLon);
        LOG.info("[GH] 1 fetchDistanceMatrix to Lat {}, Lon {}", destinationLat, destinationLon);

        ghmRequest.addFromPoint(new GHPoint(originLat, originLon));
        ghmRequest.addToPoint(new GHPoint(destinationLat, destinationLon));

        MatrixResponse response = buildSyncClient().route(ghmRequest);

        if (response.hasErrors())
            throw new Exception(response.getErrors().get(0).toString());
        return response;
    }

    public MatrixResponse fetchDistanceMatrix(List<Addressable> origins, List<Addressable> destinations, VehicleType vehicleType) throws Exception {
        GHMRequest ghmRequest = buildRequest(vehicleType);

        origins.forEach(ori -> {
            LOG.info("[GH] 2 fetchDistanceMatrix from Lat {}, Lon {}", ori.getLatLon().getLat(),  ori.getLatLon().getLon());
            ghmRequest.addFromPoint(new GHPoint(ori.getLatLon().getLat(), ori.getLatLon().getLon()));
        });
        destinations.forEach(dest -> {
            LOG.info("[GH] 2 fetchDistanceMatrix to Lat {}, Lon {}", dest.getLatLon().getLat(),  dest.getLatLon().getLon());
            ghmRequest.addToPoint(new GHPoint(dest.getLatLon().getLat(), dest.getLatLon().getLon()));
        });

        MatrixResponse response = buildAsyncClient().route(ghmRequest);

        if (response.hasErrors())
            throw new Exception(response.getErrors().get(0).toString());
        return response;
    }

    private GHMRequest buildRequest(VehicleType vehicleType) {
        GHMRequest ghmRequest = new GHMRequest();
        ghmRequest.addOutArray("distances");
        ghmRequest.addOutArray("times");

        if (vehicleType == null) {
            vehicleType = getDefaultVehicleType();
        }
        ghmRequest.setVehicle(vehicleType.toString());

        return ghmRequest;
    }

    private GraphHopperMatrixWeb buildAsyncClient() {
        if (asyncClient == null) {
            GHMatrixBatchRequester ghMatrixBatchRequester =  new GHMatrixBatchRequester();
            ghMatrixBatchRequester.setSleepAfterGET(300L);
            asyncClient = new GraphHopperMatrixWeb((GHMatrixAbstractRequester)(ghMatrixBatchRequester));
        }
        if (property == null) {
            property = new GraphhopperProperty();
        }

        asyncClient.setKey(property.getApiKey());
        return asyncClient;
    }

    private GraphHopperMatrixWeb buildSyncClient() {
        if (syncClient == null) {
            GHMatrixSyncRequester ghMatrixSyncRequester =  new GHMatrixSyncRequester();
            syncClient = new GraphHopperMatrixWeb((GHMatrixAbstractRequester)(ghMatrixSyncRequester));
        }
        if (property == null) {
            property = new GraphhopperProperty();
        }

        syncClient.setKey(property.getApiKey());
        return syncClient;
    }

}
