package com.happyfresh.fulfillment.common.service;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.graphhopper.jsprit.core.algorithm.AlgorithmUtil;
import com.graphhopper.jsprit.core.algorithm.VehicleRoutingAlgorithm;
import com.graphhopper.jsprit.core.algorithm.box.Jsprit;
import com.graphhopper.jsprit.core.algorithm.recreate.VariableTransportCostCalculator;
import com.graphhopper.jsprit.core.algorithm.state.StateId;
import com.graphhopper.jsprit.core.algorithm.state.StateManager;
import com.graphhopper.jsprit.core.algorithm.termination.PrematureAlgorithmTermination;
import com.graphhopper.jsprit.core.algorithm.termination.TimeTermination;
import com.graphhopper.jsprit.core.problem.Location;
import com.graphhopper.jsprit.core.problem.VehicleRoutingProblem;
import com.graphhopper.jsprit.core.problem.constraint.ConstraintManager;
import com.graphhopper.jsprit.core.problem.constraint.SoftActivityConstraint;
import com.graphhopper.jsprit.core.problem.constraint.VehicleDependentTimeWindowConstraints;
import com.graphhopper.jsprit.core.problem.job.Delivery;
import com.graphhopper.jsprit.core.problem.job.Job;
import com.graphhopper.jsprit.core.problem.solution.VehicleRoutingProblemSolution;
import com.graphhopper.jsprit.core.problem.solution.route.VehicleRoute;
import com.graphhopper.jsprit.core.problem.solution.route.activity.TimeWindow;
import com.graphhopper.jsprit.core.problem.vehicle.Vehicle;
import com.graphhopper.jsprit.core.problem.vehicle.VehicleImpl;
import com.graphhopper.jsprit.core.problem.vehicle.VehicleType;
import com.graphhopper.jsprit.core.problem.vehicle.VehicleTypeImpl;
import com.graphhopper.jsprit.core.reporting.SolutionPrinter;
import com.graphhopper.jsprit.core.util.Solutions;
import com.graphhopper.jsprit.core.util.VehicleRoutingTransportCostsMatrix;
import com.happyfresh.fulfillment.common.jpa.Addressable;
import com.happyfresh.fulfillment.common.tracking.SlotOptimizationEventTracker;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.common.util.SolutionPrinterUtil;
import com.happyfresh.fulfillment.common.util.VrpUtil;
import com.happyfresh.fulfillment.common.vrp.*;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.slot.model.RouteResult;
import com.happyfresh.fulfillment.slot.model.VRPParam;
import com.happyfresh.fulfillment.slot.model.VRPResult;
import com.happyfresh.fulfillment.slot.model.VehicleBatchTime;
import com.happyfresh.fulfillment.slot.service.SlotReservedService;
import lombok.AllArgsConstructor;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.graphhopper.jsprit.core.problem.VehicleRoutingProblem.FleetSize.FINITE;
import static com.graphhopper.jsprit.core.problem.VehicleRoutingProblem.FleetSize.INFINITE;

@Service
public class VRPService {
    private final Logger LOGGER = LoggerFactory.getLogger(VRPService.class);

    private static final String MUST_NOT_BE_NULL = "The attribute must not be null!";
    private static final int DIMENSION_TOTAL_VOLUME = 0;
    private static final int DIMENSION_TOTAL_MAX_ORDER = 1;
    public static final int DIMENSION_TOTAL_SHOPPING_TIME = 2;
    private static final int MINIMAL_SOLUTION_ACCEPTED = 1;
    private static final int MINIMAL_SOLUTION_ACCEPTED_FOR_SET_SLOT = 5;

    public static final String USER_DATA_SLOT_ID = "SlotID";
    public static final String USER_DATA_SLOT_TIME = "SlotTime";
    public static final String USER_DATA_ORDER_NUMBER = "OrderNumber";
    public static final String USER_DATA_SHIFT_ID = "ShiftID";
    public static final String USER_DATA_SHIFT_START_TIME = "ShiftStartTime";
    public static final String USER_DATA_VEHICLE = "Vehicle";
    public static final String USER_DATA_STOCK_LOCATION_ID = "StockLocationID";
    public static final String USER_DATA_IS_SPECIAL_STOCK_LOCATION = "IsSpecialStockLocation";
    public static final String USER_DATA_SHIPMENT_ID = "ShipmentID";
    public static final String USER_DATA_SHOPPING_DURATION = "ShoppingDuration";

    public static final String USER_DATA_QUADRANT = "Quadrant";

    @Autowired
    private CostMatrixService costMatrixService;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private ApplicationContext applicationContext;

    public VRPResult deliverySolution(VRPParam param) throws Exception {
        Assert.notNull(param.getComplexId(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getFreeDriverCount(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getFreeDriverWithEmptyBatchCount(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getMaxDeliveryVolumeInML(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getMaxDeliveryHandoverInSeconds(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getSlot(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getModifiableShipments(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getAvoidTolls(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getSpecialStore(), MUST_NOT_BE_NULL);
        Assert.notNull(param.isReservedSlot(), MUST_NOT_BE_NULL);

        if (param.getSpecialStore()) {
            Assert.notNull(param.getShopperAveragePickingTimePerUniqItem(), MUST_NOT_BE_NULL);
            Assert.notNull(param.getShoppingServiceTime(), MUST_NOT_BE_NULL);
        }

        if (param.getFreeDriverCount() <= 0) {
            return solutionWithUnassignedJob();

        } else {
            List<Vehicle> drivers = Lists.newArrayList();
            List<com.graphhopper.jsprit.core.problem.job.Service> services = Lists.newArrayList();

            final VehicleType driverDefaultType = VehicleTypeImpl.Builder.newInstance("driver").setCostPerDistance(0)
                    .addCapacityDimension(DIMENSION_TOTAL_VOLUME, param.getMaxDeliveryVolumeInML())
                    .addCapacityDimension(DIMENSION_TOTAL_MAX_ORDER, Integer.MAX_VALUE)
                    .setCostPerTransportTime(1).build();
            final VehicleType driverUnlimitedVolumeType = VehicleTypeImpl.Builder.newInstance("driver").setCostPerDistance(0)
                    .addCapacityDimension(DIMENSION_TOTAL_VOLUME, Integer.MAX_VALUE)
                    .addCapacityDimension(DIMENSION_TOTAL_MAX_ORDER, 1)
                    .setCostPerTransportTime(1).build();

            long shipmentWithExceedVolume = param.getModifiableShipments().stream()
                    .filter(s -> !s.isNewObject() && s.getTotalVolumeInML() > param.getMaxDeliveryVolumeInML())
                    .count();

            long freeDriverWithEmptyBatchCount = param.getFreeDriverWithEmptyBatchCount() + shipmentWithExceedVolume;
            double maxDeliveryRoundTripInSeconds = param.getSlot().durationInSeconds();
            for (int i = 0; i < param.getFreeDriverCount(); i++) {
                VehicleImpl.Builder vehicleBuilder = VehicleImpl.Builder
                        .newInstance(Integer.toString(i + 1))
                        .setStartLocation(Location.newInstance(param.getComplexId()))
                        .setLatestArrival(maxDeliveryRoundTripInSeconds);

                if (freeDriverWithEmptyBatchCount > 0) {
                    vehicleBuilder.setType(driverUnlimitedVolumeType);
                    freeDriverWithEmptyBatchCount--;
                } else {
                    vehicleBuilder.setType(driverDefaultType);
                }

                final Vehicle vehicle = vehicleBuilder.build();
                drivers.add(vehicle);
            }

            for (Shipment shipment : param.getModifiableShipments()) {
                final String id = shipment.getComplexId();
                final com.graphhopper.jsprit.core.problem.job.Service.Builder serviceBuilder = com.graphhopper.jsprit.core.problem.job.Service.Builder.newInstance(id);

                Double serviceTime = param.getMaxDeliveryHandoverInSeconds();
                if (param.getSpecialStore()) {
                    int totalItem = shipment.getTotalItemCount(param.isCompareWithAverageItem());
                    long shoppingTimeInSecond = (long) (totalItem * param.getShopperAveragePickingTimePerUniqItem() + param.getShoppingServiceTime()) * 60;
                    serviceTime += shoppingTimeInSecond;
                }

                final com.graphhopper.jsprit.core.problem.job.Service service = serviceBuilder
                        .addSizeDimension(DIMENSION_TOTAL_VOLUME, (int) shipment.getTotalVolumeInML())
                        .addSizeDimension(DIMENSION_TOTAL_MAX_ORDER, 1)
                        .setLocation(Location.newInstance(id))
                        .setServiceTime(serviceTime)
                        .build();

                services.add(service);
            }

            final List<Addressable> shipmentAddressables = Lists.newArrayList(param.getModifiableShipments());
            final List<String> orderNumbers = param.getModifiableShipments().stream().map(shipment -> shipment.getOrderNumber()).collect(Collectors.toList());
            VehicleRoutingTransportCostsMatrix costsMatrix = costMatrixService.deliveryCostMatrix(param.getSlot(), shipmentAddressables, param.getAvoidTolls(), param.getOrderNumber(), orderNumbers);
            final VehicleRoutingProblem vrp = VehicleRoutingProblem.Builder.newInstance()
                    .setFleetSize(FINITE)
                    .setRoutingCost(costsMatrix)
                    .addAllVehicles(drivers)
                    .addAllJobs(services).build();

            VehicleRoutingAlgorithm vehicleRoutingAlgorithm = Jsprit.Builder.newInstance(vrp)
                    .setProperty(Jsprit.Parameter.THREADS, "5")
                    .buildAlgorithm();

            PrematureAlgorithmTermination solutionTermination = new SolutionIterationTermination(MINIMAL_SOLUTION_ACCEPTED_FOR_SET_SLOT);
            if (!param.isReservedSlot())
                solutionTermination = new SolutionIterationTermination(MINIMAL_SOLUTION_ACCEPTED);

            vehicleRoutingAlgorithm.setPrematureAlgorithmTermination(solutionTermination);

            VehicleRoutingProblemSolution solution = Solutions.bestOf(vehicleRoutingAlgorithm.searchSolutions());
//            SolutionPrinter.print(vrp, solution, SolutionPrinter.Print.VERBOSE);

            return new VRPResult(solution, costsMatrix);
        }
    }

    public VRPResult deliveryLongerSlotSolution(VRPParam param) throws Exception {
        Assert.notNull(param.getSlot(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getClusteredSlotsIds(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getShifts(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getModifiableShipments(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getMaxDeliveryVolumeInML(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getMaxDeliveryHandoverInSeconds(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getAvoidTolls(), MUST_NOT_BE_NULL);

        int maxOrderPerBatch = Integer.MAX_VALUE;
        if (!param.isEnableDeliveryPooling()) {
            maxOrderPerBatch = 1;
        }

        final VehicleType driverDefaultType = VehicleTypeImpl.Builder.newInstance("driver").setCostPerDistance(0)
                .addCapacityDimension(DIMENSION_TOTAL_VOLUME, param.getMaxDeliveryVolumeInML())
                .addCapacityDimension(DIMENSION_TOTAL_MAX_ORDER, maxOrderPerBatch)
                .setCostPerTransportTime(1).build();

        Set<StockLocation> uniqueStockLocations = new HashSet<>();

        /**
         * Ini agak tricky kalo vehicle masih kosong semua, maka VRP index prioritynya ditentuin dari yang paling akhir dibuat.
         * ex: shift -> punya vehicle 2. maka kita ingin vehicle 1 dulu yg di isi, oleh karena itu di sorting vehicleNumbers nya (3,2,1) biar route nya ke isi di vehicle 1 duluan
         * */

        List<Vehicle> drivers = Lists.newArrayList();
        Map<Vehicle, Double> maxDistanceMap = new HashMap<>();
        for (Shift shift : param.getShifts()) {
            uniqueStockLocations.add(shift.getStockLocation());

            List<Integer> vehicleNumbers = new ArrayList<>();
            for (int i = shift.getCount(); i > 0; i--)
                vehicleNumbers.add(i);

            List<VehicleBatchTime> vehicleBatchTimes = getVehicleBatchTimes(param, shift);

            for (int i = 1; i <= shift.getCount(); i++) {
                Integer vehicleNumber = vehicleNumbers.get(0);
                VehicleBatchTime vehicleBatchTime;
                LocalDateTime earliestStartTime = shift.getStartTime();
                if (vehicleBatchTimes.size() > 0) {
                    vehicleBatchTime = vehicleBatchTimes.get(0);
                    vehicleNumber = vehicleBatchTime.getVehicleNumber();
                    earliestStartTime = DateTimeUtil.getMaxDatesBetween(shift.getStartTime(), vehicleBatchTime.getEndTime());

                    vehicleBatchTimes.remove(vehicleBatchTime);
                }
                vehicleNumbers.remove(vehicleNumber);

                double earliestStart = DateTimeUtil.localDateTimeToEpochSecond(earliestStartTime);
                double latestArrival = DateTimeUtil.localDateTimeToEpochSecond(shift.getEndTime());
                if (earliestStart > latestArrival)
                    continue;

                StockLocation stockLocation = shift.getStockLocation();
                VehicleImpl.Builder vehicleBuilder = VehicleImpl.Builder
                        .newInstance(shift.getId() + "_" + vehicleNumber)
                        .setStartLocation(Location.newInstance(stockLocation.getComplexId()))
                        .setType(driverDefaultType)
                        .setEarliestStart(earliestStart)
                        .setLatestArrival(latestArrival)
                        .setUserData(ImmutableMap.of(
                                USER_DATA_SHIFT_ID, shift.getId().toString(),
                                USER_DATA_SHIFT_START_TIME, DateTimeUtil.localDateTimeToEpochSecond(shift.getStartTime()).toString(),
                                USER_DATA_VEHICLE, vehicleNumber.toString()
                        ));

                Vehicle vehicle = vehicleBuilder.build();
                drivers.add(vehicle);
                maxDistanceMap.put(vehicle, stockLocation.getMaximumTraveledDistance());
            }
        }

        // build cost matrix
        for (Shipment shipment : param.getModifiableShipments()) {
            StockLocation stockLocation = shipment.getSlot().getStockLocation();
            uniqueStockLocations.add(stockLocation);
        }
        VehicleRoutingTransportCostsMatrix costsMatrix = costMatrixService.deliveryCostMatrixLongerDelivery(param.getSlot(), param.getModifiableShipments(), Lists.newArrayList(uniqueStockLocations), param.getAvoidTolls(), param.getOrderNumber());

        // build jobs
        List<com.graphhopper.jsprit.core.problem.job.Job> jobs = Lists.newArrayList();
        for (Shipment shipment : param.getModifiableShipments()) {
            StockLocation stockLocation = shipment.getSlot().getStockLocation();
            Location pickupLocation = Location.newInstance(stockLocation.getComplexId());
            Location deliveryLocation = Location.newInstance(shipment.getComplexId());

            double travelTimeStoreToShipment = costsMatrix.getTransportTime(pickupLocation, deliveryLocation, 0, null, null);
            double shoppingTime = 0;
            if (stockLocation.isSpecial()) {
                // TODO more valid shopping time
                // DDS Ranger not include shopping queue replacement time as the time spent will be spread to picking time
                shoppingTime = (shipment.getTotalItemCount(false) * stockLocation.getShopperAveragePickingTimePerUniqItem()) * 60;
                travelTimeStoreToShipment += shoppingTime;
            }

            LocalDateTime minimumPickupStart = shipment.getSlot().getStartTime().minusSeconds((long) travelTimeStoreToShipment);
            LocalDateTime pickupStartTime;
            if (stockLocation.isSpecial()) {
                pickupStartTime = DateTimeUtil.getMaxDatesBetween(minimumPickupStart, LocalDateTime.now());
            } else {
                Batch shoppingBatch = shipment.getShoppingJob().get().getBatch();
                pickupStartTime = DateTimeUtil.getMaxDatesBetween(minimumPickupStart, shoppingBatch.getEndTime());
            }

            LocalDateTime pickupEndTime = shipment.getSlot().getEndTime();
            if (pickupStartTime.isAfter(pickupEndTime)) {
                pickupEndTime = pickupStartTime;

                ThreadContext.put("SLOT_ID", shipment.getSlot().getId().toString());
                ThreadContext.put("ORDER_NUMBER", shipment.getOrderNumber());
                if (!stockLocation.isSpecial()) {
                    Batch shoppingBatch = shipment.getShoppingJob().get().getBatch();
                    ThreadContext.put("SHOPPING_END_TIME", shoppingBatch.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")));
                }
                ThreadContext.put("SLOT_END_TIME", shipment.getSlot().getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")));
                LOGGER.warn("Batch shopping end time is greater than slot end time");
                ThreadContext.clearAll();
            }
            TimeWindow pickupTimeWindow = buildTimeWindow(pickupStartTime, pickupEndTime);

            Double deliveryHandoverTime = param.getMaxDeliveryHandoverInSeconds();
            Double serviceTime = deliveryHandoverTime;
            Double pickupServiceTime = 0.0;
            LocalDateTime deliveryStartTime;
            if (stockLocation.isSpecial()) {
                pickupServiceTime += shoppingTime;
                deliveryStartTime = pickupStartTime.plusSeconds((long) shoppingTime);
            } else {
                deliveryStartTime = shipment.getSlot().getStartTime();
            }

            LocalDateTime deliveryEndTime = shipment.getSlot().getEndTime().minusSeconds(deliveryHandoverTime.longValue());
            if (param.isCrossSlotOptimization()) {
                deliveryEndTime = shipment.getSlot().getEndTime();
            }

            TimeWindow deliveryTimeWindow = buildTimeWindow(deliveryStartTime, deliveryEndTime);

            double totalVolume = shipment.getTotalVolumeInML() < param.getMaxDeliveryVolumeInML() ? shipment.getTotalVolumeInML() : param.getMaxDeliveryVolumeInML();

            ImmutableMap.Builder<String, String> userDataBuilder = ImmutableMap.<String, String>builder()
                    .put(USER_DATA_STOCK_LOCATION_ID, stockLocation.getId().toString())
                    .put(USER_DATA_IS_SPECIAL_STOCK_LOCATION, String.valueOf(stockLocation.isSpecial()))
                    .put(USER_DATA_SHIPMENT_ID, shipment.getId().toString())
                    .put(USER_DATA_SLOT_TIME, DateTimeUtil.localDateTimeToEpochSecond(shipment.getSlot().getStartTime()).toString())
                    .put(USER_DATA_SHOPPING_DURATION, String.valueOf(shoppingTime));

            if (preventDifferentQuadrant(param)) {
                userDataBuilder.put(USER_DATA_QUADRANT, getShipmentQuadrantToStore(shipment, stockLocation));
            }

            ImmutableMap<String,String> userData = userDataBuilder.build();

            com.graphhopper.jsprit.core.problem.job.Shipment job = com.graphhopper.jsprit.core.problem.job.Shipment.Builder
                    .newInstance(shipment.getComplexId())
                    .setName(shipment.getOrderNumber())
                    .addSizeDimension(DIMENSION_TOTAL_VOLUME, (int) totalVolume)
                    .addSizeDimension(DIMENSION_TOTAL_MAX_ORDER, 1)
                    .setPickupLocation(pickupLocation)
                    .setPickupTimeWindow(pickupTimeWindow)
                    .setDeliveryLocation(deliveryLocation)
                    .setDeliveryTimeWindow(deliveryTimeWindow)
                    .setDeliveryServiceTime(serviceTime)
                    .setPickupServiceTime(pickupServiceTime)
                    .setUserData(userData)
                    .build();
            jobs.add(job);
        }

        final VehicleRoutingProblem vrp = VehicleRoutingProblem.Builder.newInstance()
                .setFleetSize(FINITE)
                .setRoutingCost(costsMatrix)
                .addAllVehicles(drivers)
                .addAllJobs(jobs).build();

        StateManager stateManager = new StateManager(vrp);
        ConstraintManager constraintManager = new ConstraintManager(vrp, stateManager);
        // This constraint will disable pickup & delivery on multi store combination or cross slot combination
        constraintManager.addConstraint(new DisableMultiStorePickupDeliveryConstraint(), ConstraintManager.Priority.CRITICAL);
        constraintManager.addConstraint(new VehicleDependentTimeWindowConstraints(stateManager, vrp.getTransportCosts(), vrp.getActivityCosts()), ConstraintManager.Priority.CRITICAL);
        if (!param.isCrossSlotOptimization()) {
            constraintManager.addConstraint(new DisableCrossSlotPickupDeliveryConstraint(), ConstraintManager.Priority.CRITICAL);
        }

        if (preventDifferentQuadrant(param)) {
            constraintManager.addConstraint(new DisableDifferentQuadrantConstraint(), ConstraintManager.Priority.CRITICAL);
        }
        constraintManager.addConstraint(new LimitDistanceConstraint(stateManager,
                stateManager.createStateId("traveledDistance"),
                vrp.getInitialVehicleRoutes(),
                costsMatrix, maxDistanceMap),
                ConstraintManager.Priority.CRITICAL);


        Jsprit.Builder vehicleRoutingBuilder = Jsprit.Builder.newInstance(vrp);
        VehicleRoutingAlgorithm vehicleRoutingAlgorithm = vehicleRoutingBuilder
                .setProperty(Jsprit.Parameter.THREADS, "5")
                .setStateAndConstraintManager(stateManager, constraintManager)
                .buildAlgorithm();

        if (param.getMaxTerminationTime() != null) {
            TimeTermination prematureTermination = new TimeTermination(param.getMaxTerminationTime()); //in seconds
            vehicleRoutingAlgorithm.setPrematureAlgorithmTermination(prematureTermination);
            vehicleRoutingAlgorithm.addListener(prematureTermination);
        }

        // build solutions
        List<VehicleRoutingProblemSolution> solutions = new ArrayList<>(vehicleRoutingAlgorithm.searchSolutions());
        Comparator<VehicleRoutingProblemSolution> costComparing = Comparator.comparing(VehicleRoutingProblemSolution::getCost);
        VehicleRoutingProblemSolution bestSolution = solutions.stream().sorted(costComparing).findFirst().orElse(null);
        if (bestSolution != null) {
            double bestCost = bestSolution.getCost();
            solutions = solutions.stream().filter(s -> s.getCost() == bestCost).collect(Collectors.toList());
            for (VehicleRoutingProblemSolution solution : solutions) {
                if (solution.getRoutes().isEmpty())
                    continue;

                // If solutions have equal of best cost than pick from the earliest of shift start time
                long bestShiftStartTime = Long.parseLong(VrpUtil.getValueOfUserData(bestSolution.getRoutes().stream().findFirst().get().getVehicle(), USER_DATA_SHIFT_START_TIME));
                long shiftStartTime = Long.parseLong(VrpUtil.getValueOfUserData(solution.getRoutes().stream().findFirst().get().getVehicle(), USER_DATA_SHIFT_START_TIME));
                if (shiftStartTime < bestShiftStartTime) {
                    bestSolution = solution;
                    continue;
                }

                // If solutions have equal of shift start time than pick from the earliest of routes end time
                double bestRoutesEndTime = Iterables.getLast(bestSolution.getRoutes()).getEnd().getArrTime();
                double routesEndTime = Iterables.getLast(solution.getRoutes()).getEnd().getArrTime();
                if (routesEndTime < bestRoutesEndTime) {
                    bestSolution = solution;
                    continue;
                }

                // If solutions have equal of routes end time than pick from the earliest vehicle number
                long bestShiftId = Long.parseLong(VrpUtil.getValueOfUserData(bestSolution.getRoutes().stream().findFirst().get().getVehicle(), USER_DATA_SHIFT_ID));
                long shiftId = Long.parseLong(VrpUtil.getValueOfUserData(solution.getRoutes().stream().findFirst().get().getVehicle(), USER_DATA_SHIFT_ID));
                long bestVehicleNumber = Long.parseLong(VrpUtil.getValueOfUserData(bestSolution.getRoutes().stream().findFirst().get().getVehicle(), USER_DATA_VEHICLE));
                long vehicleNumber = Long.parseLong(VrpUtil.getValueOfUserData(solution.getRoutes().stream().findFirst().get().getVehicle(), USER_DATA_VEHICLE));
                if (bestShiftId == shiftId && vehicleNumber < bestVehicleNumber)
                    bestSolution = solution;
            }

            segmentTracker(param, bestSolution, vrp);
        }

        SolutionPrinterUtil.print(param.getSlot().getId(), vrp, bestSolution, SolutionPrinter.Print.VERBOSE);
//        SolutionPrinter.print(vrp, bestSolution, SolutionPrinter.Print.VERBOSE);

        return new VRPResult(bestSolution, costsMatrix);
    }

    private Boolean preventDifferentQuadrant(VRPParam param) {
        return param.getSlot().getStockLocation().getTenant().isEnableDriverPoolingQuadrantConstraint();
    }
    public static String getShipmentQuadrantToStore(Shipment shipment, StockLocation stockLocation) {
        Double lat = shipment.getAddressLat();
        Double lon = shipment.getAddressLon();
        Double storeLat = stockLocation.getLat();
        Double storeLon = stockLocation.getLon();
        // bearingAngle is degree relative to North
        Double bearingAngle = getBearingAngleFromAToB(storeLat, storeLon, lat, lon);
        if (bearingAngle <= 90) {
            return "1";
        } else if (bearingAngle <= 180) {
            return "4";
        } else if (bearingAngle <= 270) {
            return "3";
        } else {
            return "2";
        }
    }

    // source: https://stackoverflow.com/questions/3932502/calculate-angle-between-two-latitude-longitude-points
    public static Double getBearingAngleFromAToB(Double lat1, Double lon1, Double lat2, Double lon2) {
        Double dLon = lon2 - lon1;
        Double y = Math.sin(dLon) * Math.cos(lat2);
        Double x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLon);
        Double bearing = Math.atan2(y, x);
        bearing = radiansToDegrees(bearing);
        bearing = (bearing + 360) % 360;
        return bearing;
    }

    public static Double radiansToDegrees(Double radians) {
        Double pi = Math.PI;
        return radians * 180 / pi;
    }

    private List<VehicleBatchTime> getVehicleBatchTimes(VRPParam param, Shift shift) {
        List<Long> optimizedBatchIds = param.getOptimizedBatchIdsWithinOffset().isEmpty() ?
                ImmutableList.of(0L) :
                param.getOptimizedBatchIdsWithinOffset();

        return batchRepository.findAllLatestUnmodifiableVehicleBatch(
                Lists.newArrayList(Batch.Type.RANGER, Batch.Type.DELIVERY),
                param.getClusteredSlotsIds(),
                ImmutableList.of(shift.getId()),
                LocalDateTime.now(),
                optimizedBatchIds);
    }

    public VRPResult rangerSolution(VRPParam param) throws Exception {
        Assert.notNull(param.getComplexId(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getFreeDriverCount(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getFreeDriverWithEmptyBatchCount(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getMaxDeliveryVolumeInML(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getMaxDeliveryHandoverInSeconds(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getSlot(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getModifiableShipments(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getAvoidTolls(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getShopperAveragePickingTimePerUniqItem(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getShoppingServiceTime(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getWiderBatchWindows(), MUST_NOT_BE_NULL);
        Assert.notNull(param.isReservedSlot(), MUST_NOT_BE_NULL);

        if (param.getFreeDriverCount() <= 0) {
            return solutionWithUnassignedJob();

        } else {
            List<Vehicle> drivers = Lists.newArrayList();
            List<com.graphhopper.jsprit.core.problem.job.Service> services = Lists.newArrayList();

            final VehicleType driverDefaultType = VehicleTypeImpl.Builder.newInstance("ranger").setCostPerDistance(0)
                    .addCapacityDimension(DIMENSION_TOTAL_VOLUME, param.getMaxDeliveryVolumeInML())
                    .addCapacityDimension(DIMENSION_TOTAL_MAX_ORDER, Integer.MAX_VALUE)
                    .addCapacityDimension(DIMENSION_TOTAL_SHOPPING_TIME, Integer.MAX_VALUE)
                    .setCostPerTransportTime(1).build();
            final VehicleType driverUnlimitedVolumeType = VehicleTypeImpl.Builder.newInstance("ranger").setCostPerDistance(0)
                    .addCapacityDimension(DIMENSION_TOTAL_VOLUME, Integer.MAX_VALUE)
                    .addCapacityDimension(DIMENSION_TOTAL_MAX_ORDER, 1)
                    .addCapacityDimension(DIMENSION_TOTAL_SHOPPING_TIME, Integer.MAX_VALUE)
                    .setCostPerTransportTime(1).build();

            long shipmentWithExceedVolume = param.getModifiableShipments().stream()
                    .filter(s -> !s.isNewObject() && s.getTotalVolumeInML() > param.getMaxDeliveryVolumeInML())
                    .count();

            double vehicleStartTime = param.getSlot().getStartTime().atZone(ZoneOffset.UTC).toEpochSecond();
            double vehicleEndTime = param.getSlot().getEndTime().atZone(ZoneOffset.UTC).toEpochSecond();
            long freeDriverWithEmptyBatchCount = param.getFreeDriverWithEmptyBatchCount() + shipmentWithExceedVolume;
            List<TimeWindow> widerBatchWindows = param.getWiderBatchWindows();
            for (int i = 0; i < param.getFreeDriverCount(); i++) {
                VehicleImpl.Builder vehicleBuilder = VehicleImpl.Builder
                        .newInstance(Integer.toString(i + 1))
                        .setStartLocation(Location.newInstance(param.getComplexId()))
                        .setEarliestStart(vehicleStartTime)
                        .setLatestArrival(vehicleEndTime);

                if (freeDriverWithEmptyBatchCount > 0) {
                    vehicleBuilder.setType(driverUnlimitedVolumeType);
                    freeDriverWithEmptyBatchCount--;
                } else {
                    if (!widerBatchWindows.isEmpty()) {
                        TimeWindow timeWindow = widerBatchWindows.get(0);
                        vehicleBuilder.setEarliestStart(timeWindow.getStart());
                        vehicleBuilder.setLatestArrival(timeWindow.getEnd());

                        widerBatchWindows.remove(0);
                    }
                    vehicleBuilder.setType(driverDefaultType);
                }

                final Vehicle vehicle = vehicleBuilder.build();
                drivers.add(vehicle);
            }

            for (Shipment shipment : param.getModifiableShipments()) {
                final String id = shipment.getComplexId();
                final com.graphhopper.jsprit.core.problem.job.Service.Builder serviceBuilder = com.graphhopper.jsprit.core.problem.job.Service.Builder.newInstance(id);

                Slot chosenSlot = shipment.getSlot() != null ? shipment.getSlot() : param.getSlot();
                double startTimeWindow = chosenSlot.getStartTime().atZone(ZoneOffset.UTC).toEpochSecond();
                double endTimeWindow = chosenSlot.getEndTime().atZone(ZoneOffset.UTC).toEpochSecond();
                TimeWindow restrictedTimeWindow = new TimeWindow(startTimeWindow, endTimeWindow);

                int totalItem = shipment.getTotalItemCount(param.isCompareWithAverageItem());
                Double shoppingTime = ((totalItem * param.getShopperAveragePickingTimePerUniqItem()) + param.getShoppingServiceTime()) * 60;
                Double serviceTime = param.getMaxDeliveryHandoverInSeconds();

                final com.graphhopper.jsprit.core.problem.job.Service service = serviceBuilder
                        .addSizeDimension(DIMENSION_TOTAL_VOLUME, (int) shipment.getTotalVolumeInML())
                        .addSizeDimension(DIMENSION_TOTAL_MAX_ORDER, 1)
                        .addSizeDimension(DIMENSION_TOTAL_SHOPPING_TIME, shoppingTime.intValue())
                        .setLocation(Location.newInstance(id))
                        .setServiceTime(serviceTime)
                        .setTimeWindow(restrictedTimeWindow)
                        .build();

                services.add(service);
            }

            final List<Addressable> shipmentAddressables = Lists.newArrayList(param.getModifiableShipments());
            final List<String> orderNumbers = param.getModifiableShipments().stream().map(shipment -> shipment.getOrderNumber()).collect(Collectors.toList());
            VehicleRoutingTransportCostsMatrix costsMatrix = costMatrixService.deliveryCostMatrix(param.getSlot(), shipmentAddressables, param.getAvoidTolls(), param.getOrderNumber(), orderNumbers);
            final VehicleRoutingProblem vrp = VehicleRoutingProblem.Builder.newInstance()
                    .setFleetSize(FINITE)
                    .setRoutingCost(costsMatrix)
                    .addAllVehicles(drivers)
                    .addAllJobs(services).build();

            Jsprit.Builder vehicleRoutingBuilder = Jsprit.Builder.newInstance(vrp);
            vehicleRoutingBuilder.addCoreStateAndConstraintStuff(false);

            StateManager stateManager = new StateManager(vrp);
            ConstraintManager constraintManager = new ConstraintManager(vrp, stateManager);
            constraintManager.addConstraint(new RangerVehicleDependentTimeWindowConstraint(stateManager, vrp.getTransportCosts(), vrp.getActivityCosts()), ConstraintManager.Priority.HIGH);

            AlgorithmUtil.addCoreConstraints(constraintManager, stateManager, vrp);
            stateManager.addStateUpdater(new AddShoppingTimeToActivity(stateManager, vrp.getTransportCosts(), vrp.getActivityCosts()));

            vehicleRoutingBuilder.setStateAndConstraintManager(stateManager, constraintManager);
            VehicleRoutingAlgorithm vehicleRoutingAlgorithm = vehicleRoutingBuilder
                    .setProperty(Jsprit.Parameter.THREADS, "5")
                    .buildAlgorithm();

            PrematureAlgorithmTermination solutionTermination = new SolutionIterationTermination(MINIMAL_SOLUTION_ACCEPTED_FOR_SET_SLOT);
            if (!param.isReservedSlot())
                solutionTermination = new SolutionIterationTermination(MINIMAL_SOLUTION_ACCEPTED);

            vehicleRoutingAlgorithm.setPrematureAlgorithmTermination(solutionTermination);

            VehicleRoutingProblemSolution solution = Solutions.bestOf(vehicleRoutingAlgorithm.searchSolutions());
            // SolutionPrinter.print(vrp, solution, SolutionPrinter.Print.VERBOSE);

            return new VRPResult(solution, costsMatrix);
        }
    }

    @Async
    public void deliveryLongerSlotSolutionWithCrossSlot(VRPParam param) throws Exception {
        if (param.isCrossSlotEnabled()) {
            param.setCrossSlotOptimization(true);
            deliveryLongerSlotSolution(param);
        }
    }

    private void segmentTracker(VRPParam param, VehicleRoutingProblemSolution solution, VehicleRoutingProblem problem) {
        if (!param.isCrossSlotEnabled())
            return;

        SlotReservedService slotReservedService = applicationContext.getBean(SlotReservedService.class);
        SlotOptimizationEventTracker slotOptimizationEventTracker = applicationContext.getBean(SlotOptimizationEventTracker.class);

        String optimizationId = param.getOptimizationId();
        Long slotId = param.getSlot().getId();
        boolean isCrossSlot = param.isCrossSlotOptimization();
        Jobs jobs = getNuOfJobs(problem);
        double cost = solution.getCost();
        int shipmentsCount = jobs.nShipments;
        int unassignedJobsCount = solution.getUnassignedJobs().size();
        int batchesCount = solution.getRoutes().stream()
                .mapToInt(v -> slotReservedService.getDeliveryBatchesOfActivity(v).size())
                .sum();

        slotOptimizationEventTracker.construct(optimizationId, slotId, cost, shipmentsCount, batchesCount, unassignedJobsCount, isCrossSlot);
        slotOptimizationEventTracker.track();
    }

    @AllArgsConstructor
    private static class Jobs {
        int nServices;
        int nShipments;
        int nBreaks;
    }

    private Jobs getNuOfJobs(VehicleRoutingProblem problem) {
        int nShipments = 0;
        int nServices = 0;
        for (Job j : problem.getJobs().values()) {
            if (j instanceof com.graphhopper.jsprit.core.problem.job.Shipment) {
                nShipments++;
            }
            if (j instanceof com.graphhopper.jsprit.core.problem.job.Service) {
                nServices++;
            }
        }
        return new Jobs(nServices, nShipments, 0);
    }

    public VRPResult shoppingSolution(VRPParam param) {
        Assert.notNull(param.getComplexId(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getShoppingTimePerShoppers(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getMaxShoppingVolumeInML(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getSlot(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getModifiableShipments(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getShoppingServiceTime(), MUST_NOT_BE_NULL);

        if (param.getShoppingTimePerShoppers().size() <= 0) {
            return solutionWithUnassignedJob();

        } else {
            List<Vehicle> shoppers = Lists.newArrayList();
            List<com.graphhopper.jsprit.core.problem.job.Service> services = Lists.newArrayList();

            final VehicleTypeImpl.Builder builder = VehicleTypeImpl.Builder.newInstance("shopper");
            final VehicleType vehicleType = builder.setCostPerDistance(0)
                    .addCapacityDimension(DIMENSION_TOTAL_VOLUME, param.getMaxShoppingVolumeInML())
                    .setCostPerTransportTime(1).build();

            int i = 0;
            Double serviceTime = param.getShoppingServiceTime();
            for (Long shoppingTime : param.getShoppingTimePerShoppers()) {
                final Vehicle vehicle = VehicleImpl.Builder
                        .newInstance(Integer.toString(i + 1))
                        .setStartLocation(Location.newInstance(param.getComplexId()))
                        .setLatestArrival(shoppingTime - serviceTime)
                        .setType(vehicleType)
                        .build();

                shoppers.add(vehicle);
                i++;
            }

            for (Shipment shipment : param.getModifiableShipments()) {
                final String id = shipment.getComplexId();
                final com.graphhopper.jsprit.core.problem.job.Service.Builder serviceBuilder = com.graphhopper.jsprit.core.problem.job.Service.Builder.newInstance(id);
                final com.graphhopper.jsprit.core.problem.job.Service service = serviceBuilder
                        .addSizeDimension(DIMENSION_TOTAL_VOLUME, (int) shipment.getTotalVolumeInML())
                        .setLocation(Location.newInstance(id))
                        .setServiceTime(0)
                        .build();

                services.add(service);
            }

            final List<Shipment> shipments = Lists.newArrayList(param.getModifiableShipments());
            VehicleRoutingTransportCostsMatrix costsMatrix = costMatrixService.shoppingCostMatrix(param.getSlot().getStockLocation(), shipments);
            final VehicleRoutingProblem vrp = VehicleRoutingProblem.Builder.newInstance()
                    .setFleetSize(FINITE)
                    .setRoutingCost(costsMatrix)
                    .addAllVehicles(shoppers)
                    .addAllJobs(services).build();

            Jsprit.Builder vehicleRoutingBuilder = Jsprit.Builder.newInstance(vrp);
            if (param.isReservedSlot()) {
                StateManager stateManager = getStateManager(vrp);
                ConstraintManager constraintManager = getConstraintManager(vrp, stateManager);
                vehicleRoutingBuilder.setStateAndConstraintManager(stateManager, constraintManager);
            }

            VehicleRoutingAlgorithm vehicleRoutingAlgorithm = vehicleRoutingBuilder
                    .setProperty(Jsprit.Parameter.THREADS, "5")
                    .buildAlgorithm();

            PrematureAlgorithmTermination solutionTermination = new SolutionIterationTermination(MINIMAL_SOLUTION_ACCEPTED_FOR_SET_SLOT);
            if (!param.isReservedSlot())
                solutionTermination = new SolutionIterationTermination(MINIMAL_SOLUTION_ACCEPTED);

            vehicleRoutingAlgorithm.setPrematureAlgorithmTermination(solutionTermination);

            VehicleRoutingProblemSolution solution = Solutions.bestOf(vehicleRoutingAlgorithm.searchSolutions());
            //SolutionPrinter.print(vrp, solution, SolutionPrinter.Print.VERBOSE);

            return new VRPResult(solution, costsMatrix);
        }
    }

    public List<RouteResult> shoppingLongerSlotSolution(VRPParam param) {
        Assert.notNull(param.getMaxShoppingVolumeInML(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getShoppingServiceTime(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getMaxShoppingTimeInMinutes(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getSlot(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getShifts(), MUST_NOT_BE_NULL);
        Assert.notNull(param.getModifiableShipments(), MUST_NOT_BE_NULL);

        StockLocation stockLocation = param.getSlot().getStockLocation();
        ZoneId zoneId = ZoneId.of(stockLocation.getState().getTimeZone());

        int totalEffectiveShoppingTime = param.getMaxShoppingTimeInMinutes() - param.getShoppingServiceTime().intValue();

        if (stockLocation.isFulfilledByThirdPartyEnabler()) {
            // To enable optimization in enabler without getting pooled together
            totalEffectiveShoppingTime = (int) stockLocation.getShopperAveragePickingTimePerUniqItem();
        }

        final VehicleTypeImpl.Builder builder = VehicleTypeImpl.Builder.newInstance("shopper");
        final VehicleType vehicleType = builder.setCostPerDistance(1)
                .addCapacityDimension(DIMENSION_TOTAL_VOLUME, param.getMaxShoppingVolumeInML())
                .addCapacityDimension(DIMENSION_TOTAL_SHOPPING_TIME, totalEffectiveShoppingTime)
                .setCostPerTransportTime(1).build();

        final Vehicle vehicle = VehicleImpl.Builder
                .newInstance("1")
                .setStartLocation(Location.newInstance(param.getComplexId()))
                .setEarliestStart(0)
                .setType(vehicleType)
                .setUserData(ImmutableMap.of("shiftId", param.getShifts().get(0).getId().toString(), "stockLocationId", stockLocation.getId().toString()))
                .build();

        List<com.graphhopper.jsprit.core.problem.job.Service> services = Lists.newArrayList();
        for (Shipment shipment : param.getModifiableShipments()) {
            final String id = shipment.getComplexId();

            double totalVolume = shipment.getTotalVolumeInML() > param.getMaxShoppingVolumeInML() ? param.getMaxShoppingVolumeInML() : shipment.getTotalVolumeInML();
            double shoppingTime = shipment.getItems().size() * stockLocation.getShopperAveragePickingTimePerUniqItem();
            double totalShoppingTime = shoppingTime > totalEffectiveShoppingTime ? totalEffectiveShoppingTime : shoppingTime;

            ZonedDateTime zonedDateTime = shipment.getSlot().getStartTime().atZone(zoneId);
            final ImmutableMap<String, String> userData = ImmutableMap.of(USER_DATA_SLOT_ID, Long.toString(shipment.getSlot().getId()),
                    USER_DATA_SLOT_TIME, Long.toString(zonedDateTime.toInstant().toEpochMilli()), USER_DATA_ORDER_NUMBER, shipment.getOrderNumber());
            final com.graphhopper.jsprit.core.problem.job.Service.Builder serviceBuilder = com.graphhopper.jsprit.core.problem.job.Service.Builder.newInstance(id);
            final com.graphhopper.jsprit.core.problem.job.Service service = serviceBuilder
                    .addSizeDimension(DIMENSION_TOTAL_VOLUME, (int) totalVolume)
                    .addSizeDimension(DIMENSION_TOTAL_SHOPPING_TIME, (int) totalShoppingTime)
                    .setUserData(userData)
                    .setLocation(Location.newInstance(id))
                    .setServiceTime(0)
                    .build();

            services.add(service);
        }

        final List<Shipment> shipments = Lists.newArrayList(param.getModifiableShipments());
        VehicleRoutingTransportCostsMatrix costsMatrix = costMatrixService.shoppingCostMatrixLongerDelivery(stockLocation, shipments);
        final VehicleRoutingProblem vrp = VehicleRoutingProblem.Builder.newInstance()
                .setFleetSize(INFINITE)
                .setRoutingCost(costsMatrix)
                .addVehicle(vehicle)
                .addAllJobs(services).build();

        StateManager stateManager = new StateManager(vrp);
        ConstraintManager constraintManager = new ConstraintManager(vrp, stateManager);
        constraintManager.addConstraint(new SlotGroupingConstraint());

        Jsprit.Builder vehicleRoutingBuilder = Jsprit.Builder.newInstance(vrp);
        VehicleRoutingAlgorithm vehicleRoutingAlgorithm = vehicleRoutingBuilder
                .setProperty(Jsprit.Parameter.THREADS, "5")
                .setStateAndConstraintManager(stateManager, constraintManager)
                .buildAlgorithm();

        VehicleRoutingProblemSolution solution = Solutions.bestOf(vehicleRoutingAlgorithm.searchSolutions());
//        SolutionPrinter.print(vrp, solution, SolutionPrinter.Print.VERBOSE);

        VRPResult vrpResult = new VRPResult(solution, costsMatrix);

        // Put The Result on RouteResult
        List<RouteResult> routeResults = new ArrayList<>();
        for (VehicleRoute vehicleRoute : vrpResult.getVrpSolution().getRoutes()) {
            RouteResult routeResult = new RouteResult();
            routeResult.setVehicleRoute(vehicleRoute);
            routeResults.add(routeResult);
        }

        return routeResults;
    }

    public static VRPResult solutionWithUnassignedJob() {
        final Delivery.Builder builder = Delivery.Builder.newInstance("delivering.");
        builder.setLocation(Location.newInstance("1"));

        final List<Job> unassignedJobs = Lists.newArrayList();
        unassignedJobs.add(builder.build());

        VehicleRoutingProblemSolution solutionWithUnassignedJob = new VehicleRoutingProblemSolution(Lists.newArrayList(), unassignedJobs, 0.0);
        return new VRPResult(solutionWithUnassignedJob);
    }

    private StateManager getStateManager(VehicleRoutingProblem problem) {
        StateManager stateManager = new StateManager(problem);

        //introduce a new state called "max-transport-time"
        StateId max_transport_time_state = stateManager.createStateId("max-transport-time");

        stateManager.putProblemState(max_transport_time_state, Double.class, 0.);
        stateManager.addStateUpdater(new MaxTransportTimeStateUpdater(stateManager, problem.getTransportCosts(), problem.getActivityCosts()));

        return stateManager;
    }

    private ConstraintManager getConstraintManager(VehicleRoutingProblem problem, StateManager stateManager) {
        ConstraintManager constraintManager = new ConstraintManager(problem, stateManager);
        // soft constraint that calculates additional transport costs when inserting a job(activity) at specified position
        constraintManager.addConstraint(new VariableTransportCostCalculator(problem.getTransportCosts(), problem.getActivityCosts()));

        SoftActivityConstraint penalyzeShiftOfMaxTransportTime = new ShiftMaxTransportTimeConstraint(problem, stateManager);
        constraintManager.addConstraint(penalyzeShiftOfMaxTransportTime);

        return constraintManager;
    }

    private TimeWindow buildTimeWindow(LocalDateTime start, LocalDateTime end) {
        double startTime = DateTimeUtil.localDateTimeToEpochSecond(start);
        double endTime = DateTimeUtil.localDateTimeToEpochSecond(end);
        return new TimeWindow(startTime, endTime);
    }

}
