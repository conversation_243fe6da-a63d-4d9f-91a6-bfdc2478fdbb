package com.happyfresh.fulfillment.common.service;

import io.searchbox.core.SearchResult;

import java.util.List;

public interface ESClientService {
    boolean createIndex();
    boolean deleteIndex();
    boolean putMapping(String mappingPayload, Class klazz);
    boolean ping();
    <T extends Object>void indexDocument(T document, boolean asynchronous) throws Exception;
    <T extends Object>void indexDocuments(List<T> documents, Class klazz, boolean asynchronous) throws Exception;
    <T extends Object>List<T> searchDocuments(String searchPayload, Class resultClass);
    SearchResult searchDocument(String searchPayload, Class resultClass);
}
