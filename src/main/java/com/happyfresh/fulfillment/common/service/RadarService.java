package com.happyfresh.fulfillment.common.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaMessage;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaTopicConfig;
import com.happyfresh.fulfillment.common.property.RadarProperty;
import com.happyfresh.fulfillment.common.service.bean.RadarEventPayload;
import com.happyfresh.fulfillment.common.service.bean.RadarUserPayload;
import com.happyfresh.fulfillment.common.service.bean.RadarWebhookPayload;
import com.happyfresh.fulfillment.common.tracking.GeofenceEventTracker;
import com.happyfresh.fulfillment.entity.Agent;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.repository.AgentRepository;
import com.happyfresh.fulfillment.repository.StockLocationRepository;
import com.happyfresh.fulfillment.user.service.AgentInGeofenceService;
import org.apache.commons.lang3.StringUtils;
import org.jobrunr.scheduling.JobScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class RadarService {

    private static final Logger logger = LoggerFactory.getLogger(RadarService.class);

    @Autowired
    private RadarProperty radarProperty;

    @Autowired
    private HmacService hmacService;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private AgentRepository agentRepository;

    @Autowired
    private StockLocationRepository stockLocationRepository;

    @Autowired
    private AgentInGeofenceService agentInGeofenceService;

    @Autowired
    private KafkaMessage kafkaMessage;

    @Autowired
    private ApplicationContext applicationContext;
    
    @Autowired
    private JobScheduler jobScheduler;

    public boolean isValidSignature(String body, String signature) {
        if (StringUtils.isBlank(body) || StringUtils.isBlank(signature))
            return false;

        final String webhookToken = radarProperty.getWebhookToken();
        try {
            String eventId = getEventId(body);
            return signature.equals(hmacService.computeHmacSha1(eventId, webhookToken));
        } catch (Exception e) {
            logger.error("Error when validate Radar webhook signature.", e);
            return false;
        }
    }

    private String getEventId(String body) throws Exception{
        JSONObject bodyObj = new JSONObject(body);
        JSONArray eventsArray = (JSONArray) bodyObj.get("events");
        JSONObject firstEvent = (JSONObject) eventsArray.get(0);
        return (String) firstEvent.get("_id");
    }

    @Async
    public void syncData(String body) {
        try {
            // Instead of using ActiveMQ, use JobRunr to schedule the processing
            jobScheduler.enqueue(() -> processRadarData(body));
            logger.info("Scheduled Radar data sync job with JobRunr");
        } catch (Exception exception){
            logger.error("Cannot sync Radar data", exception);
        }
    }
    
    public void processRadarData(String body) {
        try {
            processData(body);
        } catch (Exception exception) {
            logger.error("Error processing Radar data", exception);
        }
    }

    public void processData(String body) {
        try {
            RadarWebhookPayload radarWebhookPayload = mapper.readValue(body, RadarWebhookPayload.class);
            for (RadarEventPayload radarEvent : radarWebhookPayload.getEvents()) {
                if (radarEvent.getUser() != null && radarEvent.getGeofence() != null && radarEvent.isRadarWarehouseTag()) {
                    String key = radarEvent.getUser().getUserId();
                    String message = mapper.writeValueAsString(radarEvent);
                    kafkaMessage.publish(KafkaTopicConfig.RADAR_EVENT_PROCESSING_TOPIC, key, message);
                }
                if (radarEvent.getUser() != null && radarEvent.getGeofence() != null && trackToSegment(radarEvent, radarWebhookPayload.getUser())){
                    GeofenceEventTracker tracker = new GeofenceEventTracker(applicationContext, radarEvent);
                    tracker.track();
                }
            }
        } catch (Exception exception){
            logger.error("Cannot process Radar data", exception);
        }
    }

    private boolean trackToSegment(RadarEventPayload event, RadarUserPayload user){
        if (!event.trackToSegment()){
            return false;
        }

        // exit or enter to store
        if (event.isRadarWarehouseTag() || event.isSupermarketTag()){
            return true;
        }

        return (user.getTrip() != null &&
                event.isShipmentNumberTag() &&
                user.getTrip().isShipmentNumberTag() &&
                user.getTrip().getDestinationGeofenceExternalId().equals(event.getGeofence().getExternalId()));
    }

    public void handleRadarEvent(RadarEventPayload radarEvent) {
        Long userId = Long.valueOf(radarEvent.getUser().getUserId());
        Long stockLocationId = Long.valueOf(radarEvent.getGeofence().getExternalId());
        if (radarEvent.getType().equalsIgnoreCase(RadarEventPayload.RADAR_EVENT_ENTER_GEOFENCE)) {
            addAgentInGeofenceData(userId, stockLocationId);
        } else if (radarEvent.getType().equalsIgnoreCase(RadarEventPayload.RADAR_EVENT_EXIT_GEOFENCE)) {
            deleteAgentInGeofenceData(userId,stockLocationId);
        }
    }

    private void addAgentInGeofenceData(Long userId, Long externalStockLocationId) {
        Agent agent = agentRepository.findByUserId(userId);
        if (agent != null) {
            StockLocation stockLocation = stockLocationRepository.findByExternalIdAndTenantId(externalStockLocationId, agent.getTenant().getId());
            if (stockLocation != null)
                agentInGeofenceService.createOrUpdateAgentInGeofence(agent, stockLocation);
        }
    }

    private void deleteAgentInGeofenceData(Long userId, Long externalStockLocationId) {
        Agent agent = agentRepository.findByUserId(userId);
        if (agent != null) {
            StockLocation stockLocation = stockLocationRepository.findByExternalIdAndTenantId(externalStockLocationId, agent.getTenant().getId());
            if (stockLocation != null)
                agentInGeofenceService.deleteAgentInGeofence(agent, stockLocation);
        }
    }
}
