package com.happyfresh.fulfillment.common.service;

import com.happyfresh.fulfillment.common.property.EncryptionProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * Implementation of AES GCM, based on:
 * https://javainterviewpoint.com/java-aes-256-gcm-encryption-and-decryption/
 * Check using unit test:
 * SymmetricEncryptionServiceTest shouldCorrectly_encryptAndDecrypt()
 */
@Service
public class SymmetricEncryptionService {

    private static final String ALGORITHM = "AES";

    private static final String AES_MODES = "AES/GCM/NoPadding";

    public static final int GCM_TAG_LENGTH = 16;

    private String secretKey;

    private String initializationVector;

    @Autowired
    private EncryptionProperty encryptionProperty;

    public SymmetricEncryptionService(EncryptionProperty encryptionProperty) {
        secretKey = encryptionProperty.getSecretKey();
        initializationVector = encryptionProperty.getInitializationVector();
    }

    public String encryptString(String message) throws Exception {
        final Cipher cipher = Cipher.getInstance(AES_MODES);
        final SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(), ALGORITHM);
        final byte [] iv = initializationVector.getBytes();
        final GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, gcmParameterSpec);

        return Base64.getEncoder().encodeToString(cipher.doFinal(message.getBytes()));
    }

    public String decryptString(String encodedMessage) throws Exception {
        final Cipher cipher = Cipher.getInstance(AES_MODES);
        final SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(), ALGORITHM);
        final byte [] iv = initializationVector.getBytes();
        final GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, gcmParameterSpec);

        final byte[] encodedMessageInByte = Base64.getDecoder().decode(encodedMessage.getBytes());
        return new String(cipher.doFinal(encodedMessageInByte));
    }
}
