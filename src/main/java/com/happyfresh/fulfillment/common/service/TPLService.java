package com.happyfresh.fulfillment.common.service;

import com.happyfresh.fulfillment.batch.form.BatchItemFinalizeForm;
import com.happyfresh.fulfillment.batch.service.BatchSndService;
import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.gosend.service.GosendService;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressService;
import com.happyfresh.fulfillment.lalamove.service.LalamoveService;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.JobRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.tpl.delyva.service.DelyvaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityNotFoundException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static com.happyfresh.fulfillment.entity.GosendDelivery.Status.INITIAL;

@Service
public class TPLService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private GrabExpressService grabExpressService;

    @Autowired
    private LalamoveService lalamoveService;

    @Autowired
    private BatchSndService batchSndService;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private DelyvaService delyvaService;

    @Autowired
    private GosendService gosendService;

    @Transactional
    public void switchToHF(String shipmentNumber) {
        Shipment shipment = shipmentRepository.findByNumber(shipmentNumber);
        Optional<Job> deliveryJobOpt = shipment.getDeliveryJob();
        if (deliveryJobOpt.isEmpty()) {
            throw new EntityNotFoundException("No delivery job assigned to shipment");
        }

        Batch batch = deliveryJobOpt.get().getBatch();
        if (batch.getTplType() == null) {
            return;
        }

        if(batch.isGrabExpress()) {
            grabExpressService.switchGEToHF(shipmentNumber);
        } else if(batch.isLalamove()) {
            lalamoveService.switchLalamoveToHF(shipment);
        } else if (batch.isDelyva()) {
            delyvaService.switchDelyvaToHFDueToManualTrigger(shipment);
        } else if (batch.isGosend()) {
            // This action is allowed only after several minutes described in Tenant#waitingTimeToEnableGosendSwithToHF
            if (!isEligibleToSwitchGosendToHF(shipment))
                throw new UnprocessableEntityException("Silahkan hubungi team ops untuk ganti ke driver Happyfresh");
            
            gosendService.cancelGosendAndSwitchGosendToHF(shipment);
        }
    }

    private boolean isEligibleToSwitchGosendToHF(Shipment shipment) {
        Slot slot = shipment.getSlot();
        if (slot.getStartTime().isBefore(LocalDateTime.now()))
            return true;

        GosendDelivery gosendDelivery = shipment.getCurrentGosendDelivery();
        Map<String, String> gosendStatuses = gosendDelivery.getStatuses();

        if (!gosendStatuses.containsKey(INITIAL.toString())) {
            logger.info("[Gosend] [switchToHF] Shipment Number: {} No INITIAL", shipment.getNumber());
            return false;
        }
        LocalDateTime allocatedTime =
                LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(gosendStatuses.get(INITIAL.toString()))),
                        ZoneId.of("UTC"));
        logger.info("[Gosend] [switchToHF] Shipment Number: {} Allocated time: {}", shipment.getNumber(), allocatedTime);
        long waitingTime = shipment.getTenant().waitingTimeToEnableGosendSwithToHF();
        if (allocatedTime.plusMinutes(waitingTime).isAfter(LocalDateTime.now())) {
            logger.info("[Gosend] [switchToHF] Shipment Number: {} Not enough time", shipment.getNumber());
            return false;
        }

        return true;
    }


    @Transactional
    public void switchToTPL(String orderNumber, String tplVendor, String tplVehicleType) {
        try {
            Batch shoppingBatch = getShoppingBatch(orderNumber);
            Batch deliveryBatch = getDeliveryBatch(orderNumber);
            Shipment shipment = getShipment(orderNumber);
            cancelTPL(deliveryBatch, shipment);
            switchDeliveryJobToNewTPLBatch(deliveryBatch, shipment, tplVendor, tplVehicleType);
            shipment = getShipment(orderNumber);
            bookTPL(shoppingBatch, shipment);
            deleteSwitchedBatch(deliveryBatch);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            logger.info("[SwitchToTPL] createTPLBatch error class. {}", e.getClass());
            logger.info("[SwitchToTPL] createTPLBatch error message. {}", e.getMessage());
            throw new UnprocessableEntityException(e.getMessage());
        }
    }

    private Batch getShoppingBatch(String orderNumber) {
        Batch shoppingBatch = batchRepository.findShoppingBatchByOrderNumber(orderNumber);
        return shoppingBatch;
    }

    private Batch getDeliveryBatch(String orderNumber) {
        Batch deliveryBatch = batchRepository.findDeliveryBatchByOrderNumber(orderNumber);
        return deliveryBatch;
    }

    private Shipment getShipment(String orderNumber) {
        Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
        return shipment;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private void switchDeliveryJobToNewTPLBatch(Batch deliveryBatch, Shipment shipment, String tplVendor, String tplVehicleType) throws Exception {
        Batch tplBatch = createTPLBatch(deliveryBatch, tplVendor, tplVehicleType);
        Job job = shipment.getDeliveryJob().get();
        job.setBatch(tplBatch);
        job.setLocus(false);
        jobRepository.save(job);
    }

    private Batch createTPLBatch(Batch deliveryBatch, String tplVendor, String tplVehicleType) throws Exception{
        Batch tplBatch = new Batch(
                deliveryBatch.getStockLocation(),
                Batch.Type.DELIVERY,
                Batch.DeliveryType.TPL,
                getTPLType(tplVendor, tplVehicleType),
                deliveryBatch.getStartTime(),
                deliveryBatch.getEndTime(),
                deliveryBatch.getHandoverTime());
        tplBatch = batchRepository.save(tplBatch);
        return tplBatch;
    }

    public Batch cloneBatch(Batch batch) {
        Batch shoppingBatch = new Batch();
        shoppingBatch.setVehicle(batch.getVehicle());
        shoppingBatch.setShift(batch.getShift());
        shoppingBatch.setStockLocation(batch.getStockLocation());
        shoppingBatch.setStartTime(batch.getStartTime());
        shoppingBatch.setEndTime(batch.getEndTime());
        shoppingBatch.setHandoverTime(batch.getHandoverTime());
        shoppingBatch.setType(batch.getType());
        shoppingBatch.setTenant(batch.getTenant());
        shoppingBatch.setCreatedBy(batch.getCreatedBy());
        return shoppingBatch;
    }

    private Batch.TplType getTPLType(String tplVendor, String tplVehicleType) throws Exception {
        if (tplVendor.equals("gosend")) {
            if (tplVehicleType.equals("bike")) {
                return Batch.TplType.GOSEND_BIKE;
            } else if (tplVehicleType.equals("car")) {
                return Batch.TplType.GOSEND_CAR;
            } else {
                throw new Exception("tpl vehicle type not supported");
            }
        } else if (tplVendor.equals("lalamove")) {
            if (tplVehicleType.equals("bike")) {
                return Batch.TplType.LALAMOVE_TWO_WHEELS;
            } else if (tplVehicleType.equals("car")) {
                return Batch.TplType.LALAMOVE_FOUR_WHEELS;
            } else if (tplVehicleType.equals("van")) {
                return Batch.TplType.LALAMOVE_VAN;
            } else {
                throw new Exception("tpl vehicle type not supported");
            }
        }
        else {
            throw new Exception("tpl vendor not supported");
        }
    }


    private void bookTPL(Batch shoppingBatch, Shipment shipment) {
        BatchItemFinalizeForm form = new BatchItemFinalizeForm();
        List<String> shipmentNumbers = new ArrayList<>(Arrays.asList(shipment.getNumber()));
        form.setShipmentNumbers(shipmentNumbers);
        batchSndService.placeOrQueueTPLDeliveries(shoppingBatch.getId(), form);
    }

    private void deleteSwitchedBatch(Batch switchedBatch) {
        Batch batch = batchRepository.findById(switchedBatch.getId()).get();
        if (batch.getJobs().isEmpty()) {
            batchRepository.delete(batch);
        }
    }

    private void cancelTPL(Batch switchedBatch, Shipment shipment) {
        if (switchedBatch.getTplType() == null) {
            return;
        }

        if (switchedBatch.getTplType().equals(Batch.TplType.GOSEND_BIKE)
                || switchedBatch.getTplType().equals(Batch.TplType.GOSEND_CAR)) {
            gosendService.cancelGosend(shipment);
        } else if (switchedBatch.getTplType().equals(Batch.TplType.LALAMOVE_TWO_WHEELS)
                || switchedBatch.getTplType().equals(Batch.TplType.LALAMOVE_FOUR_WHEELS)
                || switchedBatch.getTplType().equals(Batch.TplType.LALAMOVE_VAN)) {
            LalamoveDelivery lalamoveDelivery = shipment.getCurrentLalamoveDelivery();
            lalamoveService.cancelLalamoveBooking(shipment, lalamoveDelivery);
        }
    }
}
