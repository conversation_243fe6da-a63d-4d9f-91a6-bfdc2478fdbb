package com.happyfresh.fulfillment.common.service.bean;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.common.service.FleetTrackingService;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class FleetTrackingMessage {

    private FleetTrackingService.EventName eventName;

    private String deviceId;

    private Long userId;

    private FleetTrackingMessageData data;

}
