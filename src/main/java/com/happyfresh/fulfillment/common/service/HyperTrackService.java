package com.happyfresh.fulfillment.common.service;

import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;
import com.happyfresh.fulfillment.entity.Agent;
import com.happyfresh.fulfillment.repository.AgentRepository;
import org.jobrunr.scheduling.JobScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.context.ApplicationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Service
public class HyperTrackService {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private AgentRepository agentRepository;
    
    @Autowired
    private JobScheduler jobScheduler;

    private final Logger LOGGER = LoggerFactory.getLogger(HyperTrackService.class);

    @Async
    public ResponseEntity confirmSubscription(String subscribeUrl) {
        try {
            return new RestTemplate().getForEntity(subscribeUrl, String.class);
        } catch (Exception exception) {
            LOGGER.warn("Cannot confirm HyperTrack subcription");
            throw new UnprocessableEntityException();
        }
    }

    public void syncData(String parameter) {
        try {
            // Instead of using ActiveMQ, directly process the data or schedule with JobRunr
            jobScheduler.enqueue(() -> processHyperTrackData(parameter));
            LOGGER.info("Scheduled HyperTrack data sync job with JobRunr");
        } catch (Exception exception) {
            LOGGER.error("Cannot sync HyperTrack data", exception);
        }
    }

    public void processHyperTrackData(String message) {
        try {
            JSONArray paramArr = new JSONArray(message);
            JSONObject parameter = (JSONObject) paramArr.get(paramArr.length()-1);
            String deviceId = parameter.getString("device_id");
            Double lat = null;
            Double lon = null;
            if (parameter.getString("type").equalsIgnoreCase("location")) {
                JSONObject data = parameter.getJSONObject("data");
                JSONObject geometry = data.getJSONObject("geometry");
                // lon, lat, altitude according to:
                // https://www.hypertrack.com/docs/references/#references-webhooks-location-payload
                JSONArray coordinates = geometry.getJSONArray("coordinates");
                lon = coordinates.getDouble(0);
                lat = coordinates.getDouble(1);
            }
            if (lat != null && lon != null) {
                updateAgentLocation(deviceId, lat, lon);
            }
        } catch (JSONException exception) {
            LOGGER.error("JSONException while processing HyperTrack data", exception);
        }
    }

    @Transactional
    public void updateAgentLocation(String deviceId, Double lat, Double lon) {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);

        try {
            if (jedisLockService.lock(String.format("update_agent_location_%s", deviceId), 1)) {
                List<Agent> agents = agentRepository.fetchByDeviceId(deviceId);
                if (!agents.isEmpty()) {
                    for (Agent agent : agents) {
                        agent.setLat(lat);
                        agent.setLon(lon);
                        agent.setUpdateLocation(true);
                        agentRepository.save(agent);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("Update agent async error", e);
        } finally {
            jedisLockService.unlock();
        }
    }
}
