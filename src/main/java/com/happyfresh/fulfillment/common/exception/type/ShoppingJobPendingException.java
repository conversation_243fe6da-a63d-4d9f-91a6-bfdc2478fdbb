package com.happyfresh.fulfillment.common.exception.type;

import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;

public class ShoppingJobPendingException extends UnprocessableEntityException {

    private static final String DEFAULT_MESSAGE = "Shopping job is still pending.";

    public ShoppingJobPendingException() {
        super(DEFAULT_MESSAGE);
    }

    public ShoppingJobPendingException(String message) {
        super(message);
    }
}
