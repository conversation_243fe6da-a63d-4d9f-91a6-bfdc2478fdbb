package com.happyfresh.fulfillment.common.exception.type;

import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;

public class OnDemandReassignmentFailedException extends UnprocessableEntityException {

    public static final String ORDER_NOT_FOUND_OR_CANCELLED = "Order not found or may have been canceled. Please check the order number and make sure that it exists and not canceled.";

    private static final String DEFAULT_MESSAGE = "On Demand reassignment is failed.";

    public OnDemandReassignmentFailedException() {
        super(DEFAULT_MESSAGE);
    }

    public OnDemandReassignmentFailedException(String message) {
        super(message);
    }
}
