package com.happyfresh.fulfillment.common.exception;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiError {
    private String type;
    private String message;
    private String field;

    public ApiError(String type, String message, String field) {
        this.field = field;
        this.type = type;
        this.message = message;
    }

    public ApiError(String type, String message) {
        this.type = type;
        this.message = message;
    }


}
