package com.happyfresh.fulfillment.common.config;

import com.happyfresh.fulfillment.common.interceptor.APIRequestResponseLoggingInterceptor;
import com.happyfresh.fulfillment.common.interceptor.RequestResponseLoggingInterceptor;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpUriRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.Collections;

@Configuration
public class RestTemplateConfig {

    static Logger LOGGER = LoggerFactory.getLogger(RestTemplateConfig.class);

    @Bean(name="APIRestLoggingTemplate")
    public RestTemplate APIRestLoggingTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        ClientHttpRequestFactory factory = new BufferingClientHttpRequestFactory(new SimpleClientHttpRequestFactory());

        restTemplate.setRequestFactory(factory);
        restTemplate.setInterceptors(Collections.singletonList(new APIRequestResponseLoggingInterceptor())); // Log request & response when error
        return restTemplate;
    }

    @Bean(name="AllowGetBodyRestTemplate")
    public RestTemplate allowGetBodyRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        ClientHttpRequestFactory factory = new BufferingClientHttpRequestFactory(new SimpleClientHttpRequestFactory());

        restTemplate.setRequestFactory(factory);
        restTemplate.setInterceptors(Collections.singletonList(new RequestResponseLoggingInterceptor()));
        restTemplate.setRequestFactory(new HttpComponentsClientHttpRequestWithBodyFactory());

        return restTemplate;
    }

    private static final class HttpComponentsClientHttpRequestWithBodyFactory extends HttpComponentsClientHttpRequestFactory {
        @Override
        protected HttpUriRequest createHttpUriRequest(HttpMethod httpMethod, URI uri) {
            if (httpMethod == HttpMethod.GET) {
                return new HttpGetRequestWithEntity(uri);
            }
            return super.createHttpUriRequest(httpMethod, uri);
        }
    }

    private static final class HttpGetRequestWithEntity extends HttpEntityEnclosingRequestBase {
        public HttpGetRequestWithEntity(final URI uri) {
            super.setURI(uri);
        }

        @Override
        public String getMethod() {
            return HttpMethod.GET.name();
        }
    }

}
