package com.happyfresh.fulfillment.common.jpa;

import com.happyfresh.fulfillment.common.security.UserPrincipal;
import com.happyfresh.fulfillment.common.security.token.UserAuthenticationToken;
import org.springframework.data.domain.AuditorAware;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service("auditorProvider")
public class AuditorAwareImpl implements AuditorAware<Long> {

    @Override
    public Optional<Long> getCurrentAuditor() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication instanceof UserAuthenticationToken))
            return Optional.empty();

        UserPrincipal principal = (UserPrincipal) authentication.getPrincipal();
        return Optional.of(principal.getUser().getId());
    }

}
