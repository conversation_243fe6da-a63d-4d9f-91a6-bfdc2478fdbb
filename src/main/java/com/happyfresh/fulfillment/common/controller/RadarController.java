package com.happyfresh.fulfillment.common.controller;

import com.happyfresh.fulfillment.common.service.RadarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/radar")
public class RadarController {

    @Autowired
    private RadarService radarService;

    @PostMapping(path = "/webhook")
    public ResponseEntity<Map<String, String>> webhook(@RequestHeader(value = "x-radar-signature", defaultValue = "") String signature,
                                                       @RequestBody String body) {
        if (radarService.isValidSignature(body, signature)) {
            radarService.syncData(body);
            return new ResponseEntity<>(HttpStatus.OK);
        }
        return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
    }
}
