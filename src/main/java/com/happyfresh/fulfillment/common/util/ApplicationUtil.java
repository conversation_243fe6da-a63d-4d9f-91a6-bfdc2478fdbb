package com.happyfresh.fulfillment.common.util;

import com.happyfresh.fulfillment.common.exception.type.MinimumSnDVersionException;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

public class ApplicationUtil {

    public static final String X_FULFILLMENT_TENANT_TOKEN = "X-Fulfillment-Tenant-Token";

    public static final String X_FULFILLMENT_USER_TOKEN = "X-Fulfillment-User-Token";

    public static final String LOCALE = "Locale";

    public static final String ES_DEFAULT_DISTANCE_VALUE = "65km";

    public static final boolean isDocumentationResource(String uri) {
        return uri.matches("^(.*/api-docs.*)?((.*/swagger.*)?)$");
    }

    public static final String GEO_SEARCH_RANGE = "100m";

    public static final int GEO_SEARCH_RANGE_TIMESTAMP = 3; // in month

    public static final Double GEO_SEARCH_RANGE_VAL = 100.0;

    public static final String ROUTE_EDGE_QUERY = "route_edge_query.ftl";

    public static final Integer MINUTE_UNTIL_JOB_ABANDONED = 5;

    public static final String REQUEST_BODY = "requestBody";

    public static final String REQUEST_URL = "requestUrl";

    public static final String REQUEST_METHOD = "requestMethod";

    public static final String REQUEST_UUID = "requestUUID";

    public static final String SND_VERSION_CODE = "sndVersionCode";

    public static final int EDITING_ORDER_TIME_LIMIT_BEFORE_SLOT = 61;

    public static final String DEFAULT_TOKEN_GENERATOR = "95421378";

    public static final String GRAB_EXPRESS_OAUTH_PATH = "/grabid/v1/oauth2/token";
    
    public static final String GRAB_EXPRESS_MAIN_PATH = "/v1/deliveries";

    public static final String BRAZE_TRIGGER_CAMPAIGN_MAIN_PATH = "/campaigns/trigger/send";

    public static final String MOENGAGE_PUSH_API_PATH = "/v2/transaction/sendpush";

    public static final String CLOUD_KARAFKA = "CloudKarafka";

    public static final String CONFLUENT = "Confluent";

    public static final String SHIPMENT_ITEMS_UPDATE_KEY = "shipment_items_update_%s";

    public static final String generateRandomKey() throws NoSuchAlgorithmException {
        SecureRandom secureRandom = new SecureRandom();
        KeyGenerator keyGen = KeyGenerator.getInstance("AES");
        keyGen.init(256, secureRandom);
        SecretKey secretKey = keyGen.generateKey();
        return Base64.getEncoder().encodeToString(secretKey.getEncoded());
    }

    public static final void validateMinimumSnDVersion(String snDVersionCode, String minimumSnDVersionCode) {
        try {
            int snDVersionCodeValue = Integer.parseInt(snDVersionCode);
            if (snDVersionCodeValue < Integer.parseInt(minimumSnDVersionCode)) {
                throw new MinimumSnDVersionException(minimumSnDVersionCode, snDVersionCode);
            }
        } catch (NumberFormatException e) {
            throw new MinimumSnDVersionException(minimumSnDVersionCode, snDVersionCode);
        }
    }
}
