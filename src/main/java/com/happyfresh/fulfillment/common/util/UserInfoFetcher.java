package com.happyfresh.fulfillment.common.util;

import com.happyfresh.fulfillment.common.security.UserPrincipal;
import com.happyfresh.fulfillment.common.security.token.ClientAuthenticationToken;
import com.happyfresh.fulfillment.common.security.token.UserAuthenticationToken;
import com.happyfresh.fulfillment.entity.Tenant;
import com.happyfresh.fulfillment.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Component
@Scope
public class UserInfoFetcher {

    public User getAuthenticatedUser() {
        UserPrincipal principal = userPrincipal();
        return (principal != null) ? principal.getUser() : null;
    }

    public Long getTenantId() {
        UserPrincipal principal = userPrincipal();
        return (principal != null) ? principal.getTenant().getId() : null;
    }

    public Tenant getTenant() {
        UserPrincipal principal = userPrincipal();
        return (principal != null) ? principal.getTenant() : null;
    }

    public String getFormattedRequestUserToken(String userTokenHeader) {
        String formattedUserToken = null;
        if (StringUtils.isNotEmpty(userTokenHeader)) {
            if (userTokenHeader.length() > 6) {
                formattedUserToken = "[" + userTokenHeader.substring(0, 3) + "..." + userTokenHeader.substring(userTokenHeader.length() - 3) + "]";
            } else {
                formattedUserToken = "[" + userTokenHeader + "]";
            }
        }
        return formattedUserToken;
    }

    private UserPrincipal userPrincipal() {
        Authentication authentication =  SecurityContextHolder.getContext().getAuthentication();
        UserPrincipal userPrincipal = null;
        if (authentication instanceof UserAuthenticationToken || authentication instanceof ClientAuthenticationToken) {
            userPrincipal = (UserPrincipal) authentication.getPrincipal();
        }
        return userPrincipal;
    }
}
