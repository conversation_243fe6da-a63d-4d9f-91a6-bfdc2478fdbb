package com.happyfresh.fulfillment.common.vrp;

import com.graphhopper.jsprit.core.problem.constraint.HardActivityConstraint;
import com.graphhopper.jsprit.core.problem.misc.JobInsertionContext;
import com.graphhopper.jsprit.core.problem.solution.route.activity.DeliverShipment;
import com.graphhopper.jsprit.core.problem.solution.route.activity.PickupShipment;
import com.graphhopper.jsprit.core.problem.solution.route.activity.TourActivity;
import com.happyfresh.fulfillment.common.service.VRPService;
import com.happyfresh.fulfillment.common.util.VrpUtil;
import org.apache.commons.lang3.StringUtils;

public class DisableMultiStorePickupDeliveryConstraint implements HardActivityConstraint {
    @Override
    public ConstraintsStatus fulfilled(JobInsertionContext iFacts, TourActivity prevAct, TourActivity newAct, TourActivity nextAct, double prevActDepTime) {

        if (prevAct instanceof PickupShipment && newAct instanceof PickupShipment) {
            String prevActStockLocationId = VrpUtil.getValueOfUserData(prevAct, VRPService.USER_DATA_STOCK_LOCATION_ID);
            String newActStockLocationId = VrpUtil.getValueOfUserData(newAct, VRPService.USER_DATA_STOCK_LOCATION_ID);
            if (!StringUtils.equals(prevActStockLocationId, newActStockLocationId))
                return ConstraintsStatus.NOT_FULFILLED;
        }

        if (newAct instanceof PickupShipment && nextAct instanceof PickupShipment) {
            String newActStockLocationId = VrpUtil.getValueOfUserData(newAct, VRPService.USER_DATA_STOCK_LOCATION_ID);
            String nextActStockLocationId = VrpUtil.getValueOfUserData(nextAct, VRPService.USER_DATA_STOCK_LOCATION_ID);
            if (!StringUtils.equals(newActStockLocationId, nextActStockLocationId))
                return ConstraintsStatus.NOT_FULFILLED;
        }

        if (prevAct instanceof DeliverShipment && newAct instanceof DeliverShipment) {
            String prevActStockLocationId = VrpUtil.getValueOfUserData(prevAct, VRPService.USER_DATA_STOCK_LOCATION_ID);
            String newActStockLocationId = VrpUtil.getValueOfUserData(newAct, VRPService.USER_DATA_STOCK_LOCATION_ID);
            if (!StringUtils.equals(prevActStockLocationId, newActStockLocationId))
                return ConstraintsStatus.NOT_FULFILLED;
        }

        if (newAct instanceof DeliverShipment && nextAct instanceof DeliverShipment) {
            String newActStockLocationId = VrpUtil.getValueOfUserData(newAct, VRPService.USER_DATA_STOCK_LOCATION_ID);
            String nextActStockLocationId = VrpUtil.getValueOfUserData(nextAct, VRPService.USER_DATA_STOCK_LOCATION_ID);
            if (!StringUtils.equals(newActStockLocationId, nextActStockLocationId))
                return ConstraintsStatus.NOT_FULFILLED;
        }

        return ConstraintsStatus.FULFILLED;
    }
}
