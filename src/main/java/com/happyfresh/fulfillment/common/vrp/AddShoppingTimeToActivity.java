package com.happyfresh.fulfillment.common.vrp;

import com.graphhopper.jsprit.core.algorithm.state.StateManager;
import com.graphhopper.jsprit.core.algorithm.state.StateUpdater;
import com.graphhopper.jsprit.core.problem.cost.ForwardTransportTime;
import com.graphhopper.jsprit.core.problem.cost.VehicleRoutingActivityCosts;
import com.graphhopper.jsprit.core.problem.solution.route.VehicleRoute;
import com.graphhopper.jsprit.core.problem.solution.route.activity.ActivityVisitor;
import com.graphhopper.jsprit.core.problem.solution.route.activity.TourActivity;
import com.graphhopper.jsprit.core.util.ActivityTimeTracker;
import com.happyfresh.fulfillment.common.service.VRPService;
import org.apache.commons.lang3.StringUtils;

public class AddShoppingTimeToActivity implements ActivityVisitor, StateUpdater {

    private StateManager stateManager;

    private ActivityTimeTracker timeTracker;

    private double shoppingTime;

    private VehicleRoute route;

    public AddShoppingTimeToActivity(StateManager stateManager, ForwardTransportTime transportTime, VehicleRoutingActivityCosts activityCosts) {
        super();
        this.stateManager = stateManager;
        this.timeTracker = new ActivityTimeTracker(transportTime, activityCosts);
    }

    @Override
    public void begin(VehicleRoute route) {
        shoppingTime = 0.0;
        if (StringUtils.equals(route.getVehicle().getType().getTypeId(), "ranger") && !route.getTourActivities().getActivities().isEmpty()) {
            for (TourActivity activity : route.getTourActivities().getActivities()) {
                shoppingTime += activity.getSize().get(VRPService.DIMENSION_TOTAL_SHOPPING_TIME);
            }
        }
        timeTracker.begin(route);
        this.route = route;
    }

    @Override
    public void visit(TourActivity activity) {
        timeTracker.visit(activity);
        activity.setArrTime(shoppingTime + timeTracker.getActArrTime());
        activity.setEndTime(shoppingTime + timeTracker.getActEndTime());
    }

    @Override
    public void finish() {
        timeTracker.finish();
        this.route.getEnd().setArrTime(shoppingTime + timeTracker.getActArrTime());
    }
}
