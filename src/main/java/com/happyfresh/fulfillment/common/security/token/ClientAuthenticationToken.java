package com.happyfresh.fulfillment.common.security.token;

import com.happyfresh.fulfillment.common.security.UserPrincipal;
import com.happyfresh.fulfillment.entity.Tenant;
import org.springframework.security.authentication.AbstractAuthenticationToken;

public class ClientAuthenticationToken extends AbstractAuthenticationToken {

    private final UserPrincipal principal;

    public ClientAuthenticationToken(Tenant tenant, Boolean authenticated) {
        super(null);
        this.principal = new UserPrincipal(null, tenant);
        this.setAuthenticated(authenticated);
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getPrincipal() {
        return this.principal;
    }
}
