package com.happyfresh.fulfillment.common.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

@Getter
@Setter
public class MoengageAPIPayload {
    public static final String PAYLOAD_KEY_ANDROID = "ANDROID";

    private String appId;
    private String campaignName;
    private String signature;
    private String requestType = "push";
    private String[] targetPlatform = new String[]{"ANDROID"};
    private String targetAudience = "User";
    private TargetUserAttribute targetUserAttributes = new TargetUserAttribute();
    private Map<String,Object> payload = new HashMap<>();
    private Map<String,Object> campaignDelivery = new HashMap<>();

    public MoengageAPIPayload(){
        this.payload.put(PAYLOAD_KEY_ANDROID, new AndroidPayload());
        this.campaignDelivery.put("type","soon");
    }

    public List<MoengageAPIPayload> splitByMaxRecipient(int maxRecipientCount){
        List<MoengageAPIPayload> result = new ArrayList<>();
        List<String> recipientList = this.targetUserAttributes.getAttributeValue();
        List<List<String>> recipientPartition = Lists.partition(recipientList, maxRecipientCount);
        for(List<String> recipientSubList : recipientPartition){
            MoengageAPIPayload subPayload = new MoengageAPIPayload();
            subPayload.appId = this.appId;
            subPayload.campaignName = this.campaignName;
            subPayload.signature = this.signature;
            subPayload.requestType = this.requestType;
            subPayload.targetPlatform = this.targetPlatform;
            subPayload.targetAudience = this.targetAudience;
            subPayload.targetUserAttributes = new TargetUserAttribute();
            subPayload.targetUserAttributes.attributeValue = recipientSubList;
            subPayload.payload = this.payload;
            subPayload.campaignDelivery = this.campaignDelivery;
            result.add(subPayload);
        }
        return result;
    }

    @Getter
    @Setter
    public static class AndroidPayload {
        private String message;
        private String title;
        private DefaultAction defaultAction;
    }

    @Getter
    @Setter
    public static class DefaultAction {
        private String type = "navigation";
        private String value = "none";
        private Map<String,Object> kvPairs;
    }

    @Getter
    @Setter
    public static class TargetUserAttribute {
        private String attribute = "USER_ATTRIBUTE_UNIQUE_ID";
        private String comparisonParameter = "in";
        private List<String> attributeValue = new ArrayList<>();
    }
}

