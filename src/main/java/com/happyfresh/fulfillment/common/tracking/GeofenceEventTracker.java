package com.happyfresh.fulfillment.common.tracking;

import com.google.common.collect.ImmutableMap;
import com.happyfresh.fulfillment.common.service.bean.RadarEventPayload;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationContext;

import java.util.List;
import java.util.stream.Collectors;

@Setter
@Getter
public class GeofenceEventTracker extends CoralogixTracker {

    private RadarEventPayload radarEvent;

    public GeofenceEventTracker(ApplicationContext applicationContext, RadarEventPayload radarEvent) {
        super("Geofence Data Log", applicationContext);
        this.radarEvent = radarEvent;
    }

    @Override
    protected String getSubsystemSuffix() {
        return null;
    }

    @Override
    protected ImmutableMap<String, Object> buildProperties() {
        ImmutableMap.Builder<String, Object> builder = ImmutableMap.builder();
        putOrEmpty(builder, "user_id", this.radarEvent.getUser().getUserId());

        List<String> userCoordinates =  this.radarEvent.getLocation().getCoordinates().stream().map(String::valueOf).collect(Collectors.toList());
        putOrEmpty(builder, "user_coordinates", String.join(",", userCoordinates));

        String sGeofenceCoordinates = "";
        if (this.getRadarEvent().getGeofence().getGeometryCenter() != null){
            List<String> geofenceCoordinates = this.getRadarEvent().getGeofence().getGeometryCenter().getCoordinates().stream().map(String::valueOf).collect(Collectors.toList());
            sGeofenceCoordinates = String.join(",", geofenceCoordinates);
        }
        putOrEmpty(builder, "geofence_coordinates", sGeofenceCoordinates);

        putOrEmpty(builder, "tag", this.radarEvent.getGeofence().getTag());
        putOrEmpty(builder, "event_type", this.radarEvent.getType());
        putOrEmpty(builder, "external_id", this.radarEvent.getGeofence().getExternalId());
        putOrEmpty(builder, "create_timestamp", this.radarEvent.getCreatedAt());
        putOrEmpty(builder, "actual_create_timestamp", this.radarEvent.getActualCreatedAt());
        return builder.build();
    }

}
