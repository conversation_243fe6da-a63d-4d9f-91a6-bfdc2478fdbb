package com.happyfresh.fulfillment.common.tracking;

import com.google.common.collect.ImmutableMap;
import com.happyfresh.fulfillment.common.service.CoralogixAPIService;
import com.happyfresh.fulfillment.common.util.WebRequestLogger;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Optional;

@Setter
public abstract class CoralogixTracker {

    @NotNull
    private ApplicationContext applicationContext;

    @NotBlank
    protected String eventCategory;

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    protected CoralogixTracker(String eventCategory, ApplicationContext applicationContext) {
        this.eventCategory = eventCategory;
        this.applicationContext = applicationContext;
    }

    public void track() {
        try {
            ImmutableMap<String, Object> properties = buildProperties();
            CoralogixAPIService coralogixAPIService = applicationContext.getBean(CoralogixAPIService.class);
            coralogixAPIService.sendLog(CoralogixAPIService.LogSeverity.INFO, getSubsystemSuffix(), eventCategory, getClass().getSimpleName(), "track", properties);
        } catch (Exception e) {
            logTrackingException(e);
        }
    }

    protected abstract String getSubsystemSuffix();

    protected abstract ImmutableMap<String, Object> buildProperties();

    protected Logger getLogger() {
        return this.logger;
    }

    protected void putOrEmpty(ImmutableMap.Builder<String, Object> builder, String key, Object value) {
        builder.put(key, Optional.ofNullable(value).orElse(""));
    }

    protected void logTrackingException(Exception e) {
        String message = "Failed tracking to Coralogix";
        try {
            WebRequestLogger webRequestLogger = applicationContext.getBean(WebRequestLogger.class);
            webRequestLogger.error(getLogger(), message, e);
        } catch (Exception ex) {
            getLogger().error(message, e);
        }
    }
}
