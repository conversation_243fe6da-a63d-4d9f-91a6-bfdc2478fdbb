package com.happyfresh.fulfillment.common.messaging.activemq.type;

public class MessageDestination {
    // These destinations are now handled by JobRunr
    public static final String GRAB_BOOKING = "grab.booking";
    public static final String HYPERTRACK_SYNC = "hypertrack.sync";
    public static final String LALAMOVE_DELIVERY = "lalamove.delivery";
    public static final String RADAR_SYNC = "radar.sync";
    public static final String FLEET_TRACKING_SYNC = "fleet.tracking.sync";
    
    // This class is kept for backward compatibility
    // All messaging is now handled by JobRunr
}

