package com.happyfresh.fulfillment.common.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.annotation.EnableRetry;

@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "app")
@EnableRetry
public class AppProperty {
    private String minSndVersionCodeToTakeJob;
}
