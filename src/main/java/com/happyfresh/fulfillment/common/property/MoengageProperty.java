package com.happyfresh.fulfillment.common.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.validation.constraints.NotNull;

@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "moengage")
public class MoengageProperty {
    @NotNull
    private Integer pushMaxRecipient;
}
