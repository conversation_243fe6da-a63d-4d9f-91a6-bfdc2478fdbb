package com.happyfresh.fulfillment.lalamove.service;

import com.amazonaws.util.Base16Lower;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.property.LalamoveProperty;
import com.happyfresh.fulfillment.common.service.HmacService;
import com.happyfresh.fulfillment.common.service.JedisLockService;
import com.happyfresh.fulfillment.common.util.WebRequestLogger;
import com.happyfresh.fulfillment.entity.LalamoveDelivery;
import com.happyfresh.fulfillment.lalamove.form.v3.LalamoveV3WebhookForm;
import com.happyfresh.fulfillment.repository.LalamoveDeliveryRepository;
import com.happyfresh.fulfillment.slot.scheduler.SlotGeneratorScheduler;
import org.apache.commons.lang3.StringUtils;
import org.jobrunr.jobs.context.JobRunrDashboardLogger;
import org.mapstruct.Mapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;
import java.util.Optional;

@Service
public class LalamoveWebhookService {

    private static final Logger logger = new JobRunrDashboardLogger(LoggerFactory.getLogger(LalamoveWebhookService.class));

    @Autowired
    private LalamoveProperty lalamoveProperty;

    @Autowired
    private HmacService hmacService;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private LalamoveDeliveryRepository lalamoveDeliveryRepository;

    @Autowired
    private LalamoveDeliveryUpdateService lalamoveDeliveryUpdateService;

    public boolean isValidSignature(LalamoveV3WebhookForm lalamoveV3WebhookForm, String country, Map<String, Object> fields) {
        if (StringUtils.isBlank(lalamoveV3WebhookForm.getSignature()))
            return false;

        final String apiSecret = lalamoveProperty.getApiSecrets().get(country.toLowerCase());
        final String httpMethod = "POST";
        final String path = LalamoveV3WebhookForm.WEBHOOK_PATH;
        try {
            String body = mapper.writer().writeValueAsString(fields.get("data"));
            String rawSignature = lalamoveV3WebhookForm.getTimestamp() + "\r\n" + httpMethod + "\r\n" +
                    path + "\r\n\r\n" + body;

            Mac sha256HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(apiSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256HMAC.init(secretKey);

            String signature = Base16Lower.encodeAsString(sha256HMAC.doFinal(rawSignature.getBytes(StandardCharsets.UTF_8)));

            return signature.equals(lalamoveV3WebhookForm.getSignature());
        } catch (Exception e) {
            logger.error("Error when validate lalamove webhook signature.", e);
            return false;
        }
    }

    private boolean isValidV3WebhookForm(LalamoveV3WebhookForm form){
        if (!form.getEventVersion().equalsIgnoreCase("v3")){
            logger.info("Webhook event version is {}, not processed in v3 handle class", form.getEventVersion());
            return false;
        }

        if (!LalamoveV3WebhookForm.processedEvents().contains(form.getEventType())) {
            logger.info("Webhook event type {} for event id {} not processed because it has not been handled",
                    form.getEventType(), form.getEventId());
            return false;
        }

        return true;
    }

    private boolean isValidLalamoveDelivery(LalamoveV3WebhookForm form, Optional<LalamoveDelivery> optDelivery) {
        if (optDelivery.isEmpty() || !LalamoveDelivery.Status.getUpdatableStatuses().contains(optDelivery.get().getStatus())) {
            logger.info("External order id {} on hff_lalamove_delivery table has not updatable status or empty delivery record", form.getData().getOrder().getOrderId());
            return false;
        }

        LalamoveDelivery delivery = optDelivery.get();
        LocalDateTime latestStatusChangedAt = LocalDateTime.ofInstant(Instant.ofEpochSecond(form.getTimestamp()), ZoneId.of("UTC"));
        if (delivery.getLatestStatusChangedAt() != null && delivery.getLatestStatusChangedAt().isAfter(latestStatusChangedAt)) {
            logger.info("Skip webhook order id {}, event type {}, webhookTs {}, latestStatusChangedAt {}", form.getData().getOrder().getOrderId(),
                    form.getEventType(), latestStatusChangedAt, delivery.getLatestStatusChangedAt());
            return false;
        }
        return true;
    }


    @Transactional
    public void processData(LalamoveV3WebhookForm form) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            logger.info("form: {}", mapper.writer().writeValueAsString(form));
        } catch (JsonProcessingException e) {
            logger.error("Error parsing lalamove form", e);
        }

        String lockKey = "lalamove_webhook_".concat(form.getEventId());

        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(lockKey)) {
                if (!isValidV3WebhookForm(form)){
                    return;
                }

                Optional<LalamoveDelivery> optDelivery = lalamoveDeliveryRepository.findByExternalOrderId(form.getData().getOrder().getOrderId());
                if(!isValidLalamoveDelivery(form, optDelivery))
                    return;

                lalamoveDeliveryUpdateService.handleLalamoveWebhookEventType(form, optDelivery.get());

            } else {
                WebRequestLogger webRequestLogger = applicationContext.getBean(WebRequestLogger.class);
                webRequestLogger.error(logger, "Acquire redis lock timeout");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            logger.error("Error when processing lalamove webhook ", e);
        } finally {
            jedisLockService.unlock();
        }
    }

}
