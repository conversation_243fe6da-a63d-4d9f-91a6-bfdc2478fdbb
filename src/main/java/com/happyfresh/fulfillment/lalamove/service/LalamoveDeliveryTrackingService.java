package com.happyfresh.fulfillment.lalamove.service;

import com.google.common.collect.ImmutableMap;
import com.happyfresh.fulfillment.common.service.CoralogixAPIService;
import com.happyfresh.fulfillment.entity.Country;
import com.happyfresh.fulfillment.entity.LalamoveDelivery;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.StockLocation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Optional;


@Service
public class LalamoveDeliveryTrackingService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CoralogixAPIService coralogixAPIService;

    @Transactional
    public void track(String eventName, LalamoveDelivery lalamoveDelivery) {
        Shipment shipment = lalamoveDelivery.getShipment();
        StockLocation stockLocation = lalamoveDelivery.getShipment().getSlot().getStockLocation();
        Country country = stockLocation.getState().getCountry();

        try {
            ImmutableMap.Builder<String, Object> builder = ImmutableMap.builder();
            putOrEmpty(builder, "recipient_address", shipment.getAddressLine());
            putOrEmpty(builder, "sent_at", lalamoveDelivery.getStatuses().getOrDefault("PICKED_UP", "0"));
            putOrEmpty(builder, "recipient_name", shipment.getAddressName());
            putOrEmpty(builder, "status", lalamoveDelivery.getStatus());
            putOrEmpty(builder, "lalamove_order_id", lalamoveDelivery.getExternalOrderId());
            putOrEmpty(builder, "driver_licence_plate", lalamoveDelivery.getDriverPlateNumber());
            putOrEmpty(builder, "driver_name", lalamoveDelivery.getDriverName());
            putOrEmpty(builder, "driver_photo_url", lalamoveDelivery.getDriverPhoto());
            putOrEmpty(builder, "order_number", shipment.getOrderNumber());
            putOrEmpty(builder, "sender_address", stockLocation.getAddress1());
            putOrEmpty(builder, "timestamp", LocalDateTime.now().toEpochSecond(ZoneOffset.UTC));
            putOrEmpty(builder, "event", eventName);
            putOrEmpty(builder, "driver_phone", lalamoveDelivery.getDriverPhone());
            putOrEmpty(builder, "country_id", country.getId());
            putOrEmpty(builder, "create_timestamp", lalamoveDelivery.getCreatedAt().toEpochSecond(ZoneOffset.UTC));
            putOrEmpty(builder, "update_timestamp", lalamoveDelivery.getUpdatedAt().toEpochSecond(ZoneOffset.UTC));
            putOrEmpty(builder, "scheduled_at", lalamoveDelivery.getScheduleAt());
            putOrEmpty(builder, "service_type_initial", lalamoveDelivery.getInitialServiceType());
            putOrEmpty(builder, "service_type", lalamoveDelivery.getServiceType());
            putOrEmpty(builder, "delivery_fee_initial", lalamoveDelivery.getInitialPrice());
            putOrEmpty(builder, "delivery_fee", lalamoveDelivery.getPrice());
            ImmutableMap<String, Object> properties = builder.build();

            coralogixAPIService.sendLog(CoralogixAPIService.LogSeverity.INFO, eventName, eventName, getClass().getSimpleName(), "track", properties);
        }   catch (Exception e) {
            logger.error(eventName, e);
        }
    }

    private void putOrEmpty(ImmutableMap.Builder<String, Object> builder, String key, Object value) {
        builder.put(key, Optional.ofNullable(value).orElse(""));
    }
}
