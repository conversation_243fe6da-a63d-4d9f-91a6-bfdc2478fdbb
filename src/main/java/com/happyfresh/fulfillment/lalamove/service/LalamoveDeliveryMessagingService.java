package com.happyfresh.fulfillment.lalamove.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.JsonObject;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.property.LalamoveProperty;
import com.happyfresh.fulfillment.common.service.WebhookPublisherService;
import com.happyfresh.fulfillment.entity.LalamoveDelivery;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.Tenant;
import com.happyfresh.fulfillment.lalamove.mapper.LalamoveDeliveryMapper;
import com.happyfresh.fulfillment.repository.LalamoveDeliveryRepository;
import org.jobrunr.scheduling.JobScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;

@Service
public class LalamoveDeliveryMessagingService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private LalamoveDeliveryRepository lalamoveDeliveryRepository;

    @Autowired
    private LalamoveProperty lalamoveProperty;

    @Autowired
    private LalamoveDeliveryMapper lalamoveDeliveryMapper;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private WebhookPublisherService webhookPublisherService;

    @Autowired
    private JobScheduler jobScheduler;

    @Autowired
    private LalamoveDeliveryUpdateService lalamoveDeliveryUpdateService;

    public void queueJob(Long id, Long delay) {
        jobScheduler.schedule(
                Instant.now().plus(delay, ChronoUnit.MINUTES),
                () -> lalamoveDeliveryUpdateService.updateById(id)
        );
        logSentMessage(id);
    }

    public void queueUpdateJob(Long id) {
        queueJob(id, (long) lalamoveProperty.getUpdaterDelay());
    }

    public void queueUpdateJob(LalamoveDeliveryUpdateService.DeliveryUpdate deliveryUpdate) {
        queueJob(deliveryUpdate.getUpdatedDelivery().getId(), deliveryUpdate.getUpdaterDelay());
    }

    @Transactional
    public void publishSynchronizeWebhook(LalamoveDelivery delivery) {
        String path = String.format("/shipments/%s/", delivery.getShipment().getNumber());
        Tenant tenant = delivery.getTenant();
        WebhookType webhookType = WebhookType.LALAMOVE_SYNCHRONIZE;
        String rootName = "lalamove_delivery";
        Object body = lalamoveDeliveryMapper.toLalamoveDeliveryPresenter(delivery);
        ObjectNode wrapperNode = JsonNodeFactory.instance.objectNode();
        JsonNode bodyNode = mapper.convertValue(body, JsonNode.class);
        wrapperNode.set(rootName, bodyNode);

        webhookPublisherService.publish(webhookType, wrapperNode, path, tenant.getId());
    }

    @Transactional
    public void publishFailedBookingWebhook(LalamoveDelivery delivery, String errorMessage) {
        ObjectNode objectNode = getWebhookErrorObjectResponse(errorMessage);

        String path = String.format("/shipments/%s/", delivery.getShipment().getNumber());
        Tenant tenant = delivery.getTenant();
        lalamoveDeliveryRepository.delete(delivery.getId());
        WebhookType webhookType = WebhookType.LALAMOVE_BOOKING_ERROR_SWITCH_TO_HF;
        webhookPublisherService.publish(webhookType, objectNode, path, tenant.getId());
    }

    @Transactional
    public void publishFailedBookingWebhook(Shipment shipment, String errorMessage) {
        ObjectNode objectNode = getWebhookErrorObjectResponse(errorMessage);

        String path = String.format("/shipments/%s/", shipment.getNumber());
        Tenant tenant = shipment.getTenant();
        LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findByShipment(shipment).orElse(null);
        if (lalamoveDelivery != null) {
            lalamoveDeliveryRepository.delete(lalamoveDelivery.getId());
        }
        WebhookType webhookType = WebhookType.LALAMOVE_BOOKING_ERROR_SWITCH_TO_HF;
        webhookPublisherService.publish(webhookType, objectNode, path, tenant.getId());
    }

    private ObjectNode getWebhookErrorObjectResponse(String errorMessage) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("error", errorMessage);
        ObjectNode objectNode = null;
        try {
            objectNode = (ObjectNode) mapper.readTree(jsonObject.toString());
        } catch (IOException e) {
            logger.error("Webhook Error: {}", errorMessage);
        }
        return objectNode;
    }

    public void logReceivedMessage(Long id) {
        logger.info("Received Lalamove update task message for id {}", id);
    }

    public void logSentMessage(Long id) {
        logger.info("Sent Lalamove update task message for id {}", id);
    }

    public void logEndOfUpdateTasks(Long id) {
        logger.info("Final update task processed for Lalamove delivery id {}", id);
    }
}
