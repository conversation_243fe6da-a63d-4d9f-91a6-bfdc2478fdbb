package com.happyfresh.fulfillment.lalamove.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.JsonObject;
import com.happyfresh.fulfillment.batch.exception.SwitchToHFFailedException;
import com.happyfresh.fulfillment.batch.service.BatchService;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.exception.type.LalamoveServiceTypeNotAvailableException;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.property.LalamoveProperty;
import com.happyfresh.fulfillment.common.service.JedisLockService;
import com.happyfresh.fulfillment.common.service.WebhookPublisherService;
import com.happyfresh.fulfillment.common.service.radar.RadarApiService;
import com.happyfresh.fulfillment.enabler.service.StratoWebhookService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.lalamove.exception.LalamoveException;
import com.happyfresh.fulfillment.lalamove.mapper.LalamoveDeliveryMapper;
import com.happyfresh.fulfillment.lalamove.mapper.LalamoveOrderDetailMapper;
import com.happyfresh.fulfillment.lalamove.model.LalamoveDeliveryParameter;
import com.happyfresh.fulfillment.lalamove.model.LalamoveDriverDetail;
import com.happyfresh.fulfillment.lalamove.model.LalamoveOrderDetail;
import com.happyfresh.fulfillment.lalamove.model.LalamoveServiceTypeEnum;
import com.happyfresh.fulfillment.lalamove.presenter.LalamoveDriverDetailPresenter;
import com.happyfresh.fulfillment.lalamove.presenter.LalamoveGetQuotationPresenter;
import com.happyfresh.fulfillment.lalamove.presenter.LalamoveOrderDetailPresenter;
import com.happyfresh.fulfillment.lalamove.presenter.LalamovePlaceOrderPresenter;
import com.happyfresh.fulfillment.lalamove.presenter.v3.*;
import com.happyfresh.fulfillment.lalamove.service.api.LalamoveApiService;
import com.happyfresh.fulfillment.lalamove.service.api.v3.LalamoveApiV3Service;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.user.service.UserService;
import org.apache.logging.log4j.ThreadContext;
import org.jobrunr.jobs.context.JobRunrDashboardLogger;
import org.jobrunr.scheduling.BackgroundJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class LalamoveService {

    private final Logger logger = new JobRunrDashboardLogger(LoggerFactory.getLogger(this.getClass()));
    private static final String BATCH_TYPE_NOT_LALAMOVE = "Batch fleet type not Lalamove";

    private static final List<String> FourWheelServiceTypes = Arrays.asList(
        LalamoveServiceTypeEnum.SEDAN.toString(),
        LalamoveServiceTypeEnum.CAR.toString());

    private static final List<String> VanServiceTypes = Arrays.asList(
        LalamoveServiceTypeEnum.VAN.toString(), 
        LalamoveServiceTypeEnum.MPV.toString());

    @Autowired
    private LalamoveApiService lalamoveApiService;

    @Autowired
    private LalamoveApiV3Service lalamoveApiV3Service;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private LalamoveProperty lalamoveProperty;

    @Autowired
    private LalamoveServiceTypeRepository serviceTypeRepository;

    @Autowired
    private LalamoveDeliveryRepository lalamoveDeliveryRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private WebhookPublisherService webhookPublisherService;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private LalamoveDeliveryMapper lalamoveDeliveryMapper;

    @Autowired
    private LalamoveDeliveryTrackingService lalamoveDeliveryTrackingService;

    @Autowired
    private LalamoveDeliveryMessagingService lalamoveDeliveryMessagingService;

    @Autowired
    private RadarApiService radarApiService;

    @Autowired
    private StratoWebhookService stratoWebhookService;

    @Autowired
    private BatchService batchService;

    @Autowired
    private UserService userService;

    @Autowired
    private LalamoveOrderDetailMapper orderDetailMapper;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private CountryRepository countryRepository;

    @Transactional(readOnly = true)
    public List<LalamoveServiceType> findAllLalamoveServiceTypeByStockLocation(StockLocation stockLocation) {
        if(Boolean.TRUE.equals(stockLocation.getTenant().isEnableLalamoveV3())){
            return this.findAllActiveV3ByStockLocationOrderByMaxWeightAsc(stockLocation);
        }
        return serviceTypeRepository.findAllV2ByCountryIdOrderByMaxWeightAsc(stockLocation.getState().getCountry().getId());
    }

    @Transactional(readOnly = true)
    public List<LalamoveServiceType> findAllActiveV3ByStockLocationOrderByMaxWeightAsc(StockLocation stockLocation) {
        List<LalamoveServiceType> lalamoveServiceTypes = serviceTypeRepository.findAllActiveV3ByCityCode(stockLocation.getState().getLalamoveCityCode());
        lalamoveServiceTypes.sort(Comparator.comparing(LalamoveServiceType::getMaxWeight));
        return lalamoveServiceTypes;
    }

    @Transactional(readOnly = true)
    public LalamoveServiceType findServiceTypeByDimensionAndWeight(StockLocation stockLocation, Double shipmentVolume, Double shipmentWeight) {
        List<LalamoveServiceType> services = this.findAllLalamoveServiceTypeByStockLocation(stockLocation);

        LalamoveServiceType selectedService = null;
        for (LalamoveServiceType serviceType : services) {
            Double serviceVolumeInLitre = getServiceVolumeInLitre(serviceType);
            if (shipmentVolume <= serviceVolumeInLitre && shipmentWeight <= serviceType.getMaxWeight()) {
                selectedService = serviceType;
                break;
            }
        }
        return selectedService;
    }

    private LalamoveServiceType findServiceTypeByShipment(Shipment shipment) {
        StockLocation stockLocation = shipment.getSlot().getStockLocation();
        double totalVolumeInLiter = shipment.getFinalizedTotalVolumeInML() / 1000;
        double totalWeightInKg = shipment.getFinalizedTotalWeightInKG();

        Optional<Job> optionalDeliveryJob = shipment.getDeliveryJob();
        LalamoveServiceType serviceType;

        if (optionalDeliveryJob.isEmpty()) {
            throw new EntityNotFoundException("No delivery job assigned to shipment");
        }

        Job deliveryJob = optionalDeliveryJob.get();
        Batch.TplType tplType = deliveryJob.getBatch().getTplType();
        if (tplType.equals(Batch.TplType.LALAMOVE_VAN)) {
            serviceType = getVanServiceTypeByCountryV3(stockLocation);
        } else if (tplType.equals(Batch.TplType.LALAMOVE_FOUR_WHEELS)) {
            serviceType = getFourWheelsServiceTypeByCountryV3(stockLocation);
        } else if (tplType.equals(Batch.TplType.LALAMOVE_TWO_WHEELS)) {
            serviceType = getServiceTypeByEnum(LalamoveServiceTypeEnum.MOTORCYCLE, null, stockLocation);
        } else {
            throw new LalamoveServiceTypeNotAvailableException("LalamoveServiceType not found for tpl type " + tplType + " for shipment " + shipment.getNumber());
        }

        logger.info("[Lalamove] TplType: {}", tplType);
        logger.info("[Lalamove] Service Type: {}", serviceType.getKey());

        if (serviceType == null)
            throw new LalamoveServiceTypeNotAvailableException("LalamoveServiceType not found (volume: " + totalVolumeInLiter + ", weight: " + totalWeightInKg + ") for shipment " + shipment.getNumber());

        return serviceType;
    }

    public Double getServiceVolumeInLitre(LalamoveServiceType service) {
        // Dimension in cm
        return (service.getMaxHeight() * service.getMaxLength() * service.getMaxWidth()) / 1000;
    }

    @Transactional(readOnly = true)
    public Double getDeliveryQuotation(LalamoveServiceType serviceType, Shipment shipment, StockLocation stockLocation) {
        Double fee = 0.0;
        Role role = roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, stockLocation.getTenant().getId());
        if (role != null) {
            User user = userRepository.findByRolesContainingAndTenantId(role, stockLocation.getTenant().getId());
            /*
                Slot Availability: shipment.getSlot() is null, so we use +1 hour as scheduleAt approximation.
                Set Slot: shipment.getSlot() is not null.
             */
            LocalDateTime scheduleAt = LocalDateTime.now().plusHours(1);
            Slot slot = shipment.getSlot();
            if (slot != null)
                scheduleAt = slot.getStartTime();

            fee = getFee(shipment, scheduleAt, serviceType, user, stockLocation);
        }
        return fee;
    }

    @Transactional(readOnly = true)
    public LalamoveServiceType getServiceTypeByEnum(LalamoveServiceTypeEnum lalamoveServiceTypeEnum, Country country, StockLocation stockLocation) {
        LalamoveServiceType lalamoveServiceType;
        Tenant tenant = stockLocation.getTenant();
        State state = stockLocation.getState();
        if (Boolean.TRUE.equals(tenant.isEnableLalamoveV3())) {
            lalamoveServiceType = serviceTypeRepository.findV3ByKeyAndCityCode(lalamoveServiceTypeEnum.toString(), state.getLalamoveCityCode());
        } else {
            lalamoveServiceType = serviceTypeRepository.findV2ByKeyAndCountryId(lalamoveServiceTypeEnum.toString(), country.getId());
        }
        return lalamoveServiceType;
    }

    @Transactional(readOnly = true)
    public LalamoveServiceType getLargestServiceTypeByCountry(Country country, StockLocation stockLocation) {
        LalamoveServiceType lalamoveServiceType;
        Tenant tenant = stockLocation.getTenant();
        State state = stockLocation.getState();
        if (Boolean.TRUE.equals(tenant.isEnableLalamoveV3())) {
            lalamoveServiceType = serviceTypeRepository.findFirstV3ByCityCodeOrderByMaxWeightDesc(state.getLalamoveCityCode());
        } else {
            lalamoveServiceType = serviceTypeRepository.findFirstV2ByCountryIdOrderByMaxWeightDesc(country.getId());
        }
        return lalamoveServiceType;
    }

    @Transactional(readOnly = true)
    public LalamoveServiceType getFourWheelsServiceTypeByCountryV3(StockLocation stockLocation) {
        LalamoveServiceType lalamoveServiceType;
        State state = stockLocation.getState();

        lalamoveServiceType = serviceTypeRepository.findFirstV3ByCityCodeByServiceTypesOrderByMaxWeightDesc(state.getLalamoveCityCode(), FourWheelServiceTypes);

        return lalamoveServiceType;
    }

    @Transactional(readOnly = true)
    public LalamoveServiceType getVanServiceTypeByCountryV3(StockLocation stockLocation) {
        LalamoveServiceType lalamoveServiceType;
        State state = stockLocation.getState();

        lalamoveServiceType = serviceTypeRepository.findFirstV3ByCityCodeByServiceTypesOrderByMaxWeightDesc(state.getLalamoveCityCode(), VanServiceTypes);

        return lalamoveServiceType;
    }

    @Transactional(readOnly = true)
    public List<LalamoveDelivery> getPendingDeliveries(Long shopperId) {
        Optional<User> shopper = userRepository.findById(shopperId);
        if (!shopper.isPresent())
            return new ArrayList<>();
        LocalDateTime oneWeekAgo = LocalDateTime.now().minusWeeks(1L);
        return lalamoveDeliveryRepository.findAllPendingBookingByShopperId(shopperId, oneWeekAgo);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void placeOrderInOneBatchAsync(Batch shoppingBatch, AbstractAuthenticationToken authenticationToken) {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);

        try {
            if (jedisLockService.lock("lalamove_place_orders_in_batch_" + shoppingBatch.getId()))
                placeOrderInOneBatch(shoppingBatch);
        } catch (Exception ex) {
            logger.error("Lalamove Place Order async failed", ex);
        } finally {
            jedisLockService.unlock();
        }
    }

    private void placeOrderInOneBatch(Batch shoppingBatch) {
        User shopper = shoppingBatch.getUser();
        if (shopper == null) {
            logger.error("[Lalamove][PlaceOrderInOneBatch] Shopper not found for started batch " + shoppingBatch.getId());
            return;
        }
        logger.info("[Lalamove][PlaceOrderInOneBatch] Shopping batch id {}s ", shoppingBatch.getId());
        List<Shipment> lalamoveShipments = shipmentRepository.findLalamoveShipmentsByShoppingBatch(shoppingBatch.getId());
        if (lalamoveShipments.isEmpty())
            logger.info("[Lalamove][PlaceOrderInOneBatch] No Lalamove Orders ");
        for (Shipment shipment : lalamoveShipments) {
            boolean placeOrder = false;
            if (shipment.getCurrentLalamoveDelivery() == null) {
                placeOrder = true;
            } else {
                LalamoveDelivery.Status status = shipment.getCurrentLalamoveDelivery().getStatus();
                if (LalamoveDelivery.Status.getSwitchableStatuses().contains(status)) {
                    placeOrder = true;
                }
            }

            if (placeOrder) {
                BackgroundJob.enqueue(() -> placeOrderByShipment(shipment.getNumber(), shopper.getEmail(), shoppingBatch.getId()));
            } else {
                logger.info("[Lalamove][PlaceOrderInOneBatch] Shipment " + shipment.getNumber() + " can't be placed");
            }
        }
    }

    @Transactional
    public void placeOrderByShipment(String shipmentNumber, String shopperEmail, long shoppingBatchID) {
        Shipment shipment = shipmentRepository.findByNumber(shipmentNumber);
        User shopper = userRepository.findByEmail(shopperEmail);
        Batch shoppingBatch = batchRepository.fetchById(shoppingBatchID);
        Batch deliveryBatch = batchRepository.findDeliveryBatchByOrderNumber(shipment.getOrderNumber());

        if (deliveryBatch == null || !deliveryBatch.isLalamove()) {
            return;
        }

        Tenant tenant = tenantRepository.getOne(shoppingBatch.getTenant().getId());
        Role role = roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, tenant.getId());
        User user = userRepository.findByRolesContainingAndTenantId(role, tenant.getId());

        AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), tenant.getToken());
        SecurityContextHolder.getContext().setAuthentication(authenticationToken);

        placeOrderByShipment(shipment, shopper, shoppingBatch);
    }

    @Transactional
    public void placeOrderByDelivery(LalamoveDelivery delivery) {
        Shipment shipment = delivery.getShipment();
        Job shoppingJob = shipment.getShoppingJob()
                .orElseThrow(() -> new EntityNotFoundException("Could not find shopping job for Lalamove delivery"));
        Batch shoppingBatch = shoppingJob.getBatch();
        User shopper = shoppingBatch.getUser();

        try {
            if (shopper != null) {
                placeOrderByShipmentWithTime(shipment, shopper, null);
            } else {
                throw new EntityNotFoundException("No shopper assigned to shopping batch");
            }
        } catch (LalamoveServiceTypeNotAvailableException ex) {
            switchLalamoveBatchToHF(shipment);
        }
    }

    @Transactional
    private void placeOrderByShipment(Shipment shipment, User shopper, Batch shoppingBatch) {
        try {
            placeOrderByShipmentWithTime(shipment, shopper, null);
        } catch (LalamoveServiceTypeNotAvailableException ex) {
            switchLalamoveBatchToHF(shipment);
        }
    }

    private LalamoveDeliveryParameter placeOrderByShipmentV2(Shipment shipment, User shopper, LocalDateTime scheduleAt, LalamoveServiceType serviceType) {
        Slot slot = shipment.getSlot();
        StockLocation stockLocation = slot.getStockLocation();
        Country country = stockLocation.getState().getCountry();

        LalamoveGetQuotationPresenter quotation = lalamoveApiService.getQuotation(shipment, scheduleAt, serviceType.getServiceTypeEnum(), shopper, country, stockLocation);
        LalamovePlaceOrderPresenter lalamoveOrder = lalamoveApiService.placeOrder(shipment, scheduleAt, serviceType.getServiceTypeEnum(), shopper, country, stockLocation, quotation);

        if(lalamoveOrder!= null && lalamoveOrder.getMessage() != null)
            handleLalamoveBookingError(lalamoveOrder.getMessage(), shipment);

        BigDecimal price = null;
        String currency = null;
        String lalamoveOrderId = null;
        LalamoveDelivery.Status status = LalamoveDelivery.Status.INITIAL;
        if (quotation != null && lalamoveOrder != null) {
            price = shipment.getCost(); // from HF DDF
            if (Boolean.TRUE.equals(stockLocation.isEnableLalamoveDeliveryFee())) {
                BigDecimal lalamoveFee = BigDecimal.valueOf(Double.parseDouble(quotation.getTotalFee()));
                BigDecimal additionalFee = BigDecimal.valueOf(stockLocation.getLalamoveFlatServiceFee());
                price = lalamoveFee.add(additionalFee);
            }
            currency = quotation.getTotalFeeCurrency();
            lalamoveOrderId = lalamoveOrder.getOrderRef();
            status = LalamoveDelivery.Status.ORDER_PLACED;
        }

        return new LalamoveDeliveryParameter(
                price,
                currency,
                lalamoveOrderId,
                status,
                null,
                null
        );
    }

    private LalamoveDeliveryParameter placeOrderByShipmentV3(Shipment shipment, User shopper, LocalDateTime scheduleAt, LalamoveServiceType serviceType) {
        Slot slot = shipment.getSlot();
        StockLocation stockLocation = slot.getStockLocation();
        Country country = stockLocation.getState().getCountry();

        LalamoveServiceTypeEnum serviceTypeEnum;
        try {
            serviceTypeEnum = serviceType.getServiceTypeEnum();
        } catch (IllegalArgumentException exception) {
            String message = String.format("LalamoveServiceType %s not found in LalamoveServiceTypeEnum.java for shipment %s", serviceType.getKey(), shipment.getNumber());
            logger.error(message);
            throw new LalamoveServiceTypeNotAvailableException(message);
        }
        String[] availableSpecialRequests = serviceType.getAvailableSpecialRequests();
        LalamoveV3GetQuotationPresenter quotationPresenter = lalamoveApiV3Service.getQuotation(shipment, scheduleAt, stockLocation, serviceTypeEnum, availableSpecialRequests);

        LalamoveV3OrderPresenter placeOrderPresenter = lalamoveApiV3Service.placeOrder(shipment, shopper, country, stockLocation, quotationPresenter);

        if (placeOrderPresenter == null) {
            if (quotationPresenter == null) {
                throw new LalamoveException("Failed to retrieve quotation for Lalamove order placement " + shipment.getOrderNumber());
            } else if (quotationPresenter.getErrors()!= null) {
                throw new LalamoveException("Failed to retrieve quotation for Lalamove order placement " + shipment.getOrderNumber() + " with message " + quotationPresenter.getErrors().get(0).getMessage());
            }
            throw new LalamoveException("Lalamove failed to place order: " + shipment.getOrderNumber());
        }

        if(placeOrderPresenter!= null && placeOrderPresenter.getErrors() != null) {
            List<LalamoveV3ErrorResponse> errors = placeOrderPresenter.getErrors();
            handleLalamoveBookingError(errors.get(0).getMessage(), shipment);
        }

        BigDecimal price = null;
        String currency = null;
        String lalamoveOrderId = null;
        String distanceValue = null;
        String distanceUnit = null;
        LalamoveDelivery.Status status = LalamoveDelivery.Status.INITIAL;
        if (quotationPresenter != null && quotationPresenter.getData() != null
                && placeOrderPresenter != null && placeOrderPresenter.getData() != null) {
            LalamoveV3GetQuotationPresenter.Data quotationData = quotationPresenter.getData();
            LalamoveV3OrderPresenter.Data placeOrderData = placeOrderPresenter.getData();

            LalamoveV3GetQuotationPresenter.LalamoveV3Price priceBreakDown = quotationData.getPriceBreakdown();

            price = shipment.getCost(); // from HF DDF
            if (Boolean.TRUE.equals(stockLocation.isEnableLalamoveDeliveryFee())) {
                BigDecimal lalamoveFee = BigDecimal.valueOf(Double.parseDouble(priceBreakDown.getTotal()));
                BigDecimal additionalFee = BigDecimal.valueOf(stockLocation.getLalamoveFlatServiceFee());
                price = lalamoveFee.add(additionalFee);
            }
            currency = priceBreakDown.getCurrency();
            lalamoveOrderId = placeOrderData.getOrderId();
            status = LalamoveDelivery.Status.ORDER_PLACED;

            if (placeOrderPresenter.getData() != null && placeOrderPresenter.getData().getDistance() != null) {
                distanceValue = placeOrderPresenter.getData().getDistance().getValue();
                distanceUnit = placeOrderPresenter.getData().getDistance().getUnit();
            }

        }

        return new LalamoveDeliveryParameter(
                price,
                currency,
                lalamoveOrderId,
                status,
                distanceValue,
                distanceUnit
        );
    }

    @Transactional
    private void placeOrderByShipmentWithTime(Shipment shipment, User shopper, LocalDateTime scheduleAt) {

        LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findByShipment(shipment)
                .orElse(new LalamoveDelivery());
        if (lalamoveDelivery.getTryCount() == null) lalamoveDelivery.setTryCount(0);

        LalamoveDelivery.Status lalamoveDeliveryStatus = lalamoveDelivery.getStatus();
        if (lalamoveDeliveryStatus != null
                && !LalamoveDelivery.Status.getOrderableStatuses().contains(lalamoveDeliveryStatus)) {
            // Prefer logging only to ensure JobRunr runs only once
            logger.info("[Lalamove] Shipment {} is not orderable with status {}", shipment.getOrderNumber(), lalamoveDeliveryStatus);
            return;
        }

        logger.info("[Lalamove] Start place order for {}", shipment.getOrderNumber());
        lalamoveDelivery.setTryCount(lalamoveDelivery.getTryCount() + 1);
        LalamoveServiceType serviceType = findServiceTypeByShipment(shipment);

        LalamoveDeliveryParameter lalamoveDeliveryParameter;

        Tenant tenant = shipment.getTenant();
        if (Boolean.TRUE.equals(tenant.isEnableLalamoveV3())) {
            lalamoveDeliveryParameter = placeOrderByShipmentV3(shipment, shopper, scheduleAt, serviceType);
        } else {
            lalamoveDeliveryParameter = placeOrderByShipmentV2(shipment, shopper, scheduleAt, serviceType);
        }

        /*
            Handle Get Quotation and Place Order failure.
            This will be handled later (retried) by scheduler
         */
        BigDecimal price = lalamoveDeliveryParameter.getPrice();
        String currency = lalamoveDeliveryParameter.getCurrency();
        String lalamoveOrderId = lalamoveDeliveryParameter.getLalamoveOrderId();
        LalamoveDelivery.Status status = lalamoveDeliveryParameter.getStatus();
        String distanceUnit = lalamoveDeliveryParameter.getDistanceUnit();
        String distanceValue = lalamoveDeliveryParameter.getDistanceValue();

        lalamoveDelivery = saveDeliveryParameters(shipment, scheduleAt, lalamoveDelivery, serviceType, price, currency, lalamoveOrderId, status, distanceUnit, distanceValue);

        if (status == LalamoveDelivery.Status.ORDER_PLACED) {
            lalamoveDeliveryTrackingService.track("Lalamove Update Log", lalamoveDelivery);
        }

        // send queueUpdateJob event via ActiveMQ
        lalamoveDeliveryMessagingService.queueUpdateJob(lalamoveDelivery.getId());
    }

    private LalamoveDelivery saveDeliveryParameters(Shipment shipment, LocalDateTime scheduleAt, LalamoveDelivery lalamoveDelivery, LalamoveServiceType serviceType,
                                                    BigDecimal price, String currency, String lalamoveOrderId, LalamoveDelivery.Status status, String distanceUnit, String distanceValue) {
        lalamoveDelivery.setShipment(shipment);
        lalamoveDelivery.setStatus(status);
        lalamoveDelivery.setExternalOrderId(lalamoveOrderId);
        lalamoveDelivery.setScheduleAt(scheduleAt);
        lalamoveDelivery.setServiceType(serviceType.getKey());
        lalamoveDelivery.setServiceDescription(serviceType.getDescription());
        lalamoveDelivery.setPrice(price);
        lalamoveDelivery.setCurrency(currency);
        lalamoveDelivery.setTenant(shipment.getTenant());
        lalamoveDelivery.setDistanceUnit(distanceUnit);
        lalamoveDelivery.setDistanceValue(distanceValue);

        return lalamoveDeliveryRepository.save(lalamoveDelivery);
    }

    private void handleLalamoveBookingError(String errorMessage, Shipment shipment) {
        // Get booking error message
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("error", errorMessage);
        ObjectNode objectNode = null;
        try {
            objectNode = (ObjectNode) new ObjectMapper().readTree(jsonObject.toString());
        } catch (IOException e) {
            logger.error("Lalamove Booking Error: " + errorMessage, e);
        }

        String path = String.format("/shipments/%s", shipment.getNumber());
        Tenant tenant = shipment.getTenant();
        webhookPublisherService.publish(WebhookType.LALAMOVE_BOOKING_ERROR, objectNode, path, tenant.getId());
    }

    @Transactional
    public void cancelLalamoveBooking(Shipment shipment, LalamoveDelivery lalamoveDelivery) {
        State state = shipment.getSlot().getStockLocation().getState();

        if (lalamoveDelivery == null) {
            logger.info("Lalamove delivery empty");
            return;
        }
        
        cancelBooking(shipment, state, lalamoveDelivery);

        lalamoveDelivery.setStatus(LalamoveDelivery.Status.SHIPMENT_CANCELLED);
        lalamoveDeliveryRepository.save(lalamoveDelivery);
        lalamoveDeliveryTrackingService.track("Lalamove Update Log", lalamoveDelivery);
    }

    @Transactional
    public void switchLalamoveToHF(Shipment shipment) {
        LalamoveDelivery lalamoveDelivery = getLalamoveDelivery(shipment.getNumber());
        Optional<Job> optionalJob = lalamoveDelivery.getShipment().getDeliveryJob();
        if (!optionalJob.isPresent())
            throw new SwitchToHFFailedException("Delivery job is not found");

        Job deliveryJob = optionalJob.get();
        if (!deliveryJob.getBatch().isLalamove())
            throw new SwitchToHFFailedException(BATCH_TYPE_NOT_LALAMOVE);

        if (deliveryJob.getState() != Job.State.FINISHED) {
            State state = shipment.getSlot().getStockLocation().getState();
            Country country = state.getCountry();

            cancelBooking(shipment, state, lalamoveDelivery);

            lalamoveDelivery.setStatus(LalamoveDelivery.Status.CANCELED);
            lalamoveDeliveryRepository.save(lalamoveDelivery);

            batchService.switchBatchToHF(deliveryJob);
            lalamoveDeliveryMessagingService.publishSynchronizeWebhook(lalamoveDelivery);

            // Create geofence for fleet tracking
            // Radar service disabled
//            radarApiService.createDeliveryGeofence(shipment, country);
        }
    }

    @Transactional
    public void switchLalamoveBatchToHF(Shipment shipment) {
        Optional<Job> optionalJob = shipment.getDeliveryJob();
        if (!optionalJob.isPresent())
            throw new SwitchToHFFailedException("Delivery job is not found");

        Job deliveryJob = optionalJob.get();
        if (!deliveryJob.getBatch().isLalamove()) {
            logger.info("Fail to switch to HF for " + shipment.getOrderNumber() + " because batch is not lalamove");
            return;
        }

        if (deliveryJob.getState() != Job.State.FINISHED) {
            batchService.switchBatchToHF(deliveryJob);
            lalamoveDeliveryMessagingService.publishFailedBookingWebhook(shipment, "Failed to book lalamove");

            // Create geofence for fleet tracking
            // Radar service disabled
//            radarApiService.createDeliveryGeofence(shipment, shipment.getSlot().getStockLocation().getState().getCountry());
        }
    }

    private LalamoveDelivery getLalamoveDelivery(String shipmentNumber) {
        Shipment shipment = shipmentRepository.findByNumber(shipmentNumber);
        if (shipment == null)
            throw new LalamoveException("Shipment not found");

        Optional<Job> job = shipment.getDeliveryJob();
        if (!job.isPresent() || !job.get().getBatch().isLalamove())
            throw new LalamoveException(BATCH_TYPE_NOT_LALAMOVE);

        LalamoveDelivery lalamoveDelivery = shipment.getCurrentLalamoveDelivery();

        if (lalamoveDelivery == null)
            throw new LalamoveException("This Lalamove delivery not found");

        return lalamoveDelivery;
    }

    public void sendStratoDriverDetail(LalamoveDelivery updatedDelivery) {
        Shipment shipment = updatedDelivery.getShipment();
        StockLocation stockLocation = shipment.getSlot().getStockLocation();
        if (stockLocation.isFulfilledByStrato()) {
            stratoWebhookService.sendDeliveryBookedWebhook(shipment.getOrderNumber());
        }
    }

    @Transactional
    public void adminSwitchToHF(Shipment shipment, Job deliveryJob) {
        if (!deliveryJob.getBatch().isLalamove())
            throw new SwitchToHFFailedException(BATCH_TYPE_NOT_LALAMOVE);

        LalamoveDelivery lalamoveDelivery = shipment.getCurrentLalamoveDelivery();

        State state = shipment.getSlot().getStockLocation().getState();
        Country country = state.getCountry();

        if (lalamoveDelivery != null){
            cancelBooking(shipment, state, lalamoveDelivery);
            lalamoveDelivery.setStatus(LalamoveDelivery.Status.CANCELED);
            lalamoveDeliveryRepository.save(lalamoveDelivery);
            lalamoveDeliveryMessagingService.publishSynchronizeWebhook(lalamoveDelivery);
        }

        batchService.switchBatchToHF(deliveryJob);

        // Create geofence for fleet tracking
        // Radar service disabled
//        radarApiService.createDeliveryGeofence(shipment, country);
    }

    private Double getFee(Shipment shipment, LocalDateTime scheduleAt, LalamoveServiceType serviceType, User user, StockLocation stockLocation){
        Double fee = 0.0;
        Country country = stockLocation.getState().getCountry();
        if (Boolean.TRUE.equals(country.getTenant().isEnableLalamoveV3())){
            try {
                LalamoveServiceTypeEnum serviceTypeEnum = serviceType.getServiceTypeEnum();
                String[] activeSpecialRequests = serviceType.getActiveSpecialRequests();
                LalamoveV3GetQuotationPresenter presenter = lalamoveApiV3Service.getQuotation(shipment, scheduleAt, stockLocation,
                        serviceTypeEnum, activeSpecialRequests);
                if (presenter != null && isQuotationNotError(presenter))
                    fee = Double.parseDouble(presenter.getData().getPriceBreakdown().getTotal());
            } catch (IllegalArgumentException exception) {
                logger.error("LalamoveServiceType {} not found in LalamoveServiceTypeEnum.java for shipment {}", serviceType.getKey(), shipment.getNumber());
            }
        } else {
            LalamoveGetQuotationPresenter presenter = lalamoveApiService.getQuotation(shipment, scheduleAt, serviceType.getServiceTypeEnum(),
                    user, stockLocation.getState().getCountry(), stockLocation);
            if (presenter != null && presenter.getMessage() == null)
                fee = Double.parseDouble(presenter.getTotalFee());
        }

        return fee;
    }

    private boolean isQuotationNotError(LalamoveV3GetQuotationPresenter presenter) {
        return presenter.getErrors() == null;
    }

    private void cancelBooking(Shipment shipment, State state, LalamoveDelivery lalamoveDelivery) {
        Tenant tenant = shipment.getTenant();
        LalamoveDelivery.Status status = lalamoveDelivery.getStatus();

        try {
            if (LalamoveDelivery.Status.getCancelableStatuses().contains(status)) {
                if (Boolean.TRUE.equals(tenant.isEnableLalamoveV3())) {
                    lalamoveApiV3Service.cancelBooking(state, lalamoveDelivery.getExternalOrderId());
                } else {
                    lalamoveApiService.cancelBooking(state, lalamoveDelivery.getExternalOrderId());
                }
            }
        } catch(Exception e) {
            ThreadContext.put("SHIPMENT_NUMBER", shipment.getNumber());
            ThreadContext.put("ORDER_NUMBER", shipment.getOrderNumber());
            logger.error("Lalamove cancel failed", e);
            ThreadContext.clearAll();
            throw e;
        }

        if (!LalamoveDelivery.Status.getSwitchableStatuses().contains(status)) {
            throw new LalamoveException("Cannot cancel Lalamove delivery in status " + status);
        }

        logger.info(String.format("Lalamove order already canceled from status %s", status));
    }

    public Optional<LalamoveDriverDetail> getDriverDetail(State state, String externalOrderId, Long driverId) {
        if (Boolean.TRUE.equals(state.getTenant().isEnableLalamoveV3())){
            Optional<LalamoveV3DriverDetailPresenter> presenter = lalamoveApiV3Service.getDriverDetail(state, externalOrderId, driverId.toString());
            return presenter.isPresent() ? presenter.get().getData().toDriverDetail() : Optional.empty();
        }

        Optional<LalamoveDriverDetailPresenter> presenter = lalamoveApiService.getDriverDetail(state, externalOrderId, driverId);
        return presenter.isPresent() ? presenter.get().toDriverDetail() : Optional.empty();
    }

    public Optional<LalamoveOrderDetail> getOrderDetail(State state, String externalOrderId) {
        Optional<LalamoveOrderDetail> optionalLalamoveOrderDetail;
        if (Boolean.TRUE.equals(state.getTenant().isEnableLalamoveV3())){
            Optional<LalamoveV3OrderPresenter> optOrderDetailV3Presenter = lalamoveApiV3Service.getOrderDetail(state, externalOrderId);
            optionalLalamoveOrderDetail = optOrderDetailV3Presenter.map(orderDetail -> orderDetailMapper.toLalamoveOrderDetail(orderDetail));
        } else {
            Optional<LalamoveOrderDetailPresenter> optOrderDetail = lalamoveApiService.getOrderDetail(state, externalOrderId);
            optionalLalamoveOrderDetail = optOrderDetail.map(orderDetail -> orderDetailMapper.toLalamoveOrderDetail(orderDetail));
        }
        return optionalLalamoveOrderDetail;
    }

    public void updateOrCreateServiceTypesForAllTenants() {
        List<Tenant> tenants = tenantRepository.findAll();
        for (Tenant tenant : tenants) {
            transactionHelper.withNewTransaction(() -> updateOrCreateServiceTypes(tenant));
        }
    }

    private void updateOrCreateServiceTypes(Tenant tenant) {
        List<String> lalamoveActiveCountries = List.of("ID", "MY", "TH");
        List<Country> countries = countryRepository.findByIsoNameInAndTenantId(lalamoveActiveCountries, tenant.getId());

        for (Country country : countries) {
            Optional<LalamoveV3GetCityInfoPresenter> optCityInfo = lalamoveApiV3Service.getCityInfo(country);
            if (optCityInfo.isEmpty() || optCityInfo.get().getErrors() != null) {
                logger.error("[Lalamove] Failed to fetch City Info for {}", country.getIsoName());
                continue;
            }

            List<LalamoveV3GetCityInfoPresenter.Data> countryCityInfoData = optCityInfo.get().getData();
            List<LalamoveServiceType> countryServiceTypes = serviceTypeRepository.findAllV3ByCountry(country);

            updateServiceTypesInCountry(countryServiceTypes, countryCityInfoData);
            createServiceTypesInCountry(country, countryServiceTypes, countryCityInfoData);
        }
    }

    private void updateServiceTypesInCountry(List<LalamoveServiceType> countryServiceTypes, List<LalamoveV3GetCityInfoPresenter.Data> countryCityInfoData) {
        ArrayList<LalamoveServiceType> updatedServiceTypes = new ArrayList<>();
        for (LalamoveServiceType serviceType : countryServiceTypes) {
            String cityCode = serviceType.getCityCode();
            String key = serviceType.getKey();

            Optional<LalamoveV3GetCityInfoPresenter.Data> optCityData = countryCityInfoData.stream()
                    .filter(d -> d.getLocode().equalsIgnoreCase(cityCode)).findFirst();
            if (optCityData.isEmpty())
                continue; // City exist on db but not on API

            List<LalamoveV3GetCityInfoPresenter.Service> cityServices = optCityData.get().getServices();

            Optional<LalamoveV3GetCityInfoPresenter.Service> optCityService = cityServices.stream()
                    .filter(s -> s.getKey().equalsIgnoreCase(key))
                    .findFirst();

            if (optCityService.isPresent()) {
                LalamoveV3GetCityInfoPresenter.Service cityService = optCityService.get();
                String[] availableSpecialRequests = cityService.getSpecialRequests().stream()
                        .map(LalamoveV3GetCityInfoPresenter.Service.SpecialRequest::getName)
                        .collect(Collectors.toList()).toArray(String[]::new);

                try {
                    serviceType.setMaxHeight(cityService.getDimensions().getHeight().getValueInCm().orElseThrow());
                    serviceType.setMaxLength(cityService.getDimensions().getLength().getValueInCm().orElseThrow());
                    serviceType.setMaxWidth(cityService.getDimensions().getWidth().getValueInCm().orElseThrow());
                } catch (NoSuchElementException e) {
                    logger.error("Failed to convert dimension to cm when updating LalamoveServiceType. Service Type: {}, City Code: {}",
                            serviceType.getKey(),
                            serviceType.getCityCode(), e);
                    continue;
                }
                serviceType.setAvailableSpecialRequests(availableSpecialRequests);
                serviceType.setMaxWeight(Double.valueOf(cityService.getLoad().getValue()));
                serviceType.setUpdatedBy(serviceType.getCreatedBy());
                updatedServiceTypes.add(serviceType);
            }
        }

        if (!updatedServiceTypes.isEmpty())
            serviceTypeRepository.saveAll(updatedServiceTypes);
    }

    private void createServiceTypesInCountry(Country country, List<LalamoveServiceType> countryServiceTypes, List<LalamoveV3GetCityInfoPresenter.Data> countryCityInfoData) {
        Tenant tenant = country.getTenant();
        Role role = roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, tenant.getId());
        User systemAdmin = userRepository.findByRolesContainingAndTenantId(role, tenant.getId());
        // find non-exist service types on DB
        ArrayList<LalamoveServiceType> newServiceTypes = new ArrayList<>();

        for (LalamoveV3GetCityInfoPresenter.Data cityInfo : countryCityInfoData) {
            String locode = cityInfo.getLocode();
            List<LalamoveV3GetCityInfoPresenter.Service> services = cityInfo.getServices();

            for (LalamoveV3GetCityInfoPresenter.Service service : services) {
                Optional<LalamoveServiceType> optExistingServiceType = countryServiceTypes.stream()
                        .filter(st -> st.getKey().equalsIgnoreCase(service.getKey()) && locode.equalsIgnoreCase(st.getCityCode()))
                        .findFirst();

                if (optExistingServiceType.isEmpty()) {
                    String[] availableSpecialRequests = service.getSpecialRequests().stream()
                            .map(LalamoveV3GetCityInfoPresenter.Service.SpecialRequest::getName)
                            .toArray(String[]::new);

                    LalamoveServiceType serviceType = new LalamoveServiceType();
                    serviceType.setCountry(country);
                    serviceType.setKey(service.getKey());
                    serviceType.setVersion("3");
                    serviceType.setActive(false);
                    serviceType.setCityCode(locode);
                    serviceType.setAvailableSpecialRequests(availableSpecialRequests);
                    try {
                        serviceType.setMaxHeight(service.getDimensions().getHeight().getValueInCm().orElseThrow());
                        serviceType.setMaxLength(service.getDimensions().getLength().getValueInCm().orElseThrow());
                        serviceType.setMaxWidth(service.getDimensions().getWidth().getValueInCm().orElseThrow());
                    } catch (NoSuchElementException e) {
                        logger.error("Failed to convert dimension to cm when creating LalamoveServiceType. Service Type: {}, City Code: {}",
                                service.getKey(),
                                locode, e);
                        continue;
                    }
                    serviceType.setMaxWeight(Double.valueOf(service.getLoad().getValue()));
                    serviceType.setCreatedBy(systemAdmin.getId());
                    serviceType.setTenant(tenant);

                    newServiceTypes.add(serviceType);
                }
            }
        }

        if (!newServiceTypes.isEmpty())
            serviceTypeRepository.saveAll(newServiceTypes);
    }
}