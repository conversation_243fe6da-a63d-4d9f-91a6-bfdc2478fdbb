package com.happyfresh.fulfillment.lalamove.model;

import java.util.Arrays;
import java.util.List;

public enum LalamoveSpecialRequestEnum {

    LALABAG,
    ROUNDTRIP_MOTORYCYCLE,
    COD,
    PURCHASE_SERVICE,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>OOR2DOOR,
    DOOR2DOOR_WITH_WORKER,
    DOOR2DOOR_TRUCK330,
    DOOR2DOOR_TRUCK550,
    THERMAL_BAG_1,
    CASH_ON_DELIVERY,
    CASH_ON_DELIVERY_2,
    ROUND_TRIP,
    TOLL_FEE_1,
    DOOR_TO_DOOR,
    CASH_ON_DELIVERY_AUTODEDUCT,

    DOOR_TO_DOOR_1DRIVER;

    @Override
    public String toString() {
        return name();
    }

    public static List<String> getCODSpecialRequests() {
        return Arrays.asList(COD.toString(), CASH_ON_DELIVERY.toString(), CASH_ON_DELIVERY_2.toString(), CASH_ON_DELIVERY_AUTODEDUCT.toString());
    }

}
