package com.happyfresh.fulfillment.lalamove.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.lalamove.form.v3.LalamoveV3WebhookForm;
import com.happyfresh.fulfillment.lalamove.service.LalamoveWebhookService;
import org.apache.commons.lang3.StringUtils;
import org.jobrunr.scheduling.BackgroundJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/lalamove")
public class LalamoveController {

    @Autowired
    private LalamoveWebhookService lalamoveWebhookService;

    @PostMapping(path = "/v3/webhook")
    public ResponseEntity<Map<String, String>> webhook(@RequestParam(required = true) String country,
                                                       @RequestBody Optional<JsonNode> jsonNode) {

        if (jsonNode.isEmpty()) {
            return new ResponseEntity<>(Collections.singletonMap("message", "ok"), HttpStatus.OK);
        }

        JsonNode node = jsonNode.get();
        Map<String, Object> fields = new LinkedHashMap<>();
        node.fields().forEachRemaining(entry -> fields.put(entry.getKey(), entry.getValue()));
        LalamoveV3WebhookForm form;

        ObjectMapper mapper = new ObjectMapper();
        try {
            form = mapper.treeToValue(node, LalamoveV3WebhookForm.class);
        } catch (JsonProcessingException e) {
            return new ResponseEntity<>(Collections.singletonMap("message", e.getMessage()), HttpStatus.BAD_REQUEST);
        }

        if (StringUtils.isBlank(form.getEventId())) {
            return new ResponseEntity<>(Collections.singletonMap("message", "ok"), HttpStatus.OK);
        }

//        if (!lalamoveWebhookService.isValidSignature(form, country, fields)) {
//            return new ResponseEntity<>(Collections.singletonMap("message", "UNAUTHORIZED"), HttpStatus.UNAUTHORIZED);
//        }

        BackgroundJob.enqueue(() -> lalamoveWebhookService.processData(form));
        return new ResponseEntity<>(Collections.singletonMap("message", "ok"),HttpStatus.OK);
    }

}
