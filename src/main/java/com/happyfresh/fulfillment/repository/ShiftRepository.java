package com.happyfresh.fulfillment.repository;

import com.happyfresh.fulfillment.entity.Shift;
import com.happyfresh.fulfillment.entity.StockLocation;
import org.javers.spring.annotation.JaversSpringDataAuditable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@JaversSpringDataAuditable
public interface ShiftRepository extends JpaRepository<Shift, Long> {

    Shift findByStockLocationAndStartTimeAndEndTimeAndType(StockLocation stockLocation, LocalDateTime startTime, LocalDateTime endTime, Shift.Type type);

    Optional<Shift> findByIdAndType(Long id, Shift.Type type);

    @Query("SELECT shift FROM Shift shift " +
            "WHERE shift.stockLocation = :stockLocation " +
            "AND shift.type = :type " +
            "AND (shift.startTime = :startTime OR shift.endTime = :endTime)")
    List<Shift> findAllByUniqueConstraints(@Param("stockLocation") StockLocation stockLocation, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("type") Shift.Type type);

    @Query( "SELECT DISTINCT(shift) FROM Shift shift " +
            "INNER JOIN shift.stockLocation stockLocation " +
            "INNER JOIN stockLocation.cluster cluster " +
            "WHERE  ((shift.startTime <= :slotStartTime AND shift.endTime > :slotStartTime) " +
                    "OR " +
                    "(shift.startTime > :slotStartTime AND shift.startTime < :slotEndTime))" +
                "AND cluster.id = :clusterId " +
                "AND shift.type = :shiftType " +
            "ORDER BY shift.startTime ")
    List<Shift> findAllShiftBetweenSlotStartTimeAndSlotEndTime(@Param("clusterId") Long clusterId, @Param("shiftType") Shift.Type shiftType, @Param("slotStartTime") LocalDateTime slotStartTime, @Param("slotEndTime") LocalDateTime slotEndTime);

    @Query("SELECT shift FROM Shift shift " +
            "WHERE shift.stockLocation = ?1 " +
            "AND shift.startTime >= ?2 " +
            "AND shift.startTime <= ?3")
    List<Shift> findAllBetweenDate(StockLocation stockLocation, LocalDateTime startTime, LocalDateTime endTime);

    @Query("SELECT shift.id FROM Shift shift " +
            "JOIN shift.batches batch " +
            "WHERE shift.id IN (?1)")
    List<Long> findShiftIdsAssociatedWithBatches(Set<Long> shiftIds);

    @Query("SELECT shift FROM Shift shift " +
            "JOIN shift.stockLocation sl " +
            "WHERE " +
            "sl.cluster.id = ?1 " +
            "AND shift.startTime >= ?2 " +
            "AND shift.startTime < ?3")
    Page<Shift> findAllInCluster(Long clusterId, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);


    @Query(value = "SELECT COALESCE(SUM( " +
            "CASE WHEN hff_shift.start_time > :start_time " +
            "THEN (EXTRACT(EPOCH FROM (hff_shift.end_time - hff_shift.start_time)) / 60) " +
            "ELSE (EXTRACT(EPOCH FROM (hff_shift.end_time - :start_time)) / 60) " +
            "END * hff_shift.count), 0) FROM hff_shift " +
            "WHERE hff_shift.end_time > :start_time AND hff_shift.end_time < :end_time " +
            "AND stock_location_id IN (SELECT id FROM hff_stock_location WHERE cluster_id = :cluster_id) AND hff_shift.type = 1 ", nativeQuery = true)
    int findDeliveryShiftDurationByClusterAndDateFilter(@Param("start_time") LocalDateTime startTime, @Param("end_time") LocalDateTime endTime, @Param("cluster_id") Long custerId);

    @Query(value = "SELECT DISTINCT(shift) FROM Shift shift " +
            "INNER JOIN shift.stockLocation stockLocation " +
            "INNER JOIN stockLocation.cluster cluster "+
            "WHERE shift.endTime > :start_time AND shift.endTime < :end_time " +
            "AND cluster.id = :cluster_id AND shift.type = 1 ")
    List<Shift> findDeliveryShiftByStockLocationAndDateFilter(@Param("start_time") LocalDateTime startTime, @Param("end_time") LocalDateTime endTime, @Param("cluster_id") Long custerId);

    @Query(value = "SELECT COALESCE(SUM( " +
            "CASE WHEN hff_shift.start_time > :start_time " +
            "THEN (EXTRACT(EPOCH FROM (hff_shift.end_time - hff_shift.start_time)) / 60) " +
            "ELSE (EXTRACT(EPOCH FROM (hff_shift.end_time - :start_time)) / 60) " +
            "END * hff_shift.count), 0) FROM hff_shift " +
            "WHERE hff_shift.end_time > :start_time AND hff_shift.end_time < :end_time " +
            "AND stock_location_id IN (:stock_location_id) AND hff_shift.type = 0 ", nativeQuery = true)
    int findShoppingShiftDurationByStockLocationAndDateFilter(@Param("start_time") LocalDateTime startTime, @Param("end_time") LocalDateTime endTime, @Param("stock_location_id") Long stockLocationId);

    @Query(value = "SELECT DISTINCT(shift) FROM Shift shift " +
            "INNER JOIN shift.stockLocation stockLocation " +
            "WHERE shift.endTime > :start_time AND shift.endTime < :end_time " +
            "AND stockLocation.id IN (:stock_location_id) AND shift.type = 0 ")
    List<Shift> findShoppingShiftByStockLocationAndDateFilter(@Param("start_time") LocalDateTime startTime, @Param("end_time") LocalDateTime endTime, @Param("stock_location_id") Long stockLocationId);

    @Query(value = "SELECT shift.* FROM hff_shift shift " +
            "INNER JOIN hff_agent_clock_in_activity clockin_activity ON clockin_activity.shift_id = shift.id " +
            "INNER JOIN hff_agent_activity agent_activity ON agent_activity.id = clockin_activity.id " +
            "INNER JOIN hff_agent agent ON agent.id = agent_activity.agent_id " +
            "WHERE agent.id = :agent_id " +
            "AND agent_activity.created_at >= :start_of_day " +
            "ORDER BY agent_activity.created_at DESC " +
            "LIMIT 1", nativeQuery = true
    )
    Optional<Shift> findLatestTodayShiftByAgentId(@Param("agent_id") Long agentId, @Param("start_of_day") LocalDateTime startOfDay);

    @Query("SELECT shift FROM Shift shift " +
            "JOIN shift.stockLocation sl " +
            "WHERE " +
            "sl.cluster.id = :cluster_id " +
            "AND sl.active = TRUE " +
            "AND shift.type = :shift_type " +
            "AND shift.startTime >= :start_time " +
            "AND shift.startTime <= :end_time " +
            "AND shift.endTime > :now_time " +
            "AND shift.count > 0")
    List<Shift> findAllInClusterBetweenDate(@Param("cluster_id") Long clusterId,
                                            @Param("shift_type") Shift.Type type,
                                            @Param("start_time") LocalDateTime startTime,
                                            @Param("end_time") LocalDateTime endTime,
                                            @Param("now_time") LocalDateTime now);

    @Query(value = "WITH eligible_delivery_shifts AS ( " +
                "SELECT DISTINCT shift.id " +
                "FROM hff_shift shift  " +
                "JOIN hff_stock_location sl on sl.id = shift.stock_location_id  " +
                "JOIN hff_cluster cluster on cluster.id = sl.cluster_id  " +
                "JOIN hff_state state on state.id = sl.state_id  " +
                "WHERE  " +
                    "state.country_id = :country_id " +
                    "AND cluster.preferences->'enable_driver_auto_assignment' = 'true'  " +
                    "AND shift.type = 1  " + // driver
                    "AND shift.start_time > :start " +
                    "AND shift.start_time <= :end " +
                    "AND shift.count > 0 " +
            ") " +
            "SELECT " +
                "distinct ON (sb.shift_id)  " +
                "db.shift_id as driver_shift_id, " +
                "sb.shift_id as shopper_shift_id, " +
                "slot.id as last_slot_id," +
                "sl.cluster_id, " +
                "slot.start_time as last_slot_start_time, " +
                "state.time_zone " +
            "FROM hff_batch db " +
            "JOIN eligible_delivery_shifts ds ON ds.id = db.shift_id " +
            "JOIN hff_job j ON db.id = j.batch_id " +
            "JOIN hff_shipment s ON s.id = j.shipment_id " +
            "LEFT JOIN hff_job sj ON sj.shipment_id = s.id and sj.type = 0 " + // shopping batch
            "LEFT JOIN hff_batch sb ON sb.id = sj.batch_id " +
            "JOIN hff_slot slot ON slot.id = s.slot_id " +
            "JOIN hff_stock_location sl ON sl.id = slot.stock_location_id " +
            "JOIN hff_state state ON sl.state_id = state.id " +
            "WHERE " +
                "db.type in (1, 2) " +
            "ORDER BY " +
                "sb.shift_id ASC, " +
                "slot.start_time DESC ", nativeQuery = true)
    List<Object[]> findRelatedShopperShiftAndLastSlot(
            @Param("country_id") Long countryId,
            @Param("start") LocalDateTime start,
            @Param("end") LocalDateTime end
    );

    @Query(value = "SELECT DISTINCT(shift) FROM Shift shift " +
            "INNER JOIN shift.stockLocation stockLocation " +
            "WHERE shift.startTime <= :start_time AND shift.endTime >= :start_time " +
            "AND stockLocation.id IN (:stock_location_id) AND shift.type = 0 ")
    List<Shift> findEquivalentShoppingShift(@Param("start_time") LocalDateTime startTime, @Param("stock_location_id") Long stockLocationId);

    @Query(value = "SELECT DISTINCT(shift) FROM Shift shift " +
            "INNER JOIN shift.stockLocation stockLocation " +
            "INNER JOIN stockLocation.cluster cluster " +
            "WHERE shift.startTime <= :start_time AND shift.endTime >= :end_time " +
            "AND cluster.id IN (:cluster_id) AND shift.type = 1 ")
    List<Shift> findEquivalentDeliveryShift(@Param("start_time") LocalDateTime startTime, @Param("end_time") LocalDateTime endTime, @Param("cluster_id") Long clusterId);

    @Query("SELECT shift FROM Shift shift " +
            "WHERE shift.stockLocation = :stockLocation " +
            "AND shift.type = :type " +
            "AND shift.endTime = :endTime")
    List<Shift> findAllByEndTime(@Param("stockLocation") StockLocation stockLocation, @Param("endTime") LocalDateTime endTime, @Param("type") Shift.Type type);

}
