package com.happyfresh.fulfillment.repository;

import com.happyfresh.fulfillment.entity.Country;
import org.javers.spring.annotation.JaversSpringDataAuditable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

@JaversSpringDataAuditable
public interface CountryRepository extends JpaRepository<Country, Long> {

    Country findByIsoName(String isoName);

    Country findByIsoNameAndTenantId(String isoName, Long tenantId);

    List<Country> findByIsoNameInAndTenantId(List<String> isoName, Long tenantId);
}
