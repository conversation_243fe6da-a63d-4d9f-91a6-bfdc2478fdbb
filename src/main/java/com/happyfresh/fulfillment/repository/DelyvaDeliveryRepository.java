package com.happyfresh.fulfillment.repository;

import com.happyfresh.fulfillment.entity.DelyvaDelivery;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.tpl.delyva.model.DelyvaStatusCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface DelyvaDeliveryRepository extends JpaRepository<DelyvaDelivery, Long> {

    Optional<DelyvaDelivery> findByShipment(Shipment shipment);

    @Query("SELECT delyvaDelivery FROM DelyvaDelivery delyvaDelivery " +
            "JOIN delyvaDelivery.shipment shipment " +
            "WHERE shipment.orderNumber = ?1")
    Optional<DelyvaDelivery> findByOrderNumber(String orderNumber);

    DelyvaDelivery findByExternalId(String externalId);

    @Modifying
    @Query("DELETE FROM DelyvaDelivery delyvaDelivery WHERE delyvaDelivery.id = :id")
    void delete(@Param("id") Long id);

    @Query(value = "SELECT DISTINCT ON (delyva_delivery.shipment_id) delyva_delivery.* " +
            "FROM ( SELECT " +
            "dlv_delivery.*, " +
            "tpl_delivery.shipment_id, " +
            "tpl_delivery.tenant_id, " +
            "tpl_delivery.created_at, " +
            "tpl_delivery.created_by, " +
            "tpl_delivery.updated_at, " +
            "tpl_delivery.updated_by " +
            "FROM " +
            "hff_delyva_delivery dlv_delivery " +
            "INNER JOIN hff_tpl_delivery tpl_delivery ON " +
            "tpl_delivery.id = dlv_delivery.id " +
            "INNER JOIN hff_shipment shipment ON " +
            "shipment.id = tpl_delivery.shipment_id " +
            "LEFT JOIN hff_job shopping_job ON " +
            "shopping_job.shipment_id = shipment.id " +
            "AND shopping_job.type = 0 " +
            "LEFT JOIN hff_job delivery_job ON " +
            "delivery_job.shipment_id = shipment.id " +
            "AND delivery_job.type = 1 " +
            "INNER JOIN hff_batch shopping_batch ON " +
            "shopping_batch.id = shopping_job.batch_id " +
            "INNER JOIN hff_batch delivery_batch ON " +
            "delivery_batch.id = delivery_job.batch_id " +
            "WHERE " +
            "shopping_batch.user_id = (?1) " +
            "AND delivery_batch.delivery_type = 2 " +
            "AND delivery_batch.tpl_type = 4 " + // DELYVA
            "AND shopping_job.state = 7 " +
            "AND delivery_job.state IS NOT NULL " +
            "AND delivery_job.state NOT IN (7, 8, 9) " +
            "AND dlv_delivery.status IN ?2 " +
            "ORDER BY " +
            "dlv_delivery.id DESC " +
            ") AS delyva_delivery " +
            "ORDER BY " +
            "delyva_delivery.shipment_id, " +
            "delyva_delivery.id DESC ",
            nativeQuery = true)
    List<DelyvaDelivery> findAllPendingBookingByShopperIdAndStatuses(Long shopperId, Set<String> statuses);

    List<DelyvaDelivery> findByPickUpScheduledAtBeforeAndStatusNotIn(LocalDateTime scheduleThreshold, List<DelyvaStatusCode> statuses);
}
