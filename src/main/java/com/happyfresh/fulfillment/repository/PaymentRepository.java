package com.happyfresh.fulfillment.repository;

import com.happyfresh.fulfillment.entity.Payment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface PaymentRepository extends JpaRepository<Payment, Long> {

    Payment findByShipmentId(Long shipmentId);

    @Query(value = "select\n" +
            "hff_shipment.order_customer_first_name,\n" +
            "hff_shipment.order_customer_last_name,\n" +
            "hff_shipment.order_customer_phone,\n" +
            "hff_shipment.order_number,\n" +
            "p.cashless,\n" +
            "p.cash,\n" +
            "hff_shipment.order_total,\n" +
            "p.created_at,\n" +
            "p.shipment_id,\n" +
            "p.outstanding_status\n" +
            "from hff_shipment join\n" +
            "(\n" +
            "    select cashless_status, hff_payment.shipment_id, cashless, cash, hff_payment.created_at, outstanding_status, hff_job.type as job_type\n" +
            "    from hff_payment\n" +
            "    join hff_job on hff_job.shipment_id = hff_payment.shipment_id\n" +
            "    join hff_batch on hff_batch.id = hff_job.batch_id\n" +
            "    where hff_batch.user_id = ?1 and hff_job.type in (1,2,5)\n" +
            "    and outstanding_status is not null\n" +
            "    and hff_payment.created_at >= now() - interval '30d'\n" +
            ") as p\n" +
            "on hff_shipment.id = p.shipment_id\n" +
            "left join hff_tpl_delivery on hff_shipment.id = hff_tpl_delivery.shipment_id\n" +
            "order by p.created_at desc;", nativeQuery = true)
    List<Object[]> findDriverOutstandingPaymentsJobBy(Long userId);

    @Query(value = "select\n" +
            "hff_shipment.order_customer_first_name,\n" +
            "hff_shipment.order_customer_last_name,\n" +
            "hff_shipment.order_customer_phone,\n" +
            "hff_shipment.order_number,\n" +
            "p.cashless,\n" +
            "p.cash,\n" +
            "hff_shipment.order_total,\n" +
            "p.created_at,\n" +
            "p.shipment_id,\n" +
            "p.outstanding_status\n" +
            "from hff_shipment join\n" +
            "(\n" +
            "    select cashless_status, hff_payment.shipment_id, cashless, cash, hff_payment.created_at, outstanding_status, hff_job.type as job_type\n" +
            "    from hff_payment\n" +
            "    join hff_job on hff_job.shipment_id = hff_payment.shipment_id\n" +
            "    join hff_batch on hff_batch.id = hff_job.batch_id\n" +
            "    where hff_batch.user_id = ?1 and hff_job.type in (0,4)\n" +
            "    and outstanding_status is not null\n" +
            "    and hff_payment.created_at >= now() - interval '30d'\n" +
            ") as p\n" +
            "on hff_shipment.id = p.shipment_id\n" +
            "left join hff_tpl_delivery on hff_shipment.id = hff_tpl_delivery.shipment_id\n" +
            "where hff_tpl_delivery.id is not null\n" +
            "order by p.created_at desc;", nativeQuery = true)
    List<Object[]> findShopperOutstandingPaymentsJobBy(Long userId);
}
