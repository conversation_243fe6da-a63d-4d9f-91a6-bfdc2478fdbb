package com.happyfresh.fulfillment.repository;

import com.happyfresh.fulfillment.entity.Agent;
import com.happyfresh.fulfillment.slot.model.FleetDeliveredJobCount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.List;

public interface AgentRepository extends JpaRepository<Agent, Long> {
    @Query(value = "SELECT new com.happyfresh.fulfillment.slot.model.FleetDeliveredJobCount(batch.user.id, COUNT(job.id)) " +
            "FROM Batch batch " +
            "LEFT JOIN batch.jobs job " +
            "WHERE " +
                "batch.user.id in :userId " +
                "AND batch.startTime >= :minStartTime " +
                "AND batch.startTime < :maxStartTime " +
                "AND job.type in (3, 5) " +  // ON_DEMAND_RANGER & ON_DEMAND_DRIVER
                "AND job.state = 7 " + // finished
            "GROUP BY batch.user.id")
    List<FleetDeliveredJobCount> countDeliveredOnDemandJob(
            @Param("userId") List<Long> userId,
            @Param("minStartTime") LocalDateTime minStartTime,
            @Param("maxStartTime") LocalDateTime maxStartTime);

    @Query(value =
                "SELECT DISTINCT agent.* " +
                "FROM hff_agent agent " +
                "INNER JOIN hff_user_role user_role ON agent.user_id = user_role.user_id " +
                "INNER JOIN hff_role role ON user_role.role_id = role.id " +
                "INNER JOIN hff_stock_location stock_location ON stock_location.id = agent.stock_location_id " +
                "WHERE role.name = :role AND agent.state = 0 " +
                "AND stock_location.on_demand_cluster_id = :on_demand_cluster_id " +
                "AND agent.clock_out_time > NOW() " +
                "ORDER BY agent.id ASC ", nativeQuery = true)
    List<Agent> getAllWorkingAgentOnSameOnDemandClusterByAgentClockOutTime(@Param("role") String role, @Param("on_demand_cluster_id") Long onDemandClusterId);

    @Query(value =
            "SELECT DISTINCT agent.user_id " +
                    "FROM hff_agent agent " +
                    "INNER JOIN hff_user_role user_role ON agent.user_id = user_role.user_id " +
                    "INNER JOIN hff_role role ON user_role.role_id = role.id " +
                    "INNER JOIN hff_stock_location stock_location ON stock_location.id = agent.stock_location_id " +
                    "WHERE role.name = ?1 AND agent.state = 0 " +
                    "AND stock_location.on_demand_cluster_id = ?2 " +
                    "AND DATE(NOW()) = DATE(agent.clock_out_time) " +
                    "AND date_part('hour', NOW()) < date_part('hour',agent.clock_out_time) ", nativeQuery = true)
    List<BigInteger> getAllWorkingAgentUserIdOnSameOnDemandClusterByAgentClockOutTime(String role, Long onDemandClusterId);

    @Query(value =
            "SELECT DISTINCT agent.* " +
                    "FROM hff_agent agent " +
                    "INNER JOIN hff_user_role user_role ON agent.user_id = user_role.user_id " +
                    "INNER JOIN hff_role role ON user_role.role_id = role.id " +
                    "INNER JOIN hff_stock_location stock_location ON stock_location.id = agent.stock_location_id " +
                    "WHERE role.name = :role AND agent.state = 0 " +
                    "AND stock_location.id = :stock_location_id " +
                    "AND agent.user_id != :user_id " +
                    "AND agent.clock_out_time > NOW() " +
                    "ORDER BY agent.id ASC ", nativeQuery = true)
    List<Agent> getAllWorkingAgentOnSameStockLocation(@Param("role") String role, @Param("stock_location_id") Long stockLocationId, @Param("user_id") Long userId);

    @Query(value =
            "SELECT DISTINCT agent.* " +
                    "FROM hff_agent agent " +
                    "INNER JOIN hff_user_role user_role ON agent.user_id = user_role.user_id " +
                    "INNER JOIN hff_role role ON user_role.role_id = role.id " +
                    "INNER JOIN hff_stock_location stock_location ON stock_location.id = agent.stock_location_id " +
                    "WHERE role.name = :role AND agent.state = 0 " +
                    "AND stock_location.cluster_id = :cluster_id " +
                    "AND agent.user_id != :user_id " +
                    "AND agent.clock_out_time > NOW() " +
                    "ORDER BY agent.id ASC ", nativeQuery = true)
    List<Agent> getAllWorkingAgentOnSameClusterId(@Param("role") String role, @Param("cluster_id") Long clusterId, @Param("user_id") Long userId);

    @Query(value = "SELECT DISTINCT agent.* " +
            "FROM hff_agent agent " +
            "LEFT JOIN hff_user_role ur ON agent.user_id = ur.user_id " +
            "LEFT JOIN hff_role role ON ur.role_id = role.id " +
            "WHERE " +
            "agent.state = 0 " +
            "AND agent.stock_location_id = ?2 " +
            "AND role.name = ?1 ", nativeQuery = true)
    List<Agent> getWorkingAgentsOnStockLocation(String role, Long stockLocationId);

    @Query(value = "WITH working_shoppers AS ( " +
                "SELECT " +
                    "agent.id AS agent_id, " +
                    "agent.user_id " +
                "FROM hff_agent agent " +
                "JOIN hff_user u ON u.id = agent.user_id " +
                "JOIN hff_user_role ur ON ur.user_id = agent.user_id " +
                "JOIN hff_role r ON ur.role_id = r.id " +
                "WHERE " +
                    "agent.state = 0 " +
                    "AND agent.stock_location_id = :stock_location_id " +
                    "AND r.name = 'SHOPPER' " +
                    "AND agent.clock_out_time > now() + interval '30 minutes' " +
                    "AND (" +
                    "u.preferences->'is_allowed_for_auto_assignment' is null OR " +
                    "u.preferences->'is_allowed_for_auto_assignment' = 'true' " +
                    ") " +
            "), " +
            "active_batch AS ( " +
                "SELECT " +
                    "batch.user_id, " +
                    "batch.id AS batch_id " +
                "FROM hff_batch batch " +
                "JOIN hff_job job ON job.batch_id = batch.id " +
                "WHERE " +
                    "batch.user_id IN (SELECT user_id FROM working_shoppers) " +
                    "AND batch.type = 0 AND batch.start_time >= :filter_date_time " +
                    "AND job.state IN (0, 1, 2, 3)  " +
            ") " +
            "SELECT agent.* " +
            "FROM hff_agent agent " +
            "JOIN working_shoppers ON agent.id = working_shoppers.agent_id " +
            "LEFT JOIN active_batch active_batch ON working_shoppers.user_id = active_batch.user_id " +
            "WHERE " +
                "active_batch.batch_id IS NULL", nativeQuery = true)
    List<Agent> getIdleWorkingShopperAgentsOnStockLocation(@Param("stock_location_id") Long stockLocationId, @Param("filter_date_time") LocalDateTime filterDateTime);

    @Query(value = "WITH working_agents AS ( " +
            "SELECT " +
                "DISTINCT ON (agent_activity.agent_id) agent_activity.agent_id AS agent_id, " +
                "agent.user_id AS user_id, " +
                "agent_activity.created_at AS clock_in_time " +
            "FROM hff_agent agent " +
            "JOIN hff_user u ON u.id = agent.user_id " +
            "JOIN hff_user_role user_role ON agent.user_id = user_role.user_id " +
            "JOIN hff_role role ON user_role.role_id = role.id " +
            "JOIN hff_agent_activity agent_activity ON agent_activity.agent_id = agent.id " +
            "LEFT JOIN hff_agent_activity next_agent_activity on next_agent_activity.agent_id = agent_activity.agent_id AND next_agent_activity.created_at > agent_activity.created_at " +
            "JOIN hff_agent_clock_in_activity clockin_activity ON agent_activity.id = clockin_activity.id  " +
            "WHERE " +
                "role.name = ?1 " +
                "AND agent.state = 0 " +
                "AND (" +
                    "preferences->'is_allowed_for_auto_assignment' is null OR " +
                    "preferences->'is_allowed_for_auto_assignment' = 'true' " +
                ") " +
                "AND clockin_activity.shift_id = ?2 " +
                "AND agent.clock_out_time > now() " +
                "AND DATE(agent_activity.created_at) >= DATE(NOW() - INTERVAL '1 day') " +
                "AND next_agent_activity.id is null " +
            "), " +
            "active_batch AS ( " +
                "SELECT " +
                    "batch.user_id, " +
                    "batch.id AS batch_id " +
            "FROM hff_batch batch " +
            "JOIN hff_job job ON job.batch_id = batch.id " +
            "WHERE " +
                "batch.user_id IN (SELECT user_id FROM working_agents) " +
                "AND batch.type IN (?3) " +
                "AND job.state NOT IN (?4) " +
                "AND batch.start_time > ?5 " +
                "AND batch.start_time <= ?6 " +
            ") " +
            "SELECT agent.* " +
            "FROM hff_agent agent " +
            "JOIN working_agents ON agent.id = working_agents.agent_id " +
            "LEFT JOIN active_batch active_batch ON working_agents.user_id = active_batch.user_id " +
            "WHERE " +
                "active_batch.batch_id IS NULL " +
            "ORDER BY working_agents.clock_in_time, agent.user_id ASC", nativeQuery = true)
    List<Agent> getAllWorkingAgentOnSameShiftId(String role, Long shiftId, List<Integer> batchTypesValue,
                                                List<Integer> inactiveStates, LocalDateTime minStartTime,
                                                LocalDateTime maxStartTime);

    @Query(value = "WITH working_agents AS ( " +
            "SELECT " +
            "DISTINCT ON (agent_activity.agent_id) agent_activity.agent_id AS agent_id, " +
            "agent.user_id AS user_id, " +
            "agent_activity.created_at AS clock_in_time " +
            "FROM hff_agent agent " +
            "JOIN hff_user u ON u.id = agent.user_id " +
            "JOIN hff_user_role user_role ON agent.user_id = user_role.user_id " +
            "JOIN hff_role role ON user_role.role_id = role.id " +
            "JOIN hff_agent_activity agent_activity ON agent_activity.agent_id = agent.id " +
            "LEFT JOIN hff_agent_activity next_agent_activity on next_agent_activity.agent_id = agent_activity.agent_id AND next_agent_activity.created_at > agent_activity.created_at " +
            "WHERE " +
            "role.name = ?1 " +
            "AND agent.state = 0 " +
            "AND (" +
            "preferences->'is_allowed_for_auto_assignment' is null OR " +
            "preferences->'is_allowed_for_auto_assignment' = 'true' " +
            ") " +
            "AND agent.stock_location_id IN (?2) " +
            "AND agent.clock_out_time > now() " +
            "AND DATE(agent_activity.created_at) >= DATE(NOW() - INTERVAL '1 day') " +
            "AND next_agent_activity.id is null " +
            "), " +
            "active_batch AS ( " +
            "SELECT " +
            "batch.user_id, " +
            "batch.id AS batch_id " +
            "FROM hff_batch batch " +
            "JOIN hff_job job ON job.batch_id = batch.id " +
            "WHERE " +
            "batch.user_id IN (SELECT user_id FROM working_agents) " +
            "AND batch.type IN (?3) " +
            "AND job.state NOT IN (?4) " +
            "AND batch.start_time > ?5 " +
            "AND batch.start_time <= ?6 " +
            ") " +
            "SELECT agent.* " +
            "FROM hff_agent agent " +
            "JOIN working_agents ON agent.id = working_agents.agent_id " +
            "LEFT JOIN active_batch active_batch ON working_agents.user_id = active_batch.user_id " +
            "WHERE " +
            "active_batch.batch_id IS NULL " +
            "ORDER BY agent.last_activity_time ASC NULLS LAST, working_agents.clock_in_time, agent.user_id ASC", nativeQuery = true)
    List<Agent> getAllWorkingAgentOnSameStockLocation(String role, List<Long> stockLocationIds, List<Integer> batchTypesValue,
                                                List<Integer> inactiveStates, LocalDateTime minStartTime,
                                                LocalDateTime maxStartTime);

    @Query(value = "SELECT DISTINCT agent.* " +
            "FROM hff_agent agent " +
            "LEFT JOIN hff_user_role ur ON agent.user_id = ur.user_id " +
            "LEFT JOIN hff_role role ON ur.role_id = role.id " +
            "WHERE " +
            "agent.state = 0 " +
            "AND agent.stock_location_id IN ?2 " +
            "AND role.name = ?1 ", nativeQuery = true)
    List<Agent> getWorkingAgentsOnStockLocations(String role, List<Long> stockLocationIds);

    @Query(value =
            "SELECT DISTINCT agent.* " +
                    "FROM hff_agent agent " +
                    "INNER JOIN hff_user_role user_role ON agent.user_id = user_role.user_id " +
                    "INNER JOIN hff_role role ON user_role.role_id = role.id " +
                    "INNER JOIN hff_stock_location stock_location ON stock_location.id = agent.stock_location_id " +
                    "WHERE role.name = :role AND agent.state = 0 " +
                    "AND stock_location.id = :stock_location_id " +
                    "AND agent.clock_out_time > NOW() " +
                    "ORDER BY agent.id ASC ", nativeQuery = true)
    List<Agent> getAllWorkingAgentOnSameStockLocationByAgentClockOutTime(@Param("role") String role, @Param("stock_location_id") Long stockLocationId);

    @Query("SELECT DISTINCT agent FROM Agent agent " +
            "WHERE agent.deviceId = ?1")
    List<Agent> fetchByDeviceId(String deviceId);

    @Query(value = "WITH working_agents AS ( " +
            "SELECT " +
                "agent.id AS agent_id, " +
                "agent.user_id, " +
                "clockin_activity.created_at AS clock_in_time " +
            "FROM hff_agent agent " +
            "JOIN hff_user_role user_role ON agent.user_id = user_role.user_id " +
            "JOIN hff_role role ON user_role.role_id = role.id " +
            "JOIN hff_agent_activity agent_activity ON agent.id = agent_activity.agent_id " +
            "LEFT JOIN hff_agent_activity next_agent_activity ON agent_activity.agent_id = next_agent_activity.agent_id " +
                "AND agent_activity.created_at < next_agent_activity.created_at " +
            "JOIN hff_agent_clock_in_activity clockin_activity ON agent_activity.id = clockin_activity.id  " +
            "WHERE " +
                "role.name = ?1 " +
                "AND agent.state = 0 " +
                "AND (" +
                    "preferences->'is_allowed_for_auto_assignment' is null OR " +
                    "preferences->'is_allowed_for_auto_assignment' = 'true' " +
                ") " +
                "AND agent.stock_location_id IN (?2) " +
                "AND agent.clock_out_time > now() " +
                "AND DATE(agent_activity.created_at) >= DATE(NOW() - INTERVAL '1 day') " +
                "AND next_agent_activity.id is null " +
            "), " +
            "active_batch AS ( " +
                "SELECT " +
                    "batch.user_id, " +
                    "batch.id AS batch_id " +
            "FROM hff_batch batch " +
            "JOIN hff_job job ON job.batch_id = batch.id " +
            "WHERE " +
                "batch.user_id IN (SELECT user_id FROM working_agents) " +
                "AND batch.type IN (?3) " +
                "AND job.state NOT IN (?4) " +
                "AND batch.start_time >= ?5 " +
                "AND batch.start_time <= ?6 " +
            ") " +
            "SELECT agent.* " +
            "FROM hff_agent agent " +
            "JOIN working_agents ON agent.id = working_agents.agent_id " +
            "LEFT JOIN active_batch active_batch ON working_agents.user_id = active_batch.user_id " +
            "WHERE " +
            "active_batch.batch_id IS NULL " +
            "ORDER BY " +
                "CASE WHEN agent.last_activity_time IS NULL THEN 1 ELSE 0 END, " +
                "agent.last_activity_time ASC, " +
                "working_agents.clock_in_time ASC, " +
                "agent.user_id ASC", nativeQuery = true)
    List<Agent> getAllWorkingAgentOnSameStockLocationOrderedByLastActivity(String role, List<Long> stockLocationIds,
                                                List<Integer> batchTypesValue, List<Integer> inactiveStates,
                                                LocalDateTime minStartTime, LocalDateTime maxStartTime);

    Agent findByUserId(Long userId);

    @Query(value = "SELECT hff_agent.* FROM hff_agent_in_geofence " +
            "INNER JOIN hff_agent ON hff_agent.id = hff_agent_in_geofence.agent_id " +
            "WHERE hff_agent_in_geofence.agent_id IN :agent_ids " +
            "AND hff_agent_in_geofence.stock_location_id = :stock_location_id " +
            "ORDER BY hff_agent_in_geofence.created_at ASC ", nativeQuery = true)
    List<Agent> findByAgentIdOrderByCreatedAtDesc(@Param("agent_ids") List<Long> agentIds, @Param("stock_location_id") Long stockLocationId);

    @Query(value = "WITH agent_shift AS ( " +
            "    SELECT " +
            "        DISTINCT ON (a.id) a.id AS agent_id, " +
            "       cia.shift_id " +
            "    FROM hff_agent a " +
            "    JOIN hff_agent_activity aa ON a.id = aa.agent_id " +
            "    JOIN hff_agent_clock_in_activity cia ON aa.id = cia.id " +
            "    WHERE " +
            "        a.state = 0 " +
            "       AND aa.created_at >= :created_at " +
            "    ORDER BY " +
            "        a.id, aa.created_at DESC " +
            ") " +
            "SELECT " +
            "    COUNT(agent_id) AS clocked_in_agent_count " +
            "FROM agent_shift " +
            "WHERE shift_id = :shift_id", nativeQuery = true)
    int countClockedInAgentByShiftId(@Param("shift_id") Long shiftId, @Param("created_at") LocalDateTime createdAt);
}

