package com.happyfresh.fulfillment.packaging.presenter;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PackagingPresenter {

    private Long id;

    private Double price;

    private String displayPrice;

    private int maxQuantity;

    private int displaySequence;

    private boolean isDefault;

    private LocalDateTime deletedAt;

    private Long packagingTypeId;

    private Long stockLocationId;

    private String internalName;

    private String clientName;

    private String sndName;

    private String imageUrl;

    private boolean isActive;

    private Long externalStockLocationId;
}
