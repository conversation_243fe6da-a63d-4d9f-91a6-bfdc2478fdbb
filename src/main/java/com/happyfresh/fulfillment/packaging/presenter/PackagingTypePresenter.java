package com.happyfresh.fulfillment.packaging.presenter;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PackagingTypePresenter {

    private Long id;

    private String internalName;

    private Map<String, String> clientNames;

    private Map<String, String> sndNames;

    private String imageUrl;

    private boolean isActive;

    private boolean isTracked;
}
