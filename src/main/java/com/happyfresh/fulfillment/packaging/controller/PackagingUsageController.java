package com.happyfresh.fulfillment.packaging.controller;

import com.happyfresh.fulfillment.common.annotation.ResponseWrapper;
import com.happyfresh.fulfillment.entity.PackagingNotFilled;
import com.happyfresh.fulfillment.entity.PackagingUsage;
import com.happyfresh.fulfillment.packaging.form.ListOfPackagingUsageForm;
import com.happyfresh.fulfillment.packaging.mapper.PackagingUsageMapper;
import com.happyfresh.fulfillment.packaging.presenter.PackagingUsageNotFilledPresenter;
import com.happyfresh.fulfillment.packaging.presenter.PackagingUsagePresenter;
import com.happyfresh.fulfillment.packaging.service.PackagingUsageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
public class PackagingUsageController {

    @Autowired
    private PackagingUsageMapper packagingUsageMapper;

    @Autowired
    private PackagingUsageService packagingUsageService;

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/batches/{batchId}/shipments/{shipmentNumber}/packaging_usages/track")
    @ResponseWrapper(rootName = "packaging_usages")
    public List<PackagingUsagePresenter> trackPackagingUsage(@PathVariable Long batchId, @PathVariable String shipmentNumber, @Validated @RequestBody ListOfPackagingUsageForm listOfPackagingUsageForm) throws Exception {
        final List<PackagingUsage> packagingUsages = packagingUsageService.track(shipmentNumber, listOfPackagingUsageForm);
        return packagingUsageMapper.toPackagingUsagePresenters(packagingUsages);
    }


    @GetMapping("/job/packaging_usages/{userId}")
    @ResponseWrapper(rootName = "shipments")
    public List<PackagingUsageNotFilledPresenter> getPackagingUsageJob(@PathVariable Long userId) throws Exception {
        List<PackagingNotFilled> packagingsNotFilled = packagingUsageService.get(userId);
        return packagingUsageMapper.toPackagingUsageNotFilledPresenters(packagingsNotFilled);
    }
}
