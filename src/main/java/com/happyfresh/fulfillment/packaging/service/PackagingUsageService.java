package com.happyfresh.fulfillment.packaging.service;

import com.happyfresh.fulfillment.entity.PackagingNotFilled;
import com.happyfresh.fulfillment.entity.PackagingUsage;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.packaging.form.ListOfPackagingUsageForm;
import com.happyfresh.fulfillment.packaging.form.PackagingUsageForm;
import com.happyfresh.fulfillment.packaging.mapper.PackagingUsageMapper;
import com.happyfresh.fulfillment.repository.PackagingUsageRepository;
import com.happyfresh.fulfillment.shipment.service.ShipmentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

@Service
public class PackagingUsageService {

    private final Logger logger = LoggerFactory.getLogger(PackagingUsageService.class);

    @Autowired
    private ShipmentService shipmentService;
    @Autowired
    private PackagingUsageRepository packagingUsageRepository;

    @Autowired
    private PackagingUsageMapper packagingUsageMapper;


    @Transactional
    public List<PackagingUsage> track(String shipmentNumber, ListOfPackagingUsageForm listOfPackagingUsageForm) throws Exception {
        Shipment shipment = shipmentService.findByNumber(shipmentNumber);

        List<PackagingUsage> packagingUsages = new ArrayList<>();
        for (PackagingUsageForm packagingUsageForm : listOfPackagingUsageForm.getPackagingUsages()) {
            PackagingUsage packagingUsage = packagingUsageRepository.findByShipmentIdAndInternalName(shipment.getId(), packagingUsageForm.getInternalName());
            if (packagingUsage == null) {
                packagingUsage = packagingUsageMapper.packagingUsageFormToPackagingUsage(packagingUsageForm);
                packagingUsage.setShipment(shipment);
            } else {
                packagingUsage.setQuantity(packagingUsageForm.getQuantity());
            }
            packagingUsageRepository.save(packagingUsage);

            packagingUsages.add(packagingUsage);
        }

        return packagingUsages;
    }

    @Transactional
    public List<PackagingNotFilled> get(Long userId) throws Exception {
        try {
            List<Object[]> sqlObjects = packagingUsageRepository.findShipmentsWhichPackagingNotFilled(userId);
            List<PackagingNotFilled> packagingsNotFilled = new ArrayList<>();
            for (Object[] obj : sqlObjects) {
                PackagingNotFilled packagingNotFilled = new PackagingNotFilled();
                packagingNotFilled.setShipmentNumber(((String) obj[0]).toString());
                packagingNotFilled.setOrderNumber(((String) obj[1]).toString());
                packagingNotFilled.setCustomerName(((String) obj[2]).toString());
                packagingNotFilled.setBatchId(((BigInteger) obj[3]));
                packagingsNotFilled.add(packagingNotFilled);
            }
            return packagingsNotFilled;
        } catch (Exception e) {
            throw e;
        }
    }
}
