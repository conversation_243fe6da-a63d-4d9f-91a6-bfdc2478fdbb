package com.happyfresh.fulfillment.grabExpress.service;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;

public class GrabExpressHeaderGenerator {
    private static final Logger LOG = LoggerFactory.getLogger(GrabExpressHeaderGenerator.class);

    private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";
    private static final String SHA256_ALGORITHM = "SHA-256";
    private static final String CONTENT_TYPE_HEADER = "Content-Type";
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String DATE_HEADER = "Date";

    private static String base64(byte[] b) {
        return new String(Base64.getEncoder().encode(b));
    }

    private static String hash(String text, String secret) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(secret.getBytes(), HMAC_SHA256_ALGORITHM);
            Mac mac = Mac.getInstance(HMAC_SHA256_ALGORITHM);
            mac.init(signingKey);
            return base64(mac.doFinal(text.getBytes()));

        }
        catch (Throwable t) {
            LOG.error("Error when hashing header for calling GE API", t);
            return text;
        }
    }

    private static String hash(String text) {
        try {
            MessageDigest digest = MessageDigest.getInstance(SHA256_ALGORITHM);
            byte[] hash = digest.digest(text.getBytes(StandardCharsets.UTF_8));
            return base64(hash);

        }
        catch (Throwable t) {
            LOG.error("Error when hashing header for calling GE API", t);
            return text;
        }
    }

    private static String createContentDigest(String body) {
        if (body!=null)
            if (body.length()>0)
                return hash(body);
        //No hash needed for content digest
        return StringUtils.EMPTY;
    }

    private static String createHMACDigest(String method, String contentType, String path, String body, String date, String clientSecret) {
        //date format:  "Thu, 20 Jul 2017 10:49:36 GMT"
        StringBuilder sb = new StringBuilder();

        // method
        sb.append(method.toUpperCase()).append("\n");

        // content-type
        if (method.equalsIgnoreCase("GET"))
            contentType = StringUtils.EMPTY;
        sb.append(contentType).append("\n");

        //date
        sb.append(date).append("\n");

        // path
        sb.append(path).append("\n");

        // content-digest
        sb.append(createContentDigest(body)).append("\n");

        return hash(sb.toString(), clientSecret);
    }

    private static String getRFC1123Date(Date date) {
        // RFC3339
        //date format: SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z").format(date)
        // RFC1123
        // Wed, 19 Jul 2017 10:04:18 +0100
        // Mon, 24 Jul 2017 01:53:07 GMT
        // Mon, 24 Jul 2017 01:50:47 GMT
        SimpleDateFormat df = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
        df.setTimeZone(TimeZone.getTimeZone("GMT"));
        return df.format(date);
    }

    public static Map<String,String> getHeaders(String authorization, String method, String mime) {
        Map<String, String> headers = new HashMap<>();
        if (!method.equalsIgnoreCase("GET")) {
            headers.put(CONTENT_TYPE_HEADER, mime);
        }
        headers.put(AUTHORIZATION_HEADER, authorization);
        return headers;
    }

    public static HttpHeaders getOAuth2_0Headers() {
        Map<String, String> headers = new HashMap<>();
        headers.put(CONTENT_TYPE_HEADER, "application/json");
        HttpHeaders requestHeaders = new HttpHeaders();
        for (String key : headers.keySet()) {
            requestHeaders.add(key, headers.get(key));
        }
        return requestHeaders;
    }
}
