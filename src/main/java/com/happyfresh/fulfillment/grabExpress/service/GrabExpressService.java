package com.happyfresh.fulfillment.grabExpress.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.ImmutableMap;
import com.google.gson.JsonObject;
import com.happyfresh.fulfillment.batch.exception.SwitchToHFFailedException;
import com.happyfresh.fulfillment.batch.service.BatchService;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.service.*;
import com.happyfresh.fulfillment.common.service.radar.RadarApiService;
import com.happyfresh.fulfillment.common.tracking.GrabExpressEventTracker;
import com.happyfresh.fulfillment.common.util.WebRequestLogger;
import com.happyfresh.fulfillment.enabler.service.StratoWebhookService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.grabExpress.exception.GrabExpressException;
import com.happyfresh.fulfillment.grabExpress.form.GrabExpressDeliveryForm;
import com.happyfresh.fulfillment.grabExpress.mapper.GrabExpressDeliveryMapper;
import com.happyfresh.fulfillment.grabExpress.model.GrabExpressBookingResponse;
import com.happyfresh.fulfillment.grabExpress.presenter.GrabExpressDeliveryPresenter;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.shipment.mapper.ShipmentMapper;
import com.happyfresh.fulfillment.user.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.jobrunr.scheduling.JobScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientResponseException;

import java.io.IOException;
import java.lang.reflect.Field;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.happyfresh.fulfillment.common.annotation.type.WebhookType.GRAB_EXPRESS_BOOKING_ERROR;
import static com.happyfresh.fulfillment.common.annotation.type.WebhookType.GRAB_EXPRESS_BOOKING_ERROR_SWITCH_TO_HF;

@Service
public class GrabExpressService {

    private final Logger LOGGER = LoggerFactory.getLogger(GrabExpressService.class);
    private static final int MAX_RETRY = 2;
    private static final String SHIPMENT_NOT_FOUND = "Shipment not found";
    private static final String BATCH_TYPE_NOT_GE = "Batch fleet type not Grab Express";
    private static final String DELIVERY_JOB_NOT_FOUND = "Delivery job is not found";

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private GrabExpressAPI grabExpressAPI;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private GrabExpressDeliveryRepository grabExpressDeliveryRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private GrabExpressDeliveryMapper grabExpressDeliveryMapper;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private WebhookPublisherService webhookPublisherService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private RadarApiService radarApiService;

    @Autowired
    private StratoWebhookService stratoWebhookService;

    @Autowired
    private BatchService batchService;

    @Autowired
    private CoralogixAPIService coralogixAPIService;

    @Autowired
    private GrabExpressEventTracker tracker;

    @Autowired
    private EnablerEventPublisherService enablerEventPublisherService;

    @Autowired
    private ShipmentMapper shipmentMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private JobScheduler jobScheduler;

    @Transactional
    public void scheduleGrabBooking(GrabExpressDelivery grabExpressDelivery) {
        long delayMillis = ChronoUnit.MILLIS.between(LocalDateTime.now(), grabExpressDelivery.getBookingScheduledAt());
        if (delayMillis < 0) delayMillis = 0;
        
        Tenant tenant = grabExpressDelivery.getTenant();
        Role role = roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, tenant.getId());
        User user = userRepository.findByRolesContainingAndTenantId(role, tenant.getId());
        AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), tenant.getToken());
        
        jobScheduler.schedule(
                Instant.now().plus(delayMillis, ChronoUnit.MILLIS),
            () -> createGrabExpressBookingAsync(grabExpressDelivery.getId(), authenticationToken)
        );
        
        LOGGER.info("Scheduled JobRunr job for Grab Express booking ID {} with delay {} ms", 
                    grabExpressDelivery.getId(), delayMillis);
    }

    @Transactional
    public ResponseEntity createGrabExpressBooking(String shipmentNumber, boolean withRetry) {
        GrabExpressDelivery grabExpressDelivery = getGrabExpressDelivery(shipmentNumber);

        if (grabExpressDelivery != null && grabExpressDelivery.isOnProgress())
            throw new GrabExpressException("This order still in progress");

        try {
            Shipment shipment = grabExpressDelivery.getShipment();
            Country country = shipment.getSlot().getStockLocation().getState().getCountry();

            String authorization = grabExpressAPI.oAuth2_0();
            ResponseEntity response = grabExpressAPI.bookingGrabExpress(shipment, country, authorization);
            GrabExpressBookingResponse grabExpressBookingResponse = parseResponse(response);

            if (StringUtils.isNotEmpty(grabExpressDelivery.getExternalId()) && !StringUtils.equals(grabExpressDelivery.getExternalId(), grabExpressBookingResponse.getDeliveryID())) {
                grabExpressDelivery = new GrabExpressDelivery();
                grabExpressDelivery.setShipment(shipment);
                grabExpressDelivery.setRetryCount(0);
            }

            grabExpressDelivery.setExternalId(grabExpressBookingResponse.getDeliveryID());
            grabExpressDelivery.setStatus(GrabExpressDelivery.Status.valueOf(grabExpressBookingResponse.getStatus()));
            grabExpressDelivery.setPin(grabExpressBookingResponse.getPickupPin());
            grabExpressDelivery.setBookingScheduledAt(LocalDateTime.now());
            grabExpressDeliveryRepository.save(grabExpressDelivery);

            return response;
        } catch (Exception ex) {
            ThreadContext.put("SHIPMENT_NUMBER", grabExpressDelivery.getShipment().getNumber());
            ThreadContext.put("GRAB_EXPRESS_DELIVERY_ID", grabExpressDelivery.getId().toString());
            ThreadContext.put("RETRY_COUNT", grabExpressDelivery.getRetryCount().toString());
            LOGGER.error("Failed booking grab express", ex);
            ThreadContext.clearAll();

            boolean isError = true;
            String errorMessage = "";
            if (ex instanceof HttpClientErrorException) {
                isError = ((HttpClientErrorException) ex).getStatusCode().isError();
                errorMessage = ((HttpClientErrorException) ex).getResponseBodyAsString();
            } else if (ex instanceof HttpServerErrorException) {
                isError = ((HttpServerErrorException) ex).getStatusCode().isError();
                errorMessage = ((HttpServerErrorException) ex).getResponseBodyAsString();
            } else {
                errorMessage = ex.toString();
            }

            if (withRetry && isError) {
                final int retryCount = grabExpressDelivery.getRetryCount() + 1;
                int delay = retryCount + 1;

                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("error", errorMessage);
                ObjectNode objectNode = null;
                try {
                    objectNode = (ObjectNode) new ObjectMapper().readTree(jsonObject.toString());
                } catch (IOException e) {
                    LOGGER.error("Webhook Error", ex);
                }

                String path = String.format("/shipments/%s", shipmentNumber);
                Tenant tenant = grabExpressDelivery.getTenant();

                if (retryCount > MAX_RETRY) {
                    failBooking(shipmentNumber, grabExpressDelivery);
                    webhookPublisherService.publish(GRAB_EXPRESS_BOOKING_ERROR_SWITCH_TO_HF, objectNode, path, tenant.getId());
                } else {
                    webhookPublisherService.publish(GRAB_EXPRESS_BOOKING_ERROR, objectNode, path, tenant.getId());
                    grabExpressDelivery.setRetryCount(retryCount);
                    LocalDateTime now = LocalDateTime.now().withSecond(0).withNano(0).plusMinutes(delay);
                    grabExpressDelivery.setBookingScheduledAt(now);
                    grabExpressDeliveryRepository.save(grabExpressDelivery);
                    scheduleGrabBooking(grabExpressDelivery);
                }
                return null;
            } else
                throw new GrabExpressException(errorMessage);
        }
    }

    @Async
    @Transactional
    public void createGrabExpressBookingAsync(Long grabExpressDeliveryId, AbstractAuthenticationToken authenticationToken) {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        SecurityContextHolder.getContext().setAuthentication(authenticationToken);

        try {
            if (jedisLockService.lock(String.format("grab_express_booking_%s", grabExpressDeliveryId.toString()), 1)) {
                GrabExpressDelivery grabExpressDelivery = grabExpressDeliveryRepository.getOne(grabExpressDeliveryId);
                if (grabExpressDelivery.getStatus() != null)
                    throw new GrabExpressException("This order already booked");

                createGrabExpressBooking(grabExpressDelivery.getShipment().getNumber(), true);
            }

        } catch (Exception e) {
            ThreadContext.put("GRAB_EXPRESS_DELIVERY_ID", grabExpressDeliveryId.toString());
            LOGGER.error("Create booking async error", e);
            ThreadContext.clearAll();
        } finally {
            SecurityContextHolder.clearContext();
            jedisLockService.unlock();
        }
    }

    @Transactional(readOnly = true)
    public ResponseEntity getGrabExpressBookingStatus(String shipmentNumber) {
        GrabExpressDelivery grabExpressDelivery = getGrabExpressDelivery(shipmentNumber);

        String deliveryId = grabExpressDelivery.getExternalId();
        String countryIsoName = grabExpressDelivery.getShipment().getSlot().getStockLocation().getState().getCountry().getIsoName();

        String authorization = grabExpressAPI.oAuth2_0();
        return grabExpressAPI.grabDeliveryInfo(deliveryId, countryIsoName, authorization);
    }

    @Transactional
    public ResponseEntity cancelGrabExpressBooking(String shipmentNumber, String cancelReason) {
        GrabExpressDelivery grabExpressDelivery = getGrabExpressDelivery(shipmentNumber);

        if (!grabExpressDelivery.isCancelable())
            throw new GrabExpressException("The grab delivery status is already " + grabExpressDelivery.getStatus().toString());

        String deliveryId = grabExpressDelivery.getExternalId();
        String countryIsoName = grabExpressDelivery.getShipment().getSlot().getStockLocation().getState().getCountry().getIsoName();

        ResponseEntity response;
        String authorization = grabExpressAPI.oAuth2_0();
        try {
            response = grabExpressAPI.cancelDelivery(deliveryId, countryIsoName, authorization);
        } catch (RestClientResponseException exception) {
            response = grabExpressAPI.grabDeliveryInfo(deliveryId, countryIsoName, authorization);
            GrabExpressBookingResponse grabExpressBookingResponse = parseResponse(response);
            if (!grabExpressDelivery.getStatus().equals(GrabExpressDelivery.Status.CANCELED)) {
                grabExpressDelivery.setStatus(GrabExpressDelivery.Status.valueOf(grabExpressBookingResponse.getStatus()));
                grabExpressDeliveryRepository.save(grabExpressDelivery);

                // If completed but attempt to cancel due to status not sync, then complete the job as well
                Shipment shipment = grabExpressDelivery.getShipment();
                if (GrabExpressDelivery.Status.valueOf(grabExpressBookingResponse.getStatus()).equals(GrabExpressDelivery.Status.IN_DELIVERY)) {
                    updateJob(shipment, GrabExpressDelivery.Status.IN_DELIVERY);
                    publishSynchronizeWebhook(grabExpressDelivery);
                } else if (GrabExpressDelivery.Status.valueOf(grabExpressBookingResponse.getStatus()).equals(GrabExpressDelivery.Status.COMPLETED)) {
                    updateJob(shipment, GrabExpressDelivery.Status.IN_DELIVERY);
                    updateJob(shipment, GrabExpressDelivery.Status.COMPLETED);
                    publishSynchronizeWebhook(grabExpressDelivery);
                }
                return response;
            } else {
                throw new GrabExpressException(exception.getMessage() + " " + exception.getResponseBodyAsString());
            }
        }

        grabExpressDelivery.setStatus(GrabExpressDelivery.Status.CANCELED);
        grabExpressDelivery.setCancelReason(GrabExpressDelivery.CancelReason.valueOf(cancelReason));
        grabExpressDeliveryRepository.save(grabExpressDelivery);

        return response;
    }

    @Transactional
    public GrabExpressDeliveryPresenter handleWebhook(GrabExpressDeliveryForm grabExpressDeliveryForm, boolean track) throws Exception {
        String lockKey = "grab_express_webhook_".concat(grabExpressDeliveryForm.getDeliveryID());
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(lockKey)) {
                try {
                    // Tracking
                    if (track) {
                        String eventName = "Grab Delivery Log";
                        ImmutableMap.Builder builder = ImmutableMap.builder();
                        for (Field f : grabExpressDeliveryForm.getClass().getDeclaredFields()) {
                            f.setAccessible(true);
                            Object value = f.get(grabExpressDeliveryForm);
                            if (value != null) {
                                builder.put(f.getName(), value);
                            }
                        }
                        ImmutableMap properties = builder.build();
                        coralogixAPIService.sendLog(CoralogixAPIService.LogSeverity.INFO, eventName, eventName, getClass().getSimpleName(), "handleWebhook", properties);
                    }
                } catch (Exception e) {
                    WebRequestLogger webRequestLogger = applicationContext.getBean(WebRequestLogger.class);
                    webRequestLogger.warn(LOGGER, "Segment track error");
                }

                GrabExpressDelivery grabExpressDelivery = grabExpressDeliveryRepository.findByExternalId(grabExpressDeliveryForm.getDeliveryID());
                if (grabExpressDelivery == null) {
                    final ImmutableMap<String, String> additionalInfo = ImmutableMap.of(
                            "orderNumber", grabExpressDeliveryForm.getMerchantOrderID(),
                            "deliveryID", grabExpressDeliveryForm.getDeliveryID()
                    );
                    WebRequestLogger webRequestLogger = applicationContext.getBean(WebRequestLogger.class);
                    webRequestLogger.warn(LOGGER, "Grab webhook update on null grabExpressDelivery", additionalInfo);
                    return null;
                }

                if (GrabExpressDelivery.Status.valueOf(grabExpressDeliveryForm.getStatus()).equals(grabExpressDelivery.getStatus())) {
                    return null;
                }

                LocalDateTime latestStatusChangedAt = LocalDateTime.ofInstant(Instant.ofEpochSecond(grabExpressDeliveryForm.getTimestamp()), ZoneId.of("UTC"));
                if (grabExpressDelivery.getLatestStatusChangedAt() != null && grabExpressDelivery.getLatestStatusChangedAt().isAfter(latestStatusChangedAt))
                    return null;

                Shipment shipment = grabExpressDelivery.getShipment();

                grabExpressDeliveryMapper.grabExpressDeliveryFormToGrabExpressDelivery(grabExpressDeliveryForm, grabExpressDelivery);

                grabExpressDelivery.setShipment(shipment);
                grabExpressDelivery = grabExpressDeliveryRepository.save(grabExpressDelivery);

                if (grabExpressDelivery.getStatus().equals(GrabExpressDelivery.Status.FAILED) && shipment.getShoppingJob().isPresent()) {
                    Optional<Job> shoppingJob = shipment.getShoppingJob();
                    if (shoppingJob.isPresent()) {
                        List<Long> userIds = new ArrayList<>();
                        Long userId = shoppingJob.get().getBatch().getUser().getId();
                        userIds.add(userId);
                        Tenant tenant = shipment.getTenant();
                        notificationService.sendPushNotificationForGrabExpressFailBooking(userIds, shipment.getOrderNumber(), tenant.getId());
                    }
                }

                updateJob(shipment, grabExpressDelivery.getStatus());
                GrabExpressDeliveryPresenter presenter = grabExpressDeliveryMapper.toGrabExpressDeliveryPresenter(grabExpressDelivery);
                if (grabExpressDelivery.getStatus().equals(GrabExpressDelivery.Status.PICKING_UP))
                    presenter.setBooked(true);

                publishSynchronizeWebhook(grabExpressDelivery);
                return presenter;

            } else {
                WebRequestLogger webRequestLogger = applicationContext.getBean(WebRequestLogger.class);
                webRequestLogger.error(LOGGER, "Acquire redis lock timeout");
                return null;
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    public void trackWebhook(String headers, GrabExpressDeliveryForm grabExpressDeliveryForm) throws JsonProcessingException {
        String body = new ObjectMapper().writeValueAsString(grabExpressDeliveryForm);
        tracker.construct(GrabExpressEventTracker.EVENT_WEBHOOK, "/api/grab_express/webhook", "request",  headers, body, null);
        tracker.track();
    }

    private void updateJob(Shipment shipment, GrabExpressDelivery.Status status) {
        Optional<Job> optionalDeliveryJob = shipment.getDeliveryJob();
        if (!optionalDeliveryJob.isPresent()) {
            return;
        }

        Job deliveryJob = optionalDeliveryJob.get();
        List<Job.State> ignoredJobState = Arrays.asList(Job.State.FINISHED, Job.State.FAILED, Job.State.CANCELLED);
        if (ignoredJobState.contains(deliveryJob.getState()) && deliveryJob.getBatch().getTplType() != Batch.TplType.GRAB_EXPRESS)
            return;

        Role role = roleRepository.findByName(Role.Name.GRAB_EXPRESS_DRIVER);
        User user = userRepository.findByRolesContainingAndTenantId(role, deliveryJob.getTenant().getId());

        if (status == GrabExpressDelivery.Status.PICKING_UP) {
            setUserToBatch(user, deliveryJob);
        } else if (status == GrabExpressDelivery.Status.IN_DELIVERY) {
            transitionToDelivery(user, deliveryJob);
        } else if (status == GrabExpressDelivery.Status.COMPLETED) {
            completeDelivery(user, deliveryJob);
        } else if (status == GrabExpressDelivery.Status.FAILED || status == GrabExpressDelivery.Status.RETURNED) {
            switchGEToHF(shipment.getNumber());
        }
    }

    private void setUserToBatch(User user, Job job) {
        Batch batch = job.getBatch();
        if (batch.getUser() == null) {
            batch.setUser(user);
            batchRepository.save(batch);
        }
    }

    private void transitionToDelivery(User user, Job job) {
        setUserToBatch(user, job);

        job.setState(Job.State.STARTED);
        job.setState(Job.State.ACCEPTED);
        job.setState(Job.State.DELIVERING);
        jobRepository.save(job);
    }

    private void completeDelivery(User user, Job job) {
        setUserToBatch(user, job);

        job.setState(Job.State.FOUND_ADDRESS);
        job.setState(Job.State.FINALIZING);
        job.setState(Job.State.FINISHED);
        jobRepository.save(job);
    }

    private void failBooking(String shipmentNumber, GrabExpressDelivery grabExpressDelivery) {
        Shipment shipment = grabExpressDelivery.getShipment();
        Optional<Job> shoppingJob = shipment.getShoppingJob();
        if (shoppingJob.isPresent()) {
            List<Long> userIds = new ArrayList<>();
            Long userId = shoppingJob.get().getBatch().getUser().getId();
            userIds.add(userId);
            Tenant tenant = shipment.getTenant();
            notificationService.sendPushNotificationForGrabExpressFailBooking(userIds, shipment.getOrderNumber(), tenant.getId());
        }
        switchGEToHF(shipmentNumber);
        grabExpressDeliveryRepository.delete(grabExpressDelivery.getId());
    }

    private GrabExpressDelivery getGrabExpressDelivery(String shipmentNumber) {
        Shipment shipment = shipmentRepository.findByNumber(shipmentNumber);
        if (shipment == null)
            throw new GrabExpressException(SHIPMENT_NOT_FOUND);

        Optional<Job> job = shipment.getDeliveryJob();
        if (!job.isPresent() || !job.get().getBatch().isGrabExpress())
            throw new GrabExpressException(BATCH_TYPE_NOT_GE);

        GrabExpressDelivery grabExpressDelivery = shipment.getCurrentGrabExpressDelivery();

        if (grabExpressDelivery == null)
            throw new GrabExpressException("This grab express delivery not found");

        return grabExpressDelivery;
    }

    @Transactional
    public void switchGEToHF(String shipmentNumber) {
        GrabExpressDelivery grabExpressDelivery = getGrabExpressDelivery(shipmentNumber);
        if (grabExpressDelivery.isOnProgress())
            throw new SwitchToHFFailedException("This order already in progress");

        Optional<Job> deliveryJob = grabExpressDelivery.getShipment().getDeliveryJob();
        if (!deliveryJob.isPresent())
            throw new SwitchToHFFailedException(DELIVERY_JOB_NOT_FOUND);

        if (!deliveryJob.get().getBatch().isGrabExpress())
            throw new SwitchToHFFailedException(BATCH_TYPE_NOT_GE);

        if (deliveryJob.get().getState() != Job.State.FINISHED) {
            Job job = deliveryJob.get();
            batchService.switchBatchToHF(job);

            // Create geofence for fleet tracking
            // Radar service disabled
//            Shipment shipment = job.getShipment();
//            Country country = shipment.getSlot().getStockLocation().getState().getCountry();
//            radarApiService.createDeliveryGeofence(shipment, country);
        }
    }

    private GrabExpressBookingResponse parseResponse(ResponseEntity responseEntity) {
        ObjectMapper mapper = new ObjectMapper();
        String jsonString = Objects.requireNonNull(responseEntity.getBody()).toString();
        GrabExpressBookingResponse grabExpressBookingResponse = new GrabExpressBookingResponse();
        try {
            grabExpressBookingResponse = mapper.readValue(jsonString, GrabExpressBookingResponse.class);
        } catch (IOException e) {
            final ImmutableMap<String, String> additionalInfo = ImmutableMap.of("JSON_STRING", jsonString);
            WebRequestLogger webRequestLogger = applicationContext.getBean(WebRequestLogger.class);
            webRequestLogger.error(LOGGER, "Fail to parse Grab Express booking response", additionalInfo, e);
        }
        return grabExpressBookingResponse;
    }

    @Transactional(readOnly = true)
    public List<GrabExpressDelivery> getPendingBooking(Long userId) {
        User shopper = userRepository.getOne(userId);

        LocalDateTime minimumStartTime = LocalDateTime.now().minusWeeks(1L);
        return grabExpressDeliveryRepository.findAllPendingBookingByShopperId(shopper.getId(), minimumStartTime);
    }

    @Transactional
    public void publishSynchronizeWebhook(GrabExpressDelivery delivery) {
        String path = String.format("/shipments/%s/", delivery.getShipment().getNumber());
        Tenant tenant = delivery.getTenant();
        WebhookType webhookType = WebhookType.GRAB_EXPRESS_CALLBACK;
        String rootName = "grab_express_delivery";
        Object body = grabExpressDeliveryMapper.toGrabExpressDeliveryPresenter(delivery);
        ObjectNode wrapperNode = JsonNodeFactory.instance.objectNode();
        JsonNode bodyNode = mapper.convertValue(body, JsonNode.class);
        wrapperNode.set(rootName, bodyNode);

        webhookPublisherService.publish(webhookType, wrapperNode, path, tenant.getId());
    }

    @Transactional
    public void bookByShipment(StockLocation stockLocation, Shipment shipment) {
        // Create GE booking scheduled for every GE batch
        Optional<Job> deliveryJob = shipment.getDeliveryJob();
        if (deliveryJob.isPresent() && deliveryJob.get().getBatch().isGrabExpress() && shipment.getCurrentGrabExpressDelivery() == null) {
            GrabExpressDelivery grabExpressDelivery = new GrabExpressDelivery();
            grabExpressDelivery.setShipment(shipment);
            grabExpressDelivery.setRetryCount(0);

            LocalDateTime now = LocalDateTime.now().withSecond(0).withNano(0);
            LocalDateTime bookingScheduledAt = shipment.getSlot().getStartTime().minusMinutes(stockLocation.getGrabExpressOffsetTime());
            if (now.isBefore(bookingScheduledAt)) {
                grabExpressDelivery.setBookingScheduledAt(bookingScheduledAt);
            } else {
                grabExpressDelivery.setBookingScheduledAt(now.plusMinutes(stockLocation.getGrabExpressDelayTime()));
            }

            grabExpressDeliveryRepository.save(grabExpressDelivery);
            scheduleGrabBooking(grabExpressDelivery);
        }
    }

    @Transactional
    public void adminSwitchToHF(Shipment shipment, Job deliveryJob) {
        if (!deliveryJob.getBatch().isGrabExpress())
            throw new SwitchToHFFailedException(BATCH_TYPE_NOT_GE);

        GrabExpressDelivery grabExpressDelivery = shipment.getCurrentGrabExpressDelivery();

        if (grabExpressDelivery != null) {
            if (grabExpressDelivery.isCancelable()) {
                String deliveryId = grabExpressDelivery.getExternalId();
                String countryIsoName = grabExpressDelivery.getShipment().getSlot().getStockLocation().getState().getCountry().getIsoName();
                String authorization = grabExpressAPI.oAuth2_0();
                grabExpressAPI.cancelDelivery(deliveryId, countryIsoName, authorization);
            }

            grabExpressDelivery.setStatus(GrabExpressDelivery.Status.CANCELED);
            grabExpressDelivery.setCancelReason(GrabExpressDelivery.CancelReason.SWITCH_TO_HF_BY_ADMIN);
            grabExpressDeliveryRepository.save(grabExpressDelivery);
        }

        Country country = shipment.getSlot().getStockLocation().getState().getCountry();
        batchService.switchBatchToHF(deliveryJob);

        // Create geofence for fleet tracking
        // Radar service disabled
//        radarApiService.createDeliveryGeofence(shipment, country);
    }
}
