package com.happyfresh.fulfillment.grabExpress.scheduler;

import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.service.JedisLockService;
import com.happyfresh.fulfillment.entity.GrabExpressDelivery;
import com.happyfresh.fulfillment.entity.Role;
import com.happyfresh.fulfillment.entity.Tenant;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressService;
import com.happyfresh.fulfillment.repository.GrabExpressDeliveryRepository;
import com.happyfresh.fulfillment.repository.RoleRepository;
import com.happyfresh.fulfillment.repository.TenantRepository;
import com.happyfresh.fulfillment.repository.UserRepository;
import com.happyfresh.fulfillment.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Component
@ConditionalOnProperty(name = "scheduler.enabled", matchIfMissing = true)
public class AutoBookingGrabExpressScheduler {

    @Autowired
    private ApplicationContext applicationContext;

    private static final Logger logger = LoggerFactory.getLogger(AutoBookingGrabExpressScheduler.class);

    @Autowired
    private GrabExpressDeliveryRepository grabExpressDeliveryRepository;

    @Autowired
    private GrabExpressService grabExpressService;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private UserService userService;

    //TODO: after implement amazon MQ, cron value should rollback to every 5 minutes and query date time minus 180 seconds
    @Scheduled(cron = "0 0/3 * * * *")
    @Transactional
    public void autoBookingUnallocatedStatus() {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock("auto_booking_grab_express_scheduler", 1, 180000)) {
                LocalDateTime dateTime = LocalDateTime.now().minusSeconds(60);
                List<Tenant> tenants = tenantRepository.findAll();
                for (Tenant tenant : tenants) {
                    Role role = roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, tenant.getId());
                    if (role != null) {
                        User user = userRepository.findByRolesContainingAndTenantId(role, tenant.getId());
                        AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), tenant.getToken());
                        SecurityContextHolder.getContext().setAuthentication(authenticationToken);

                        List<GrabExpressDelivery> pendingGrabExpressDeliveries = grabExpressDeliveryRepository.findAllByStatusIsNullAndBookingScheduledAtIsBefore(dateTime);
                        for (GrabExpressDelivery grabExpressDelivery : pendingGrabExpressDeliveries) {
                            createGrabExpressBooking(grabExpressDelivery, authenticationToken);
                        }
                        SecurityContextHolder.clearContext();
                    }
                }
            }
        } catch (Exception ex) {
            logger.error("[autoBookingUnallocatedStatus] FAILED", ex);
        } finally {
            jedisLockService.releaseConnection();
        }
    }

    private void createGrabExpressBooking(GrabExpressDelivery grabExpressDelivery, AbstractAuthenticationToken authenticationToken) {
        try {
            transactionHelper.withNewTransaction(() -> grabExpressService.createGrabExpressBookingAsync(grabExpressDelivery.getId(), authenticationToken));
        } catch (Exception e) {
            String message = String.format(
                    "Failed auto booking JOB. SHIPMENT_NUMBER: %s. GRAB_EXPRESS_DELIVERY_ID: %s",
                    grabExpressDelivery.getShipment().getNumber(),
                    grabExpressDelivery.getId().toString()
            );
            logger.error(message, e);
        }
    }
}
