package com.happyfresh.fulfillment.grabExpress.presenter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Transient;
import java.time.LocalDateTime;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class GrabExpressDeliveryPresenter {

    private Long id;

    private Long batchId;

    private String shipmentNumber;

    private String orderNumber;

    private String status;

    private String externalId;

    private String driverName;

    private String driverPhone;

    private String pin;

    private String driverPhotoUrl;

    private String cancelReason;

    private String trackingUrl;

    @JsonIgnore
    private LocalDateTime latestStatusChangedAt;

    @JsonIgnore
    private LocalDateTime bookingScheduledAt;

    @JsonProperty("latest_status_changed_at")
    public String getLatestStatusChangedAtString() {
        if (latestStatusChangedAt != null)
            return latestStatusChangedAt.toString();
        return null;
    }

    public void setLatestStatusChangedAtString(String latestStatusChangedAtString) {
        if (latestStatusChangedAtString == null)
            latestStatusChangedAt = null;
        else
            latestStatusChangedAt = LocalDateTime.parse(latestStatusChangedAtString);
    }

    @JsonProperty("booking_scheduled_at")
    public String getBookingScheduledAtString() {
        return bookingScheduledAt.toString();
    }

    public void setBookingScheduledAtString(String bookingScheduledAtString) {
        bookingScheduledAt = LocalDateTime.parse(bookingScheduledAtString);
    }

    @JsonIgnore
    private LocalDateTime slotStartTime;

    @JsonIgnore
    private LocalDateTime slotEndTime;

    @JsonIgnore
    private LocalDateTime createdAt;

    @JsonProperty("slot_start_time")
    public String getSlotStartTimeString() {
        return slotStartTime.toString();
    }

    public void setSlotStartTimeString(String slotStartTimeString) {
        slotStartTime = LocalDateTime.parse(slotStartTimeString);
    }

    @JsonProperty("slot_end_time")
    public String getSlotEndTimeString() {
        return slotEndTime.toString();
    }

    public void setSlotEndTimeString(String slotEndTimeString) {
        slotEndTime = LocalDateTime.parse(slotEndTimeString);
    }

    @JsonProperty("created_at")
    public String getCreatedAtString() {
        return createdAt.toString();
    }

    public void setCreatedAtString(String createdAtString) {
        createdAt = LocalDateTime.parse(createdAtString);
    }

    @Transient
    private boolean isBooked;
}
