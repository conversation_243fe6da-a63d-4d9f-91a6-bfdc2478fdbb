package com.happyfresh.fulfillment.sayurbox.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.happyfresh.fulfillment.common.util.UserInfoFetcher;
import com.happyfresh.fulfillment.common.util.WebRequestLogger;
import com.happyfresh.fulfillment.enabler.form.EnablerItemForm;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.repository.RoleRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.repository.StockLocationRepository;
import com.happyfresh.fulfillment.repository.UserRepository;
import com.happyfresh.fulfillment.sayurbox.form.SayurboxWebhookForm;
import com.happyfresh.fulfillment.sayurbox.model.SayurboxCapacity;
import com.happyfresh.fulfillment.sayurbox.model.SayurboxOrderDetail;
import com.happyfresh.fulfillment.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.support.BasicAuthorizationInterceptor;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SayurboxService {
    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private StockLocationRepository stockLocationRepository;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private SayurboxWebhookService sayurboxWebhookService;

    @Autowired
    private UserService userService;

    private final Logger LOGGER = LoggerFactory.getLogger(SayurboxService.class);

    @Autowired
    private UserInfoFetcher userInfoFetcher;

    @Transactional(readOnly = true)
    public List<SayurboxCapacity> getTimeslots(String deliveryType, Double lat, Double lon, String orderNumber, Long externalUserId) {
        Tenant tenant = userInfoFetcher.getTenant();

        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getInterceptors().add(new BasicAuthorizationInterceptor(tenant.getOmsAuthUsername(), tenant.getOmsAuthPassword()));
        String url = tenant.getOmsBaseUrl() + "/sayurbox/timeslots?delivery_type=" + deliveryType + "&lat=" + lat + "&lon=" + lon+ "&order_number=" + orderNumber+ "&user_id=" + externalUserId;
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

        List<SayurboxCapacity> items = new ArrayList<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode actualObj = mapper.readTree(response.getBody());
            TypeFactory typeFactory = mapper.getTypeFactory();
            items = mapper.readValue(actualObj.get("data").get("timeslots").toString(), typeFactory.constructCollectionType(List.class, SayurboxCapacity.class));
        } catch (Exception ex) {
            WebRequestLogger webRequestLogger = applicationContext.getBean(WebRequestLogger.class);
            webRequestLogger.error(LOGGER, "Failed calling get time slots rom Sayurbox Service", ex);
        }

        return items;
    }

    @Transactional(readOnly = true)
    public SayurboxOrderDetail getOrderDetails(Shipment shipment) {
        Tenant tenant = shipment.getTenant();

        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getInterceptors().add(new BasicAuthorizationInterceptor(tenant.getOmsAuthUsername(), tenant.getOmsAuthPassword()));
        String url = tenant.getOmsBaseUrl() + "/sayurbox/order_details?order_number=" + shipment.getOrderNumber();
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

        SayurboxOrderDetail sayurboxOrderDetail = null;
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode actualObj = mapper.readTree(response.getBody());
            TypeFactory typeFactory = mapper.getTypeFactory();
            sayurboxOrderDetail = mapper.readValue(actualObj.get("data").toString(), typeFactory.constructType(SayurboxOrderDetail.class));
        } catch (Exception ex) {
            WebRequestLogger webRequestLogger = applicationContext.getBean(WebRequestLogger.class);
            webRequestLogger.error(LOGGER, "Failed calling get order details rom Sayurbox Service", ex);
        }

        return sayurboxOrderDetail;
    }

    @Transactional
    public void checkSayurboxLateOrder(Tenant tenant) {
        if(Boolean.FALSE.equals(tenant.isEnableSayurboxCheckStatusScheduler()))
            return;

        List<StockLocation> stockLocations = stockLocationRepository.findByEnablerAndTenant(StockLocation.Enabler.SAYURBOX, tenant);
        LocalDateTime minimumStartTime = LocalDateTime.now().minusWeeks(1L);
        List<Long> stockLocationIds = stockLocations.stream().map(StockLocation::getId).collect(Collectors.toList());
        List<Shipment> shipments = shipmentRepository.findAllLateShipments(stockLocationIds, minimumStartTime, Job.getOngoingDeliveryStatesValue());

        for (Shipment shipment : shipments) {
            setSystemAdminAuthToken(shipment);
            fetchSayurboxOrderDetailAndUpdateOrderStatus(shipment);
        }
    }

    @Transactional
    public void fetchSayurboxOrderDetailAndUpdateOrderStatus(Shipment shipment) {
        SayurboxOrderDetail sayurboxOrderDetail = getOrderDetails(shipment);
        if (sayurboxOrderDetail != null) {
            handleSayurboxOrderStatus(shipment, sayurboxOrderDetail);
        }else{
            LOGGER.info("[checkSayurboxLateOrder] Order not found: {}", shipment.getOrderNumber());
        }
    }

    private void handleSayurboxOrderStatus(Shipment shipment, SayurboxOrderDetail sayurboxOrderDetail) {
        List<SayurboxOrderDetail.StatusHistory> statusHistories = sayurboxOrderDetail.getStatusHistories();
        Collections.sort(statusHistories, Comparator.comparing(SayurboxOrderDetail.StatusHistory::getExecutionTime));

        for (SayurboxOrderDetail.StatusHistory statusHistory : statusHistories) {
            SayurboxWebhookForm sayurboxWebhookForm = convertSayurboxOrderDetailToSayurboxWebhookForm(sayurboxOrderDetail, statusHistory);
            updateOrderStatus(shipment, sayurboxWebhookForm);
        }
    }

    @Transactional
    public void sanitizeAndUpdateOrderStatus(Shipment shipment, SayurboxWebhookForm sayurboxWebhookForm) {
        List<EnablerItemForm> enablerItemForms = sayurboxWebhookForm.getItems();
        List<EnablerItemForm> newEnablerItemForms = new ArrayList<>();
        for (EnablerItemForm enablerItemForm : enablerItemForms) {
            EnablerItemForm sanitizedEnablerItemForm = sanitizeItemForm(enablerItemForm);
            newEnablerItemForms.add(sanitizedEnablerItemForm);
        }

        sayurboxWebhookForm.setItems(newEnablerItemForms);
        updateOrderStatus(shipment, sayurboxWebhookForm);
    }

    private void updateOrderStatus(Shipment shipment, SayurboxWebhookForm sayurboxWebhookForm) {
        StockLocation stockLocation = shipment.getSlot().getStockLocation();
        switch (sayurboxWebhookForm.getStatus().toLowerCase()) {
            case "created":
                sayurboxWebhookService.handleSayurboxOrderCreated(shipment, stockLocation, sayurboxWebhookForm);
                break;
            case "confirmed":
                sayurboxWebhookService.handleSayurboxOrderConfirmed(shipment, stockLocation, sayurboxWebhookForm);
                break;
            case "shipped":
                sayurboxWebhookService.handleSayurboxOrderShipped(shipment, stockLocation, sayurboxWebhookForm);
                break;
            case "delivered":
                sayurboxWebhookService.handleSayurboxOrderDelivered(shipment, stockLocation, sayurboxWebhookForm);
                break;
            case "cancelled":
                sayurboxWebhookService.handleSayurboxOrderCancelled(shipment, stockLocation, sayurboxWebhookForm);
                break;
            default:
                break;
        }
    }

    private SayurboxWebhookForm convertSayurboxOrderDetailToSayurboxWebhookForm(SayurboxOrderDetail sayurboxOrderDetail, SayurboxOrderDetail.StatusHistory statusHistory) {
        SayurboxWebhookForm form = new SayurboxWebhookForm();
        form.setStatus(statusHistory.getOrderStatus());
        form.setStatusTime(statusHistory.getExecutionTime());
        form.setOrderNumber(sayurboxOrderDetail.getRefNumber());

        List<EnablerItemForm> itemForms = sayurboxOrderDetail.getItems().stream().map(
                item -> getEnablerItemForm(item.getAdditionalInfo().getPartnerCode(), item.getQuantity(), item.getRejectQuantity())
        ).collect(Collectors.toList());

        form.setItems(itemForms);
        return form;
    }

    private EnablerItemForm getEnablerItemForm(String sku, Integer qtyOnhand, Integer qtyOos) {
        EnablerItemForm itemForm = new EnablerItemForm();
        itemForm.setItemSku(sku);
        itemForm.setQuantityOnHand(qtyOnhand);
        itemForm.setQuantityOutOfStock(qtyOos);

        return sanitizeItemForm(itemForm);
    }

    private EnablerItemForm sanitizeItemForm(EnablerItemForm enablerItemForm) {
        enablerItemForm.setItemSku(enablerItemForm.getItemSku()+"-ID");
        return enablerItemForm;
    }

    private AbstractAuthenticationToken setSystemAdminAuthToken(Shipment shipment) {
        Tenant tenant = shipment.getTenant();
        Role role = roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, tenant.getId());
        User user = userRepository.findByRolesContainingAndTenantId(role, tenant.getId());

        AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), user.getTenant().getToken());
        SecurityContextHolder.getContext().setAuthentication(authenticationToken);
        return authenticationToken;
    }
}
