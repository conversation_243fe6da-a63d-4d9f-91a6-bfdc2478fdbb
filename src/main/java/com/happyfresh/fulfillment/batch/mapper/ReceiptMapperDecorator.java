package com.happyfresh.fulfillment.batch.mapper;

import com.happyfresh.fulfillment.entity.Job;
import com.happyfresh.fulfillment.entity.Receipt;
import com.happyfresh.fulfillment.entity.Slot;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.receipt.presenter.PendingReceiptPresenter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Optional;

public abstract class ReceiptMapperDecorator implements ReceiptMapper {

    @Autowired
    @Qualifier("delegate")
    private ReceiptMapper delegate;

    @Override
    public PendingReceiptPresenter receiptToPendingReceiptPresenter(Receipt receipt) {
        PendingReceiptPresenter presenter = delegate.receiptToPendingReceiptPresenter(receipt);

        Slot slot = receipt.getShipment().getSlot();
        StockLocation stockLocation = slot.getStockLocation();
        presenter.setDateTime(slot.getStartTime());
        presenter.setStockLocationName(stockLocation.getName());
        presenter.setTaxRequired(stockLocation.getState().getCountry().isTaxRequired());

        Optional<Job> job = receipt.getShipment().getJobs().stream().filter(j -> j.isShopping() || j.isOnDemandShopping() || j.isRanger() || j.isOnDemandRanger()).findFirst();
        if (job.isPresent()) {
            presenter.setBatchId(job.get().getBatch().getId());
        }


        return presenter;
    }
}