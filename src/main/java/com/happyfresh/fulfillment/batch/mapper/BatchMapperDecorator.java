package com.happyfresh.fulfillment.batch.mapper;

import com.happyfresh.fulfillment.batch.presenter.BatchPresenterWithException;
import com.happyfresh.fulfillment.batch.presenter.BatchPresenter;
import com.happyfresh.fulfillment.batch.service.BatchService;
import com.happyfresh.fulfillment.common.exception.ApiError;
import com.happyfresh.fulfillment.entity.Batch;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.shipment.mapper.ShipmentMapper;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentPresenter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public abstract class BatchMapperDecorator implements BatchMapper {

    @Autowired
    @Qualifier("delegate")
    private BatchMapper delegate;

    @Autowired
    private BatchService batchService;

    @Autowired
    private ShipmentMapper shipmentMapper;

    @Override
    public BatchPresenter batchToBatchPresenter(Batch batch) {
        BatchPresenter presenter = delegate.batchToBatchPresenter(batch);
        return setBatchDetail(presenter, batch, false, false);
    }

    @Override
    public BatchPresenter batchToAvailableBatchPresenter(Batch batch) {
        BatchPresenter presenter = delegate.batchToBatchPresenter(batch);
        return setBatchDetail(presenter, batch, false, true);
    }

    @Override
    public BatchPresenter batchToUpcomingBatchPresenter(Batch batch) {
        BatchPresenter presenter = delegate.batchToUpcomingBatchPresenter(batch);
        return setUpcomingBatchDetail(presenter, batch, false);
}

    @Override
    public BatchPresenterWithException batchToBatchPresenterWithException(Batch batch, List<ApiError> exceptions) {
        BatchPresenter batchPresenter = delegate.batchToBatchPresenter(batch);
        batchPresenter =  setBatchDetail(batchPresenter, batch, true, false);

        BatchPresenterWithException presenter = new BatchPresenterWithException();
        presenter.setBatchPresenter(batchPresenter);
        presenter.setExceptions(exceptions);

        return presenter;
    }

    private List<ShipmentPresenter> createShipmentPresenters(Batch batch, boolean withItems, boolean availableBatch) {
        List<Shipment> completedShipments = batchService.getAllCompletedShipments(batch);

        if (!availableBatch) {
            return completedShipments.stream().sorted(Comparator.comparing(Shipment::getId)).map(
                    shipment -> withItems ? shipmentMapper.shipmentToShipmentItemPresenter(shipment) : shipmentMapper.shipmentToShipmentPresenter(shipment)
            ).collect(Collectors.toList());
        }

        return completedShipments.stream().sorted(Comparator.comparing(Shipment::getId)).map(
                shipment -> shipmentMapper.shipmentToBatchAvailableShipmentPresenter(shipment)
        ).collect(Collectors.toList());

    }

    private BatchPresenter setBatchDetail(BatchPresenter presenter, Batch batch, boolean withItems, boolean availableBatch) {
        List<ShipmentPresenter> shipmentPresenters = createShipmentPresenters(batch, withItems, availableBatch);
        presenter.setShipments(shipmentPresenters);
        presenter.setState(batchService.getBatchState(batch));
        presenter.setEstimatedShoppingTime(batchService.getEstimatedShoppingTime(batch));
        presenter.setStoreGeoPoint(batch.getStockLocation().getLatLon());

        return presenter;
    }

    private List<ShipmentPresenter> getPendingShipmentPresenters(Batch batch, boolean withItems) {
        List<Shipment> shipments = batchService.getAllShipments(batch);
        return shipments.stream()
                .sorted(Comparator.comparing(Shipment::getId))
                .map(shipment -> withItems ?
                        shipmentMapper.shipmentToShipmentItemPresenter(shipment) :
                        shipmentMapper.shipmentToShipmentPresenter(shipment)
                )
                .collect(Collectors.toList());
    }

    private BatchPresenter setUpcomingBatchDetail(BatchPresenter presenter, Batch batch, boolean withItems) {
        List<ShipmentPresenter> shipmentPresenters = getPendingShipmentPresenters(batch, withItems);

        presenter.setShipments(shipmentPresenters);
        presenter.setState(batchService.getBatchState(batch));
        presenter.setEstimatedShoppingTime(batchService.getEstimatedShoppingTime(batch));
        presenter.setStoreGeoPoint(batch.getStockLocation().getLatLon());

        return presenter;
    }
}
