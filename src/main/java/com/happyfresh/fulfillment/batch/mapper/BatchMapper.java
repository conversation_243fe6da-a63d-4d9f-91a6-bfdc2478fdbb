package com.happyfresh.fulfillment.batch.mapper;

import com.happyfresh.fulfillment.batch.presenter.BatchPresenterWithException;
import com.happyfresh.fulfillment.batch.presenter.BatchPresenter;
import com.happyfresh.fulfillment.common.exception.ApiError;
import com.happyfresh.fulfillment.entity.Batch;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
@DecoratedWith(BatchMapperDecorator.class)
public interface BatchMapper {

    BatchPresenter batchToBatchPresenter(Batch batch);

    BatchPresenter batchToAvailableBatchPresenter(Batch batch);

    BatchPresenter batchToUpcomingBatchPresenter(Batch batch);

    BatchPresenterWithException batchToBatchPresenterWithException(Batch batch, List<ApiError> exceptions);

}
