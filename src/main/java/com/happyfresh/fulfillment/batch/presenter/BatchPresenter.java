package com.happyfresh.fulfillment.batch.presenter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.entity.Batch;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentPresenter;
import lombok.Getter;
import lombok.Setter;
import org.elasticsearch.common.geo.GeoPoint;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class BatchPresenter {

    private Long id;

    @JsonIgnore
    private LocalDateTime startTime;

    @JsonIgnore
    private LocalDateTime endTime;

    @JsonIgnore
    private LocalDateTime handoverTime;

    private BatchStatePresenter state;

    private List<ShipmentPresenter> shipments;

    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Double estimatedShoppingTime;

    protected Double storeDistance;

    private GeoPoint storeGeoPoint;

    private Batch.Type type;

    @JsonProperty("start_time")
    public String getStartTimeString() {
        return DateTimeUtil.localDateTimeToStringSlotDateTime(startTime);
    }

    public void setStartTimeString(String startTimeString) {
        startTime = LocalDateTime.parse(startTimeString);
    }

    @JsonProperty("end_time")
    public String getEndTimeString() {
        return DateTimeUtil.localDateTimeToStringSlotDateTime(endTime);
    }

    public void setEndTimeString(String endTimeString) {
        endTime = LocalDateTime.parse(endTimeString);
    }

    @JsonProperty("handover_time")
    public String getHandoverTimeString() {
        return DateTimeUtil.localDateTimeToStringSlotDateTime(handoverTime);
    }

    public void setHandoverTimeString(String handoverTimeString) {
        handoverTime = LocalDateTime.parse(handoverTimeString);
    }

}
