package com.happyfresh.fulfillment.batch.controller;

import com.happyfresh.fulfillment.batch.bean.ItemShopParam;
import com.happyfresh.fulfillment.batch.form.*;
import com.happyfresh.fulfillment.batch.mapper.BatchMapper;
import com.happyfresh.fulfillment.batch.presenter.BatchItemPresenter;
import com.happyfresh.fulfillment.batch.presenter.BatchPresenter;
import com.happyfresh.fulfillment.batch.service.BatchAvailabilityService;
import com.happyfresh.fulfillment.batch.service.BatchSndService;
import com.happyfresh.fulfillment.batch.service.ItemSndService;
import com.happyfresh.fulfillment.common.annotation.Publish;
import com.happyfresh.fulfillment.common.annotation.RequestWrapper;
import com.happyfresh.fulfillment.common.annotation.ResponseWrapper;
import com.happyfresh.fulfillment.common.annotation.ValidImage;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.exception.ApiError;
import com.happyfresh.fulfillment.common.exception.BadRequestException;
import com.happyfresh.fulfillment.common.exception.type.RedisLockTimeoutException;
import com.happyfresh.fulfillment.common.messaging.activemq.type.MessageDestination;
import com.happyfresh.fulfillment.common.property.AppProperty;
import com.happyfresh.fulfillment.common.property.RedisProperty;
import com.happyfresh.fulfillment.common.service.JedisLockService;
import com.happyfresh.fulfillment.common.service.TPLService;
import com.happyfresh.fulfillment.common.util.DistanceUtil;
import com.happyfresh.fulfillment.common.util.UserInfoFetcher;
import com.happyfresh.fulfillment.enabler.service.StratoWebhookService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.gosend.service.GosendService;
import com.happyfresh.fulfillment.grabExpress.form.CancelReasonForm;
import com.happyfresh.fulfillment.grabExpress.mapper.
        GrabExpressDeliveryMapper;
import com.happyfresh.fulfillment.grabExpress.presenter.GrabExpressDeliveryPresenter;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressService;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.GrabExpressDeliveryRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.repository.SlotRepository;
import com.happyfresh.fulfillment.shipment.form.ShipmentFailedForm;
import com.happyfresh.fulfillment.shipment.mapper.DeliveryPhotoMapper;
import com.happyfresh.fulfillment.shipment.mapper.ItemMapper;
import com.happyfresh.fulfillment.shipment.mapper.ShipmentMapper;
import com.happyfresh.fulfillment.shipment.presenter.*;
import com.happyfresh.fulfillment.slot.presenter.GeofenceStatusPresenter;
import lombok.Setter;
import org.elasticsearch.common.geo.GeoPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.StringTokenizer;

@RestController
@RequestMapping("/api/batches")
@Validated
public class BatchController {

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private SlotRepository slotRepository;

    @Autowired
    private BatchMapper batchMapper;

    @Autowired
    private DeliveryPhotoMapper deliveryPhotoMapper;

    @Autowired
    private GrabExpressDeliveryMapper grabExpressDeliveryMapper;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private ShipmentMapper shipmentMapper;

    @Autowired
    private BatchAvailabilityService batchAvailabilityService;

    @Autowired
    private BatchSndService batchSndService;

    @Autowired
    private UserInfoFetcher userInfoFetcher;

    @Autowired
    private ItemSndService itemSndService;

    @Autowired
    private ItemMapper itemMapper;

    @Autowired
    private GrabExpressService grabExpressService;

    @Autowired
    private GrabExpressDeliveryRepository grabExpressDeliveryRepository;

    @Autowired
    private TPLService tplService;

    @Autowired
    private RedisProperty redisProperty;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private StratoWebhookService stratoWebhookService;

    @Setter
    @Autowired
    private AppProperty appProperty;

    private static final String BATCH_FINALIZE_LOCK_KEY = "finalize_batch_%s";

    public static final String BATCH_START_LOCK_KEY = "start_batch_%s";

    private static final Logger LOG = LoggerFactory.getLogger(BatchController.class);

    @PreAuthorize("isAgentAuthenticated()")
    @GetMapping(value = "/active")
    @ResponseWrapper(rootName = "batches")
    public List<BatchPresenter> activeBatches(@RequestParam(required = false, name = "lat") String latString,
                                              @RequestParam(required = false, name = "lon") String lonString) {
        User currentUser = userInfoFetcher.getAuthenticatedUser();
        GeoPoint fleetGeoPoint = DistanceUtil.toGeoPoint(latString, lonString);

        return batchAvailabilityService.getActiveBatchesWithStoreDistance(currentUser, fleetGeoPoint);

    }

    @PreAuthorize("isAgentAuthenticated()")
    @GetMapping(value = "/upcoming")
    @ResponseWrapper(rootName = "batches")
    public List<BatchPresenter> upcomingBatches(@RequestParam(required = false, name = "lat") String latString,
                                                @RequestParam(required = false, name = "lon") String lonString) {
        User currentUser = userInfoFetcher.getAuthenticatedUser();
        GeoPoint fleetGeoPoint = DistanceUtil.toGeoPoint(latString, lonString);

        return batchAvailabilityService.getUpcomingBatches(currentUser, fleetGeoPoint);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @GetMapping(value = "/{batchId}")
    @ResponseWrapper(rootName = "batch")
    public ResponseEntity<BatchPresenter> getBatch(@PathVariable Long batchId) {
        Batch batch = batchRepository.getOne(batchId);

        BatchPresenter batchPresenter = batchMapper.batchToBatchPresenter(batch);
        return new ResponseEntity<>(batchPresenter, HttpStatus.OK);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @GetMapping("/{batchId}/items")
    @ResponseWrapper(rootName = "batch")
    public ResponseEntity<BatchItemPresenter> getBatchItem(@PathVariable Long batchId) {
        List<Shipment> shipments = shipmentRepository.fetchAllByBatchIdFetchItems(batchId);
        if (shipments.isEmpty())
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);

        List<ShipmentItemPresenter> shipmentPresenters = shipmentMapper.shipmentToShipmentItemPresenters(shipments);
        BatchItemPresenter batchItemPresenter = new BatchItemPresenter();
        batchItemPresenter.setShipments(shipmentPresenters);
        return new ResponseEntity<>(batchItemPresenter, HttpStatus.OK);
    }

    @PreAuthorize("isAgentAuthenticated()")
    @PostMapping(value = "/{batchId}/start")
    @ResponseWrapper(rootName = "batch")
    @Publish(WebhookType.START_BATCH)
    public ResponseEntity<BatchPresenter> start(@PathVariable Long batchId, HttpServletRequest request) throws InterruptedException {
        // TODO: PENDING. need forwarding the header from fulfillment gateway first
        // String sndVersionCode = request.getHeader("X-Snd-Version-Code");
        // ApplicationUtil.validateMinimumSnDVersion(sndVersionCode, appProperty.getMinSndVersionCodeToTakeJob());

        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format(BATCH_START_LOCK_KEY, batchId))) {
                User currentUser = userInfoFetcher.getAuthenticatedUser();
                final Batch batch = batchSndService.start(batchId, currentUser);
                return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isDriverAuthenticated()")
    @PostMapping(value = "/{batchId}/book")
    @ResponseWrapper(rootName = "batch")
    @Publish(WebhookType.BOOK_BATCH)
    public ResponseEntity<BatchPresenter> book(@PathVariable Long batchId) {
        User currentUser = userInfoFetcher.getAuthenticatedUser();
        final Batch batch = batchSndService.book(batchId, currentUser);
        BatchPresenter presenter = batchMapper.batchToBatchPresenter(batch);

        stratoWebhookService.sendDeliveryBookedWebhook(presenter.getShipments().get(0).getOrderNumber());
        return new ResponseEntity<>(presenter, HttpStatus.OK);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/pickup")
    @ResponseWrapper(rootName = "batch")
    @Publish(WebhookType.PICKUP_BATCH)
    public ResponseEntity<BatchPresenter> pickup(@PathVariable Long batchId) {
        final Batch batch = batchSndService.pickup(batchId);
        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/accept")
    @ResponseWrapper(rootName = "batch")
    @Publish(WebhookType.ACCEPT_SHIPMENT)
    public ResponseEntity<BatchPresenter> accept(@PathVariable Long batchId, @PathVariable String shipmentNumber) {
        final Batch batch = batchSndService.accept(shipmentNumber);
        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);

    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/deliver")
    @ResponseWrapper(rootName = "batch")
    @Publish(WebhookType.DELIVER_SHIPMENT)
    public ResponseEntity<BatchPresenter> deliver(@PathVariable Long batchId, @PathVariable String shipmentNumber) {
        final Batch batch = batchSndService.deliver(shipmentNumber);
        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/arrive")
    @ResponseWrapper(rootName = "batch")
    @Publish(WebhookType.ARRIVE_SHIPMENT)
    public ResponseEntity<BatchPresenter> arrive(@PathVariable Long batchId, @PathVariable String shipmentNumber) {
        final Batch batch = batchSndService.arrive(shipmentNumber);
        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/capture")
    @ResponseWrapper(rootName = "batch")
    public ResponseEntity<BatchPresenter> capture(@PathVariable Long batchId, @PathVariable String shipmentNumber, HttpServletRequest request) throws Exception {
        String clientIPAddress = request.getRemoteAddr();
        String xForwardedForHeader = request.getHeader("X-FORWARDED-FOR");
        if (xForwardedForHeader != null) {
            clientIPAddress = new StringTokenizer(xForwardedForHeader, ",").nextToken().trim();
        }

        final Batch batch = batchSndService.capture(shipmentNumber, clientIPAddress);
        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/switch_to_cod")
    @ResponseWrapper(rootName = "batch")
    @Publish(WebhookType.SWITCH_TO_COD)
    public ResponseEntity<BatchPresenter> switchToCOD(@PathVariable Long batchId, @PathVariable String shipmentNumber, HttpServletRequest request) throws Exception {
        String clientIPAddress = request.getRemoteAddr();
        String xForwardedForHeader = request.getHeader("X-FORWARDED-FOR");
        if (xForwardedForHeader != null) {
            clientIPAddress = new StringTokenizer(xForwardedForHeader, ",").nextToken().trim();
        }
        final Batch batch = batchSndService.switchToCOD(shipmentNumber, clientIPAddress);
        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/verify_payment")
    @ResponseWrapper(rootName = "batch")
    public ResponseEntity<BatchPresenter> verifyPayment(@PathVariable Long batchId, @PathVariable String shipmentNumber, @RequestBody VerifyPaymentForm verifyPayment, HttpServletRequest request) throws Exception {
        String clientIPAddress = request.getRemoteAddr();
        String xForwardedForHeader = request.getHeader("X-FORWARDED-FOR");
        if (xForwardedForHeader != null) {
            clientIPAddress = new StringTokenizer(xForwardedForHeader, ",").nextToken().trim();
        }

        final Batch batch = batchSndService.verifyPayment(shipmentNumber, clientIPAddress, verifyPayment, false);
        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/split_to_cod")
    @ResponseWrapper(rootName = "batch")
    public ResponseEntity<BatchPresenter> splitToCOD(@PathVariable Long batchId, @PathVariable String shipmentNumber, HttpServletRequest request) throws Exception {
        final Batch batch = batchSndService.splitToCOD(shipmentNumber);
        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/finish")
    @ResponseWrapper(rootName = "batch")
    @Publish(WebhookType.FINISH_SHIPMENT)
    public ResponseEntity<BatchPresenter> finish(@PathVariable Long batchId, @PathVariable String shipmentNumber,
                                                 @Validated DeliveryInfoForm deliveryInfoForm) throws Exception {
        final Batch batch = batchSndService.finish(shipmentNumber, deliveryInfoForm);
        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/finalize")
    @RequestWrapper(rootName = "items")
    @Publish(WebhookType.FINALIZE_SHIPMENT)
    public ResponseEntity<ShipmentItemPresenterWithException> finalizeShipment(@PathVariable Long batchId, @PathVariable String shipmentNumber, @Valid @NotEmpty @RequestBody List<ItemFinalizeForm> forms) throws InterruptedException {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format(BATCH_FINALIZE_LOCK_KEY, batchId))) {
                final List<ApiError> exceptions = batchSndService.finalizeShipment(shipmentNumber, forms);
                final Shipment shipment = shipmentRepository.findByNumber(shipmentNumber);

                if (exceptions.isEmpty()) {
                    Optional<Job> deliveryJob = shipment.getDeliveryJob();

                    if (deliveryJob.isPresent()) {
                        Batch deliveryBatch = deliveryJob.get().getBatch();
                        if (deliveryBatch.isGosendBike()) {
                            final Batch batch = batchRepository.fetchById(batchId);
                            batchSndService.placeOrQueueGosendDeliveries(batch, shipment, GosendService.ServiceType.INSTANT);
                        }

                        if (deliveryBatch.isGosendCar()) {
                            final Batch batch = batchRepository.fetchById(batchId);
                            batchSndService.placeOrQueueGosendDeliveries(batch, shipment, GosendService.ServiceType.INSTANTCAR);
                        }

                        if (deliveryBatch.isGrabExpress()) {
                            Optional<GrabExpressDelivery> grabExpressDelivery = grabExpressDeliveryRepository.findAllByShipmentNumber(shipment.getNumber()).stream().max(Comparator.comparing(GrabExpressDelivery::getId));
                            if (grabExpressDelivery.isPresent()) {
                                grabExpressService.scheduleGrabBooking(grabExpressDelivery.get());
                            }
                        }
                    }
                }

                HttpStatus httpStatus = exceptions.isEmpty() ? HttpStatus.OK : HttpStatus.MULTI_STATUS;
                return new ResponseEntity<>(shipmentMapper.shipmentToShipmentItemPresenterWithException(shipment, exceptions), httpStatus);
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/pay")
    @ResponseWrapper(rootName = "batch")
    @Publish(WebhookType.PAY_SHIPMENT)
    public ResponseEntity<BatchPresenter> pay(@PathVariable Long batchId, @PathVariable String shipmentNumber,
                                              @Validated ListOfReceiptForm receiptForm) throws Exception {
        final Batch batch = batchSndService.pay(batchId, shipmentNumber, receiptForm);

        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping(value = "/{batchId}/cancel")
    @ResponseWrapper(rootName = "batch")
    @Publish(WebhookType.CANCEL_BATCH)
    public BatchPresenter cancel(@PathVariable Long batchId) throws Exception {
        User currentUser = userInfoFetcher.getAuthenticatedUser();
        Batch batch = batchSndService.cancel(batchId, currentUser);
        return batch != null ? batchMapper.batchToBatchPresenter(batch) : null;
    }

    @PreAuthorize("isShopperAuthenticated()")
    @GetMapping(value = "/grab/pending_bookings")
    @ResponseWrapper(rootName = "grab_express_deliveries")
    public List<GrabExpressDeliveryPresenter> grabExpressPendingBooking() {
        User currentUser = userInfoFetcher.getAuthenticatedUser();
        List<GrabExpressDelivery> grabExpressPendingBookings = grabExpressService.getPendingBooking(currentUser.getId());
        return grabExpressDeliveryMapper.toGrabExpressDeliveryPresenters(grabExpressPendingBookings);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PutMapping("/{batchId}/shipments/{shipmentNumber}/items/{itemSku}/shop")
    @ResponseWrapper(rootName = "item")
    @Publish(WebhookType.MARK_ITEM_ON_HAND)
    public ItemPresenter shopItem(@PathVariable Long batchId, @PathVariable String shipmentNumber, @PathVariable String itemSku,
                                  @Validated @RequestBody ItemShopForm itemShopForm) throws InterruptedException {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format(BATCH_FINALIZE_LOCK_KEY, batchId))) {
                User currentUser = userInfoFetcher.getAuthenticatedUser();

                ItemShopParam itemShopParam = new ItemShopParam(batchId, currentUser.getId(), shipmentNumber, itemSku, itemShopForm.getQuantity());
                itemShopParam.setWeight(itemShopForm.getWeight());
                itemShopParam.setShopperNotesFulfilled(itemShopForm.isShopperNotesFulfilled());

                Item item = itemSndService.shop(itemShopParam);
                return itemMapper.toItemPresenter(item);
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PutMapping("/{batchId}/shipments/{shipmentNumber}/items/{itemSku}/mark_oos")
    @ResponseWrapper(rootName = "item")
    @Publish(WebhookType.MARK_ITEM_OOS)
    public ItemPresenter markOosItem(@PathVariable Long batchId, @PathVariable String shipmentNumber, @PathVariable String itemSku,
                                     @Validated @RequestBody ItemOOSForm itemOOSForm) throws InterruptedException {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format(BATCH_FINALIZE_LOCK_KEY, batchId))) {
                final User currentUser = userInfoFetcher.getAuthenticatedUser();
                final Item item = itemSndService.markOOS(batchId, shipmentNumber, itemSku, itemOOSForm.getOosType(), itemOOSForm.getOosDetail(), currentUser.getId());
                return itemMapper.toItemPresenter(item);
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PutMapping("/{batchId}/shipments/{shipmentNumber}/items/{itemSku}/unmark_oos")
    @ResponseWrapper(rootName = "item")
    @Publish(WebhookType.UNMARK_ITEM_OOS)
    public ItemPresenter unmarkOosItem(@PathVariable Long batchId, @PathVariable String shipmentNumber, @PathVariable String itemSku) throws InterruptedException {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format(BATCH_FINALIZE_LOCK_KEY, batchId))) {
                final User currentUser = userInfoFetcher.getAuthenticatedUser();
                final String type = "oos";
                final Item item = itemSndService.unmark(batchId, shipmentNumber, itemSku, type, currentUser.getId());
                return itemMapper.toItemPresenter(item);
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/items/{itemSku}/mark_price_discrepancy")
    @ResponseWrapper(rootName = "item")
    @Publish(WebhookType.MARK_ITEM_PRICE_DISCREPANCY)
    public ItemWithPriceDiscrepancyPresenter markPriceDiscrepancyItem(@PathVariable Long batchId, @PathVariable String shipmentNumber, @PathVariable String itemSku,
                                                                      @NotNull @RequestParam Double newCostPrice,
                                                                      @NotNull @RequestParam MultipartFile attachment) throws Exception {
        final User currentUser = userInfoFetcher.getAuthenticatedUser();
        final String type = "price_discrepancy";
        final Item item = itemSndService.mark(batchId, shipmentNumber, itemSku, type, newCostPrice, attachment, currentUser.getId());
        return itemMapper.toItemWithPriceDiscrepancyPresenter(item, currentUser);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PutMapping("/{batchId}/shipments/{shipmentNumber}/items/{itemSku}/unmark_price_discrepancy")
    @ResponseWrapper(rootName = "item")
    @Publish(WebhookType.UNMARK_ITEM_PRICE_DISCREPANCY)
    public ItemWithPriceDiscrepancyPresenter unmarkPriceDiscrepancyItem(@PathVariable Long batchId, @PathVariable String shipmentNumber, @PathVariable String itemSku) {
        final User currentUser = userInfoFetcher.getAuthenticatedUser();
        final String type = "price_discrepancy";
        final Item item = itemSndService.unmark(batchId, shipmentNumber, itemSku, type, currentUser.getId());
        return itemMapper.toItemWithPriceDiscrepancyPresenter(item, currentUser);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/items/{itemSku}/mark_product_mismatch")
    @ResponseWrapper(rootName = "item")
    @Publish(WebhookType.MARK_ITEM_MISMATCHED)
    public ItemPresenter markProductMismatchItem(@PathVariable Long batchId, @PathVariable String shipmentNumber, @PathVariable String itemSku,
                                                 @NotNull @RequestParam MultipartFile attachment) throws Exception {

        final User currentUser = userInfoFetcher.getAuthenticatedUser();
        final String type = "product_mismatch";
        final Item item = itemSndService.mark(batchId, shipmentNumber, itemSku, type, null, attachment, currentUser.getId());
        return itemMapper.toItemPresenter(item);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PutMapping("/{batchId}/shipments/{shipmentNumber}/items/{itemSku}/unmark_product_mismatch")
    @ResponseWrapper(rootName = "item")
    @Publish(WebhookType.UNMARK_ITEM_MISMATCHED)
    public ItemPresenter unmarkProductMismatchItem(@PathVariable Long batchId, @PathVariable String shipmentNumber, @PathVariable String itemSku) {
        final User currentUser = userInfoFetcher.getAuthenticatedUser();
        final String type = "product_mismatch";
        final Item item = itemSndService.unmark(batchId, shipmentNumber, itemSku, type, currentUser.getId());
        return itemMapper.toItemPresenter(item);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/items/{itemSku}/replacements")
    @ResponseWrapper(rootName = "item")
    @Publish(WebhookType.CREATE_REPLACEMENT)
    public ItemPresenter createReplacement(@PathVariable Long batchId, @PathVariable String shipmentNumber, @PathVariable String itemSku,
                                           @Validated @RequestBody ItemReplacementForm itemReplacementForm) throws InterruptedException {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format(BATCH_FINALIZE_LOCK_KEY, batchId))) {
                return itemMapper.toItemPresenter(itemSndService.createReplacement(shipmentNumber, itemSku, itemReplacementForm));
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @DeleteMapping("/{batchId}/shipments/{shipmentNumber}/items/{itemSku}/replacements/{replacementSku}")
    @ResponseWrapper(rootName = "item")
    @Publish(WebhookType.DELETE_REPLACEMENT)
    public ItemPresenter deleteReplacement(@PathVariable Long batchId, @PathVariable String shipmentNumber, @PathVariable String itemSku,
                                           @PathVariable String replacementSku) throws InterruptedException {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format(BATCH_FINALIZE_LOCK_KEY, batchId))) {
                return itemMapper.toItemPresenter(itemSndService.deleteReplacement(shipmentNumber, itemSku, replacementSku));
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @DeleteMapping("/{batchId}/shipments/{shipmentNumber}/items/replacements")
    @ResponseWrapper(rootName = "success")
    @Publish(WebhookType.DELETE_REPLACEMENTS)
    public boolean emptyReplacement(@PathVariable Long batchId, @PathVariable String shipmentNumber) throws InterruptedException {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format(BATCH_FINALIZE_LOCK_KEY, batchId))) {
                return itemSndService.emptyReplacement(shipmentNumber);
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PutMapping("/{batchId}/shipments/{shipmentNumber}/items/rejects")
    @ResponseWrapper(rootName = "shipment")
    @RequestWrapper(rootName = "items")
    @Publish(WebhookType.MARK_ITEM_REJECTED)
    public ShipmentItemPresenter rejectItem(@PathVariable Long batchId, @PathVariable String shipmentNumber,
                                            @Valid @RequestBody List<ItemRejectForm> rejectForms) {
        Shipment shipment = itemSndService.reject(shipmentNumber, rejectForms);

        return shipmentMapper.shipmentToShipmentItemPresenter(shipment);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PutMapping("/{batchId}/shipments/{shipmentNumber}/fail_delivery")
    @ResponseWrapper(rootName = "batch")
    @Publish(WebhookType.FAILED_SHIPMENT)
    public BatchPresenter failDelivery(@PathVariable Long batchId, @PathVariable String shipmentNumber, @Validated @RequestBody ShipmentFailedForm form) {
        throw new BadRequestException("Fail delivery is not allowed, please contact admin");

//        Batch batch = batchSndService.failDelivery(shipmentNumber, form.getReason());
//        return batchMapper.batchToBatchPresenter(batch);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/grab")
    @ResponseWrapper(ignore = true)
    public ResponseEntity bookGrabExpress(@PathVariable Long batchId, @PathVariable String shipmentNumber) {
        return grabExpressService.createGrabExpressBooking(shipmentNumber, false);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PutMapping("/{batchId}/shipments/{shipmentNumber}/grab/cancel")
    @ResponseWrapper(ignore = true)
    public ResponseEntity cancelGrabExpressBooking(@PathVariable Long batchId, @PathVariable String shipmentNumber, @Validated @RequestBody CancelReasonForm cancelReasonForm) {
        return grabExpressService.cancelGrabExpressBooking(shipmentNumber, cancelReasonForm.getCancelReason());
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PutMapping("/{batchId}/shipments/{shipmentNumber}/grab/to_hf")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ResponseWrapper(ignore = true)
    public void changeGrabExpressOrderToHF(@PathVariable Long batchId, @PathVariable String shipmentNumber) {
        grabExpressService.switchGEToHF(shipmentNumber);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PutMapping("/{batchId}/shipments/{shipmentNumber}/to_hf")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ResponseWrapper(ignore = true)
    public void changeTPLOrderToHF(@PathVariable Long batchId, @PathVariable String shipmentNumber) {
        tplService.switchToHF(shipmentNumber);
    }


    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/fail_shopping")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void cancelOrder(@PathVariable Long batchId, @PathVariable String shipmentNumber, @Valid CancellationForm cancellationForm) throws Exception {
        throw new BadRequestException("Action is not allowed");
//        batchSndService.failShopping(shipmentNumber, cancellationForm);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/geofence_status")
    @ResponseWrapper(rootName = "geofence_status")
    public GeofenceStatusPresenter geofenceStatus(@PathVariable Long batchId,
                                                  @PathVariable String shipmentNumber,
                                                  @RequestParam("lat") Double lat,
                                                  @RequestParam("lon") Double lon,
                                                  @RequestParam(required = false, name = "accuracy") Double accuracy) {
        return batchSndService.getGeofenceStatus(shipmentNumber, lat, lon);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/delivery_photos")
    @ResponseWrapper(rootName = "delivery_photo")
    @ResponseStatus(HttpStatus.CREATED)
    public DeliveryPhotoPresenter addDeliveryPhoto(@PathVariable Long batchId, @PathVariable String shipmentNumber, @NotNull @ValidImage MultipartFile attachment) throws Exception {
        DeliveryPhoto deliveryPhoto = batchSndService.addDeliveryPhoto(shipmentNumber, attachment);
        return deliveryPhotoMapper.deliveryPhotoToDeliveryPhotoPresenter(deliveryPhoto);

    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @DeleteMapping("/{batchId}/shipments/{shipmentNumber}/delivery_photos/{deliveryPhotoId}")
    @ResponseStatus(HttpStatus.ACCEPTED)
    public void deleteDeliveryPhoto(@PathVariable Long batchId, @PathVariable String shipmentNumber, @PathVariable Long deliveryPhotoId, HttpServletResponse response) {
        response.setHeader("Content-Type", "application/json; charset=UTF-8");
        batchSndService.deleteDeliveryPhoto(shipmentNumber, deliveryPhotoId);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @GetMapping("/{batchId}/shipments/{shipmentNumber}/delivery_photos")
    @ResponseWrapper(rootName = "delivery_photos")
    public List<DeliveryPhotoPresenter> deleteDeliveryPhoto(@PathVariable Long batchId, @PathVariable String shipmentNumber) {
       List<DeliveryPhoto> deliveryPhotos = batchSndService.getDeliveryPhotos(shipmentNumber);

        return deliveryPhotoMapper.deliveryPhotosToDeliveryPhotoPresenters(deliveryPhotos);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/pending")
    @ResponseWrapper(rootName = "batch")
    @Publish(WebhookType.PENDING_SHIPMENT)
    public ResponseEntity<BatchPresenter> pending(@PathVariable Long batchId, @PathVariable String shipmentNumber) {
        final Batch batch = batchSndService.pending(shipmentNumber);
        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/continue")
    @ResponseWrapper(rootName = "batch")
    public ResponseEntity<BatchPresenter> continueDelivery(@PathVariable Long batchId, @PathVariable String shipmentNumber) {
        final Batch batch = batchSndService.continueDelivery(shipmentNumber);
        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
    }
}
