package com.happyfresh.fulfillment.batch.controller;

import com.happyfresh.fulfillment.batch.form.*;
import com.happyfresh.fulfillment.batch.mapper.BatchMapper;
import com.happyfresh.fulfillment.batch.presenter.BatchPresenter;
import com.happyfresh.fulfillment.batch.presenter.BatchPresenterWithException;
import com.happyfresh.fulfillment.batch.service.BatchAvailabilityService;
import com.happyfresh.fulfillment.batch.service.BatchSndService;
import com.happyfresh.fulfillment.batch.service.ItemSndService;
import com.happyfresh.fulfillment.common.annotation.Publish;
import com.happyfresh.fulfillment.common.annotation.RequestWrapper;
import com.happyfresh.fulfillment.common.annotation.ResponseWrapper;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.exception.ApiError;
import com.happyfresh.fulfillment.common.exception.type.RedisLockTimeoutException;
import com.happyfresh.fulfillment.common.service.JedisLockService;
import com.happyfresh.fulfillment.common.util.UserInfoFetcher;
import com.happyfresh.fulfillment.enabler.service.StratoWebhookService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressService;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.GrabExpressDeliveryRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.shipment.mapper.ItemMapper;
import com.happyfresh.fulfillment.shipment.mapper.ShipmentMapper;
import com.happyfresh.fulfillment.shipment.presenter.ItemPresenter;
import com.happyfresh.fulfillment.shipment.presenter.ItemWithPriceDiscrepancyPresenter;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentItemPresenter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.StringTokenizer;

@RestController
@RequestMapping("/api/v2/batches")
@Validated
public class BatchV2Controller {

    @Autowired
    private BatchSndService batchSndService;

    @Autowired
    private BatchMapper batchMapper;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private GrabExpressDeliveryRepository grabExpressDeliveryRepository;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ShipmentMapper shipmentMapper;

    @Autowired
    private UserInfoFetcher userInfoFetcher;

    @Autowired
    private BatchAvailabilityService batchAvailabilityService;

    @Autowired
    private ItemSndService itemSndService;

    @Autowired
    private GrabExpressService grabExpressService;

    @Autowired
    private ItemMapper itemMapper;

    @Autowired
    private StratoWebhookService stratoWebhookService;

    private static final String BATCH_FINALIZE_LOCK_KEY = "finalize_batch_%s";

    @PreAuthorize("isAgentAuthenticated()")
    @GetMapping(value = "/available")
    @ResponseWrapper(rootName = "batches")
    public List<BatchPresenter> availableBatches(@RequestParam(required = false, name = "lat") String latString,
                                                 @RequestParam(required = false, name = "lon") String lonString) {
        User currentUser = userInfoFetcher.getAuthenticatedUser();

        Double lat = null;
        Double lon = null;
        if (!StringUtils.isBlank(latString) && !StringUtils.isBlank(lonString)) {
            try {
                lat = Double.parseDouble(latString);
                lon = Double.parseDouble(lonString);
            } catch (NumberFormatException e) {
                // default lat & lon is null
            }
        }
        return batchAvailabilityService.getAvailableBatches(currentUser, lat, lon);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/pay")
    @ResponseWrapper(rootName = "batch")
    @RequestWrapper(rootName = "receipts")
    @Publish(WebhookType.PAY_SHIPMENT)
    public ResponseEntity<BatchPresenter> pay(@PathVariable Long batchId, @PathVariable String shipmentNumber,
                                              @Valid @NotEmpty @RequestBody List<ReceiptV2Form> receipts) {
        final Batch batch = batchSndService.pay(batchId, shipmentNumber, receipts);

        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/capture")
    @ResponseWrapper(rootName = "batch")
    public ResponseEntity<BatchPresenter> capture(@PathVariable Long batchId, @PathVariable String shipmentNumber, HttpServletRequest request) throws Exception {
        String clientIPAddress = request.getRemoteAddr();
        String xForwardedForHeader = request.getHeader("X-FORWARDED-FOR");
        if (xForwardedForHeader != null) {
            clientIPAddress = new StringTokenizer(xForwardedForHeader, ",").nextToken().trim();
        }

        final Batch batch = batchSndService.captureV2(shipmentNumber, clientIPAddress);
        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/finalize")
    @RequestWrapper(rootName = "items")
    @Publish(WebhookType.FINALIZE_BATCH)
    public ResponseEntity<BatchPresenterWithException> finalizeBatch(@PathVariable Long batchId, @Valid @NotEmpty @RequestBody List<ItemFinalizeForm> forms) throws InterruptedException {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format(BATCH_FINALIZE_LOCK_KEY, batchId))) {
                List<ApiError> exceptions = batchSndService.finalizeBatch(batchId, forms);
                final Batch batch = batchRepository.fetchById(batchId);

                if (exceptions.isEmpty()) {
                    List<Shipment> shipments = shipmentRepository.findAllByBatch(batch);
                    for (Shipment shipment : shipments) {
                        Optional<Job> deliveryJob = shipment.getDeliveryJob();

                        if (deliveryJob.isPresent() && deliveryJob.get().getBatch().isGrabExpress()) {
                            Optional<GrabExpressDelivery> grabExpressDelivery = grabExpressDeliveryRepository.findAllByShipmentNumber(shipment.getNumber()).stream().max(Comparator.comparing(GrabExpressDelivery::getId));
                            if (grabExpressDelivery.isPresent()) {
                                grabExpressService.scheduleGrabBooking(grabExpressDelivery.get());
                            }
                        }
                    }
                }

                HttpStatus httpStatus = exceptions.isEmpty() ? HttpStatus.OK : HttpStatus.MULTI_STATUS;
                return new ResponseEntity<>(batchMapper.batchToBatchPresenterWithException(batch, exceptions), httpStatus);
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PutMapping("/{batchId}/shipments/{shipmentNumber}/finalize_delivery")
    @ResponseWrapper(rootName = "shipment")
    @RequestWrapper(rootName = "items")
    @Publish(WebhookType.MARK_ITEM_REJECTED)
    public ShipmentItemPresenter finalizeDelivery(@PathVariable Long batchId, @PathVariable String shipmentNumber,
                                                  @Valid @RequestBody List<ItemRejectForm> rejectForms) throws Exception {
        Shipment shipment = batchSndService.finalizeDelivery(shipmentNumber, rejectForms);
        if (shipment.getSlot().getStockLocation().isFulfilledByStrato())
            stratoWebhookService.sendItemRejectedWebhook(shipment);
        
        return shipmentMapper.shipmentToShipmentItemPresenter(shipment);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/items/{itemSku}/mark_product_mismatch")
    @ResponseWrapper(rootName = "item")
    @Publish(WebhookType.MARK_ITEM_MISMATCHED)
    public ItemPresenter markProductMismatchItem(@PathVariable Long batchId, @PathVariable String shipmentNumber, @PathVariable String itemSku,
                                                 @Valid MarkProductMismatchForm form) throws Exception {

        final User currentUser = userInfoFetcher.getAuthenticatedUser();
        final Item item = itemSndService.markProductMismatch(batchId, shipmentNumber, itemSku, form, currentUser.getId());
        return itemMapper.toItemPresenter(item);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/items/{itemSku}/mark_price_discrepancy")
    @ResponseWrapper(rootName = "item")
    @Publish(WebhookType.MARK_ITEM_PRICE_DISCREPANCY)
    public ItemWithPriceDiscrepancyPresenter markPriceDiscrepancyItem(@PathVariable Long batchId, @PathVariable String shipmentNumber, @PathVariable String itemSku,
                                                                      @Valid MarkItemPriceChangeForm form) throws Exception {
        final User currentUser = userInfoFetcher.getAuthenticatedUser();
        final Item item = itemSndService.markPriceDiscrepancy(batchId, shipmentNumber, itemSku, form, currentUser.getId());
        return itemMapper.toItemWithPriceDiscrepancyPresenter(item, currentUser);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/finish")
    @ResponseWrapper(rootName = "batch")
    @Publish(WebhookType.FINISH_SHIPMENT)
    public ResponseEntity<BatchPresenter> finish(@PathVariable Long batchId, @PathVariable String shipmentNumber,
                                                 @Validated DeliveryInfoV2Form deliveryInfoForm) throws Exception {
        final Batch batch = batchSndService.finishV2(shipmentNumber, deliveryInfoForm);
        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
    }
}
