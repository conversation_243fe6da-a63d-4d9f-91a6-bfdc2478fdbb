package com.happyfresh.fulfillment.batch.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.batch.mapper.BatchMapper;
import com.happyfresh.fulfillment.batch.model.ShoppingBatchEarliestDelivery;
import com.happyfresh.fulfillment.batch.model.StockLocationAvailableBatchCount;
import com.happyfresh.fulfillment.batch.model.StockLocationShoppingBatchEarliestDelivery;
import com.happyfresh.fulfillment.batch.presenter.BatchPresenter;
import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;
import com.happyfresh.fulfillment.common.service.JedisCacheService;
import com.happyfresh.fulfillment.common.util.ApplicationUtil;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.common.util.DistanceUtil;
import com.happyfresh.fulfillment.common.util.UserRoleUtil;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.SlotRepository;
import com.happyfresh.fulfillment.repository.StockLocationRepository;
import org.elasticsearch.common.geo.GeoPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Arrays.asList;

@Service
public class BatchAvailabilityService {

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private SlotRepository slotRepository;

    @Autowired
    private StockLocationRepository stockLocationRepository;

    @Autowired
    private JedisCacheService jedisCacheService;

    @Autowired
    private BatchMapper batchMapper;

    @Autowired
    private ObjectMapper mapper;

    public static final String BATCH_AVAILABLE_CACHE_KEY_PREFIX = "batches-available";

    public static final int BATCH_AVAILABLE_CACHE_EXPIRY_IN_SECONDS = 300;

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Transactional(readOnly = true)
    public List<BatchPresenter> getActiveBatchesWithStoreDistance(User user, GeoPoint fleetGeoPoint) {
        List<Batch> activeBatches = getActiveBatches(user);
        List<BatchPresenter> presenters = activeBatches.stream()
                .map(batchMapper::batchToAvailableBatchPresenter)
                .collect(Collectors.toList());

        return setAndSortByExpressAndStoreDistance(presenters, fleetGeoPoint);
    }

    @Transactional(readOnly = true)
    public List<Batch> getActiveBatches(User user) {
        List<Integer> batchTypesValue = null;
        if (UserRoleUtil.isShopper(user)) {
            batchTypesValue = new ArrayList<>(asList(Batch.Type.SHOPPING.getValue(), Batch.Type.ON_DEMAND_SHOPPING.getValue()));
        } else if (UserRoleUtil.isDriver(user)) {
            batchTypesValue = new ArrayList<>(asList(Batch.Type.DELIVERY.getValue(), Batch.Type.RANGER.getValue()));
        } else if (UserRoleUtil.isOnDemandRanger(user)) {
            batchTypesValue = new ArrayList<>(asList(Batch.Type.ON_DEMAND.getValue(), Batch.Type.ON_DEMAND_DELIVERY.getValue()));
        } else {
            return new ArrayList<>();
        }

        LocalDateTime oneWeekAgo = LocalDateTime.now().minusWeeks(1L);
        List<Batch> activeBatches = batchRepository.findAllActiveBatches(batchTypesValue, user.getId(), Job.getInactiveStates(), oneWeekAgo);

        if (UserRoleUtil.isShopper(user)) {
            // To prevent displaying Batch.Type.ON_DEMAND_SHOPPING
            // when there's active Batch.Type.SHOPPING
            final boolean activeShoppingBatchExists = activeBatches.stream().anyMatch(batch -> batch.getType().equals(Batch.Type.SHOPPING));
            if (activeShoppingBatchExists) {
                activeBatches = activeBatches.stream().filter(batch -> batch.getType().equals(Batch.Type.SHOPPING)).collect(Collectors.toList());
            } else {
                activeBatches = activeBatches.stream().filter(batch -> batch.getJobs().get(0).getShipment().getState().equals(Shipment.State.READY)).collect(Collectors.toList());
            }
        } else if (UserRoleUtil.isOnDemandRanger(user)) {
            activeBatches = activeBatches.stream()
                    .filter(batch -> batch.getJobs().stream()
                            .allMatch(job -> job.getShipment().getState().equals(Shipment.State.READY))
                    ).collect(Collectors.toList());

            activeBatches.sort((Batch batch1, Batch batch2) -> {
                Job.State batch1JobState = batch1.getJobs().get(0).getState();
                Job.State batch2JobState = batch2.getJobs().get(0).getState();
                if ((batch1JobState.equals(Job.State.INITIAL) && !batch2JobState.equals(Job.State.INITIAL))
                        || (batch2.getType().equals(Batch.Type.ON_DEMAND_DELIVERY) && batch1JobState.equals(Job.State.INITIAL) && batch2JobState.equals(Job.State.INITIAL)))
                    return 1;
                else if ((!batch1JobState.equals(Job.State.INITIAL) && batch2JobState.equals(Job.State.INITIAL))
                        || (batch1.getType().equals(Batch.Type.ON_DEMAND_DELIVERY) && batch1JobState.equals(Job.State.INITIAL) && batch2JobState.equals(Job.State.INITIAL)))
                    return -1;
                else
                    return batch1.getCreatedAt().compareTo(batch2.getCreatedAt());
            });
        }

        return activeBatches;
    }

    @Transactional(readOnly = true)
    public List<BatchPresenter> getAvailableBatches(User user, Double lat, Double lon) {
        List<BatchPresenter> availableBatches = new ArrayList<>();

        Agent agent = user.getAgent();
        StockLocation stockLocation = agent.getStockLocation();
        try {
            if (UserRoleUtil.isShopper(user)) {
                availableBatches = availableShoppingBatches(stockLocation);

            } else if (UserRoleUtil.isDriver(user)) {
                availableBatches = availableDeliveryBatches(user, stockLocation, lat, lon);
            }
        } catch (Exception e) {
            LOGGER.error(e.toString());
        }

        return availableBatches;
    }

    @Transactional(readOnly = true)
    public List<BatchPresenter> getAvailableBatchesV3(User user, GeoPoint fleetGeoPoint) {
        List<BatchPresenter> availableBatches = new ArrayList<>();

        Agent agent = user.getAgent();
        if (agent == null)
            throw new UnprocessableEntityException("User need to clock in first");

        StockLocation stockLocation = user.getAgent().getStockLocation();
        try {
            if (UserRoleUtil.isShopper(user)) {
                availableBatches = availableShoppingBatchesWithAutoAssignmentValidation(stockLocation, user);
            } else if (Boolean.TRUE.equals(UserRoleUtil.isDriver(user))) {
                availableBatches = checkAutoAssignmentFlag(user, stockLocation);
            }
        } catch (Exception e) {
            LOGGER.error(e.toString());
        }

        return setAndSortByExpressAndStoreDistance(availableBatches, fleetGeoPoint);
    }

    private List<BatchPresenter> checkAutoAssignmentFlag(User user, StockLocation stockLocation) throws IOException {
        Cluster cluster = stockLocation.getCluster();
        Tenant tenant = cluster.getTenant();
        if (user.isAllowedForAutoAssignment()
                && cluster.isEnableDriverAutoAssignment()
                && tenant.isAutoAssignmentHideAvailableJobs()
                && Boolean.FALSE.equals(UserRoleUtil.isBackOfficeDriver(user))
        ) {
            return Collections.emptyList();
        }

        return availableDeliveryBatchesV3Cached(user, stockLocation);
    }

    private List<BatchPresenter> setAndSortByExpressAndStoreDistance(List<BatchPresenter> presenters, GeoPoint fleetGeoPoint) {
        List<BatchPresenter> result = new ArrayList<>();

        for (BatchPresenter presenter : presenters) {
            if (presenter.getStoreGeoPoint() != null && fleetGeoPoint != null) {
                Double storeDistance = DistanceUtil.airDistanceInKM(presenter.getStoreGeoPoint(), fleetGeoPoint);
                presenter.setStoreDistance(storeDistance);
            }
            result.add(presenter);
        }

        return result.stream()
                .sorted(Comparator
                        .comparing((BatchPresenter b) -> b.getShipments().get(0).isExpressDelivery())
                        .thenComparing(b -> b.getStoreDistance(), Comparator.nullsLast(Comparator.naturalOrder()))
                ).collect(Collectors.toList());

    }

    public List<StockLocationAvailableBatchCount> getAvailableShoppingBatchesCount(Country country) {
        List<StockLocation> stockLocations = stockLocationRepository.findAllWithPendingJobNotifEnabled(country.getId());
        Map<Long, StockLocation> stockLocationById = stockLocations.stream()
                .collect(Collectors.toMap(StockLocation::getId, Function.identity()));
        List<Long> stockLocationIds = new ArrayList<>(stockLocationById.keySet());
        if (stockLocationIds.isEmpty())
            return new ArrayList<>();

        StockLocation stockLocationInCountry = stockLocations.get(0);
        LocalDateTime currentDayEnd = DateTimeUtil.getTodayEndOfTheDayInUTC(stockLocationInCountry);

        List<StockLocationShoppingBatchEarliestDelivery> sbEarliestDeliveries = getShoppingBatchEarliestDeliveries(stockLocationIds, currentDayEnd);
        Map<Long, List<StockLocationShoppingBatchEarliestDelivery>> mapBySlId = sbEarliestDeliveries.stream()
                .collect(Collectors.groupingBy(StockLocationShoppingBatchEarliestDelivery::getStockLocationId));
        stockLocationIds = new ArrayList<>(mapBySlId.keySet());

        List<StockLocationAvailableBatchCount> availableBatchCounts = new ArrayList<>();
        for (Long slId : stockLocationIds) {
            List<StockLocationShoppingBatchEarliestDelivery> shoppingBatchesByStockLocation = mapBySlId.get(slId);
            StockLocation sl = stockLocationById.get(slId);
            List<StockLocationShoppingBatchEarliestDelivery> visibleBatches = new ArrayList<>();
            for (StockLocationShoppingBatchEarliestDelivery sb : shoppingBatchesByStockLocation) {
                LocalDateTime earliestDeliveryStart = DateTimeUtil.getMinDatesBetween(sb.getEarliestDeliveryBatchStartTime(), sb.getSlotStartTime());
                boolean visible = shoppingBatchVisible(
                        sl,
                        sb.getShoppingBatchStartTime(),
                        sb.getShoppingBatchEndTime(),
                        earliestDeliveryStart);
                if (visible)
                    visibleBatches.add(sb);
            }
            StockLocationAvailableBatchCount abc = new StockLocationAvailableBatchCount(
                    sl.getId(),
                    sl.getName(),
                    visibleBatches.size());
            availableBatchCounts.add(abc);
        }
        return availableBatchCounts;
    }

    private List<StockLocationShoppingBatchEarliestDelivery> getShoppingBatchEarliestDeliveries(List<Long> stockLocationIds, LocalDateTime currentDayEnd) {
        List<StockLocationShoppingBatchEarliestDelivery> result = new ArrayList<>();

        List<Object[]> sqlObjects = batchRepository.findAllShoppingBatchIdsWithEarliestDeliveryTime(stockLocationIds, currentDayEnd);
        for (Object[] obj : sqlObjects) {
            try {
                Long stockLocationId = ((BigInteger) obj[0]).longValue();
                Long shoppingBatchId = ((BigInteger) obj[1]).longValue();
                Timestamp earliestDeliveryTime = (Timestamp) obj[2];
                Timestamp shoppingBatchStart = (Timestamp) obj[3];
                Timestamp shoppingBatchEnd = (Timestamp) obj[4];
                Timestamp slotStartTime = (Timestamp) obj[5];
                StockLocationShoppingBatchEarliestDelivery e = new StockLocationShoppingBatchEarliestDelivery(
                        stockLocationId,
                        shoppingBatchId,
                        earliestDeliveryTime.toLocalDateTime(),
                        shoppingBatchStart.toLocalDateTime(),
                        shoppingBatchEnd.toLocalDateTime(),
                        slotStartTime.toLocalDateTime());

                result.add(e);
            } catch (Exception e) {
                LOGGER.error("[ShoppingBatchEarliestDelivery] Failed to parse query response: " + e.getMessage(), e);
            }
        }

        return result;
    }

    public List<Batch> getAvailableDeliveryBatchesInCluster(Cluster cluster) {
        StockLocation sampleStockLocation = cluster.getStockLocations().get(0);
        List<Long> slIds = cluster.getStockLocations().stream()
                .map(StockLocation::getId)
                .collect(Collectors.toList());

        LocalDateTime minStartTime = LocalDateTime.now().minusWeeks(1L);
        LocalDateTime maxStartTime = maxAvailableShoppingBatchStartTime(sampleStockLocation);

        List<Batch> regularDeliveries = batchRepository.findAllAvailableRegularDeliveryBatchesWithNoInitialShoppingJob(slIds, maxStartTime, minStartTime);
        List<Batch> rangerDeliveries = getAvailableRangerDeliveryBatchesInCluster(slIds, cluster);


        return Stream.concat(regularDeliveries.stream(), rangerDeliveries.stream())
                .collect(Collectors.toList());
    }

    private List<Batch> getAvailableRangerDeliveryBatchesInCluster(List<Long> stockLocationIds, Cluster cluster) {
        StockLocation stockLocationInCountry = cluster.getStockLocations().get(0);

        LocalDateTime minStartTime = LocalDateTime.now().minusDays(1L);
        LocalDateTime maxStartTime = DateTimeUtil.getTodayEndOfTheDayInUTC(stockLocationInCountry);
        List<Batch> rangerDeliveries = batchRepository.findAllAvailableRangerDeliveryBatches(stockLocationIds, maxStartTime, minStartTime);

        return getFilteredAvailableRangerDeliveryBatches(rangerDeliveries);

    }

    private List<Batch> getFilteredAvailableRangerDeliveryBatches(List<Batch> rangerDeliveries) {
        LocalDateTime now = LocalDateTime.now();
        return rangerDeliveries.stream().filter(
                batch -> {
                    Slot slot = batch.getJobs().get(0).getShipment().getSlot();
                    int shoppingNotifiedOffset = batch.getStockLocation().getShoppingBatchNotifiedOffset();
                    LocalDateTime minDateTime = DateTimeUtil.getMinDatesBetween(batch.getStartTime(), slot.getStartTime());

                    return now.isAfter(minDateTime.minusMinutes(shoppingNotifiedOffset))
                            || now.isEqual(minDateTime.minusMinutes(shoppingNotifiedOffset));
                }
        ).collect(Collectors.toList());
    }

    private List<BatchPresenter> availableShoppingBatchesWithAutoAssignmentValidation(StockLocation stockLocation, User user) throws IOException {
        Tenant tenant = stockLocation.getTenant();
        if (stockLocation.isEnableShopperAutoAssignment()
                && tenant.isAutoAssignmentHideAvailableJobs()
                && user.isAllowedForAutoAssignment()
        ) {
            return Collections.emptyList();
        }

        return availableShoppingBatches(stockLocation);
    }

    private List<BatchPresenter> availableShoppingBatches(StockLocation stockLocation) throws IOException {
        final String key = getShoppingBatchesAvailableKey(stockLocation.getId());
        List<BatchPresenter> availableShoppingBatchesPresenter;
        Optional<String> optionalShoppingBatches = jedisCacheService.get(key);
        boolean enableCache = !isLongerDeliveryStore(stockLocation);
        if (optionalShoppingBatches.isPresent() && enableCache) {
            String cachedShoppingBatches = optionalShoppingBatches.get();
            availableShoppingBatchesPresenter = mapper.readValue(cachedShoppingBatches, new TypeReference<List<BatchPresenter>>() {
            });
        } else {
            availableShoppingBatchesPresenter = getDdsAvailableShoppingBatches(stockLocation);
            if (enableCache) {
                // Save to redis
                String cachedShoppingBatches = mapper.writeValueAsString(availableShoppingBatchesPresenter);
                jedisCacheService.setWithExpiry(key, cachedShoppingBatches, BATCH_AVAILABLE_CACHE_EXPIRY_IN_SECONDS);
            }
        }

        return availableShoppingBatchesPresenter;
    }

    private List<BatchPresenter> getDdsAvailableShoppingBatches(StockLocation stockLocation) {
        boolean enableEarliestVisibility = stockLocation.isEnableEarliestShoppingBatchVisibility();
        if (enableEarliestVisibility)
            return getEarliestVisibleDdsShoppingBatches(stockLocation);
        return getDdsShoppingBatches(stockLocation);
    }

    private List<BatchPresenter> getDdsShoppingBatches(StockLocation stockLocation) {
        LocalDateTime maxBatchStartTime = maxAvailableShoppingBatchStartTime(stockLocation);
        LocalDateTime minimumStartTime = LocalDateTime.now().minusWeeks(1L);
        List<Batch> batches = batchRepository.findAllAvailableShoppingBatches(
                Collections.singletonList(stockLocation.getId()),
                maxBatchStartTime,
                minimumStartTime);
        return batches.stream()
                .map(batchMapper::batchToBatchPresenter)
                .collect(Collectors.toList());
    }

    private List<BatchPresenter> getEarliestVisibleDdsShoppingBatches(StockLocation stockLocation) {
        LocalDateTime minStartTime = LocalDateTime.now().minusWeeks(1L);
        LocalDateTime currentDayEnd = DateTimeUtil.getTodayEndOfTheDayInUTC(stockLocation);

        List<ShoppingBatchEarliestDelivery> sbEarliestDeliveries = batchRepository.findAllShoppingBatchEarliestDelivery(
                stockLocation.getId(),
                minStartTime,
                currentDayEnd);
        if (sbEarliestDeliveries.isEmpty())
            return new ArrayList<>();

        List<Long> visibleShoppingBatchesIds = new ArrayList<>();

        for (ShoppingBatchEarliestDelivery sbe : sbEarliestDeliveries) {
            LocalDateTime earliestDeliveryStart = DateTimeUtil.getMinDatesBetween(sbe.getEarliestDeliveryBatchStartTime(), sbe.getSlotStartTime());
            boolean visible = shoppingBatchVisible(
                    stockLocation,
                    sbe.getBatchStartTime(),
                    sbe.getBatchEndTime(),
                    earliestDeliveryStart);
            if (visible)
                visibleShoppingBatchesIds.add(sbe.getShoppingBatchId());
        }

        if (visibleShoppingBatchesIds.isEmpty())
            return new ArrayList<>();

        List<Batch> visibleShoppingBatches = batchRepository.findAllById(visibleShoppingBatchesIds);

        return visibleShoppingBatches.stream()
                .map(batchMapper::batchToBatchPresenter)
                .collect(Collectors.toList());
    }

    private boolean shoppingBatchVisible(
            StockLocation stockLocation,
            LocalDateTime shoppingBatchStart,
            LocalDateTime shoppingBatchEnd,
            LocalDateTime earliestDeliveryStart
    ) {
        LocalDateTime now = LocalDateTime.now();
        long shoppingBatchDuration = ChronoUnit.MINUTES.between(shoppingBatchStart, shoppingBatchEnd);

        LocalDateTime threshold = now
                .plusMinutes(shoppingBatchDuration)
                .plusMinutes(stockLocation.getShoppingBatchNotifiedOffset())
                .plusMinutes((int) stockLocation.getShopperHandoverToDriverTime());

        return threshold.isAfter(earliestDeliveryStart)
                || threshold.isEqual(earliestDeliveryStart);
    }

    private List<Batch> findEarliestShoppingBatchesGroupedByVehicle(StockLocation stockLocation, LocalDateTime maxBatchStartTime, LocalDateTime minimumStartTime) {
        List<Batch> allBatches = batchRepository.findAllAvailableShoppingBatches(
                Collections.singletonList(stockLocation.getId()), maxBatchStartTime, minimumStartTime);
        Map<String, List<Batch>> vehicleBatches = groupBatchesByShiftVehicle(allBatches);

        return vehicleBatches.entrySet().stream()
                .map(this::getEarliestBatchOnShiftAndVehicle)
                .sorted(Comparator.comparing(Batch::getStartTime))
                .collect(Collectors.toList());
    }

    private List<Batch> findEarliestDeliveryBatchesGroupedByVehicle(List<Batch> batches, Double lat, Double lon) {
        List<Batch> tplBatches = batches.stream()
                .filter(batch -> batch.getDeliveryType().equals(Batch.DeliveryType.TPL))
                .collect(Collectors.toList());

        batches.removeAll(tplBatches); // normal batches

        Map<String, List<Batch>> batchesGroupedByShiftVehicle = groupBatchesByShiftVehicle(batches);
        // Filter 1 earliest and then nearest on each group
        List<Batch> filteredBatches = new ArrayList<>();
        for (Map.Entry<String, List<Batch>> entry : batchesGroupedByShiftVehicle.entrySet()) {
            if (entry.getValue().isEmpty()) continue;
            // if more than 1 batch have exactly the same start_time,
            // sort by start_time and then location.
            List<Batch> sortedBatches = sortByLocationAndStartTime(entry.getValue(), lat, lon, true);
            filteredBatches.add(sortedBatches.get(0));
        }

        // BackOffice can see TPL batches
        if (!tplBatches.isEmpty()) {
            filteredBatches.addAll(tplBatches);
            return sortByLocationAndStartTime(filteredBatches, null, null, false);
        }

        return sortByLocationAndStartTime(filteredBatches, lat, lon, false);
    }

    private List<Batch> sortByLocationAndStartTime(List<Batch> batches, Double agentLat, Double agentLon, boolean sortByStartTimeFirst) {
        if (agentLat == null || agentLon == null) {
            batches.sort(Comparator.comparing(Batch::getStartTime));
            return batches;
        }

        boolean shouldSortByLocation = true;
        final GeoPoint agentGeoPoint = new GeoPoint(agentLat, agentLon);
        for (Batch batch : batches) {
            StockLocation stockLocation = batch.getStockLocation();
            GeoPoint slGeoPoint = new GeoPoint(stockLocation.getLat(), stockLocation.getLon());
            Double distance = DistanceUtil.distance(agentGeoPoint, slGeoPoint); // in meters

            if (distance > DistanceUtil.ACCURATE_DISTANCE_THRESHOLD_IN_METERS) {
                shouldSortByLocation = false;
                break;
            }
            batch.setDriverDistanceToStockLocation(distance);
        }

        if (shouldSortByLocation) {
            if (sortByStartTimeFirst) {
                batches.sort(Comparator.comparing(Batch::getStartTime).thenComparing(Batch::getDriverDistanceToStockLocation));
            } else {
                batches.sort(Comparator.comparing(Batch::getDriverDistanceToStockLocation).thenComparing(Batch::getStartTime));
            }
        } else {
            batches.sort(Comparator.comparing(Batch::getStartTime));
        }

        return batches;
    }

    private Batch getEarliestBatchOnShiftAndVehicle(Map.Entry<String, List<Batch>> batchesByShiftAndVehicle) {
        return batchesByShiftAndVehicle.getValue().stream()
                .min(Comparator.comparing(Batch::getStartTime))
                .orElse(null);
    }

    private Map<String, List<Batch>> groupBatchesByShiftVehicle(List<Batch> batches) {
        // key: "VEHICLE-SHIFT_ID"
        return batches.stream().collect(
                Collectors.groupingBy(batch -> {
                    String vehicle = batch.getVehicle() == null ? "0" : batch.getVehicle().toString();
                    String shiftId = batch.getShift() == null ? "0" : batch.getShift().getId().toString();
                    return vehicle + "-" + shiftId;
                })
        );
    }

    private boolean isLongerDeliveryStore(StockLocation stockLocation) {
        return stockLocation.getCluster().getSlotType().equals(Slot.Type.LONGER_DELIVERY);
    }

    private List<BatchPresenter> availableDeliveryBatches(User user, StockLocation stockLocation, Double lat, Double lon) throws IOException {
        List<Batch> availableDeliveryBatches;
        List<BatchPresenter> availableDeliveryBatchesPresenter;
        Long clusterId = stockLocation.getCluster().getId();
        final String key = getDeliveryBatchesAvailableKey(clusterId);
        boolean enableCache = !isLongerDeliveryStore(stockLocation) && !UserRoleUtil.isBackOfficeDriver(user);
        Optional<String> optionalDeliveryBatches = jedisCacheService.get(key);
        if (optionalDeliveryBatches.isPresent() && enableCache) {
            String cachedDeliveryBatches = optionalDeliveryBatches.get();
            availableDeliveryBatchesPresenter = mapper.readValue(cachedDeliveryBatches, new TypeReference<List<BatchPresenter>>() {
            });
        } else {
            // Fetch data
            List<StockLocation> stockLocations = stockLocationRepository.findByClusterId(clusterId);
            List<Long> stockLocationIds = stockLocations.stream().map(StockLocation::getId).collect(Collectors.toList());

            LocalDateTime maxBatchStartTime = maxAvailableDeliveryBatchStartTime(stockLocations);
            LocalDateTime minimumStartTime = LocalDateTime.now().minusWeeks(1L);

            LocalDateTime abandonNotifiedBatchTime = LocalDateTime.now().minusMinutes(ApplicationUtil.MINUTE_UNTIL_JOB_ABANDONED);

            if (UserRoleUtil.isBackOfficeDriver(user)) {
                availableDeliveryBatches = batchRepository.findAllAvailableDeliveryBatchesForBackOffice(stockLocationIds, maxBatchStartTime, minimumStartTime, user.getId(), abandonNotifiedBatchTime);
            } else {
                availableDeliveryBatches = batchRepository.findAllAvailableDeliveryBatches(stockLocationIds, maxBatchStartTime, minimumStartTime, user.getId(), abandonNotifiedBatchTime);
            }

            if (isLongerDeliveryStore(stockLocation)) {
                availableDeliveryBatches = findEarliestDeliveryBatchesGroupedByVehicle(availableDeliveryBatches, lat, lon);
            }

            List<Batch> toRemoveBatches = new ArrayList<>();
            for (Batch batch : availableDeliveryBatches) {
                LocalDateTime batchStartDateTime = batch.getStartTime();
                int offsetTimeInMinutes = batch.getStockLocation().getDeliveryBatchNotifiedOffset();
                LocalDateTime batchDisplayTime = batchStartDateTime.minusMinutes(offsetTimeInMinutes);
                if (batchDisplayTime.isAfter(LocalDateTime.now())) {
                    toRemoveBatches.add(batch);
                }
            }
            availableDeliveryBatches.removeAll(toRemoveBatches);

            availableDeliveryBatchesPresenter = availableDeliveryBatches.stream().map(batchMapper::batchToBatchPresenter).collect(Collectors.toList());

            if (enableCache) {
                // Save to redis
                String cachedDeliveryBatches = mapper.writeValueAsString(availableDeliveryBatchesPresenter);
                jedisCacheService.setWithExpiry(key, cachedDeliveryBatches, BATCH_AVAILABLE_CACHE_EXPIRY_IN_SECONDS);
            }
        }

        return availableDeliveryBatchesPresenter;
    }

    private List<BatchPresenter> availableDeliveryBatchesV3Cached(User user, StockLocation stockLocation) throws IOException {
        List<BatchPresenter> availableDeliveryBatchesPresenter;

        Long clusterId = stockLocation.getCluster().getId();
        final String key = getDeliveryBatchesAvailableV3Key(clusterId);
        boolean enableCache = !isLongerDeliveryStore(stockLocation) && !UserRoleUtil.isBackOfficeDriver(user);
        Optional<String> optionalCacheResult = jedisCacheService.get(key);

        if (enableCache && optionalCacheResult.isPresent()) {
            String batches = optionalCacheResult.get();
            availableDeliveryBatchesPresenter = mapper.readValue(batches, new TypeReference<List<BatchPresenter>>() {});
        } else {
            availableDeliveryBatchesPresenter = availableDeliveryBatchesV3(user, stockLocation);

            if (enableCache) {
                String cachedDeliveryBatches = mapper.writeValueAsString(availableDeliveryBatchesPresenter);
                jedisCacheService.setWithExpiry(key, cachedDeliveryBatches, BATCH_AVAILABLE_CACHE_EXPIRY_IN_SECONDS);
            }
        }

        return availableDeliveryBatchesPresenter;
    }

    private List<BatchPresenter> availableDeliveryBatchesV3(User user, StockLocation stockLocation) {
        Long clusterId = stockLocation.getCluster().getId();
        List<StockLocation> stockLocations = stockLocationRepository.findByClusterId(clusterId);
        List<Long> stockLocationIds = stockLocations.stream().map(StockLocation::getId).collect(Collectors.toList());

        LocalDateTime minStartTime = LocalDateTime.now().minusWeeks(1L);
        LocalDateTime maxBatchStartTime = maxAvailableShoppingBatchStartTime(stockLocation);

        List<Batch> availableDeliveryBatches;
        if (UserRoleUtil.isBackOfficeDriver(user)) {
            availableDeliveryBatches = batchRepository.findAvailableTplDeliveryBatchesWithNoInitialShoppingJob(stockLocationIds, maxBatchStartTime, minStartTime);
        } else {
            availableDeliveryBatches = getEarliestVisibleDriverBatches(stockLocation, stockLocationIds);
        }

        return availableDeliveryBatches.stream()
                .map(batchMapper::batchToAvailableBatchPresenter)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<BatchPresenter> getUpcomingBatches(User user, GeoPoint fleetGeoPoint) {
        List<BatchPresenter> upcomingBatches = new ArrayList<>();

        Agent agent = user.getAgent();
        if (agent == null)
            throw new UnprocessableEntityException("User need to clock in first");

        StockLocation stockLocation = agent.getStockLocation();

        if (UserRoleUtil.isShopper(user)) {
            upcomingBatches = upcomingShoppingBatches(stockLocation);
        } else if (UserRoleUtil.isDriver(user)) {
            upcomingBatches = upcomingDeliveryBatches(stockLocation, user);
        }

        return setAndSortByExpressAndStoreDistance(upcomingBatches, fleetGeoPoint);
    }

    private List<BatchPresenter> upcomingShoppingBatches(StockLocation stockLocation) {
        LocalDateTime minStartTime = LocalDateTime.now().minusWeeks(1L);
        LocalDateTime maxStartTime = maxAvailableShoppingBatchStartTime(stockLocation);

        List<Long> stockLocationIds = Collections.singletonList(stockLocation.getId());
        return batchRepository.findUpcomingShoppingBatches(stockLocationIds, maxStartTime, minStartTime)
                .stream()
                .map(batchMapper::batchToUpcomingBatchPresenter)
                .collect(Collectors.toList());
    }

    private List<BatchPresenter> upcomingDeliveryBatches(StockLocation stockLocation, User user) {
        Long clusterId = stockLocation.getCluster().getId();
        List<StockLocation> stockLocations = stockLocationRepository.findByClusterId(clusterId);
        List<Long> stockLocationIds = stockLocations.stream()
                .map(StockLocation::getId)
                .collect(Collectors.toList());
        if (stockLocationIds.isEmpty())
            return new ArrayList<>();

        LocalDateTime minStartTime = LocalDateTime.now().minusWeeks(1L);
        LocalDateTime maxStartTime = maxAvailableShoppingBatchStartTime(stockLocation);

        List<Batch> upcomingBatches;
        if (UserRoleUtil.isBackOfficeDriver(user)) {
            upcomingBatches = batchRepository.findUpcomingTplBatches(stockLocationIds, minStartTime, maxStartTime);
        } else {
            upcomingBatches = batchRepository.findUpcomingDeliveryBatches(stockLocationIds, minStartTime, maxStartTime);
        }

        return upcomingBatches.stream()
                .map(batchMapper::batchToUpcomingBatchPresenter)
                .collect(Collectors.toList());
    }

    private LocalDateTime maxAvailableShoppingBatchStartTime(StockLocation stockLocation) {
        int offsetTimeInMinutes = stockLocation.getShoppingBatchNotifiedOffset();

        return LocalDateTime.now().plusMinutes(offsetTimeInMinutes);
    }

    private LocalDateTime maxAvailableDeliveryBatchStartTime(List<StockLocation> stockLocationsInCluster) {
        Optional<StockLocation> stockLocationWithMaximumOffset = stockLocationsInCluster.stream().max(Comparator.comparing(StockLocation::getDeliveryBatchNotifiedOffset));
        int offsetTimeInMinutes = stockLocationWithMaximumOffset.map(StockLocation::getDeliveryBatchNotifiedOffset).orElse(0);

        return LocalDateTime.now().plusMinutes(offsetTimeInMinutes);
    }

    private String getShoppingBatchesAvailableKey(Long stockLocationId) {
        return BATCH_AVAILABLE_CACHE_KEY_PREFIX + ":stock_location:" + stockLocationId;
    }

    private String getDeliveryBatchesAvailableKey(Long clusterId) {
        return BATCH_AVAILABLE_CACHE_KEY_PREFIX + ":cluster:" + clusterId;
    }

    private String getDeliveryBatchesAvailableV3Key(Long clusterId) {
        return BATCH_AVAILABLE_CACHE_KEY_PREFIX + "-v3:cluster:" + clusterId;
    }

    public void invalidateShoppingBatchesAvailable(StockLocation stockLocation) {
        final String key = getShoppingBatchesAvailableKey(stockLocation.getId());
        jedisCacheService.del(key);
    }

    public void invalidateDeliveryBatchesAvailable(Cluster cluster) {
        final String key = getDeliveryBatchesAvailableKey(cluster.getId());
        jedisCacheService.del(key);
    }

    public void invalidateDeliveryBatchesAvailableV3(Cluster cluster) {
        final String key = getDeliveryBatchesAvailableV3Key(cluster.getId());
        jedisCacheService.del(key);
    }

    @Transactional(readOnly = true)
    public void invalidateShoppingAndDeliveryBatchesAvailable(StockLocation stockLocation) {
        invalidateShoppingBatchesAvailable(stockLocation);
        invalidateDeliveryBatchesAvailable(stockLocation.getCluster());
        invalidateDeliveryBatchesAvailableV3(stockLocation.getCluster());
    }

    private List<Batch> getEarliestVisibleDriverBatches(StockLocation stockLocation, List<Long> stockLocationIds){
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime minStartTime = now.minusWeeks(1L);
        LocalDateTime maxBatchStartTime = maxAvailableShoppingBatchStartTime(stockLocation);
        LocalDateTime currentDayEnd = DateTimeUtil.getTodayEndOfTheDayInUTC(stockLocation);

        List<Batch> driverAvailableDeliveryBatches = batchRepository.findAllAvailableRegularDeliveryBatchesWithNoInitialShoppingJob(
                stockLocationIds, maxBatchStartTime, minStartTime);

        List<Batch> rangerAvailableDeliveryBatches = batchRepository.findAllAvailableRangerDeliveryBatches(
                stockLocationIds, currentDayEnd, minStartTime);

        if(driverAvailableDeliveryBatches.isEmpty() && rangerAvailableDeliveryBatches.isEmpty()){
            return new ArrayList<>();
        }

        // ranger batch filtered
        rangerAvailableDeliveryBatches = rangerAvailableDeliveryBatches.stream().filter(
                batch -> rangerBatchVisible(batch, stockLocation, now)
        ).collect(Collectors.toList());

        return Stream.concat(driverAvailableDeliveryBatches.stream(), rangerAvailableDeliveryBatches.stream()).collect(Collectors.toList());
    }

    private boolean rangerBatchVisible(Batch b, StockLocation stockLocation, LocalDateTime now){
        LocalDateTime earliestDeliveryStart = DateTimeUtil.getMinDatesBetween(
                        b.getStartTime(),b.getJobs().get(0).getShipment()
                                .getSlot().getStartTime())
                .minusMinutes(stockLocation.getShoppingBatchNotifiedOffset());
        return now.isAfter(earliestDeliveryStart) || now.equals(earliestDeliveryStart);
    }

    public List<Batch> getUnassignedShoppingBatchesByNLimit(StockLocation stockLocation, int queryLimit) {
        List<ShoppingBatchEarliestDelivery> sbEarliestDeliveries = new ArrayList<>();

        LocalDateTime minStartTime = LocalDateTime.now().minusWeeks(1L);
        LocalDateTime currentDayEnd = DateTimeUtil.getTodayEndOfTheDayInUTC(stockLocation);

        List<Object[]> result = batchRepository.findAllAvailableUnassignedShoppingBatchesByNLimit(
                stockLocation.getId(), minStartTime, currentDayEnd, queryLimit);
        if (result.isEmpty())
            return new ArrayList<>();

        parseResult(sbEarliestDeliveries, result);

        List<Long> visibleShoppingBatchesIds = new ArrayList<>();
        getVisibleShoppingBatches(stockLocation, sbEarliestDeliveries, visibleShoppingBatchesIds);

        if (visibleShoppingBatchesIds.isEmpty())
            return new ArrayList<>();

        return batchRepository.findAllById(visibleShoppingBatchesIds)
                .stream().sorted(Comparator.comparing(Batch::getStartTime))
                .collect(Collectors.toList());
    }

    private void parseResult(List<ShoppingBatchEarliestDelivery> sbEarliestDeliveries, List<Object[]> result) {
        for (Object[] sbEarliestDelivery : result) {
            try {
                Long shoppingBatchId = ((BigInteger) sbEarliestDelivery[0]).longValue();
                Timestamp shoppingBatchStart = (Timestamp) sbEarliestDelivery[1];
                Timestamp shoppingBatchEnd = (Timestamp) sbEarliestDelivery[2];
                Timestamp earliestDeliveryTime = (Timestamp) sbEarliestDelivery[3];
                Timestamp slotStartTime = (Timestamp) sbEarliestDelivery[4];
                ShoppingBatchEarliestDelivery e = new ShoppingBatchEarliestDelivery(
                        shoppingBatchId,
                        shoppingBatchStart.toLocalDateTime(),
                        shoppingBatchEnd.toLocalDateTime(),
                        earliestDeliveryTime.toLocalDateTime(),
                        slotStartTime.toLocalDateTime());

                sbEarliestDeliveries.add(e);
            } catch (Exception e) {
                LOGGER.error("[ShoppingBatchEarliestDelivery] Failed to parse query response: " + e.getMessage(), e);
            }
        }
    }

    private void getVisibleShoppingBatches(StockLocation stockLocation, List<ShoppingBatchEarliestDelivery> sbEarliestDeliveries, List<Long> visibleShoppingBatchesIds) {
        sbEarliestDeliveries.forEach( shoppingBatch -> {
            LocalDateTime earliestDeliveryStart = DateTimeUtil.getMinDatesBetween(shoppingBatch.getEarliestDeliveryBatchStartTime(), shoppingBatch.getSlotStartTime());
            boolean visible = shoppingBatchVisible(
                    stockLocation,
                    shoppingBatch.getBatchStartTime(),
                    shoppingBatch.getBatchEndTime(),
                    earliestDeliveryStart);
            if (visible)
                visibleShoppingBatchesIds.add(shoppingBatch.getShoppingBatchId());
        });
    }

    public List<Batch> getUnassignedDriverBatchesByShiftId(StockLocation stockLocation, Long shiftId){
        LocalDateTime minStartTime = LocalDateTime.now().minusDays(1L);
        LocalDateTime maxStartTime = DateTimeUtil.getTodayEndOfTheDayInUTC(stockLocation);

        List<Batch> results = batchRepository.findAllAvailableUnassignedDriverBatchesByShiftId(minStartTime, maxStartTime, shiftId);
        if (results.isEmpty())
            return new ArrayList<>();

        return getFilteredAvailableRangerDeliveryBatches(results);
    }

    public List<Batch> getUnassignedDriverBatchesByStockLocations(List<StockLocation> stockLocations){
        LocalDateTime minStartTime = LocalDateTime.now().minusDays(1L);
        LocalDateTime maxStartTime = DateTimeUtil.getTodayEndOfTheDayInUTC(stockLocations.get(0));

        List<Batch> results = batchRepository.findAllAvailableUnassignedDriverBatchesByStockLocationIds(minStartTime, maxStartTime, stockLocations.stream().map(StockLocation::getId).collect(Collectors.toList()));
        if (results.isEmpty())
            return new ArrayList<>();

        return getFilteredAvailableRangerDeliveryBatches(results);
    }
}
