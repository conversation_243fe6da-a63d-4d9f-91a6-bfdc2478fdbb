package com.happyfresh.fulfillment.batch.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.admin.form.AdminShipmentStatusUpdateForm;
import com.happyfresh.fulfillment.batch.form.*;
import com.happyfresh.fulfillment.batch.mapper.BatchMapper;
import com.happyfresh.fulfillment.batch.mapper.ReceiptMapper;
import com.happyfresh.fulfillment.batch.presenter.BatchPresenter;
import com.happyfresh.fulfillment.batch.presenter.UncollectedPaymentDataEvent;
import com.happyfresh.fulfillment.batch.presenter.UncollectedPaymentEvent;
import com.happyfresh.fulfillment.batch.presenter.tracking.SwitchJobPayload;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.exception.ApiError;
import com.happyfresh.fulfillment.common.exception.BadRequestException;
import com.happyfresh.fulfillment.common.exception.ResourceNotFoundException;
import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;
import com.happyfresh.fulfillment.common.exception.type.*;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaMessage;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaTopicConfig;
import com.happyfresh.fulfillment.common.service.BrazeAPIService;
import com.happyfresh.fulfillment.common.service.FleetTrackingService;
import com.happyfresh.fulfillment.common.service.WebhookPublisherService;
import com.happyfresh.fulfillment.common.service.radar.RadarApiService;
import com.happyfresh.fulfillment.common.tracking.LongerDeliverySlotOptimizationEventTracker;
import com.happyfresh.fulfillment.common.util.CurrencyUtil;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.common.util.DistanceUtil;
import com.happyfresh.fulfillment.common.util.UserRoleUtil;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.gosend.service.GosendAsyncService;
import com.happyfresh.fulfillment.gosend.service.GosendService;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressService;
import com.happyfresh.fulfillment.lalamove.service.LalamoveService;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.shipment.form.ItemForm;
import com.happyfresh.fulfillment.shipment.mapper.DeliveryInfoMapper;
import com.happyfresh.fulfillment.shipment.mapper.ShipmentMapper;
import com.happyfresh.fulfillment.shipment.model.PaymentOption;
import com.happyfresh.fulfillment.shipment.service.CatalogService;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import com.happyfresh.fulfillment.shipment.service.PaymentService;
import com.happyfresh.fulfillment.shipment.service.ShipmentService;
import com.happyfresh.fulfillment.slot.presenter.GeofenceStatusPresenter;
import com.happyfresh.fulfillment.slot.presenter.SlotOptimizationEvent;
import com.happyfresh.fulfillment.slot.service.BatchRecalculateService;
import com.happyfresh.fulfillment.slot.service.PublishSlotOptimizationService;
import com.happyfresh.fulfillment.tpl.delyva.service.DelyvaService;
import com.happyfresh.fulfillment.user.service.AgentService;
import com.happyfresh.fulfillment.user.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.geo.GeoPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.EntityNotFoundException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.happyfresh.fulfillment.entity.Payment.CashlessStatus.PENDING_SPLIT_TO_EWALLET;

@Service
public class BatchSndService {

    private final static long MAX_DELIVERY_PHOTO_SIZE = 2097152; // 2MB in bytes

    private final Logger LOGGER = LoggerFactory.getLogger(BatchSndService.class);

    @Autowired
    private JobSndService jobSndService;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private SndTrackingService sndTrackingService;

    @Autowired
    private BatchAvailabilityService batchAvailabilityService;

    @Autowired
    private ReceiptRepository receiptRepository;

    @Autowired
    private DeliveryInfoRepository deliveryInfoRepository;

    @Autowired
    private ReceiptMapper receiptMapper;

    @Autowired
    private DeliveryInfoMapper deliveryInfoMapper;

    @Autowired
    private FileUploaderService fileUploaderService;

    @Autowired
    private ShipmentService shipmentService;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private ItemSndService itemSndService;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private CatalogService catalogService;

    @Autowired
    private ItemRepository itemRepository;

    @Autowired
    private OrderService orderService;

    @Autowired
    private GrabExpressDeliveryRepository grabExpressDeliveryRepository;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private BatchRecalculateService batchRecalculateService;

    @Autowired
    private PaymentRepository paymentRepository;

    @Autowired
    private BrazeAPIService brazeAPIService;

    @Lazy
    @Autowired
    private LalamoveService lalamoveService;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private DeliveryPhotoRepository deliveryPhotoRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private WebhookPublisherService webhookPublisherService;

    @Autowired
    private ShipmentMapper shipmentMapper;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private LongerDeliverySlotOptimizationEventTracker ldsTracker;

    @Autowired
    private GrabExpressService grabExpressService;

    @Autowired
    private FleetTrackingService fleetTrackingService;

    @Autowired
    private RadarApiService radarApiService;

    @Lazy
    @Autowired
    private DelyvaService delyvaService;

    @Lazy
    @Autowired
    private AgentService agentService;

    @Lazy
    @Autowired
    private DriverAutoAssignmentService driverAutoAssignmentService;

    @Autowired
    private BatchMapper batchMapper;

    @Autowired
    private ItemReplacementPreferenceRepository itemReplacementPreferenceRepository;

    @Autowired
    private GosendAsyncService gosendAsyncService;

    @Autowired
    private KafkaMessage kafkaMessage;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private BookingLogService bookingLogService;

    @Autowired
    private PublishSlotOptimizationService publishSlotOptimizationService;

    @Transactional
    public Batch start(Long batchId, User user) {
        Batch batch = batchRepository.fetchById(batchId);
        if (batch == null)
            throw new EntityNotFoundException();

        if ((batch.getUser() != null && batch.getType() != Batch.Type.ON_DEMAND) && !isShopperAllowedToStart(batch, user)
                && !isDriverAllowedToStart(batch, user)) {
            throw new BatchAlreadyTakenException();
        }

        if (hasOnDemandShoppingBatchAssigned(user))
            throw new HasOnDemandShoppingAssignedException();

        for (Job job : batch.getJobs())
            jobSndService.start(job);

        batch.setUser(user);
        batchRepository.save(batch);

        sndTrackingService.trackSwitchJob(batch.getId(), user.getId(), SwitchJobPayload.Operation.START);
        if (batch.getType().equals(Batch.Type.SHOPPING) || batch.getType().equals(Batch.Type.DELIVERY) || batch.getType().equals(Batch.Type.RANGER))
            updateLastActivityTime(user.getAgent(), batch.getEndTime());

        return batch;
    }

    private boolean isShopperAllowedToStart(Batch batch, User user) {
        return batch.getUser() != null
                && batch.getUser().getId().equals(user.getId())
                && UserRoleUtil.isShopper(user)
                && batch.getType().equals(Batch.Type.SHOPPING);
    }

    private boolean isDriverAllowedToStart(Batch batch, User user) {
        Cluster cluster = batch.getStockLocation().getCluster();
        return batch.getUser() != null
                && batch.getUser().getId().equals(user.getId())
                && cluster.isEnableDriverAutoAssignment()
                && UserRoleUtil.isDriver(user)
                && (batch.getType().equals(Batch.Type.DELIVERY) || batch.getType().equals(Batch.Type.RANGER));
    }

    private void updateLastActivityTime(Agent agent, LocalDateTime lastActivityTime) {
        agentService.updateLastActivityTime(agent, lastActivityTime);
    }

    private boolean hasOnDemandShoppingBatchAssigned(User user) {
        List<Batch> activeBatches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND_SHOPPING, user, Job.getInactiveJobStates());
        return activeBatches.size() > 0;
    }

    public boolean hasOnDemandBatchAssigned(User user) {
        List<Batch> activeBatches = batchRepository.findActiveBatchesByTypesAndUser(Lists.newArrayList(Batch.Type.ON_DEMAND, Batch.Type.ON_DEMAND_DELIVERY), user, Job.getInactiveJobStates());
        return activeBatches.size() > 0;
    }

    @Transactional
    public Batch book(long batchId, User user) {
        Batch batch = batchRepository.fetchById(batchId);
        if (batch == null)
            throw new EntityNotFoundException();

        if (batch.getUser() != null)
            throw new BatchAlreadyTakenException();

        if (UserRoleUtil.isDriver(user)) {
            List<Batch> batches = batchAvailabilityService.getActiveBatches(user);
            if (batches.size() >= batch.getTenant().maxNumberOfBatchesPerDriver()) {
                throw new UnprocessableEntityException("Maximum number of delivery batches reached.");
            }
        }

        StockLocation stockLocation = batch.getStockLocation();
        Cluster cluster = stockLocation.getCluster();
        if (cluster.getSlotType() == Slot.Type.LONGER_DELIVERY) {
            List<Job> shoppingJobs = batch.getJobs().stream().filter(job -> {
                Optional<Job> optionalShoppingJob = job.getShipment().getShoppingJob();
                if (optionalShoppingJob.isPresent()) {
                    Job shoppingJob = optionalShoppingJob.get();
                    return shoppingJob.getState() == Job.State.INITIAL;
                } else {
                    return false;
                }
            }).collect(Collectors.toList());
            if (shoppingJobs.size() > 0) {
                throw new HaveUnstartedShoppingJobException();
            }
        }

        if (batch.getType() == Batch.Type.RANGER)
            throw new JobNotFoundException("This ranger job cannot booked as delivery job");

        batch.setUser(user);
        batchRepository.save(batch);
        for (Shipment shipment : batch.getAllShipments()) {
            // Add logging
            bookingLogService.logAssignment(batch, shipment, user, BookingLog.State.ASSIGNED, BookingLog.AssignedBy.MANUAL);
        }


        return batch;
    }

    @Transactional
    public Batch pickup(long batchId) {
        Batch batch = batchRepository.fetchById(batchId);
        if (batch == null)
            throw new EntityNotFoundException();

        List<Batch.Type> allowedBatchTypes = Lists.newArrayList(Batch.Type.DELIVERY, Batch.Type.ON_DEMAND_DELIVERY);
        if (!allowedBatchTypes.contains(batch.getType()))
            throw new UnprocessableEntityException("Invalid batch type.");

        batch.getJobs().stream()
                .filter(job -> job.getType() == Job.Type.DRIVER || job.getType() == Job.Type.ON_DEMAND_DRIVER)
                .forEach(job -> jobSndService.start(job));

        for (Shipment shipment : batch.getAllShipments()) {
            // Add logging
            bookingLogService.logAssignment(batch, shipment, batch.getUser(), BookingLog.State.PICKED_UP, BookingLog.AssignedBy.MANUAL);
        }

        return batch;
    }

    @Transactional
    public Batch accept(String shipmentNumber) {
        Job deliveryJob = getDeliveryJob(shipmentNumber);
        List<Job.Type> allowedJobTypes = Lists.newArrayList(Job.Type.DRIVER, Job.Type.ON_DEMAND_DRIVER);
        if (!allowedJobTypes.contains(deliveryJob.getType()))
            throw new UnprocessableEntityException("Invalid job type.");

        jobSndService.changeJobState(deliveryJob, Job.State.ACCEPTED);

        return batchRepository.fetchById(deliveryJob.getBatch().getId());
    }

    @Transactional
    public Batch deliver(String shipmentNumber) {
        Job deliveryJob = getDeliveryJob(shipmentNumber);

        jobSndService.changeJobState(deliveryJob, Job.State.DELIVERING);

        fleetTrackingService.syncFleetTracking(shipmentNumber, FleetTrackingService.EventName.DELIVERING);

        return batchRepository.fetchById(deliveryJob.getBatch().getId());
    }

    @Transactional
    public Batch pending(String shipmentNumber) {
        Job deliveryJob = getDeliveryJob(shipmentNumber);

        jobSndService.pendingDelivery(deliveryJob);

        fleetTrackingService.syncFleetTracking(shipmentNumber, FleetTrackingService.EventName.FOUND_ADDRESS);

        return batchRepository.fetchById(deliveryJob.getBatch().getId());

    }

    @Transactional
    public Batch continueDelivery(String shipmentNumber) {
        Job deliveryJob = getDeliveryJob(shipmentNumber);

        jobSndService.continueDelivery(deliveryJob);

        return batchRepository.fetchById(deliveryJob.getBatch().getId());

    }

    @Transactional
    public Batch arrive(String shipmentNumber) {
        Job deliveryJob = getDeliveryJob(shipmentNumber);

        jobSndService.changeJobState(deliveryJob, Job.State.FOUND_ADDRESS);

        fleetTrackingService.syncFleetTracking(shipmentNumber, FleetTrackingService.EventName.FOUND_ADDRESS);

        return batchRepository.fetchById(deliveryJob.getBatch().getId());
    }

    @Transactional
    public Batch capture(String shipmentNumber, String clientIPAddress) throws Exception {
        Shipment shipment = shipmentService.findByNumber(shipmentNumber);
        Job deliveryJob = getDeliveryJob(shipmentNumber);
        if (deliveryJob.getState() != Job.State.FOUND_ADDRESS)
            throw new InvalidJobStateException("Current job state is not found address");

        paymentService.capturePayment(shipment.getNumber(), clientIPAddress, true);

        if (Set.of(Shipment.CC_PAYMENT_TYPE, Shipment.CC_PAYMENT_TYPE_XENDIT).contains(shipment.getOrderPaymentMethod())) {
            shipment.setPaymentClear(true);
            shipmentRepository.save(shipment);
        }

        return batchRepository.fetchById(deliveryJob.getBatch().getId());
    }

    @Transactional
    public Batch captureV2(String shipmentNumber, String clientIPAddress) throws Exception {
        Shipment shipment = shipmentService.findByNumber(shipmentNumber);
        Job deliveryJob = getDeliveryJob(shipment);
        if (deliveryJob.getState() != Job.State.FINALIZING_DELIVERY)
            throw new InvalidJobStateException("Current job state is not finalizing delivery");

        ResponseEntity<String> response = paymentService.capturePayment(shipment.getNumber(), clientIPAddress, false);
        ObjectMapper mapper = new ObjectMapper();
        JsonNode arrayObj = mapper.readTree(response.getBody());
        if (!arrayObj.isArray() || arrayObj.size() == 0)
            throw new InvalidPaymentCapturedException("Payment response is invalid.");

        String isoName = shipment.getSlot().getStockLocation().getState().getCountry().getIsoName();

        int numOfPendingPayment = 0;
        BigDecimal cashless = BigDecimal.ZERO;
        BigDecimal cash = BigDecimal.ZERO;
        List<PaymentOption> paymentOptions = new ArrayList<>();
        for (JsonNode obj : arrayObj) {
            String paymentMethodType = obj.get("payment_method_type").asText();
            if (StringUtils.equals(obj.get("state").asText(), "checkout")) {
                PaymentOption paymentOption = new PaymentOption();
                JsonNode expiredAt = obj.get("expired_at");
                if (expiredAt != null)
                    paymentOption.setExpiredAt(DateTimeUtil.spreeStringToLocalDateTime(expiredAt.asText()));

                JsonNode qrCode = obj.get("qr_code");
                if (qrCode != null)
                    paymentOption.setQrCode(qrCode.asText());

                JsonNode name = obj.get("payment_method_name");
                if (name != null)
                    paymentOption.setName(name.asText());

                BigDecimal amount = BigDecimal.valueOf(obj.get("amount").asDouble(0.0));
                paymentOption.setAmount(amount);
                paymentOption.setDisplayAmount(CurrencyUtil.format(amount, isoName));
                paymentOption.setType(paymentMethodType);
                paymentOptions.add(paymentOption);

                numOfPendingPayment++;
            }

            if (StringUtils.equals(obj.get("state").asText(), "completed") && (Shipment.getCashlessPaymentMethodTypes().contains(paymentMethodType))) {
                cashless = cashless.add(BigDecimal.valueOf(obj.get("amount").asDouble()));
            } else if (StringUtils.equals(paymentMethodType, Shipment.COD_PAYMENT_TYPE)) {
                cash = cash.add(BigDecimal.valueOf(obj.get("amount").asDouble()));
            }
        }

        if (numOfPendingPayment >= 1) {
            Payment payment = shipment.getPayment();
            payment.setPaymentOptions(paymentOptions);
            payment.setCashlessStatus(PENDING_SPLIT_TO_EWALLET);
            payment.setCash(cash);
            payment.setCashless(cashless);
            paymentRepository.save(payment);

            Boolean shouldAutomateVerificationForOutstandingPayment = shipment.getSlot().getStockLocation().shouldAutomateVerificationForOutstandingPayment();
            if (shouldAutomateVerificationForOutstandingPayment) {
                Float moneyDiscrepency = cash.floatValue();
                Batch updatedBatch = automateVerificationForOutstandingPayment(shipmentNumber, clientIPAddress);
                sendKafkaUncollectedPaymentEvent(updatedBatch, shipment, moneyDiscrepency);
                return updatedBatch;
            } else {
                Batch updatedBatch = batchRepository.fetchById(getDeliveryJob(shipment).getBatch().getId());
                return updatedBatch;
            }
        } else {
            return finalizePayment(shipment, arrayObj);
        }
    }

    private void sendKafkaUncollectedPaymentEvent(Batch batch, Shipment shipment, Float moneyDiscrepancy) throws JsonProcessingException {
        sendKafkaUncollectedPaymentEvent(
                batch.getUser().getEmail(),
                moneyDiscrepancy,
                "auto created outstanding payment",
                shipment.getOrderNumber()
        );
    }

    private void sendKafkaUncollectedPaymentEvent(String fleetEmail, Float amount, String description, String orderNumber) throws JsonProcessingException {
        try {
            UncollectedPaymentDataEvent data = new UncollectedPaymentDataEvent();
            data.setFleetEmail(fleetEmail);
            data.setAmount(amount);
            data.setDescription(description);
            data.setOrderNumber(orderNumber);
            UncollectedPaymentEvent event = new UncollectedPaymentEvent();
            event.setData(data);
            String message = objectMapper.writeValueAsString(event);
            kafkaMessage.publish(KafkaTopicConfig.UNCOLLECTED_PAYMENT_TOPIC, orderNumber, message);
        } catch (JsonProcessingException e) {
            LOGGER.error("["+ KafkaTopicConfig.UNCOLLECTED_PAYMENT_TOPIC +"]" + orderNumber + " | " + fleetEmail + " | " + amount + " | " + description);
        }

    }

    public Batch automateVerificationForOutstandingPayment(String shipmentNumber, String clientIPAddress) throws Exception {
        VerifyPaymentForm verifyPaymentForm = new VerifyPaymentForm();
        verifyPaymentForm.setName("COD");
        verifyPaymentForm.setType("Spree::PaymentMethod::Check");
        return verifyPayment(shipmentNumber, clientIPAddress, verifyPaymentForm, true);
    }


    @Transactional
    public Batch verifyPayment(String shipmentNumber, String clientIPAddress, VerifyPaymentForm verifyPayment, Boolean isAutomated) throws Exception {
        Shipment shipment = shipmentService.findByNumber(shipmentNumber);
        Job deliveryJob = getDeliveryJob(shipment);
        if (deliveryJob.getState() != Job.State.FINALIZING_DELIVERY)
            throw new InvalidJobStateException("Current job state is not finalizing delivery");

        String paymentType = verifyPayment.getType();
        String paymentName = verifyPayment.getName();
        ResponseEntity<String> response = paymentService.verifyPayment(shipment.getNumber(), clientIPAddress, paymentType, paymentName);
        ObjectMapper mapper = new ObjectMapper();
        JsonNode arrayObj = mapper.readTree(response.getBody());
        if (!arrayObj.isArray() || arrayObj.size() == 0)
            throw new InvalidPaymentCapturedException("Payment response is invalid.");

        boolean isSplitToCod = false;
        BigDecimal cashless = BigDecimal.ZERO;
        BigDecimal cash = BigDecimal.ZERO;
        Payment.Type initialPaymentMethodType = shipment.getPayment().getType();

        for (JsonNode obj : arrayObj) {
            String paymentMethodType = obj.get("payment_method_type").asText();
            if (Shipment.getCashlessPaymentMethodTypes().contains(paymentMethodType)) {
                cashless = cashless.add(BigDecimal.valueOf(obj.get("amount").asDouble()));
            } else {
                cash = cash.add(BigDecimal.valueOf(obj.get("amount").asDouble()));
                if (!initialPaymentMethodType.equals(Payment.Type.CASH))
                    isSplitToCod = true;
            }
        }

        Payment payment = shipment.getPayment();
        if (isSplitToCod) {
            payment.setCashlessStatus(Payment.CashlessStatus.SPLIT_TO_COD);
        } else {
            payment.setCashlessStatus(Payment.CashlessStatus.SPLIT_TO_EWALLET);
        }

        if (cashless.doubleValue() == 0.0 && initialPaymentMethodType.equals(Payment.Type.CASH)) {
            payment.setCashlessStatus(null);
        } else if (cashless.doubleValue() > 0.0 && initialPaymentMethodType.equals(Payment.Type.CASH)) {
            payment.setCashlessStatus(Payment.CashlessStatus.SWITCH_TO_EWALLET);
        }

        /**
         * current flow on SnD need to see status as CAPTURED to by pass the collect bill screen
         */
        if (isAutomated) {
            payment.setCashlessStatus(Payment.CashlessStatus.CAPTURED);
            payment.setOutstandingStatus("DEBT");
        }

        payment.setState(Payment.State.SUCCESS);
        payment.setCashless(cashless);
        payment.setCash(cash);
        paymentRepository.save(payment);

        shipment.setPaymentClear(true);
        shipmentRepository.save(shipment);

        return batchRepository.fetchById(getDeliveryJob(shipment).getBatch().getId());
    }

    @Transactional
    public Batch splitToCOD(String shipmentNumber) throws Exception {
        Shipment shipment = shipmentService.findByNumber(shipmentNumber);
        Job deliveryJob = getDeliveryJob(shipment);
        if (deliveryJob.getState() != Job.State.FINALIZING_DELIVERY)
            throw new InvalidJobStateException("Current job state is not finalizing delivery");

        Payment payment = shipment.getPayment();
        payment.setCashlessStatus(Payment.CashlessStatus.SPLIT_TO_COD);
        payment.setState(Payment.State.SUCCESS);
        paymentRepository.save(payment);

        shipment.setPaymentClear(true);
        shipmentRepository.save(shipment);

        return batchRepository.fetchById(getDeliveryJob(shipment).getBatch().getId());
    }

    private Batch finalizePayment(Shipment shipment, JsonNode arrayObj) {
        Payment payment = shipment.getPayment();
        BigDecimal cashless = BigDecimal.ZERO;
        BigDecimal cash = BigDecimal.ZERO;
        for (JsonNode obj : arrayObj) {
            if (StringUtils.equals(obj.get("payment_method_type").asText(), Shipment.COD_PAYMENT_TYPE))
                cash = cash.add(BigDecimal.valueOf(obj.get("amount").asDouble()));
            else
                cashless = cashless.add(BigDecimal.valueOf(obj.get("amount").asDouble()));
        }

        Payment.CashlessStatus cashlessStatus;
        if (cash.doubleValue() == 0.0 && cashless.doubleValue() >= 0.0)
            cashlessStatus = Payment.CashlessStatus.CAPTURED;
        else if (cash.doubleValue() > 0.0 && cashless.doubleValue() == 0.0)
            cashlessStatus = Payment.CashlessStatus.SWITCH_TO_COD;
        else if (cash.doubleValue() > 0.0 && cashless.doubleValue() > 0.0)
            cashlessStatus = Payment.CashlessStatus.SPLIT_TO_COD;
            // Hack handle negative order total
        else if (cashless.doubleValue() < 0.0)
            cashlessStatus = Payment.CashlessStatus.CAPTURED;
        else
            throw new InvalidPaymentCapturedException("No completed payment.");

        if (payment.getType().equals(Payment.Type.CASH))
            payment.setCashlessStatus(null);
        else
            payment.setCashlessStatus(cashlessStatus);

        payment.setCash(cash);
        payment.setCashless(cashless);
        payment.setState(Payment.State.SUCCESS);
        paymentRepository.save(payment);

        shipment.setPaymentClear(true);
        shipmentRepository.save(shipment);

        return batchRepository.fetchById(getDeliveryJob(shipment).getBatch().getId());
    }

    @Transactional
    public Batch switchToCOD(String shipmentNumber, String clientIPAddress) throws Exception {
        Shipment shipment = shipmentService.findByNumber(shipmentNumber);
        Job deliveryJob = getDeliveryJob(shipmentNumber);
        if (deliveryJob.getState() != Job.State.FOUND_ADDRESS)
            throw new InvalidJobStateException("Current job state is not found address");

        paymentService.switchToCOD(shipment, clientIPAddress);
        shipment.setOrderPaymentMethod(Shipment.COD_PAYMENT_TYPE);
        shipment.setPaymentClear(true);
        shipmentRepository.save(shipment);

        return batchRepository.fetchById(deliveryJob.getBatch().getId());
    }

    @Transactional
    public Batch finish(String shipmentNumber, DeliveryInfoForm deliveryInfoForm) throws Exception {
        Shipment shipment = finishShipment(shipmentNumber, deliveryInfoForm.isAgeConsent());
        Job deliveryJob = getDeliveryJob(shipment);

        DeliveryInfo deliveryInfo = deliveryInfoMapper.deliveryInfoFormToDeliveryInfo(deliveryInfoForm);
        String url = fileUploaderService.uploadSignature(deliveryInfoForm.getAttachment());
        deliveryInfo.setSignatureUrl(url);
        deliveryInfo.setShipment(shipment);
        deliveryInfoRepository.save(deliveryInfo);

        return batchRepository.fetchById(deliveryJob.getBatch().getId());
    }

    @Transactional
    public Batch finishV2(String shipmentNumber, DeliveryInfoV2Form deliveryInfoForm) throws Exception {
        Shipment shipment = finishShipment(shipmentNumber, deliveryInfoForm.isAgeConsent());
        Job deliveryJob = getDeliveryJob(shipment);

        DeliveryInfo deliveryInfo = deliveryInfoMapper.deliveryInfoV2FormToDeliveryInfo(deliveryInfoForm);
        deliveryInfo.setShipment(shipment);
        deliveryInfoRepository.save(deliveryInfo);
        Batch batch = deliveryJob.getBatch();

        long deliveryBatchId = batch.getShift() != null ? batch.getShift().getId() : 0L;

        if (Batch.Type.getNormalDeliveryBatchTypes().contains(batch.getType())
                && batch.getStockLocation().getCluster().isEnableDriverAutoAssignment()
                && batchRepository.isTheLastShipmentInBatch(batch.getId(), shipment.getOrderNumber(), Job.getInactiveJobStates()))
            driverAutoAssignmentService.publishAutoAssignmentEvent(batch.getStockLocation().getCluster().getId().toString(),
                    Long.toString(deliveryBatchId), SlotOptimizationEvent.AutoAssignmentTriggerEvent.DELIVERY_FINISHED);

        return batchRepository.fetchById(batch.getId());
    }

    @Transactional
    public Batch finishV3(String shipmentNumber, DeliveryInfoV3Form deliveryInfoForm) {
        Shipment shipment = finishShipment(shipmentNumber, deliveryInfoForm);
        Job deliveryJob = getDeliveryJob(shipment);
        Batch batch = deliveryJob.getBatch();

        DeliveryInfo deliveryInfo = deliveryInfoMapper.deliveryInfoV3FormToDeliveryInfo(deliveryInfoForm);
        deliveryInfo.setShipment(shipment);
        deliveryInfoRepository.save(deliveryInfo);

        long deliveryBatchId = batch.getShift() != null ? batch.getShift().getId() : 0L;

        if (Batch.Type.getNormalDeliveryBatchTypes().contains(batch.getType())
                && batch.getStockLocation().getCluster().isEnableDriverAutoAssignment()
                && batchRepository.isTheLastShipmentInBatch(batch.getId(), shipment.getOrderNumber(), Job.getInactiveJobStates()))
            driverAutoAssignmentService.publishAutoAssignmentEvent(batch.getStockLocation().getCluster().getId().toString(),
                    Long.toString(deliveryBatchId), SlotOptimizationEvent.AutoAssignmentTriggerEvent.DELIVERY_FINISHED);

        return batchRepository.fetchById(batch.getId());
    }

    private Shipment finishShipment(String shipmentNumber, boolean isAgeConsent) throws Exception {
        Shipment shipment = shipmentService.findByNumber(shipmentNumber);

        if (!shipment.isPaymentClear()) {
            throw new PaymentNotClearException();
        }

        Job deliveryJob = getDeliveryJob(shipment);
        jobSndService.changeJobState(deliveryJob, Job.State.FINISHED);

        if (deliveryJob.getType().equals(Job.Type.ON_DEMAND_RANGER) || deliveryJob.getType().equals(Job.Type.ON_DEMAND_DRIVER)) {
            startNextOnDemandJob(deliveryJob);
        }

        shipment.setAgeConsent(isAgeConsent);
        return shipmentRepository.save(shipment);
    }

    private Shipment finishShipment(String shipmentNumber, DeliveryInfoV3Form deliveryInfoForm) {
        Shipment shipment = shipmentService.findByNumber(shipmentNumber);

        if (!shipment.isPaymentClear()) {
            throw new PaymentNotClearException();
        }

        Job deliveryJob = getDeliveryJob(shipment);
        jobSndService.changeJobState(deliveryJob, Job.State.FINISHED);

        if (deliveryJob.getType().equals(Job.Type.ON_DEMAND_RANGER) || deliveryJob.getType().equals(Job.Type.ON_DEMAND_DRIVER)) {
            startNextOnDemandJob(deliveryJob);
        }

        processLocationAccuracyInfo(deliveryInfoForm, shipment);

        shipment.setAgeConsent(deliveryInfoForm.isAgeConsent());
        return shipmentRepository.save(shipment);
    }

    private void processLocationAccuracyInfo(DeliveryInfoV3Form deliveryInfoForm, Shipment shipment) {
        DeliveryInfoV3Form.LocationAccuracy locationAccuracy = deliveryInfoForm.getLocationAccuracy();
        if(locationAccuracy != null) {
            Boolean isAddressIncorrect = locationAccuracy.getIsAddressIncorrect();
            shipment.updateFlags(Shipment.FLAG_IS_ADDRESS_INCORRECT, isAddressIncorrect.toString());

            if(Boolean.TRUE.equals(isAddressIncorrect)) {
                if(locationAccuracy.getIncorrectAddressReasons() == null || locationAccuracy.getIncorrectAddressReasons().isEmpty())
                    throw new BadRequestException("Provide at least one reason when address is incorrect");

                String reasons = locationAccuracy.getIncorrectAddressReasons().stream()
                        .map(DeliveryInfoV3Form.LocationAccuracy.WrongLocationReasonEnum::toString)
                        .collect(Collectors.joining(","));
                shipment.updateFlags(Shipment.FLAG_LOCATION_INCORRECT_REASONS, reasons);

            }
        }
    }

    public void startNextOnDemandJob(Job finishedJob) {
        Batch finishedBatch = finishedJob.getBatch();
        List<Batch> activeBatches = batchRepository.findActiveBatchesByTypesAndUser(Batch.Type.getOnDemandRangerBatchTypes(), finishedBatch.getUser(), Job.getInactiveJobStates());
        Optional<Job> nextJob = activeBatches.stream()
                .map(batch -> batch.getJobs().get(0))
                .filter(job -> !job.getId().equals(finishedJob.getId()))
                .findFirst();

        if (nextJob.isPresent() && nextJob.get().getType() == Job.Type.ON_DEMAND_RANGER && nextJob.get().getState().equals(Job.State.INITIAL) && nextJob.get().getShipment().getState().equals(Shipment.State.READY)) {
            Job job = nextJob.get();
            job.setState(Job.State.STARTED);
            jobRepository.save(job);
        }
    }

    @Transactional
    public Shipment cancelJobByShipment(String number) throws Exception {
        Shipment shipment = shipmentRepository.findByNumber(number);
        boolean isReset = false;
        for (Job job : shipment.getJobs()) {
            if (job.isOnDemandRanger() || job.isOnDemandDelivery() || job.isOnDemandShopping()) {
                throw new InvalidJobStateException("Cannot reset this order");
            }

            if (job.isRanger()) {
                Job.State[] states = new Job.State[]{Job.State.INITIAL, Job.State.STARTED};
                List<Job.State> eligibleStates = Arrays.asList(states);

                isReset = cancelJob(eligibleStates, job, true);
            }

            if (job.isDelivery()) {
                Job.State[] states = new Job.State[]{Job.State.INITIAL};
                List<Job.State> eligibleStates = Arrays.asList(states);

                isReset = cancelJob(eligibleStates, job, false);
            }

            if (job.isShopping()) {
                Job.State[] states = new Job.State[]{Job.State.INITIAL, Job.State.STARTED};
                List<Job.State> eligibleStates = Arrays.asList(states);

                // Shopping does not change isReset status
                cancelJob(eligibleStates, job, true);
            }
        }

        if (!isReset) {
            throw new InvalidJobStateException("Cannot reset this order");
        }

        Slot slot = shipment.getSlot();
        if (slot.getType().equals(Slot.Type.LONGER_DELIVERY)) {
            ldsTracker.construct(LongerDeliverySlotOptimizationEventTracker.EVENT_CANCEL_JOB, slot.getStockLocation().getCluster(), shipment, slot, batchRepository, null);
            String batchIds = shipment.getJobs().stream().map(job -> job.getId()).collect(Collectors.toList()).toString();
            ldsTracker.setValue("Batch IDs:" + batchIds);
            ldsTracker.track();
        }

        return shipmentRepository.findByNumber(number);
    }

    @Transactional
    public Shipment cancelShoppingJobByShipment(String number) throws Exception {
        Shipment shipment = shipmentRepository.findByNumber(number);
        boolean isReset = false;
        for (Job job : shipment.getJobs()) {
            if (job.isOnDemandRanger() || job.isOnDemandDelivery() || job.isOnDemandShopping()) {
                throw new InvalidJobStateException("Cannot reset this order");
            }

            if (job.isShopping()) {
                Job.State[] states = new Job.State[]{Job.State.INITIAL, Job.State.STARTED};
                List<Job.State> eligibleStates = Arrays.asList(states);

                // Shopping does not change isReset status
                isReset = cancelJob(eligibleStates, job, true);
            }
        }

        if (!isReset) {
            throw new InvalidJobStateException("Cannot reset this order");
        }

        return shipmentRepository.findByNumber(number);
    }

    @Transactional
    public Shipment cancelDeiveryJobByShipment(String number) throws Exception {
        Shipment shipment = shipmentRepository.findByNumber(number);
        boolean isReset = false;
        for (Job job : shipment.getJobs()) {
            if (job.isOnDemandRanger() || job.isOnDemandDelivery() || job.isOnDemandShopping()) {
                throw new InvalidJobStateException("Cannot reset this order");
            }

            if (job.isRanger()) {
                Job.State[] states = new Job.State[]{Job.State.INITIAL, Job.State.STARTED};
                List<Job.State> eligibleStates = Arrays.asList(states);

                isReset = cancelJob(eligibleStates, job, true);
            }

            if (job.isDelivery()) {
                Job.State[] states = new Job.State[]{Job.State.INITIAL};
                List<Job.State> eligibleStates = Arrays.asList(states);

                isReset = cancelJob(eligibleStates, job, false);
            }
        }

        if (!isReset) {
            throw new InvalidJobStateException("Cannot reset this order");
        }

        return shipmentRepository.findByNumber(number);
    }

    private boolean cancelJob(List<Job.State> eligibleStates, Job job, boolean clearItem) {
        if (eligibleStates.contains(job.getState())) {
            Batch batch = job.getBatch();
            for (Job _job : batch.getJobs()) {
                // Handle case on ranger job, if there is other job whic state accepted, it should not be allowed to cancel
                // This is due to finalize per order feature
                if (_job.getState() == Job.State.ACCEPTED) {
                    throw new InvalidJobStateException("Cannot reset this order");
                }

                jobSndService.cancelJob(_job, clearItem);
            }
            batch.setUser(null);
            batchRepository.save(batch);

            return true;
        } else {
            return false;
        }
    }

    @Transactional
    public Batch cancel(Long batchId, User user) throws Exception {
        Batch batch = batchRepository.getOne(batchId);
        for (Job job : batch.getJobs()) {
            if (job.getState().equals(Job.State.CANCELLED)) {
                jobRepository.delete(job);
            } else {
                jobSndService.cancelJob(job, true);
            }
        }

        batchRecalculateService.recalculateBatch(batch.getId());
        Optional<Batch> currentBatch = batchRepository.findById(batch.getId());
        if (!currentBatch.isPresent()) {
            return null;
        }

        batch.setUser(null);
        batchRepository.save(batch);

        if (user != null) {
            sndTrackingService.trackSwitchJob(batch.getId(), user.getId(), SwitchJobPayload.Operation.CANCEL);
        }
        return batch;
    }

    @Transactional
    public Shipment finalizeDelivery(String shipmentNumber, List<ItemRejectForm> rejectForms) throws Exception {
        Shipment shipment = itemSndService.reject(shipmentNumber, rejectForms);
        Payment payment = shipment.getPayment();
        if (payment == null) {
            payment = new Payment(shipment, shipment.getOrderPaymentMethod());
            shipment.setPayment(payment);
            payment = shipmentRepository.save(shipment).getPayment();
        }

        if (payment.getState() == Payment.State.SUCCESS)
            throw new PaymentAlreadyCompletedException();

        Job deliveryJob = getDeliveryJob(shipment);
        jobSndService.changeJobState(deliveryJob, Job.State.FINALIZING_DELIVERY);

        return shipment;
    }

    @Transactional
    public List<ApiError> finalizeBatchByShipments(BatchItemFinalizeForm form) {
        List<Shipment> shipments = shipmentRepository.findByNumberIn(form.getShipmentNumbers());
        return finalizeShipment(shipments, form.getItems());
    }

    @Transactional
    public List<ApiError> finalizeBatch(Long batchId, List<ItemFinalizeForm> itemsForm) {
        Batch batch = batchRepository.getOne(batchId);
        List<Shipment> shipments = shipmentRepository.findAllByBatch(batch);
        return finalizeShipment(shipments, itemsForm);
    }

    @Transactional
    public List<ApiError> finalizeShipment(String shipmentNumber, List<ItemFinalizeForm> itemsForm) {
        Shipment shipment = shipmentRepository.findByNumber(shipmentNumber);
        return finalizeShipment(Lists.newArrayList(shipment), itemsForm);
    }

    @Transactional
    public Batch pay(Long batchId, String shipmentNumber, ListOfReceiptForm listOfReceiptForm) throws Exception {
        Shipment shipment = shipmentService.findByNumber(shipmentNumber);

        Job shoppingJob = getShoppingJob(shipment);
        jobSndService.changeJobState(shoppingJob, Job.State.FINISHED);

        for (ReceiptForm receiptForm : listOfReceiptForm.getReceipts()) {
            Receipt receipt = receiptMapper.receiptFormToReceipt(receiptForm);
            for (MultipartFile file : receiptForm.getAttachments()) {
                String url = fileUploaderService.uploadReceipt(file);
                ReceiptImage receiptImage = new ReceiptImage();
                receiptImage.setUrl(url);
                receiptImage.setReceipt(receipt);

                receipt.getReceiptImages().add(receiptImage);
            }

            receipt.setCompleted(true);
            receipt.setShipment(shipment);
            if (!receipt.isValid()) {
                throw new ReceiptNumberRequiredException();
            }

            receiptRepository.save(receipt);
        }

        Batch batch = batchRepository.getOne(batchId);
        if (batch.getType() == Batch.Type.RANGER || batch.getType() == Batch.Type.ON_DEMAND) {
            Job deliveryJob = getDeliveryJob(shipmentNumber);
            jobSndService.changeJobState(deliveryJob, Job.State.ACCEPTED);
        }

        return batchRepository.fetchById(batchId);
    }

    @Transactional
    public Batch pay(Long batchId, String shipmentNumber, List<ReceiptV2Form> receiptV2Forms) {
        Shipment shipment = shipmentService.findByNumber(shipmentNumber);

        Job shoppingJob = getShoppingJob(shipment);
        jobSndService.changeJobState(shoppingJob, Job.State.FINISHED);

        for (ReceiptV2Form receiptV2Form : receiptV2Forms) {
            Receipt receipt = receiptMapper.receiptFormToReceipt(receiptV2Form);
            receipt.setShipment(shipment);

            receiptRepository.save(receipt);
        }

        Batch batch = batchRepository.getOne(batchId);
        if (batch.getType() == Batch.Type.RANGER || batch.getType() == Batch.Type.ON_DEMAND) {
            Job deliveryJob = getDeliveryJob(shipmentNumber);
            jobSndService.changeJobState(deliveryJob, Job.State.ACCEPTED);
        }

        updateLastActivityTimeStamp(batch);

        return batchRepository.fetchById(batchId);
    }

    private void updateLastActivityTimeStamp(Batch batch) {
        if (batch.getType() == Batch.Type.SHOPPING && isAllJobsFinished(batch))
            agentService.updateLastActivityTime(batch.getUser().getAgent(), getLastFinishTime(batch));
    }

    private boolean isAllJobsFinished(Batch batch) {
        for (Job job : batch.getJobs()) {
            if (!Job.getInactiveJobStates().contains(job.getState()))
                return false;
        }
        return true;
    }

    private LocalDateTime getLastFinishTime(Batch batch) {
        List<Job> jobs = batch.getJobs();
        String jobStateToCheck = jobs.get(0).isRanger() ? "finalizing" : "finished";
        return jobs.stream().map(j -> {
            if (j.getJobStates() == null)
                return null;
            return stringDateTimeToLocalDateTime(j.getJobStates().get(jobStateToCheck));
        }).filter(Objects::nonNull).max(LocalDateTime::compareTo).orElse(null);
    }

    private LocalDateTime stringDateTimeToLocalDateTime(String strLocalDateTime) {
        return DateTimeUtil.stringTimeStampToLocalDateTime(strLocalDateTime);
    }


    @Transactional
    public Batch failDelivery(String shipmentNumber, String reason) {
        Job deliveryJob = getDeliveryJob(shipmentNumber);

        deliveryJob.setNote(Job.Note.valueOf(StringUtils.capitalize(reason)));

        jobSndService.changeJobState(deliveryJob, Job.State.FAILED);

        if (Job.Type.getOnDemandRangerJobTypes().contains(deliveryJob.getType())) {
            startNextOnDemandJob(deliveryJob);
        }

        fleetTrackingService.syncFleetTracking(shipmentNumber, FleetTrackingService.EventName.FAILED_DELIVERY);

        Cluster cluster = deliveryJob.getShipment().getSlot().getStockLocation().getCluster();
        Batch batch = deliveryJob.getBatch();
        long deliveryBatchId = batch.getShift() != null ? batch.getShift().getId() : 0L;

        if (cluster.isEnableDriverAutoAssignment()
                && batchRepository.isTheLastShipmentInBatch(batch.getId(),
                deliveryJob.getShipment().getOrderNumber(), Job.getInactiveJobStates()))
            driverAutoAssignmentService.publishAutoAssignmentEvent(cluster.getId().toString(),
                    Long.toString(deliveryBatchId), SlotOptimizationEvent.AutoAssignmentTriggerEvent.FAILED_DELIVERY);

        return batchRepository.fetchById(batch.getId());
    }

    @Transactional
    public void failShopping(String shipmentNumber, CancellationForm cancellationForm) throws Exception {
        Shipment shipment = shipmentRepository.findByNumber(shipmentNumber);
        if (shipment == null)
            throw new EntityNotFoundException("Shipment not found");

        shipmentService.cancelByShopper(shipment, cancellationForm, cancellationForm.getReason());

        String path = String.format("/shipments/%s/", shipment.getNumber());
        Tenant tenant = shipment.getTenant();
        WebhookType webhookType = WebhookType.CANCEL_BY_SHOPPER;
        Object body = shipmentMapper.shipmentToShipmentPresenter(shipment);
        String rootName = "shipment";
        JsonNode bodyNode = mapper.convertValue(body, JsonNode.class);
        ObjectNode wrapperNode = JsonNodeFactory.instance.objectNode();
        wrapperNode.set(rootName, bodyNode);

        webhookPublisherService.publish(webhookType, wrapperNode, path, tenant.getId());
    }

    @Transactional
    public void handleManualShipmentStatusUpdate(String number, AdminShipmentStatusUpdateForm form) {
        List<String> allowedStatus = List.of(Job.State.FOUND_ADDRESS.toString(), Job.State.STARTED.toString());

        String status = form.getStatus();
        if (!allowedStatus.contains(status))
            throw new BadRequestException("Action is not allowed.");

        Shipment shipment = shipmentRepository.findByNumber(number);
        if (shipment == null)
            throw new ResourceNotFoundException();

        // This should be converted to switch statement later
        // when there is a new handler for another job state.
        if (status.equals(Job.State.FOUND_ADDRESS.toString())) {
            Job deliveryJob = getDeliveryJob(number);
            if (deliveryJob == null || deliveryJob.getState() != Job.State.DELIVERING) {
                throw new BadRequestException("Action is not allowed.");
            }
            Batch batch = arrive(number);
            publishArriveWebhook(shipment, batch);
        } else if (status.equals(Job.State.STARTED.toString())) {
            shipmentService.resetToStarting(number);
        }
    }

    private void publishArriveWebhook(Shipment shipment, Batch batch) {
        BatchPresenter batchPresenter = batchMapper.batchToBatchPresenter(batch);

        ObjectNode wrapperNode = JsonNodeFactory.instance.objectNode();
        JsonNode bodyNode = mapper.convertValue(batchPresenter, JsonNode.class);
        wrapperNode.set("batch", bodyNode);

        Tenant tenant = shipment.getTenant();
        String path = "/api/batches/" + batch.getId() + "/shipments/" + shipment.getNumber() + "/arrive";

        webhookPublisherService.publish(WebhookType.ARRIVE_SHIPMENT, wrapperNode, path, tenant.getId());
    }

    @Transactional
    public DeliveryPhoto addDeliveryPhoto(String shipmentNumber, MultipartFile attachment) throws Exception {
        if (attachment.getSize() > MAX_DELIVERY_PHOTO_SIZE) {
            throw new MaxUploadSizeExceededException(MAX_DELIVERY_PHOTO_SIZE);
        }

        Shipment shipment = shipmentService.findByNumber(shipmentNumber);

        Job deliveryJob = getDeliveryJob(shipment);
        if (deliveryJob.getState() == Job.State.FINISHED) {
            throw new JobAlreadyFinishedException();
        }

        DeliveryPhoto deliveryPhoto = new DeliveryPhoto();
        deliveryPhoto.setUrl(fileUploaderService.uploadSignature(attachment));
        deliveryPhoto.setShipment(shipment);

        return deliveryPhotoRepository.save(deliveryPhoto);
    }

    @Transactional
    public void deleteDeliveryPhoto(String shipmentNumber, Long deliveryPhotoId) {
        Shipment shipment = shipmentService.findByNumber(shipmentNumber);

        Job deliveryJob = getDeliveryJob(shipment);
        if (deliveryJob.getState() == Job.State.FINISHED) {
            throw new JobAlreadyFinishedException();
        }

        DeliveryPhoto deliveryPhoto = deliveryPhotoRepository.getOne(deliveryPhotoId);
        if (!deliveryPhoto.getShipment().getId().equals(shipment.getId())) {
            throw new EntityNotFoundException();
        }

        deliveryPhotoRepository.delete(deliveryPhoto);
    }

    @Transactional(readOnly = true)
    public List<DeliveryPhoto> getDeliveryPhotos(String shipmentNumber) {
        Shipment shipment = shipmentService.findByNumber(shipmentNumber);

        return shipment.getDeliveryPhotos();
    }

    private Job getShoppingJob(Shipment shipment) {
        Optional<Job> shoppingJob = shipment.getJobs().stream().filter(job -> job.isShopping() || job.isOnDemandShopping() || job.isRanger() || job.isOnDemandRanger()).findAny();

        if (!shoppingJob.isPresent())
            throw new EntityNotFoundException();

        return shoppingJob.get();
    }

    public Job getDeliveryJob(String shipmentNumber) {
        Shipment shipment = shipmentService.findByNumber(shipmentNumber);

        return getDeliveryJob(shipment);
    }

    private Job getDeliveryJob(Shipment shipment) {
        Optional<Job> deliveryJob = shipment.getJobs().stream().filter(job -> job.isDelivery() || job.isRanger() || job.isOnDemandRanger() || job.isOnDemandDelivery()).findAny();

        if (!deliveryJob.isPresent())
            throw new EntityNotFoundException();

        return deliveryJob.get();
    }

    private List<ApiError> finalizeShipment(List<Shipment> shipments, List<ItemFinalizeForm> itemsForm) {
        StockLocation stockLocation = shipments.get(0).getSlot().getStockLocation();
        Country country = stockLocation.getState().getCountry();

        List<ApiError> exceptions = new ArrayList<>();
        List<Long> cancelledItemIds = new ArrayList<>();

        Iterator<Shipment> iteratorShipment = shipments.iterator();
        while (iteratorShipment.hasNext()) {
            Shipment shipment = iteratorShipment.next();
            if (shipment.getState().equals(Shipment.State.CANCELLED)) {
                cancelledItemIds.addAll(shipment.getItems()
                        .stream()
                        .map(Item::getId)
                        .collect(Collectors.toList())
                );
                iteratorShipment.remove();
            }
        }

        /*** Should empty all replacements, due to optimistic calling API on client*/
        transactionHelper.withNewTransaction(() -> {
            for (Shipment shipment : shipments) {
                itemReplacementPreferenceRepository.deleteAllReplacementPreferencesOfReplacementItemsByShipmentId(shipment.getId());
                itemRepository.deleteAllReplacementByShipment(shipment);
            }
        });

        if (!cancelledItemIds.isEmpty()) {
            for (ItemFinalizeForm itemFinalize : itemsForm) {
                long itemId = itemFinalize.getReplacedItemId() == null ? itemFinalize.getId() : itemFinalize.getReplacedItemId();
                if (cancelledItemIds.stream().anyMatch(cancelledItemId -> cancelledItemId.equals(itemId))) {
                    exceptions.add(new ApiError(ShipmentCancelledException.class.getSimpleName(), "One of orders in batch has been canceled. Please reload the shopping list to continue shopping."));
                    break;
                }
            }
        }

        itemsForm.stream()
                .filter(i -> i.getReplacedItemId() != null)
                .collect(Collectors.groupingBy(ItemFinalizeForm::getReplacedItemId, Collectors.counting()))
                .forEach((replacedItemId, total) -> {
                    if (total > 1) {
                        String sku = itemsForm.stream().filter(i -> i.getId().equals(replacedItemId)).findFirst().get().getSku();
                        exceptions.add(new ApiError(InvalidReplacementItemException.class.getSimpleName(), "Replaced items is more than one.", sku));
                        itemsForm.removeIf(i -> i.getReplacedItemId() != null && i.getReplacedItemId().equals(replacedItemId));
                    }
                });

        if (!shipments.isEmpty()) {
            List<String> skus = itemsForm.stream()
                    .filter(i -> i.getReplacedItemId() != null)
                    .map(ItemFinalizeForm::getSku).collect(Collectors.toList());
            List<ItemForm> itemsBySku = skus.isEmpty() ? new ArrayList<>() : catalogService.getItemsFromCatalogService(skus, stockLocation.getExternalId());

            for (ItemFinalizeForm form : itemsForm) {
                try {
                    itemSndService.finalizeItem(form, country, stockLocation, itemsBySku);
                } catch (Exception exception) {
                    exceptions.add(new ApiError(exception.getClass().getSimpleName(), exception.getMessage(), form.getSku()));
                }
            }
        }

        checkForMissingItems(shipments, itemsForm, exceptions);

        if (exceptions.isEmpty()) {
            List<Long> shipmentIds = shipments.stream().map(Shipment::getId).collect(Collectors.toList());
            List<Job> shoppingJobs = jobRepository.findAllByShipmentIdIn(shipmentIds).stream()
                    .filter(job -> job.isShopping() || job.isOnDemandShopping() || job.isRanger() || job.isOnDemandRanger()).collect(Collectors.toList());
            for (Job shoppingJob : shoppingJobs)
                jobSndService.changeJobState(shoppingJob, Job.State.FINALIZING);

            for (Shipment shipment : shipments) {
                // Calculate order total for every shipment in a batch
                Double newOrderTotal = orderService.getOrderTotal(shipment.getNumber());
                BigDecimal orderTotal = new BigDecimal(newOrderTotal);
                shipment.setOrderTotal(orderTotal);
                shipmentRepository.save(shipment);

                // Create GE booking scheduled for every GE batch
                Optional<Job> deliveryJob = shipment.getDeliveryJob();
                if (deliveryJob.isPresent() && deliveryJob.get().getBatch().isGrabExpress() && shipment.getCurrentGrabExpressDelivery() == null) {
                    GrabExpressDelivery grabExpressDelivery = new GrabExpressDelivery();
                    grabExpressDelivery.setShipment(shipment);
                    grabExpressDelivery.setRetryCount(0);

                    LocalDateTime now = LocalDateTime.now().withSecond(0).withNano(0);
                    LocalDateTime bookingScheduledAt = shipment.getSlot().getStartTime().minusMinutes(stockLocation.getGrabExpressOffsetTime());
                    if (now.isBefore(bookingScheduledAt)) {
                        grabExpressDelivery.setBookingScheduledAt(bookingScheduledAt);
                    } else {
                        grabExpressDelivery.setBookingScheduledAt(now.plusMinutes(stockLocation.getGrabExpressDelayTime()));
                    }

                    grabExpressDeliveryRepository.save(grabExpressDelivery);
                }

                Optional<Job> deliveryOrRanger = shipment.getDeliveryOrRangerJob();
                if (deliveryOrRanger.isPresent() && deliveryOrRanger.get().getBatch().getTplType() == null) {
                    // Create geofence for fleet tracking
                    // Radar service disabled
//                    radarApiService.createDeliveryGeofence(shipment, country);
                }

                publishSlotOptimizationService.publishSlotOptimizationAndAutoAssignments(shipment);
            }
        }

        return exceptions;
    }

    private void checkForMissingItems(List<Shipment> shipments, List<ItemFinalizeForm> itemsForm, List<ApiError> exceptions) {
        List<Long> itemFormIds = itemsForm.stream().filter(itemForm -> itemForm.getReplacedItemId() == null)
                .map(ItemFinalizeForm::getId).collect(Collectors.toList());

        Map<Long, String> mapShipmentItemsSkuById = shipments.stream().flatMap(
                shipment -> shipment.getItems().stream().filter(item -> item.getReplacedItem() == null)
        ).collect(Collectors.toMap(Item::getId, Item::getSku));

        for (Long itemId : itemFormIds) {
            mapShipmentItemsSkuById.remove(itemId);
        }

        for (String itemSku : mapShipmentItemsSkuById.values()) {
            exceptions.add(new ApiError(InvalidItemQtyException.class.getSimpleName(), "Missing finalize item(s)", itemSku));
        }
    }

    @Transactional
    public GeofenceStatusPresenter getGeofenceStatus(String shipmentNumber, Double lat, Double lon) {
        Shipment shipment = shipmentService.findByNumber(shipmentNumber);

        Double addressLat = shipment.getAddressLat();
        Double addressLon = shipment.getAddressLon();
        GeoPoint addressPoint = new GeoPoint(addressLat, addressLon);
        GeoPoint driverPoint = new GeoPoint(lat, lon);

        Double distance = DistanceUtil.distance(driverPoint, addressPoint);
        Double threshold = getGeofenceThreshold(shipment);

        GeofenceStatusPresenter result = new GeofenceStatusPresenter();
        result.setWithinGeofence(distance <= threshold);
        result.setGeofenceRadius(threshold);
        result.setAirDistance(distance);

        return result;
    }

    private Double getGeofenceThreshold(Shipment shipment) {
        StockLocation stockLocation = shipment.getSlot().getStockLocation();
        Country country = stockLocation.getState().getCountry();
        return country.getDeliveryGeofenceRadius();
    }

    private void placeOrQueueLalamoveDeliveries(Batch batch) {
        if (batch.getType().equals(Batch.Type.SHOPPING)) {
            lalamoveService.placeOrderInOneBatchAsync(batch, null);
        }
    }

    private void placeOrQueueDelyvaDeliveries(Batch batch) {
        if (batch.getType().equals(Batch.Type.SHOPPING)) {
            Tenant tenant = batch.getTenant();
            Role role = roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, tenant.getId());
            User user = userRepository.findByRolesContainingAndTenantId(role, tenant.getId());
            // AbstractAuthenticationToken is needed to get tenant in Async method
            AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), tenant.getToken());
            delyvaService.placeOrderInOneBatchAsync(batch, authenticationToken);
        }
    }

    private void placeOrQueueGosendDeliveries(Batch batch, List<String> shipmentNumbers) {
        if (batch.getType().equals(Batch.Type.SHOPPING)) {
            gosendAsyncService.placeOrderInOneBatch(batch.getId().longValue(), shipmentNumbers);
        }
    }

    public void placeOrQueueGosendDeliveries(Batch batch, Shipment shipment, GosendService.ServiceType serviceType) {
        if (batch.getType().equals(Batch.Type.SHOPPING)) {
            gosendAsyncService.placeOrderByShipment(batch.getId().longValue(), shipment.getId().longValue(), serviceType);
        }
    }

    private void placeOrQueueGrabExpressDeliveries(List<Shipment> shipments) {
        for (Shipment shipment : shipments) {
            Optional<Job> deliveryJob = shipment.getDeliveryJob();

            if (deliveryJob.isPresent() && deliveryJob.get().getBatch().isGrabExpress()) {
                Optional<GrabExpressDelivery> grabExpressDelivery = grabExpressDeliveryRepository.findAllByShipmentNumber(shipment.getNumber()).stream().max(Comparator.comparing(GrabExpressDelivery::getId));
                if (grabExpressDelivery.isPresent()) {
                    grabExpressService.scheduleGrabBooking(grabExpressDelivery.get());
                }
            }
        }
    }

    public void placeOrQueueTPLDeliveries(Long batchId, BatchItemFinalizeForm form) {
        Batch batch = batchRepository.fetchById(batchId);
        List<Shipment> shipments = shipmentRepository.findByNumberIn(form.getShipmentNumbers());
        placeOrQueueLalamoveDeliveries(batch);
        placeOrQueueDelyvaDeliveries(batch);
        placeOrQueueGosendDeliveries(batch, form.getShipmentNumbers());
        placeOrQueueGrabExpressDeliveries(shipments);
    }
}
