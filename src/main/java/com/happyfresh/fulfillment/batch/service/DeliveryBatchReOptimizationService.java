package com.happyfresh.fulfillment.batch.service;

import com.happyfresh.fulfillment.entity.Country;
import com.happyfresh.fulfillment.entity.Tenant;
import com.happyfresh.fulfillment.repository.CountryRepository;
import com.happyfresh.fulfillment.repository.ShiftRepository;
import com.happyfresh.fulfillment.repository.TenantRepository;
import com.happyfresh.fulfillment.slot.bean.SlotOptimizationContext;
import com.happyfresh.fulfillment.slot.service.SlotOptimizationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class DeliveryBatchReOptimizationService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private SlotOptimizationService slotOptimizationService;

    @Autowired
    private ShiftRepository shiftRepository;

    @Transactional(readOnly = true)
    public void reOptimize(String countryIsoName, int periodInMinutes) {
        for (Tenant tenant : tenantRepository.findAll()) {
            Country country = countryRepository.findByIsoNameAndTenantId(countryIsoName, tenant.getId());
            if (country != null)
                reOptimize(country, periodInMinutes);
        }
    }

    private void reOptimize(Country country, int periodInMinutes) {
        // 1. Get eligible delivery shifts (count > 0 AND within scheduler period)
        // 2. Get related shopping shifts and last slot based on delivery batch within delivery shift
        LocalDateTime start = LocalDateTime.now();
        LocalDateTime end = start.plusMinutes(periodInMinutes);
        List<Object[]> queryResults = shiftRepository.findRelatedShopperShiftAndLastSlot(country.getId(), start, end);
        Tenant tenant = country.getTenant();

        for (Object[] res : queryResults) {
            Long driverShiftId = ((BigInteger) res[0]).longValue();
            Long shopperShiftId = res[1] == null ? 0 : ((BigInteger) res[1]).longValue();
            Long lastSlotId = ((BigInteger) res[2]).longValue();
            Long clusterId = ((BigInteger) res[3]).longValue();
            LocalDateTime lastSlotStartTime = ((Timestamp) res[4]).toLocalDateTime();
            String timeZone = (String) res[5];

            SlotOptimizationContext context = new SlotOptimizationContext(lastSlotId, shopperShiftId, clusterId, driverShiftId, lastSlotStartTime, false, true, timeZone, tenant.isEnableSlotOptimizationEventDeduplication());
            slotOptimizationService.publishSlotOptimization(context);

            logger.info("[DeliveryBatchReOptimizationScheduler] Slot Optimization event published for " +
                    "cluster_id: {}, driver shift_id: {}, shopper shift_id: {}, slot_id: {}",
                    clusterId, driverShiftId, shopperShiftId, lastSlotId);
        }
    }

}
