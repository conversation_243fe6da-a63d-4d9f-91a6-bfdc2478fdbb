package com.happyfresh.fulfillment.batch.service;

import com.happyfresh.fulfillment.batch.presenter.tracking.MarkItemPayload;
import com.happyfresh.fulfillment.batch.presenter.tracking.SwitchJobPayload;
import com.happyfresh.fulfillment.common.tracking.EventTrackingService;
import com.happyfresh.fulfillment.common.tracking.lambda.LambdaEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class SndTrackingService {

    private static final String SWITCH_JOB_EVENT_NAME = "SWITCH_JOB";

    private EventTrackingService eventTrackingService;

    @Autowired
    public SndTrackingService(EventTrackingService eventTrackingService) {
        this.eventTrackingService = eventTrackingService;
    }

    @Async
    public void trackSwitchJob(Long batchId, Long userId, SwitchJobPayload.Operation operation) {
        SwitchJobPayload payload = new SwitchJobPayload(userId, batchId, operation, LocalDateTime.now());
        LambdaEvent lambdaEvent = new LambdaEvent<>(SWITCH_JOB_EVENT_NAME, payload);
        lambdaEvent.setUniqueName(String.valueOf(batchId));
        this.eventTrackingService.track(lambdaEvent);
    }

    @Async
    public void trackItemFound(Long batchId, Long userId, String sku, String orderNumber, MarkItemPayload.Event event, MarkItemPayload.Operation operation) {
        MarkItemPayload payload = new MarkItemPayload(LocalDateTime.now());
        payload.setBatchId(batchId);
        payload.setUserId(userId);
        payload.setItemSku(sku);
        payload.setOrderNumber(orderNumber);
        payload.setOperation(operation);

        LambdaEvent lambdaEvent = new LambdaEvent<>(event.name(), payload);
        lambdaEvent.setUniqueName(sku);
        this.eventTrackingService.track(lambdaEvent);
    }

}
