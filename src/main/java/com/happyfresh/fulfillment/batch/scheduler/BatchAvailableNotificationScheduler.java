package com.happyfresh.fulfillment.batch.scheduler;

import com.happyfresh.fulfillment.batch.service.BatchAvailableNotificationService;
import net.javacrumbs.shedlock.core.LockAssert;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(name = "scheduler.enabled", matchIfMissing = true)
public class BatchAvailableNotificationScheduler {

    @Autowired
    BatchAvailableNotificationService service;

    @Scheduled(cron = "0 */10 * * * *")
    @SchedulerLock(name = "shopperSchedulerID", lockAtMostFor = "9m", lockAtLeastFor = "9m")
    public void shopperSchedulerID() {
        LockAssert.assertLocked();
        service.sendShoppingNotification("ID");
    }

    // Turned off due to MY stop operating
    // @Scheduled(cron = "5 */10 * * * *")
    @SchedulerLock(name = "shopperSchedulerMY", lockAtMostFor = "9m", lockAtLeastFor = "9m")
    public void shopperSchedulerMY() {
        LockAssert.assertLocked();
        service.sendShoppingNotification("MY");
    }

    // Turned off due to TH stop operating
    // @Scheduled(cron = "10 */10 * * * *")
    @SchedulerLock(name = "shopperSchedulerTH", lockAtMostFor = "9m", lockAtLeastFor = "9m")
    public void shopperSchedulerTH() {
        LockAssert.assertLocked();
        service.sendShoppingNotification("TH");
    }

    @Scheduled(cron = "15 */10 * * * *")
    @SchedulerLock(name = "driverSchedulerID", lockAtMostFor = "9m", lockAtLeastFor = "9m")
    public void driverSchedulerID() {
        LockAssert.assertLocked();
        service.sendDeliveryNotification("ID");
    }

    // Turned off due to MY stop operating
    // @Scheduled(cron = "20 */10 * * * *")
    @SchedulerLock(name = "driverSchedulerMY", lockAtMostFor = "9m", lockAtLeastFor = "9m")
    public void driverSchedulerMY() {
        LockAssert.assertLocked();
        service.sendDeliveryNotification("MY");
    }

    // Turned off due to TH stop operating
    // @Scheduled(cron = "25 */10 * * * *")
    @SchedulerLock(name = "driverSchedulerTH", lockAtMostFor = "9m", lockAtLeastFor = "9m")
    public void driverSchedulerTH() {
        LockAssert.assertLocked();
        service.sendDeliveryNotification("TH");
    }
}
