server.port=${PORT:8080}

# Logging Configuration Variables
logging.config=classpath:log4j2-${FSRV_LOGGING_PROFILE:development}.xml
logz.io.token=${FSRV_LOGZ_IO_TOKEN:}
sentry.environment=${SENTRY_ENVIRONMENT:local}
sentry.stacktrace.app.packages=${SENTRY_STACKTRACE_APP_PACKAGES:}
sentry.dsn=${SENTRY_DSN:}

# Redis Configuration Variables
redis.url=${FSRV_REDIS_URL:redis://pub-redis-11394.ap-southeast-1-1.1.ec2.garantiadata.com:11394}
redis.password=${FSRV_REDIS_PASSWORD:talavera16}
redis.namespace=${FSRV_REDIS_NAMESPACE:staging}

# Data Migration Configuration Variables
spring.flyway.baseline-on-migrate=true
spring.flyway.sql-migration-prefix=v
spring.flyway.sql-migration-suffixes=.sql
spring.flyway.out-of-order=true

# Elasticsearch Configuration Variables #
spring.elasticsearch.jest.uris=${FSRV_ES_CLUSTER_NODES:https://eb843037.qb0x.com:31954}
spring.elasticsearch.jest.prefix=${FSRV_ES_PREFIX:staging_2}
spring.elasticsearch.jest.username=${FSRV_ES_USERNAME:70893f64053100044e18}
spring.elasticsearch.jest.password=${FSRV_ES_PASSWORD:45b7daa543}

# Data Source Configuration Variables #
spring.datasource.url=${FSRV_DATABASE_URL_KEY:***********************************************************************************************}
spring.jpa.properties.hibernate.default_schema=${FSRV_JPA_DEFAULT_SCHEMA:public}
spring.jpa.show-sql=false

# File size limit
spring.servlet.multipart.max-file-size=10MB

# Security Configuration Variables
spring.security.filter.order=2

# Google
google.service.api-key=${FSRV_GOOGLE_SERVICE_API_KEY:AIzaSyBhD3-6KtWLKI3wp9L8AkusUdNITRirhAs}

# AWS SNS config
sns.prefix-arn=${FSRV_SNS_PREFIX_ARN:}
sns.topic-arn=${FSRV_SNS_TOPIC_ARN:}
sns.environment=${FSRV_SNS_ENV:development}


# AWS S3
s3.url=${FSRV_S3_URL:}
s3.bucket-name=${FSRV_S3_BUCKET_NAME:}
s3.receipt-key-prefix-name=${FSRV_S3_RECEIPT_KEY_PREFIX_NAME:}
s3.signature-key-prefix-name=${FSRV_S3_SIGNATURE_KEY_PREFIX_NAME:}
s3.attachment-key-prefix-name=${FSRV_S3_ATTACHMENT_KEY_PREFIX_NAME:}

# Cache
spring.cache.cache-names=default,expireAfterAccess10m
spring.cache.caffeine.spec=expireAfterWrite=3600s,expireAfterAccess=600s

# Grab Express
GRAB_API_URL=${FSRV_GRAB_API_URL_KEY:}
ID_GRAB_PARTNER_ID=${FSRV_ID_GRAB_PARTNER_ID_KEY:}
ID_GRAB_PARTNER_SECRET=${FSRV_ID_GRAB_PARTNER_SECRET_KEY:}
TH_GRAB_PARTNER_ID=${FSRV_TH_GRAB_PARTNER_ID_KEY:}
TH_GRAB_PARTNER_SECRET=${FSRV_TH_GRAB_PARTNER_SECRET_KEY:}
MY_GRAB_PARTNER_ID=${FSRV_MY_GRAB_PARTNER_ID_KEY:}
MY_GRAB_PARTNER_SECRET=${FSRV_MY_GRAB_PARTNER_SECRET_KEY:}

# Segment
segment.analytics.android-write-key:${SEGMENT_ANDROID_WRITE_KEY:V8dxZYAhkvjvKpNwZc3QiKBeRVWJ60wy}
segment.analytics.ios-write-key:${SEGMENT_IOS_WRITE_KEY:V8dxZYAhkvjvKpNwZc3QiKBeRVWJ60wy}
segment.analytics.web-write-key:${SEGMENT_WEB_WRITE_KEY:V8dxZYAhkvjvKpNwZc3QiKBeRVWJ60wy}
segment.analytics.mobileweb-write-key:${SEGMENT_MOBILEWEB_WRITE_KEY:V8dxZYAhkvjvKpNwZc3QiKBeRVWJ60wy}
segment.analytics.grabfresh-write-key:${SEGMENT_GRABFRESH_WRITE_KEY:V8dxZYAhkvjvKpNwZc3QiKBeRVWJ60wy}
segment.analytics.sprinkles-write-key:${SEGMENT_SPRINKLES_WRITE_KEY:V8dxZYAhkvjvKpNwZc3QiKBeRVWJ60wy}

# Active MQ
spring.activemq.broker-url=${FSRV_ACTIVEMQ_BROKER_URL:}
spring.activemq.user=${FSRV_ACTIVEMQ_USER:}
spring.activemq.password=${FSRV_ACTIVEMQ_PASSWORD:}
activemq.environment=${FSRV_ACTIVEMQ_ENVIRONMENT:}


# Braze
BRAZE_API_URL=${FSRV_BRAZE_API_URL_KEY:}
BRAZE_API_KEY=${FSRV_BRAZE_API_KEY:}
BRAZE_CAMPAIGN_KEY=${FSRV_BRAZE_CAMPAIGN_KEY:}

# API Documentation
API_DOCUMENTATION_USERNAME=${FSRV_API_DOCUMENTATION_USERNAME:admin}
API_DOCUMENTATION_PASSWORD=${FSRV_API_DOCUMENTATION_PASSWORD:admin}