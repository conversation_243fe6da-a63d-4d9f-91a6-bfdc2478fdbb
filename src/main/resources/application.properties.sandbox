server.port=${PORT:8080}

# Logging Configuration Variables
logging.config=classpath:log4j2-${FSRV_LOGGING_PROFILE:development}.xml
logz.io.token=${FSRV_LOGZ_IO_TOKEN:}
sentry.environment=${SENTRY_ENVIRONMENT:local}
sentry.stacktrace.app.packages=${SENTRY_STACKTRACE_APP_PACKAGES:}
sentry.dsn=${SENTRY_DSN:}

# Redis Configuration Variables
redis.url=${FSRV_REDIS_URL:redis://pub-redis-11394.ap-southeast-1-1.1.ec2.garantiadata.com:11394}
redis.password=${FSRV_REDIS_PASSWORD:talavera16}
redis.namespace=${FSRV_REDIS_NAMESPACE:sandbox}

# Data Migration Configuration Variables
spring.flyway.baseline-on-migrate=true
spring.flyway.sql-migration-prefix=v
spring.flyway.sql-migration-suffixes=.sql
spring.flyway.out-of-order=true

# Elasticsearch Configuration Variables #
spring.elasticsearch.jest.uris=${FSRV_ES_CLUSTER_NODES:https://search-fulfillment-staging-huxud6bxwbnua5j2tvlikpxvmm.ap-southeast-1.es.amazonaws.com}
spring.elasticsearch.jest.prefix=${FSRV_ES_PREFIX:sandbox}
spring.elasticsearch.jest.username=${FSRV_ES_USERNAME:70893f64053100044e18}
spring.elasticsearch.jest.password=${FSRV_ES_PASSWORD:45b7daa543}

# Data Source Configuration Variables #
spring.datasource.url=${FSRV_DATABASE_URL_KEY:*****************************************************************************************************************************************************************************}
spring.jpa.properties.hibernate.default_schema=${FSRV_JPA_DEFAULT_SCHEMA:public}
spring.jpa.show-sql=false

# File size limit
spring.servlet.multipart.max-file-size=10MB

# Security Configuration Variables
spring.security.filter.order=2

# Google
google.service.api-key=${FSRV_GOOGLE_SERVICE_API_KEY:AIzaSyBhD3-6KtWLKI3wp9L8AkusUdNITRirhAs}

# Graphhopper
graphhopper.api-key=${GRAPHHOPPER_API_KEY:************************************}

# Data 365
data365.service.api-key=${FSRV_DATA365_SERVICE_API_KEY:KeiN3iegei4eequa3chaikaithai4}

# AWS SNS config
sns.prefix-arn=${FSRV_SNS_PREFIX_ARN:}
sns.topic-arn=${FSRV_SNS_TOPIC_ARN:}
sns.environment=${FSRV_SNS_ENV:development}


# AWS S3
s3.url=${FSRV_S3_URL:}
s3.bucket-name=${FSRV_S3_BUCKET_NAME:}
s3.receipt-key-prefix-name=${FSRV_S3_RECEIPT_KEY_PREFIX_NAME:}
s3.signature-key-prefix-name=${FSRV_S3_SIGNATURE_KEY_PREFIX_NAME:}
s3.attachment-key-prefix-name=${FSRV_S3_ATTACHMENT_KEY_PREFIX_NAME:}

# Cache
spring.cache.cache-names=default,expireAfterAccess10m
spring.cache.caffeine.spec=expireAfterWrite=3600s,expireAfterAccess=600s

# Grab Express
GRAB_API_URL=${FSRV_GRAB_API_URL_KEY:}
ID_GRAB_PARTNER_ID=${FSRV_ID_GRAB_PARTNER_ID_KEY:}
ID_GRAB_PARTNER_SECRET=${FSRV_ID_GRAB_PARTNER_SECRET_KEY:}
TH_GRAB_PARTNER_ID=${FSRV_TH_GRAB_PARTNER_ID_KEY:}
TH_GRAB_PARTNER_SECRET=${FSRV_TH_GRAB_PARTNER_SECRET_KEY:}
MY_GRAB_PARTNER_ID=${FSRV_MY_GRAB_PARTNER_ID_KEY:}
MY_GRAB_PARTNER_SECRET=${FSRV_MY_GRAB_PARTNER_SECRET_KEY:}

# Segment
segment.analytics.android-write-key:${SEGMENT_ANDROID_WRITE_KEY:a}
segment.analytics.ios-write-key:${SEGMENT_IOS_WRITE_KEY:a}
segment.analytics.web-write-key:${SEGMENT_WEB_WRITE_KEY:a}
segment.analytics.mobileweb-write-key:${SEGMENT_MOBILEWEB_WRITE_KEY:a}
segment.analytics.grabfresh-write-key:${SEGMENT_GRABFRESH_WRITE_KEY:a}
segment.analytics.sprinkles-write-key:${SEGMENT_SPRINKLES_WRITE_KEY:a}

# Active MQ
spring.activemq.broker-url=${FSRV_ACTIVEMQ_BROKER_URL:}
spring.activemq.user=${FSRV_ACTIVEMQ_USER:}
spring.activemq.password=${FSRV_ACTIVEMQ_PASSWORD:}
activemq.environment=${FSRV_ACTIVEMQ_ENVIRONMENT:}

# Kafka
spring.kafka.producer.bootstrap-servers=${FSRV_KAFKA_SERVER_URL:grey-barge-01.srvs.cloudkafka.com:9094}
spring.kafka.consumer.bootstrap-servers=${FSRV_KAFKA_SERVER_URL:grey-barge-01.srvs.cloudkafka.com:9094}
spring.kafka.consumer.group-id=${FSRV_KAFKA_CONSUMER_GROUP:internal_processing}
kafka.consumer.jubelio-group-id=${FSRV_KAFKA_JUBELIO_CONSUMER_GROUP:jubelio_processing_local}
kafka.consumer.resi-group-id=${FSRV_KAFKA_RESI_CONSUMER_GROUP:resi_processing_local}
kafka.authentication.username=${FSRV_KAFKA_USERNAME:stage}
kafka.authentication.password=${FSRV_KAFKA_PASSWORD:0f211074-ba4d-44ef-ba77-2c5d95fd727b}
kafka.provider=${FSRV_KAFKA_PROVIDER:CloudKarafka}

# Braze
BRAZE_API_URL=${FSRV_BRAZE_API_URL_KEY:}
BRAZE_API_KEY=${FSRV_BRAZE_API_KEY:}
BRAZE_CAMPAIGN_KEY=${FSRV_BRAZE_CAMPAIGN_KEY:}

# API Documentation
API_DOCUMENTATION_USERNAME=${FSRV_API_DOCUMENTATION_USERNAME:admin}
API_DOCUMENTATION_PASSWORD=${FSRV_API_DOCUMENTATION_PASSWORD:admin}

# AES CBC-128 symmetric encryption
encryption.secret-key=${FSRV_ENCRYPTION_SECRET_KEY:}
encryption.initialization-vector=${FSRV_ENCRYPTION_INITIALIZATION_VECTOR:}

# ResiId credentials
resi.email=${FSRV_RESI_EMAIL:}
resi.password=${FSRV_RESI_PASSWORD:}
resi.apiKey=${FSRV_RESI_API_KEY:}

# Jubelio
jubelio.source-id=${FSRV_JUBELIO_SOURCE_ID:}

