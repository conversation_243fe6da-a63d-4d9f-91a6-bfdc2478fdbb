CREATE TABLE hff_delivery_info (
    id BIGSERIAL,
    shipment_id BIGINT REFERENCES hff_shipment (id),
    cash_amount NUMERIC,
    signature_url VARCHAR(255),
    receiver VARCHAR(255) NOT NULL,
    lat DOUBLE PRECISION,
    lon <PERSON>O<PERSON><PERSON><PERSON> PRECISION,
    tenant_id BIGINT NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    created_by BIGINT NOT NULL,

    PRIMARY KEY (id)
);

CREATE INDEX index_delivery_info_on_shipment_id ON hff_delivery_info (shipment_id);
CREATE INDEX index_delivery_info_on_tenant_id ON hff_delivery_info (tenant_id);