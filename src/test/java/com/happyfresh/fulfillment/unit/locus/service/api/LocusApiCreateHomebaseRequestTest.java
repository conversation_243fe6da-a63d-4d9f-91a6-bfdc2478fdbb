package com.happyfresh.fulfillment.unit.locus.service.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.property.LocusProperty;
import com.happyfresh.fulfillment.entity.Role;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.integrationTest.test.common.NoResetRequestExpectationManager;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.locus.form.LocusHomebaseForm;
import com.happyfresh.fulfillment.locus.presenter.LocusHomebasePresenter;
import com.happyfresh.fulfillment.locus.service.api.LocusApiCreateHomebaseRequest;
import com.happyfresh.fulfillment.locus.service.api.LocusApiFormService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.Optional;

import static java.nio.file.Files.readAllBytes;
import static java.nio.file.Paths.get;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

@RunWith(MockitoJUnitRunner.class)
public class LocusApiCreateHomebaseRequestTest {

    private MockRestServiceServer mockServer;
    private LocusApiCreateHomebaseRequest request;
    private LocusApiFormService locusApiFormService;

    @Before
    public void setup() {
        locusApiFormService = new LocusApiFormService();

        LocusProperty locusProperty = new LocusProperty();
        locusProperty.setClientId("happyfresh");
        locusProperty.setApiKey("12345");
        locusProperty.setBaseUrl("https://locusapitest.com/v1/client/happyfresh");

        RestTemplate template = new RestTemplate();
        mockServer = MockRestServiceServer.bindTo(template).build(new NoResetRequestExpectationManager());

        User user = new UserFactory().createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        StockLocation stockLocation = new StockLocationFactory().createNStockLocationsOnTheSameCluster(1, user).get(0);
        stockLocation.setId(1L);
        ObjectMapper mapper = new ObjectMapper();
        LocusHomebaseForm form = locusApiFormService
                .createLocusHomebaseForm(stockLocation, locusProperty.getClientId(), Collections.singletonList("Team1"));

        request = new LocusApiCreateHomebaseRequest(form, mapper);
        request.setLocusProperty(locusProperty);
        request.setRestTemplate(template);
    }

    @Test
    public void shouldCallCreateHomebaseApi() {
        String url = "https://locusapitest.com/v1/client/happyfresh/homebase/homebase_1";
        mockServer.expect(ExpectedCount.once(), requestTo(url))
                .andExpect(method(HttpMethod.PUT))
                .andRespond(withSuccess("{ \"status\" : \"ok\" }", MediaType.APPLICATION_JSON));

        request.makeRequest();
        mockServer.verify();
    }

    @Test
    public void shouldRespondWithOptional() throws Exception{
        byte[] response = readAllBytes(get("src", "test", "resources", "fixtures", "locus_create_homebase_response.json"));
        String url = "https://locusapitest.com/v1/client/happyfresh/homebase/homebase_1";
        mockServer.expect(ExpectedCount.once(), requestTo(url))
                .andExpect(method(HttpMethod.PUT))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));

        Optional<LocusHomebasePresenter> maybePresenter = request.makeRequest();

        mockServer.verify();
        Assert.assertTrue(maybePresenter.isPresent());
        Assert.assertEquals("homebase_1", maybePresenter.get().getId());
    }

    @Test
    public void withResponseErrorShouldReturnOptionalEmpty() throws Exception{
        String url = "https://locusapitest.com/v1/client/happyfresh/homebase/homebase_1";
        mockServer.expect(ExpectedCount.once(), requestTo(url))
                .andExpect(method(HttpMethod.PUT))
                .andRespond(withStatus(HttpStatus.UNPROCESSABLE_ENTITY)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body("{ \"status\": \"error\" }")
                );

        Optional<LocusHomebasePresenter> maybePresenter = request.makeRequest();

        mockServer.verify();
        Assert.assertFalse(maybePresenter.isPresent());
    }

}
