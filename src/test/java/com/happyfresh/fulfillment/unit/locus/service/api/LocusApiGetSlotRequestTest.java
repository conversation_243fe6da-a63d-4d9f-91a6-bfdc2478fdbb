package com.happyfresh.fulfillment.unit.locus.service.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.property.LocusProperty;
import com.happyfresh.fulfillment.entity.Cluster;
import com.happyfresh.fulfillment.integrationTest.test.common.NoResetRequestExpectationManager;
import com.happyfresh.fulfillment.locus.form.LocusGetSlotForm;
import com.happyfresh.fulfillment.locus.model.*;
import com.happyfresh.fulfillment.locus.presenter.LocusGetSlotPresenter;
import com.happyfresh.fulfillment.locus.service.api.LocusApiGetSlotRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.Collections;
import java.util.Optional;

import static java.nio.file.Files.readAllBytes;
import static java.nio.file.Paths.get;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

@RunWith(MockitoJUnitRunner.class)
public class LocusApiGetSlotRequestTest {

    private LocusProperty locusProperty;
    private ObjectMapper mapper;
    private Cluster cluster;
    private LocusGetSlotForm form;
    private RestTemplate template;
    private MockRestServiceServer mockServer;
    private LocusApiGetSlotRequest request;
    private String uri;

    @Before
    public void setup() {
        locusProperty = new LocusProperty();
        locusProperty.setClientId("happyfresh");
        locusProperty.setApiKey("12345");
        locusProperty.setBaseUrl("https://locusapitest.com/v1/client/happyfresh");

        mapper = new ObjectMapper();

        cluster = new Cluster();
        cluster.setId(1L);

        form = new LocusGetSlotForm();
        fillDefaultForm();

        template = new RestTemplate();

        mockServer = MockRestServiceServer.bindTo(template).build(new NoResetRequestExpectationManager());

        request = new LocusApiGetSlotRequest(form, "batch_1_20200303", "plan_1_20200303", mapper);
        request.setLocusProperty(locusProperty);
        request.setRestTemplate(template);

        uri = locusProperty.getBaseUrl() + "/batch/batch_1_20200303/plan/plan_1_20200303/spmd-task-slot";
    }

    @Test
    public void shouldCallGetSlotApi() throws Exception{
        mockServer.expect(ExpectedCount.once(),requestTo(new URI(uri)))
            .andExpect(method(HttpMethod.POST))
            .andRespond(withSuccess("{ \"status\": \"ok\" }", MediaType.APPLICATION_JSON));

        request.makeRequest();

        mockServer.verify();
    }

    @Test
    public void shouldRespondWithOptionalGetSlotPresenter() throws Exception{
        byte[] response = readAllBytes(get("src", "test", "resources", "fixtures", "locus_get_slot_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo(new URI(uri)))
            .andExpect(method(HttpMethod.POST))
            .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));

        Optional<LocusGetSlotPresenter> maybePresenter = request.makeRequest();

        mockServer.verify();
        Assert.assertTrue(maybePresenter.isPresent());

        LocusGetSlotPresenter presenter = maybePresenter.get();
        Assert.assertEquals("2020-02-25T03:00:00.000+0000", presenter.getSlotSuggestions().get(0).getStart());
        Assert.assertEquals("2020-02-25T04:00:00.000+0000", presenter.getSlotSuggestions().get(0).getEnd());
    }

    @Test
    public void withResponseErrorShouldReturnOptionalEmpty() throws Exception{
        mockServer.expect(ExpectedCount.once(), requestTo(new URI(uri)))
            .andExpect(method(HttpMethod.POST))
            .andRespond(withStatus(HttpStatus.UNPROCESSABLE_ENTITY)
                .contentType(MediaType.APPLICATION_JSON)
                .body("{ \"status\": \"error\" }")
            );

        Optional<LocusGetSlotPresenter> maybePresenter = request.makeRequest();

        mockServer.verify();
        Assert.assertFalse(maybePresenter.isPresent());
    }


    private void fillDefaultForm() {
        form.setHomebaseId("homebase_1");

        LocusLineItem item = new LocusLineItem();
        item.setId("masked");
        item.setName("masked");
        item.setQuantity(1);
        form.setLineItems(Collections.singletonList(item));

        LocusLatLng latLng = new LocusLatLng();
        latLng.setLat(0.1);
        latLng.setLng(0.2);
        form.setLatLng(latLng);

        form.setTaskType(LocusTaskType.DROP);

        form.setTransactionDuration(10);

        LocusTimeSlot slot = new LocusTimeSlot();
        slot.setStart("2020-03-04T10:00:00.444Z");
        slot.setEnd("2020-03-04T22:00:00.444Z");
        form.setSlot(slot);

        LocusVolume volume = new LocusVolume();
        volume.setUnit(LocusVolume.VolumeDefinition.METERS_CUBIC);
        volume.setValue(0.01);
        form.setVolume(volume);
    }

}
