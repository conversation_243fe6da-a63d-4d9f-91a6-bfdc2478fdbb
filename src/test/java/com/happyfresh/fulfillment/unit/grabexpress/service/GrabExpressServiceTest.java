package com.happyfresh.fulfillment.unit.grabexpress.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.batch.exception.SwitchToHFFailedException;
import com.happyfresh.fulfillment.batch.service.BatchService;
import com.happyfresh.fulfillment.common.service.JedisLockService;
import com.happyfresh.fulfillment.common.service.WebhookPublisherService;
import com.happyfresh.fulfillment.common.service.radar.RadarApiService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.grabExpress.exception.GrabExpressException;
import com.happyfresh.fulfillment.grabExpress.model.GrabExpressBookingResponse;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressAPI;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressService;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.GrabExpressDeliveryRepository;
import com.happyfresh.fulfillment.repository.JobRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class GrabExpressServiceTest {

    @InjectMocks
    private GrabExpressService grabExpressService;

    @Mock
    private ShipmentRepository shipmentRepository;

    @Mock
    private GrabExpressAPI grabExpressAPI;

    @Mock
    private GrabExpressDeliveryRepository grabExpressDeliveryRepository;

    @Mock
    private BatchRepository batchRepository;

    @Mock
    private JobRepository jobRepository;

    @Mock
    private RadarApiService radarApiService;

    @Mock
    private BatchService batchService;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private JedisLockService jedisLockService;

    @Mock
    private AbstractAuthenticationToken abstractAuthenticationToken;

    @Mock
    private WebhookPublisherService webhookPublisherService;

    @Rule
    public ExpectedException expectedEx = ExpectedException.none();

    @Spy
    private Logger logger;

    private User user;
    private User userSystemAdmin;
    private User shopper;
    private User driver;
    private Country country;
    private List<StockLocation> stockLocations;
    private StockLocation stockLocation;
    private List<Slot> slots;
    private Shipment shipment;
    private Batch shoppingBatch;
    private Batch deliveryBatch;
    private GrabExpressDelivery grabExpressDelivery;
    private BatchFactory batchFactory;
    private GrabExpressDeliveryFactory grabExpressDeliveryFactory;

    @Before
    public void setup() throws Exception {
        injectLogger();
        batchFactory = new BatchFactory();
        setupUser();
        setupStockLocation();
        setupSlot();
        setupShipmentAndBatch();
        setupGrabExpressDelivery();
    }

    private void injectLogger() throws Exception {
        Field loggerField = grabExpressService.getClass().getDeclaredField("LOGGER");
        loggerField.setAccessible(true);
        Field modifiersField = loggerField.getClass().getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(loggerField, loggerField.getModifiers() & ~Modifier.FINAL);
        loggerField.set(grabExpressService, logger);
    }

    private void setupUser() {
        UserFactory userFactory = new UserFactory();
        user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        userSystemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, user.getTenant());
        shopper = userFactory.createUserData(Role.Name.SHOPPER, user.getTenant());
        driver = userFactory.createUserData(Role.Name.DRIVER, user.getTenant());
    }

    private void setupStockLocation() {
        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user);
        stockLocation = stockLocations.get(0);

        country = stockLocation.getState().getCountry();
    }

    private void setupSlot() {
        SlotFactory slotFactory = new SlotFactory();
        slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, shopper);
    }

    private void setupShipmentAndBatch() {
        ShipmentFactory shipmentFactory = new ShipmentFactory();
        shipment = shipmentFactory.createShipmentWithBatch(slots.get(0), shopper, "H234567");

        shoppingBatch = batchFactory.createBatch(shopper, shipment, slots.get(0), Batch.Type.SHOPPING);
        shoppingBatch.setUser(shopper);

        deliveryBatch = batchFactory.createBatch(driver, shipment, slots.get(0), Batch.Type.DELIVERY);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.GRAB_EXPRESS);

        shipment.setJobs(Stream.concat(shoppingBatch.getJobs().stream(), deliveryBatch.getJobs().stream())
                .collect(Collectors.toList()));
    }

    private void setupGrabExpressDelivery() {
        grabExpressDeliveryFactory = new GrabExpressDeliveryFactory();
        grabExpressDelivery = grabExpressDeliveryFactory.createGrabExpressDelivery(shipment, user);
        grabExpressDelivery.setId(1L);
        grabExpressDelivery.setStatus(GrabExpressDelivery.Status.PICKING_UP);
        shipment.setTplDeliveries(Collections.singletonList(grabExpressDelivery));
    }

    @Test
    public void adminSwitchToHF(){
        grabExpressService.adminSwitchToHF(shipment, shipment.getDeliveryJob().get());
        verify(grabExpressAPI, atLeastOnce()).cancelDelivery(anyString(), anyString(), "");
        verify(grabExpressDeliveryRepository, atLeastOnce()).save(any(GrabExpressDelivery.class));
        verify(batchService, atLeastOnce()).switchBatchToHF(any(Job.class));
        // Radar service disabled
        verify(radarApiService, never()).createDeliveryGeofence(any(Shipment.class), any(Country.class));
    }

    @Test
    public void adminSwitchToHF_whenDeliveryJobNotGE(){
        Batch nonGEDeliveryBatch = batchFactory.createBatch(driver, shipment, slots.get(0), Batch.Type.DELIVERY);
        nonGEDeliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        nonGEDeliveryBatch.setTplType(Batch.TplType.LALAMOVE);
        shipment.setJobs(Stream.concat(shoppingBatch.getJobs().stream(), nonGEDeliveryBatch.getJobs().stream())
                .collect(Collectors.toList()));

        expectedEx.expect(SwitchToHFFailedException.class);
        expectedEx.expectMessage("Batch fleet type not Grab Express");
        grabExpressService.adminSwitchToHF(shipment, shipment.getDeliveryJob().get());
        verify(grabExpressAPI, never()).cancelDelivery(anyString(), anyString(), "");
        verify(grabExpressDeliveryRepository, never()).save(any(GrabExpressDelivery.class));
        verify(batchService, never()).switchBatchToHF(any(Job.class));
        verify(radarApiService, never()).createDeliveryGeofence(any(Shipment.class), any(Country.class));
    }

    @Test
    public void adminSwitchToHF_whenStatusNotCancelable_shouldDoUpdateBatchRecord(){
        grabExpressDelivery.setStatus(GrabExpressDelivery.Status.COMPLETED);
        grabExpressService.adminSwitchToHF(shipment, shipment.getDeliveryJob().get());
        verify(grabExpressAPI, never()).cancelDelivery(anyString(), anyString(), "");
        verify(grabExpressDeliveryRepository, atLeastOnce()).save(any(GrabExpressDelivery.class));
        verify(batchService, atLeastOnce()).switchBatchToHF(any(Job.class));
        // Radar service disabled
        verify(radarApiService, never()).createDeliveryGeofence(any(Shipment.class), any(Country.class));
    }

    @Test
    public void adminSwitchToHF_whenGrabExpressDeliveryNull_shouldDoUpdateBatchRecord(){
        shipment.setTplDeliveries(Collections.emptyList());
        grabExpressService.adminSwitchToHF(shipment, shipment.getDeliveryJob().get());
        verify(grabExpressAPI, never()).cancelDelivery(anyString(), anyString(), "");
        verify(grabExpressDeliveryRepository, never()).save(any(GrabExpressDelivery.class));
        verify(batchService, atLeastOnce()).switchBatchToHF(any(Job.class));
        // Radar service disabled
        verify(radarApiService, never()).createDeliveryGeofence(any(Shipment.class), any(Country.class));
    }

    @Test
    public void adminSwitchToHF_whenGrabExpressDeliveryStatusNull_shouldDoUpdateBatchRecord(){
        GrabExpressDelivery geDeliveryNullStatus = grabExpressDeliveryFactory.createGrabExpressDelivery(shipment, user);
        grabExpressDelivery.setId(1L);
        grabExpressDelivery.setStatus(null);
        shipment.setTplDeliveries(Collections.singletonList(geDeliveryNullStatus));

        grabExpressService.adminSwitchToHF(shipment, shipment.getDeliveryJob().get());
        verify(grabExpressAPI, never()).cancelDelivery(anyString(), anyString(), "");
        verify(grabExpressDeliveryRepository, atLeastOnce()).save(any(GrabExpressDelivery.class));
        verify(batchService, atLeastOnce()).switchBatchToHF(any(Job.class));
        // Radar service disabled
        verify(radarApiService, never()).createDeliveryGeofence(any(Shipment.class), any(Country.class));
    }

    @Test
    public void createGrabExpressBookingAsync() throws InterruptedException, JsonProcessingException {
        Long grabExpressDeliveryId = 1L;
        grabExpressDelivery.setStatus(null);
        Mockito.when(applicationContext.getBean(JedisLockService.class)).thenReturn(jedisLockService);
        Mockito.when(jedisLockService.lock(String.format("grab_express_booking_%s", grabExpressDeliveryId.toString()),1)).thenReturn(true);
        Mockito.when(grabExpressDeliveryRepository.getOne(grabExpressDeliveryId)).thenReturn(grabExpressDelivery);
        Mockito.when(shipmentRepository.findByNumber(anyString())).thenReturn(shipment);
        GrabExpressBookingResponse grabExpressBookingResponse = new GrabExpressBookingResponse();
        grabExpressBookingResponse.setStatus("PICKING_UP");
        ObjectMapper mapper = new ObjectMapper();

        ResponseEntity<?> responseEntity = new ResponseEntity<>(
                mapper.writeValueAsString(grabExpressBookingResponse),
                HttpStatus.OK
        );
        Mockito.when(grabExpressAPI.bookingGrabExpress(any(Shipment.class), any(Country.class), "")).thenReturn(responseEntity);
        grabExpressService.createGrabExpressBookingAsync(1L, abstractAuthenticationToken);
        Mockito.verify(grabExpressDeliveryRepository, Mockito.atLeastOnce()).save(any(GrabExpressDelivery.class));
    }

    @Test
    public void createGrabExpressBookingAsync_whenDeliveryStatusNotNull() throws InterruptedException, JsonProcessingException {
        Long grabExpressDeliveryId = 1L;
        grabExpressDelivery.setStatus(GrabExpressDelivery.Status.PICKING_UP);
        Mockito.when(applicationContext.getBean(JedisLockService.class)).thenReturn(jedisLockService);
        Mockito.when(jedisLockService.lock(String.format("grab_express_booking_%s", grabExpressDeliveryId.toString()),1)).thenReturn(true);
        Mockito.when(grabExpressDeliveryRepository.getOne(1L)).thenReturn(grabExpressDelivery);
        grabExpressService.createGrabExpressBookingAsync(grabExpressDeliveryId, abstractAuthenticationToken);
        Mockito.verify(logger, Mockito.atLeastOnce()).error(eq("Create booking async error"), (Throwable) any());
    }

    @Test
    public void createGrabExpressBooking_whenOrderStillInProgress(){
        grabExpressDelivery.setStatus(GrabExpressDelivery.Status.IN_DELIVERY);
        Mockito.when(shipmentRepository.findByNumber(anyString())).thenReturn(shipment);
        expectedEx.expect(GrabExpressException.class);
        expectedEx.expectMessage("This order still in progress");
        grabExpressService.createGrabExpressBooking(shipment.getNumber(), true);
    }

    @Test
    public void createGrabExpressBooking_whenHttpClientErrorException_withoutRetry(){
        grabExpressDelivery.setStatus(GrabExpressDelivery.Status.CANCELED);
        Mockito.when(shipmentRepository.findByNumber(anyString())).thenReturn(shipment);
        expectedEx.expect(GrabExpressException.class);
        expectedEx.expectMessage("Bad gateway");
        Mockito.when(grabExpressAPI.bookingGrabExpress(any(Shipment.class), any(Country.class), "")).thenThrow(new HttpClientErrorException(HttpStatus.BAD_GATEWAY, "Bad gateway", "Bad gateway".getBytes(), null));
        grabExpressService.createGrabExpressBooking(shipment.getNumber(), false);
    }

    @Test
    public void createGrabExpressBooking_whenHttpServerErrorException_withoutRetry(){
        grabExpressDelivery.setStatus(GrabExpressDelivery.Status.CANCELED);
        Mockito.when(shipmentRepository.findByNumber(anyString())).thenReturn(shipment);
        expectedEx.expect(GrabExpressException.class);
        expectedEx.expectMessage("Bad gateway");
        Mockito.when(grabExpressAPI.bookingGrabExpress(any(Shipment.class), any(Country.class), "")).thenThrow(new HttpServerErrorException(HttpStatus.BAD_GATEWAY, "Bad gateway", "Bad gateway".getBytes(), null));
        grabExpressService.createGrabExpressBooking(shipment.getNumber(), false);
    }

    @Test
    public void createGrabExpressBooking_whenHttpConnectTimeoutException_withoutRetry(){
        grabExpressDelivery.setStatus(GrabExpressDelivery.Status.CANCELED);
        Mockito.when(shipmentRepository.findByNumber(anyString())).thenReturn(shipment);
        expectedEx.expect(GrabExpressException.class);
        expectedEx.expectMessage("NPE");
        Mockito.when(grabExpressAPI.bookingGrabExpress(any(Shipment.class), any(Country.class), "")).thenThrow(new NullPointerException("NPE"));
        grabExpressService.createGrabExpressBooking(shipment.getNumber(), false);
    }

}
