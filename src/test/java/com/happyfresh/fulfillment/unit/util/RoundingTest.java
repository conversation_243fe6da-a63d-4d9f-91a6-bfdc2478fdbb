package com.happyfresh.fulfillment.unit.util;

import com.happyfresh.fulfillment.common.util.I18nUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

@RunWith(MockitoJUnitRunner.class)
public class RoundingTest {

    @Test
    public void expectRoundingNumberCase1() {
        BigDecimal paramNumber = BigDecimal.valueOf(52300);
        BigDecimal expectedNumber = BigDecimal.valueOf(53000);
        paramNumber = I18nUtil.roundToNearestThousand(paramNumber);

        Assert.assertEquals(expectedNumber, paramNumber);
    }

    @Test
    public void expectRoundingNumberCase2() {
        BigDecimal paramNumber = BigDecimal.valueOf(322300);
        BigDecimal expectedNumber = BigDecimal.valueOf(323000);
        paramNumber = I18nUtil.roundToNearestThousand(paramNumber);

        Assert.assertEquals(expectedNumber, paramNumber);
    }

    @Test
    public void expectRoundingNumberCase3() {
        BigDecimal paramNumber = BigDecimal.valueOf(7321);
        BigDecimal expectedNumber = BigDecimal.valueOf(8000);
        paramNumber = I18nUtil.roundToNearestThousand(paramNumber);

        Assert.assertEquals(expectedNumber, paramNumber);
    }

}
