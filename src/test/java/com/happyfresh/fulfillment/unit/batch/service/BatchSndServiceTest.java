package com.happyfresh.fulfillment.unit.batch.service;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.happyfresh.fulfillment.admin.form.AdminShipmentStatusUpdateForm;
import com.happyfresh.fulfillment.batch.form.*;
import com.happyfresh.fulfillment.batch.mapper.BatchMapperImpl;
import com.happyfresh.fulfillment.batch.mapper.ReceiptMapper;
import com.happyfresh.fulfillment.batch.presenter.BatchPresenter;
import com.happyfresh.fulfillment.batch.presenter.tracking.SwitchJobPayload;
import com.happyfresh.fulfillment.batch.service.*;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.exception.ApiError;
import com.happyfresh.fulfillment.common.exception.BadRequestException;
import com.happyfresh.fulfillment.common.exception.ResourceNotFoundException;
import com.happyfresh.fulfillment.common.exception.type.BatchAlreadyTakenException;
import com.happyfresh.fulfillment.common.exception.type.HasOnDemandShoppingAssignedException;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.service.FleetTrackingService;
import com.happyfresh.fulfillment.common.service.NotificationService;
import com.happyfresh.fulfillment.common.service.WebhookPublisherService;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.lalamove.service.LalamoveService;
import com.happyfresh.fulfillment.lezcash.service.LezCashService;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.shipment.mapper.DeliveryInfoMapper;
import com.happyfresh.fulfillment.shipment.mapper.ShipmentMapper;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentPresenter;
import com.happyfresh.fulfillment.shipment.service.CatalogService;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import com.happyfresh.fulfillment.shipment.service.ShipmentService;
import com.happyfresh.fulfillment.stockLocation.service.StockLocationService;
import com.happyfresh.fulfillment.tpl.delyva.service.DelyvaService;
import com.happyfresh.fulfillment.unit.PowerMockBase;
import com.happyfresh.fulfillment.user.service.AgentService;
import com.happyfresh.fulfillment.user.service.UserService;
import org.apache.commons.io.IOUtils;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.*;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.mock.web.MockMultipartFile;

import javax.persistence.EntityNotFoundException;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;

import static java.nio.file.Files.readAllBytes;
import static java.nio.file.Paths.get;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

public class BatchSndServiceTest extends PowerMockBase {

    @Mock
    private NotificationService notificationService;

    @Mock
    private ShipmentRepository shipmentRepository;

    @Mock
    private JobRepository jobRepository;

    @Mock
    private BatchRepository batchRepository;

    @Mock
    private StockLocationService stockLocationService;

    @InjectMocks
    private BatchSndService batchSndService;

    @InjectMocks
    private LezCashService lezCashService;

    @Mock
    private JobSndService jobSndService;

    @Mock
    private SndTrackingService sndTrackingService;

    @Mock
    private AgentService agentService;

    @Captor
    private ArgumentCaptor<LocalDateTime> lastActivityCaptor;

    @Mock
    private ReceiptMapper receiptMapper;

    @Mock
    private ShipmentService shipmentService;

    @Mock
    private ReceiptRepository receiptRepository;

    @Mock
    private DriverAutoAssignmentService driverAutoAssignmentService;

    @Mock
    private DeliveryInfoMapper deliveryInfoMapper;

    @Mock
    private DeliveryInfoRepository deliveryInfoRepository;

    @Mock
    private FleetTrackingService fleetTrackingService;

    @Mock
    private RoleRepository roleRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private UserService userService;

    @Mock
    private LalamoveService lalamoveService;

    @Mock
    private DelyvaService delyvaService;

    @Mock
    private TransactionHelper transactionHelper;

    @Mock
    private CatalogService catalogService;

    @Mock
    private ItemSndService itemSndService;

    @Mock
    private OrderService orderService;

    @Mock
    private GrabExpressDeliveryRepository grabExpressDeliveryRepository;

    @Mock
    private BatchMapperImpl batchMapper;

    @Mock
    private ObjectMapper mockMapper;

    @Mock
    private WebhookPublisherService webhookPublisherService;

    @Mock
    private ShipmentMapper shipmentMapper;

    @Mock
    private ItemReplacementPreferenceRepository itemReplacementPreferenceRepository;

    @Mock
    private ItemRepository itemRepository;

    @Rule
    public ExpectedException expectedEx = ExpectedException.none();

    private Shipment shipment1;
    private Shipment shipment2;
    private User admin;
    private StockLocation stockLocation;
    private UserFactory userFactory;
    private SlotFactory slotFactory;
    private ShiftFactory shiftFactory;
    private ShipmentFactory shipmentFactory;
    private BatchFactory batchFactory;
    private Shift shopperShift;
    private Shift deliveryShift;
    private User userShopper;
    private User userDriver;
    private Slot shoppingSlot;
    private Slot deliverySlot;
    private ObjectMapper mapper;
    private ItemFactory itemFactory;
    private GrabExpressDeliveryFactory grabExpressDeliveryFactory;

    @Before
    public void setup() {
        userFactory = new UserFactory();
        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        admin = userFactory.createUserData(Role.Name.ADMIN);
        userShopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());
        userDriver = userFactory.createUserData(Role.Name.DRIVER, admin.getTenant());
        stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin).get(0);

        Tenant tenant = admin.getTenant();
        tenant.setId(1L);

        shipmentFactory = new ShipmentFactory();
        slotFactory = new SlotFactory();
        shiftFactory = new ShiftFactory();
        batchFactory = new BatchFactory();

        Slot onDemandSlot1 = slotFactory.createOnDemandSlot(stockLocation, admin);
        Slot onDemandSlot2 = slotFactory.createOnDemandSlot(stockLocation, admin);
        shipment1 = shipmentFactory.createShipmentWithOnDemandRangerBatch(onDemandSlot1, admin, "R123");
        shipment2 = shipmentFactory.createShipmentWithOnDemandRangerBatch(onDemandSlot2, admin, "R456");

        batchFactory = new BatchFactory();
        mapper = new ObjectMapper();
        itemFactory = new ItemFactory();
        grabExpressDeliveryFactory = new GrabExpressDeliveryFactory();
    }

    private BatchItemFinalizeForm getBatchItemFinalizeForm() throws IOException {
        String json = getResponseFromResourceFileAsString("batch_shopping_finalize_request.json");
        return mapper.readValue(json, BatchItemFinalizeForm.class);
    }

    private String getResponseFromResourceFileAsString(String fileName) throws IOException {
        return new String(readAllBytes(get("src", "test", "resources", "fixtures", fileName)));
    }

    @Test
    public void testStartNextOnDemandJob_shouldStartNextJobButNotTriggerPushNotification() throws Exception {
        String shipmentNumber1 = "H123";
        shipment1.setNumber(shipmentNumber1);
        shipment1.setState(Shipment.State.READY);
        User odRanger = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, admin.getTenant());
        Job odRangerJob1 = shipment1.getDeliveryOrRangerJob().get();
        odRangerJob1.setId(1l);
        odRangerJob1.setTenant(admin.getTenant());
        odRangerJob1.setType(Job.Type.ON_DEMAND_RANGER);
        odRangerJob1.setState(Job.State.FINISHED);
        Batch odRangerBatch1 = odRangerJob1.getBatch();
        odRangerBatch1.setUser(odRanger);
        odRangerBatch1.setTenant(admin.getTenant());
        odRangerBatch1.setId(1l);

        String shipmentNumber2 = "H345";
        shipment2.setNumber(shipmentNumber2);
        shipment2.setState(Shipment.State.READY);
        Job odRangerJob2 = shipment2.getDeliveryOrRangerJob().get();
        odRangerJob2.setId(2l);
        odRangerJob2.setTenant(admin.getTenant());
        odRangerJob2.setType(Job.Type.ON_DEMAND_RANGER);
        odRangerJob2.setState(Job.State.INITIAL);
        Batch odRangerBatch2 = odRangerJob2.getBatch();
        odRangerBatch2.setUser(odRanger);
        odRangerBatch2.setTenant(admin.getTenant());
        odRangerBatch2.setId(2l);

        Mockito.doReturn(Lists.newArrayList(odRangerBatch2)).when(batchRepository).findActiveBatchesByTypesAndUser(anyList(), eq(odRanger), anyList());

        batchSndService.startNextOnDemandJob(odRangerJob1);

        Assert.assertEquals(Job.State.STARTED, odRangerJob2.getState());
        Mockito.verify(jobRepository, Mockito.atMost(1)).save(any(Job.class));
        Mockito.verify(notificationService, Mockito.never()).sendPushNotificationForNewOnDemandJob(anyLong(), anyString(), anyString(), anyString(), anyLong());
    }

    @Test
    public void testStartNextOnDemandJob_shouldNotStartNextJob_onOnDemandTypeDelivery() throws Exception {
        String shipmentNumber1 = "H123";
        shipment1.setNumber(shipmentNumber1);
        shipment1.setState(Shipment.State.READY);
        User odRanger = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, admin.getTenant());
        Job odRangerJob1 = shipment1.getDeliveryOrRangerJob().get();
        odRangerJob1.setId(1l);
        odRangerJob1.setTenant(admin.getTenant());
        odRangerJob1.setType(Job.Type.ON_DEMAND_RANGER);
        odRangerJob1.setState(Job.State.FINISHED);
        Batch odRangerBatch1 = odRangerJob1.getBatch();
        odRangerBatch1.setUser(odRanger);
        odRangerBatch1.setTenant(admin.getTenant());
        odRangerBatch1.setId(1l);

        String shipmentNumber2 = "H345";
        shipment2.setNumber(shipmentNumber2);
        shipment2.setState(Shipment.State.READY);
        Job odRangerJob2 = shipment2.getDeliveryOrRangerJob().get();
        odRangerJob2.setId(2l);
        odRangerJob2.setTenant(admin.getTenant());
        odRangerJob2.setType(Job.Type.ON_DEMAND_DRIVER);
        odRangerJob2.setState(Job.State.INITIAL);
        Batch odRangerBatch2 = odRangerJob2.getBatch();
        odRangerBatch2.setUser(odRanger);
        odRangerBatch2.setTenant(admin.getTenant());
        odRangerBatch2.setId(2l);

        Mockito.doReturn(Lists.newArrayList(odRangerBatch2)).when(batchRepository).findActiveBatchesByTypesAndUser(anyList(), eq(odRanger), anyList());

        batchSndService.startNextOnDemandJob(odRangerJob1);

        Assert.assertEquals(Job.State.INITIAL, odRangerJob2.getState());
        Mockito.verify(jobRepository, Mockito.never()).save(any(Job.class));
        Mockito.verify(notificationService, Mockito.never()).sendPushNotificationForNewOnDemandJob(anyLong(), anyString(), anyString(), anyString(), anyLong());
    }

    private void setupForViewActiveBatchesTest(StockLocation stockLocation) {
        shoppingSlot = slotFactory.createSlot(stockLocation, admin, 1, 0);
        deliverySlot = slotFactory.createLongerDeliverySlot(stockLocation, admin, LocalDateTime.now(), 1);
        shopperShift = shiftFactory.createShopperShifts(stockLocation, shoppingSlot.getStartTime(), 1, 5, 1, admin).get(0);
        shopperShift.setId(1L);
        deliveryShift = shiftFactory.createDriverShifts(stockLocation, deliverySlot.getStartTime(), 1, 5, 1, admin).get(0);
        deliveryShift.setId(2L);

    }

    private Shipment createShipment(Slot slot, int itemCount, String orderNumber) {
        ShipmentFactory shipmentFactory = new ShipmentFactory();
        ItemFactory itemFactory = new ItemFactory();

        Shipment shipment = shipmentFactory.createShipment(slot, admin, orderNumber, orderNumber);
        List<Item> items = itemFactory.createItems(shipment, admin, itemCount);
        shipment.setOrderCustomerMemberTier("silver");
        int i = 0;
        for (Item item : items) {
            if (i % 2 == 0) {
                item.setFoundQty(new Random().nextInt(2));
                item.setOosQty(item.getRequestedQty() - item.getFoundQty());
            }
            i++;
        }
        shipment.setItems(items);
        return shipment;
    }

    private Batch createBatch(List<Shipment> shipments, Slot slot, Batch.Type batchType, boolean isAssigned, Long batchId) {
        BatchFactory batchFactory = new BatchFactory();
        Batch batch = batchFactory.createBatch(admin, shipments, slot, batchType);
        if (batchType == Batch.Type.SHOPPING) {
            batch.setShift(shopperShift);
            if(isAssigned)
                batch.setUser(userShopper);
        } else if (batchType == Batch.Type.DELIVERY || batchType == Batch.Type.RANGER) {
            batch.setShift(deliveryShift);
            if(isAssigned)
                batch.setUser(userDriver);
        }
        batch.setId(batchId);
        return batch;
    }

    private LocalDateTime getLastFinishTime(Batch batch) {
        List<Job> jobs = batch.getJobs();
        String jobStateToCheck = jobs.get(0).isRanger() ? "finalizing" : "finished";
        return jobs.stream().map(j -> {
            if (j.getJobStates() == null)
                return null;
            return stringDateTimeToLocalDateTime(j.getJobStates().get(jobStateToCheck));
        }).filter(Objects::nonNull).max(LocalDateTime::compareTo).orElse(null);
    }

    private LocalDateTime stringDateTimeToLocalDateTime(String strLocalDateTime) {
        return DateTimeUtil.stringTimeStampToLocalDateTime(strLocalDateTime);
    }

    private void addJobState(Batch batch, Job.State state) {
        for (Job job : batch.getJobs()) {
            job.setState(state);
        }
    }

    private void addJobState(Batch batch, String orderNumber, Job.State state) {
        for (Job job : batch.getJobs()) {
            if (job.getShipment().getOrderNumber().equals(orderNumber)) {
                job.setState(state);
            }
        }
    }

    private Receipt getReceipt(ReceiptV2Form form) {
        Receipt receipt = new Receipt();
        receipt.setId(1L);
        receipt.setNumber(form.getNumber());
        receipt.setPaymentMethod(form.getPaymentMethod());
        return receipt;
    }

    private ReceiptV2Form getReceiptForm(Shipment shipment1) {
        ReceiptV2Form form = new ReceiptV2Form();
        form.setNumber(shipment1.getNumber());
        form.setPaymentMethod(Receipt.PaymentMethod.CASH);
        return form;
    }

    @Test
    public void testStart_shopping_shouldUpdateLastActivityTimestamp() {
        setupForViewActiveBatchesTest(stockLocation);
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch shoppingBatch1 = createBatch(Arrays.asList(shipment1), shoppingSlot, Batch.Type.SHOPPING, false, 1L);
        Mockito.when(batchRepository.fetchById(shoppingBatch1.getId())).thenReturn(shoppingBatch1);


        batchSndService.start(1L, userShopper);
        Mockito.verify(agentService).updateLastActivityTime(eq(userDriver.getAgent()), lastActivityCaptor.capture());
        Assert.assertEquals(shoppingBatch1.getEndTime(), lastActivityCaptor.getValue());
    }

    @Test
    public void testStart_delivery_shouldUpdateLastActivityTimestamp() {
        setupForViewActiveBatchesTest(stockLocation);
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch shoppingBatch1 = createBatch(Arrays.asList(shipment1), shoppingSlot, Batch.Type.DELIVERY, false, 1L);
        Mockito.when(batchRepository.fetchById(shoppingBatch1.getId())).thenReturn(shoppingBatch1);


        batchSndService.start(1L, userShopper);
        Mockito.verify(agentService).updateLastActivityTime(eq(userDriver.getAgent()), lastActivityCaptor.capture());
        Assert.assertEquals(shoppingBatch1.getEndTime(), lastActivityCaptor.getValue());
    }

    @Test
    public void testStart_ranger_shouldUpdateLastActivityTimestamp() {
        setupForViewActiveBatchesTest(stockLocation);
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch shoppingBatch1 = createBatch(Arrays.asList(shipment1), shoppingSlot, Batch.Type.RANGER, false, 1L);
        Mockito.when(batchRepository.fetchById(shoppingBatch1.getId())).thenReturn(shoppingBatch1);


        batchSndService.start(1L, userShopper);
        Mockito.verify(agentService).updateLastActivityTime(eq(userDriver.getAgent()), lastActivityCaptor.capture());
        Assert.assertEquals(shoppingBatch1.getEndTime(), lastActivityCaptor.getValue());
    }

    @Test
    public void testStart_whenBatchIsAlreadyAssigned_andTypeIsOnDemand_shouldNotUpdateLastActivity() {
        setupForViewActiveBatchesTest(stockLocation);
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch shoppingBatch1 = createBatch(Arrays.asList(shipment1), shoppingSlot, Batch.Type.ON_DEMAND, true, 1L);
        Mockito.when(batchRepository.fetchById(shoppingBatch1.getId())).thenReturn(shoppingBatch1);

        batchSndService.start(1L, userShopper);
        Mockito.verify(agentService, Mockito.never()).updateLastActivityTime(any(Agent.class), any(LocalDateTime.class));
    }

    @Test(expected = EntityNotFoundException.class)
    public void testStart_whenBatchIsNull_shouldThrowException() {
        setupForViewActiveBatchesTest(stockLocation);
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch shoppingBatch1 = createBatch(Arrays.asList(shipment1), shoppingSlot, Batch.Type.SHOPPING, false, 1L);

        batchSndService.start(1L, userShopper);
        Mockito.verify(agentService, Mockito.never()).updateLastActivityTime(any(Agent.class), any(LocalDateTime.class));
    }

    @Test(expected = HasOnDemandShoppingAssignedException.class)
    public void testStart_whenFleetStillHasActiveBatch_shouldThrowException() {
        setupForViewActiveBatchesTest(stockLocation);
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch shoppingBatch1 = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, false, 1L);
        Mockito.when(batchRepository.fetchById(shoppingBatch1.getId())).thenReturn(shoppingBatch1);
        Shipment shipment2 = createShipment(shoppingSlot, 5, "order-002");
        Batch shoppingBatch2 = createBatch(List.of(shipment2), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Mockito.when(batchRepository.findActiveBatchesByTypeAndUser(any(), any(), anyList())).thenReturn(List.of(shoppingBatch2));

        batchSndService.start(1L, userShopper);
        Mockito.verify(agentService, Mockito.never()).updateLastActivityTime(any(Agent.class), any(LocalDateTime.class));
    }

    @Test
    public void testPay_whenAllJobFinished_shouldUpdateLastActivityWithLastFinishedJob() {
        setupForViewActiveBatchesTest(stockLocation);
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Shipment shipment2 = createShipment(shoppingSlot, 5, "order-002");
        Batch shoppingBatch1 = createBatch(List.of(shipment1, shipment2), shoppingSlot, Batch.Type.SHOPPING, true, 1L);

        addJobState(shoppingBatch1, Job.State.FINISHED);

        shipment1.setJobs(shoppingBatch1.getJobs());
        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);

        ReceiptV2Form form = getReceiptForm(shipment1);
        Receipt receipt = getReceipt(form);

        Mockito.when(receiptMapper.receiptFormToReceipt(form)).thenReturn(receipt);
        Mockito.when(batchRepository.getOne(shoppingBatch1.getId())).thenReturn(shoppingBatch1);

        batchSndService.pay(1L, shipment1.getNumber(), List.of(form));
        LocalDateTime lastFinishedTime = getLastFinishTime(shoppingBatch1);
        Mockito.verify(agentService).updateLastActivityTime(shoppingBatch1.getUser().getAgent(), lastFinishedTime);

    }

    @Test
    public void testPay_whenNotAllJobFinished_shouldNotUpdateLastActivityWithLastFinishedJob() {
        setupForViewActiveBatchesTest(stockLocation);
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Shipment shipment2 = createShipment(shoppingSlot, 5, "order-002");
        Batch shoppingBatch1 = createBatch(List.of(shipment1, shipment2), shoppingSlot, Batch.Type.SHOPPING, true, 1L);

        addJobState(shoppingBatch1, Job.State.STARTED);
        addJobState(shoppingBatch1, shipment1.getOrderNumber(), Job.State.FINISHED);

        shipment1.setJobs(shoppingBatch1.getJobs());
        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);

        ReceiptV2Form form = getReceiptForm(shipment1);
        Receipt receipt = getReceipt(form);

        Mockito.when(receiptMapper.receiptFormToReceipt(form)).thenReturn(receipt);
        Mockito.when(batchRepository.getOne(shoppingBatch1.getId())).thenReturn(shoppingBatch1);

        batchSndService.pay(1L, shipment1.getNumber(), List.of(form));
        LocalDateTime lastFinishedTime = getLastFinishTime(shoppingBatch1);
        Mockito.verify(agentService, Mockito.never()).updateLastActivityTime(shoppingBatch1.getUser().getAgent(), lastFinishedTime);

    }

    @Test
    public void testPay_whenJobStateIsNull_shouldNotError() {
        setupForViewActiveBatchesTest(stockLocation);
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Shipment shipment2 = createShipment(shoppingSlot, 5, "order-002");
        Batch shoppingBatch1 = createBatch(List.of(shipment1, shipment2), shoppingSlot, Batch.Type.SHOPPING, true, 1L);

        addJobState(shoppingBatch1, Job.State.INITIAL);
        addJobState(shoppingBatch1, Job.State.FINISHED);

        shoppingBatch1.getJobs().get(0).setJobStates(null);
        shoppingBatch1.getJobs().get(1).setJobStates(null);

        shipment1.setJobs(shoppingBatch1.getJobs());
        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);

        ReceiptV2Form form = getReceiptForm(shipment1);
        Receipt receipt = getReceipt(form);

        Mockito.when(receiptMapper.receiptFormToReceipt(form)).thenReturn(receipt);
        Mockito.when(batchRepository.getOne(shoppingBatch1.getId())).thenReturn(shoppingBatch1);

        batchSndService.pay(1L, shipment1.getNumber(), List.of(form));
        LocalDateTime lastFinishedTime = getLastFinishTime(shoppingBatch1);
        Mockito.verify(agentService).updateLastActivityTime(shoppingBatch1.getUser().getAgent(), lastFinishedTime);

    }

    @Test
    public void testPay_whenBatchIsRanger_shouldChangeToAcceptedState() {
        setupForViewActiveBatchesTest(stockLocation);
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Shipment shipment2 = createShipment(shoppingSlot, 5, "order-002");
        Batch shoppingBatch1 = createBatch(List.of(shipment1, shipment2), shoppingSlot, Batch.Type.RANGER, true, 1L);

        addJobState(shoppingBatch1, Job.State.STARTED);
        addJobState(shoppingBatch1, Job.State.FINISHED);

        shipment1.setJobs(shoppingBatch1.getJobs());
        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);

        ReceiptV2Form form = getReceiptForm(shipment1);
        Receipt receipt = getReceipt(form);

        Mockito.when(receiptMapper.receiptFormToReceipt(form)).thenReturn(receipt);
        Mockito.when(batchRepository.getOne(shoppingBatch1.getId())).thenReturn(shoppingBatch1);

        batchSndService.pay(1L, shipment1.getNumber(), List.of(form));
        LocalDateTime lastFinishedTime = getLastFinishTime(shoppingBatch1);
        Mockito.verify(agentService, Mockito.never()).updateLastActivityTime(shoppingBatch1.getUser().getAgent(), lastFinishedTime);
        Mockito.verify(jobSndService).changeJobState(any(Job.class), eq(Job.State.ACCEPTED));

    }

    @Test
    public void testStart_whenShopperGetAssigned_onShopperAutoAssignmentEnable_jobShouldStart() {
        Map<String,String> pref = stockLocation.getPreferences();
        pref.put("enable_shopper_auto_assignment", "true");
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        shoppingSlot = slotFactory.createSlot(stockLocation, admin, 1, 0);
        Batch shoppingBatch1 = createBatch(Arrays.asList(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        shoppingBatch1.setUser(userShopper);
        Mockito.when(batchRepository.fetchById(shoppingBatch1.getId())).thenReturn(shoppingBatch1);

        batchSndService.start(1L, userShopper);
        Mockito.verify(batchRepository, Mockito.atLeastOnce()).save(any(Batch.class));
        Mockito.verify(sndTrackingService, Mockito.atLeastOnce()).trackSwitchJob(anyLong(), anyLong(), eq(SwitchJobPayload.Operation.START));
        Mockito.verify(agentService, Mockito.atLeastOnce()).updateLastActivityTime(eq(userShopper.getAgent()), lastActivityCaptor.capture());
        Mockito.verify(jobSndService, Mockito.atLeastOnce()).start(any(Job.class));
    }

    @Test
    public void testStart_whenShopperGetAssigned_onShopperAutoAssignmentDisabled_andNotManualAssignment_jobShouldAbleStart() {
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        shoppingSlot = slotFactory.createSlot(stockLocation, admin, 1, 0);
        Batch shoppingBatch1 = createBatch(Arrays.asList(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        shoppingBatch1.setUser(userShopper);
        Mockito.when(batchRepository.fetchById(shoppingBatch1.getId())).thenReturn(shoppingBatch1);

        batchSndService.start(1L, userShopper);
        Mockito.verify(batchRepository).save(any(Batch.class));
        Mockito.verify(sndTrackingService).trackSwitchJob(anyLong(), anyLong(), eq(SwitchJobPayload.Operation.START));
        Mockito.verify(agentService).updateLastActivityTime(eq(userShopper.getAgent()), lastActivityCaptor.capture());
        Assert.assertEquals(shoppingBatch1.getEndTime(), lastActivityCaptor.getValue());
        Mockito.verify(jobSndService).start(any(Job.class));
    }

    @Test
    public void testStart_whenShopperManualAssign_onShopperAutoAssignmentDisabled_jobShouldAbleStart() {
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        shoppingSlot = slotFactory.createSlot(stockLocation, admin, 1, 0);
        Batch shoppingBatch1 = createBatch(Arrays.asList(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        shoppingBatch1.setUser(userShopper);
        shoppingBatch1.updateFlags(Batch.FLAG_MANUAL_ASSIGNED, "true");
        shoppingBatch1.updateFlags(Batch.FLAG_MANUAL_ASSIGNMENT_REASON, "NA");
        shoppingBatch1.updateFlags(Batch.FLAG_MANUAL_ASSIGNMENT_TIMESTAMP, String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli()));
        Mockito.when(batchRepository.fetchById(shoppingBatch1.getId())).thenReturn(shoppingBatch1);

        batchSndService.start(1L, userShopper);
        Mockito.verify(batchRepository, Mockito.atLeastOnce()).save(any(Batch.class));
        Mockito.verify(sndTrackingService, Mockito.atLeastOnce()).trackSwitchJob(anyLong(), anyLong(), eq(SwitchJobPayload.Operation.START));
        Mockito.verify(agentService, Mockito.atLeastOnce()).updateLastActivityTime(eq(userShopper.getAgent()), lastActivityCaptor.capture());
        Mockito.verify(jobSndService, Mockito.atLeastOnce()).start(any(Job.class));
    }

    @Test(expected = BatchAlreadyTakenException.class)
    public void testStart_whenShopperGetAssigned_onShopperAutoAssignmentEnabled_withShopperOtherThanOwner() {
        Map<String,String> pref = stockLocation.getPreferences();
        pref.put("enable_shopper_auto_assignment", "true");
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        shoppingSlot = slotFactory.createSlot(stockLocation, admin, 1, 0);
        Batch shoppingBatch1 = createBatch(Arrays.asList(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        shoppingBatch1.setUser(userShopper);
        Mockito.when(batchRepository.fetchById(shoppingBatch1.getId())).thenReturn(shoppingBatch1);
        User userShopper1 = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());
        batchSndService.start(1L, userShopper1);
        Mockito.verify(batchRepository, Mockito.never()).save(any(Batch.class));
        Mockito.verify(sndTrackingService, Mockito.never()).trackSwitchJob(anyLong(), anyLong(), eq(SwitchJobPayload.Operation.START));
        Mockito.verify(agentService, Mockito.never()).updateLastActivityTime(eq(userShopper1.getAgent()), lastActivityCaptor.capture());
        Mockito.verify(jobSndService, Mockito.never()).start(any(Job.class));
    }

    @Test(expected = BatchAlreadyTakenException.class)
    public void testStart_whenDriverGetAssigned_onShopperAutoAssignmentEnabled() {
        Map<String,String> pref = stockLocation.getPreferences();
        pref.put("enable_shopper_auto_assignment", "true");
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        shoppingSlot = slotFactory.createSlot(stockLocation, admin, 1, 0);
        Batch shoppingBatch1 = createBatch(Arrays.asList(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);
        shoppingBatch1.setUser(userDriver);
        Mockito.when(batchRepository.fetchById(shoppingBatch1.getId())).thenReturn(shoppingBatch1);

        batchSndService.start(1L, userDriver);
        Mockito.verify(batchRepository, Mockito.never()).save(any(Batch.class));
        Mockito.verify(sndTrackingService, Mockito.never()).trackSwitchJob(anyLong(), anyLong(), eq(SwitchJobPayload.Operation.START));
        Mockito.verify(agentService, Mockito.never()).updateLastActivityTime(eq(userDriver.getAgent()), lastActivityCaptor.capture());
        Mockito.verify(jobSndService, Mockito.never()).start(any(Job.class));
    }

    @Test
    public void test_finishV2_shouldTriggerDriverAutoAssignment() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV2Form form = new DeliveryInfoV2Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV2FormToDeliveryInfo(any(DeliveryInfoV2Form.class))).thenReturn(deliveryInfo);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(true);

        batchSndService.finishV2(shipment1.getNumber(), form);

        Mockito.verify(driverAutoAssignmentService).publishAutoAssignmentEvent(anyString(), anyString(), any());
    }

    @Test
    public void test_finishV2_whenShiftIsNull_shouldSetShiftIdToZeroBeforeTriggerDriverAutoAssignment() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV2Form form = new DeliveryInfoV2Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);
        dBatch.setShift(null);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV2FormToDeliveryInfo(any(DeliveryInfoV2Form.class))).thenReturn(deliveryInfo);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(true);

        batchSndService.finishV2(shipment1.getNumber(), form);

        Mockito.verify(driverAutoAssignmentService).publishAutoAssignmentEvent(anyString(), eq("0"), any());
    }

    @Test
    public void test_finishV2_whenConfigIsFalse_shouldNotTriggerDriverAutoAssignment() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV2Form form = new DeliveryInfoV2Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "false");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV2FormToDeliveryInfo(any(DeliveryInfoV2Form.class))).thenReturn(deliveryInfo);

        batchSndService.finishV2(shipment1.getNumber(), form);

        Mockito.verify(driverAutoAssignmentService, Mockito.never()).publishAutoAssignmentEvent(anyString(), anyString(), any());
    }

    @Test
    public void test_finishV2_whenIsNotTheLastShipmentInBatch_shouldNotTriggerDriverAutoAssignment() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV2Form form = new DeliveryInfoV2Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV2FormToDeliveryInfo(any(DeliveryInfoV2Form.class))).thenReturn(deliveryInfo);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(false);

        batchSndService.finishV2(shipment1.getNumber(), form);

        Mockito.verify(driverAutoAssignmentService, Mockito.never()).publishAutoAssignmentEvent(anyString(), anyString(), any());
    }

    @Test
    public void test_finishV2_ranger_shouldTriggerDriverAutoAssignment() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV2Form form = new DeliveryInfoV2Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.RANGER, true, 1L);

        List<Job> jobs = new ArrayList<>(sBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV2FormToDeliveryInfo(any(DeliveryInfoV2Form.class))).thenReturn(deliveryInfo);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(true);

        batchSndService.finishV2(shipment1.getNumber(), form);

        Mockito.verify(driverAutoAssignmentService).publishAutoAssignmentEvent(anyString(), anyString(), any());
    }

    @Test
    public void test_finishV2_onDemand_shouldNotTriggerDriverAutoAssignment() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV2Form form = new DeliveryInfoV2Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.ON_DEMAND_SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.ON_DEMAND_DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV2FormToDeliveryInfo(any(DeliveryInfoV2Form.class))).thenReturn(deliveryInfo);

        batchSndService.finishV2(shipment1.getNumber(), form);

        Mockito.verify(driverAutoAssignmentService, Mockito.never()).publishAutoAssignmentEvent(anyString(), anyString(), any());
    }

    @Test
    public void test_failDelivery_shouldTriggerDriverAutoAssignment() {
        setupForViewActiveBatchesTest(stockLocation);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(true);

        batchSndService.failDelivery("order-001", "FAIL_DELIVERY_INCORRECT_SHIPPING_ADDRESS");

        Mockito.verify(driverAutoAssignmentService).publishAutoAssignmentEvent(anyString(), anyString(), any());
    }

    @Test
    public void test_failDelivery_whenShiftIsNull_shouldSetShiftIdToZeroBeforeTriggerDriverAutoAssignment() {
        setupForViewActiveBatchesTest(stockLocation);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);
        dBatch.setShift(null);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(true);

        batchSndService.failDelivery("order-001", "FAIL_DELIVERY_INCORRECT_SHIPPING_ADDRESS");

        Mockito.verify(driverAutoAssignmentService).publishAutoAssignmentEvent(anyString(), eq("0"), any());
    }

    @Test
    public void test_failDelivery_whenThereIsStillOnGoingShipment_shouldNotTriggerDriverAutoAssignment() {
        setupForViewActiveBatchesTest(stockLocation);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Shipment shipment2 = createShipment(shoppingSlot, 5, "order-002");
        Batch sBatch = createBatch(List.of(shipment1, shipment2), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1, shipment2), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(false);

        batchSndService.failDelivery("order-001", "FAIL_DELIVERY_INCORRECT_SHIPPING_ADDRESS");

        Mockito.verify(driverAutoAssignmentService, Mockito.never()).publishAutoAssignmentEvent(anyString(), anyString(), any());
    }

    @Test
    public void test_failDelivery_whenConfigIsSetFalse_shouldNotTriggerDriverAutoAssignment() {
        setupForViewActiveBatchesTest(stockLocation);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "false");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Shipment shipment2 = createShipment(shoppingSlot, 5, "order-002");
        Batch sBatch = createBatch(List.of(shipment1, shipment2), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1, shipment2), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);

        batchSndService.failDelivery("order-001", "FAIL_DELIVERY_INCORRECT_SHIPPING_ADDRESS");

        Mockito.verify(driverAutoAssignmentService, Mockito.never()).publishAutoAssignmentEvent(anyString(), anyString(), any());
    }

    @Test
    public void test_failDelivery_ranger_shouldTriggerDriverAutoAssignment() {
        setupForViewActiveBatchesTest(stockLocation);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.RANGER, true, 1L);

        List<Job> jobs = new ArrayList<>(sBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(true);

        batchSndService.failDelivery("order-001", "FAIL_DELIVERY_INCORRECT_SHIPPING_ADDRESS");

        Mockito.verify(driverAutoAssignmentService).publishAutoAssignmentEvent(anyString(), anyString(), any());
    }

    @Test
    public void testStart_whenDriverGetAssignedToDeliveryorRangerBatch_onDriverAutoAssignmentEnable_jobShouldStart() {
        Map<String,String> pref = stockLocation.getCluster().getPreferences();
        pref.put("enable_driver_auto_assignment", "true");
        Shipment shipment1 = createShipment(deliverySlot, 5, "order-001");
        deliverySlot = slotFactory.createSlot(stockLocation, admin, 1, 0);
        Batch driverBatch = createBatch(Arrays.asList(shipment1), deliverySlot, Batch.Type.DELIVERY, true, 1L);
        driverBatch.setUser(userDriver);
        Mockito.when(batchRepository.fetchById(driverBatch.getId())).thenReturn(driverBatch);

        batchSndService.start(1L, userDriver);
        Mockito.verify(batchRepository, Mockito.atLeastOnce()).save(any(Batch.class));
        Mockito.verify(sndTrackingService, Mockito.atLeastOnce()).trackSwitchJob(anyLong(), anyLong(), eq(SwitchJobPayload.Operation.START));
        Mockito.verify(agentService, Mockito.atLeastOnce()).updateLastActivityTime(eq(userDriver.getAgent()), lastActivityCaptor.capture());
        Mockito.verify(jobSndService, Mockito.atLeastOnce()).start(any(Job.class));

        Batch rangerBatch = createBatch(Arrays.asList(shipment1), deliverySlot, Batch.Type.RANGER, true, 2L);
        rangerBatch.setUser(userDriver);
        Mockito.when(batchRepository.fetchById(rangerBatch.getId())).thenReturn(rangerBatch);

        batchSndService.start(2L, userDriver);
        Mockito.verify(batchRepository, Mockito.atLeastOnce()).save(any(Batch.class));
        Mockito.verify(sndTrackingService, Mockito.atLeastOnce()).trackSwitchJob(anyLong(), anyLong(), eq(SwitchJobPayload.Operation.START));
        Mockito.verify(agentService, Mockito.atLeastOnce()).updateLastActivityTime(eq(userDriver.getAgent()), lastActivityCaptor.capture());
        Mockito.verify(jobSndService, Mockito.atLeastOnce()).start(any(Job.class));
    }

    @Test(expected = BatchAlreadyTakenException.class)
    public void testStart_whenDriverGetAssignedToDeliveryBatch_onDriverAutoAssignmentDisabled_jobShouldNotStart() {
        Shipment shipment1 = createShipment(deliverySlot, 5, "order-001");
        deliverySlot = slotFactory.createSlot(stockLocation, admin, 1, 0);
        Batch driverBatch = createBatch(Arrays.asList(shipment1), deliverySlot, Batch.Type.DELIVERY, true, 1L);
        driverBatch.setUser(userDriver);
        Mockito.when(batchRepository.fetchById(driverBatch.getId())).thenReturn(driverBatch);

        batchSndService.start(1L, userDriver);
        Mockito.verify(batchRepository, Mockito.never()).save(any(Batch.class));
        Mockito.verify(sndTrackingService, Mockito.never()).trackSwitchJob(anyLong(), anyLong(), eq(SwitchJobPayload.Operation.START));
        Mockito.verify(agentService, Mockito.never()).updateLastActivityTime(eq(userDriver.getAgent()), lastActivityCaptor.capture());
        Mockito.verify(jobSndService, Mockito.never()).start(any(Job.class));
    }

    @Test(expected = BatchAlreadyTakenException.class)
    public void testStart_whenDriverGetAssignedToRangerBatch_onDriverAutoAssignmentDisabled_jobShouldNotStart() {
        Shipment shipment1 = createShipment(deliverySlot, 5, "order-001");
        deliverySlot = slotFactory.createSlot(stockLocation, admin, 1, 0);
        Batch rangerBatch = createBatch(Arrays.asList(shipment1), deliverySlot, Batch.Type.RANGER, true, 1L);
        rangerBatch.setUser(userDriver);
        Mockito.when(batchRepository.fetchById(rangerBatch.getId())).thenReturn(rangerBatch);

        batchSndService.start(1L, userDriver);
        Mockito.verify(batchRepository, Mockito.never()).save(any(Batch.class));
        Mockito.verify(sndTrackingService, Mockito.never()).trackSwitchJob(anyLong(), anyLong(), eq(SwitchJobPayload.Operation.START));
        Mockito.verify(agentService, Mockito.never()).updateLastActivityTime(eq(userDriver.getAgent()), lastActivityCaptor.capture());
        Mockito.verify(jobSndService, Mockito.never()).start(any(Job.class));
    }

    @Test(expected = BatchAlreadyTakenException.class)
    public void testStart_whenDriverGetAssignedToDeliveryBatch_withDriverOtherThanOwner() {
        Map<String,String> pref = stockLocation.getCluster().getPreferences();
        pref.put("enable_driver_auto_assignment", "true");
        Shipment shipment1 = createShipment(deliverySlot, 5, "order-001");
        deliverySlot = slotFactory.createSlot(stockLocation, admin, 1, 0);
        Batch driverBatch = createBatch(Arrays.asList(shipment1), deliverySlot, Batch.Type.DELIVERY, true, 1L);
        driverBatch.setUser(userDriver);
        Mockito.when(batchRepository.fetchById(driverBatch.getId())).thenReturn(driverBatch);

        User driver1 = userFactory.createUserData(Role.Name.DRIVER, admin.getTenant());
        batchSndService.start(1L, driver1);
        Mockito.verify(batchRepository, Mockito.never()).save(any(Batch.class));
        Mockito.verify(sndTrackingService, Mockito.never()).trackSwitchJob(anyLong(), anyLong(), eq(SwitchJobPayload.Operation.START));
        Mockito.verify(agentService, Mockito.never()).updateLastActivityTime(eq(driver1.getAgent()), lastActivityCaptor.capture());
        Mockito.verify(jobSndService, Mockito.never()).start(any(Job.class));
    }

    @Test(expected = BatchAlreadyTakenException.class)
    public void testStart_whenDriverGetAssignedToRangerBatch_withDriverOtherThanOwner() {
        Map<String,String> pref = stockLocation.getCluster().getPreferences();
        pref.put("enable_driver_auto_assignment", "true");
        Shipment shipment1 = createShipment(deliverySlot, 5, "order-001");
        deliverySlot = slotFactory.createSlot(stockLocation, admin, 1, 0);
        Batch rangerBatch = createBatch(Arrays.asList(shipment1), deliverySlot, Batch.Type.RANGER, true, 1L);
        rangerBatch.setUser(userDriver);
        Mockito.when(batchRepository.fetchById(rangerBatch.getId())).thenReturn(rangerBatch);

        User driver1 = userFactory.createUserData(Role.Name.DRIVER, admin.getTenant());
        batchSndService.start(1L, driver1);
        Mockito.verify(batchRepository, Mockito.never()).save(any(Batch.class));
        Mockito.verify(sndTrackingService, Mockito.never()).trackSwitchJob(anyLong(), anyLong(), eq(SwitchJobPayload.Operation.START));
        Mockito.verify(agentService, Mockito.never()).updateLastActivityTime(eq(driver1.getAgent()), lastActivityCaptor.capture());
        Mockito.verify(jobSndService, Mockito.never()).start(any(Job.class));
    }

    @Test
    public void finalizeBatchByShipments_shoppingBatch() throws IOException {
        Mockito.doCallRealMethod().when(transactionHelper).withNewTransaction(Mockito.any(Runnable.class));

        Slot slot = slotFactory.createSlot(stockLocation, admin);
        Batch batch = batchFactory.createBatch(admin, slot, Batch.Type.SHOPPING);
        batch.setId(1L);
        Shipment shipment = shipmentFactory.createShipment(slot, admin, "H51788915003", "H51788915003");
        List<Item> items = itemFactory.createItems(shipment, admin, 2);
        items.get(0).setId(1L);
        items.get(0).setSku("item1-ID");
        items.get(1).setId(2L);
        items.get(1).setSku("item1-ID");
        shipment.setItems(items);

        BatchItemFinalizeForm form = getBatchItemFinalizeForm();

        Mockito.when(shipmentRepository.findByNumberIn(anyList())).thenReturn(List.of(shipment));
        Mockito.when(orderService.getOrderTotal(anyString())).thenReturn(5000.0);

        List<ApiError> apiErrors = batchSndService.finalizeBatchByShipments(form);

        Assert.assertEquals(0, apiErrors.size());

        verify(itemReplacementPreferenceRepository).deleteAllReplacementPreferencesOfReplacementItemsByShipmentId(shipment.getId());
        verify(itemRepository).deleteAllReplacementByShipment(shipment);

    }

    @Test
    public void finalizeBatchByShipments_withMissingSku_shouldReturnExceptions() throws IOException {
        Mockito.doCallRealMethod().when(transactionHelper).withNewTransaction(Mockito.any(Runnable.class));

        Slot slot = slotFactory.createSlot(stockLocation, admin);
        Batch batch = batchFactory.createBatch(admin, slot, Batch.Type.SHOPPING);
        batch.setId(1L);
        Shipment shipment = shipmentFactory.createShipment(slot, admin, "H51788915003", "H51788915003");
        List<Item> items = itemFactory.createItems(shipment, admin, 2);
        items.get(0).setId(1L);
        items.get(0).setSku("item1-ID");
        items.get(1).setId(2L);
        items.get(1).setSku("item2-ID");
        shipment.setItems(items);

        BatchItemFinalizeForm form = getBatchItemFinalizeForm();
        List<ItemFinalizeForm> items1 = form.getItems();
        items1.remove(0);

        Mockito.when(shipmentRepository.findByNumberIn(anyList())).thenReturn(List.of(shipment));
        Mockito.when(orderService.getOrderTotal(anyString())).thenReturn(5000.0);

        List<ApiError> apiErrors = batchSndService.finalizeBatchByShipments(form);

        Assert.assertEquals(1, apiErrors.size());
        ApiError apiError = apiErrors.get(0);
        Assert.assertEquals("Missing finalize item(s)", apiError.getMessage());
        Assert.assertEquals("item1-ID", apiError.getField());

        verify(itemReplacementPreferenceRepository).deleteAllReplacementPreferencesOfReplacementItemsByShipmentId(shipment.getId());
        verify(itemRepository).deleteAllReplacementByShipment(shipment);

    }

    @Test
    public void placeOrQueueTPLDeliveries_shoppingBatch_shouldCallPlaceOrderLalamoveAndDelyva() {
        User sysAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, admin.getTenant());
        Slot slot = slotFactory.createSlot(stockLocation, admin);
        Batch batch = batchFactory.createBatch(admin, slot, Batch.Type.SHOPPING);
        batch.setId(1L);
        Shipment shipment = shipmentFactory.createShipment(slot, admin, "order001", "order001");

        Mockito.when(batchRepository.fetchById(anyLong())).thenReturn(batch);
        Mockito.when(shipmentRepository.findByNumberIn(anyList())).thenReturn(List.of(shipment));
        Mockito.when(userRepository.findByRolesContainingAndTenantId(any(), anyLong())).thenReturn(sysAdmin);

        BatchItemFinalizeForm form = new BatchItemFinalizeForm();
        form.setShipmentNumbers(List.of("order001"));
        batchSndService.placeOrQueueTPLDeliveries(1L, form);

        Mockito.verify(delyvaService).placeOrderInOneBatchAsync(Mockito.eq(batch), any());
        Mockito.verify(lalamoveService).placeOrderInOneBatchAsync(Mockito.eq(batch), any());
    }

    @Test
    public void placeOrQueueTPLDeliveries_deliveryBatch_shouldNotCallPlaceOrderLalamoveAndDelyva() {
        Slot slot = slotFactory.createSlot(stockLocation, admin);
        Batch batch = batchFactory.createBatch(admin, slot, Batch.Type.DELIVERY);
        batch.setId(1L);
        Shipment shipment = shipmentFactory.createShipment(slot, admin, "order001", "order001");

        Mockito.when(batchRepository.fetchById(anyLong())).thenReturn(batch);
        Mockito.when(shipmentRepository.findByNumberIn(anyList())).thenReturn(List.of(shipment));

        BatchItemFinalizeForm form = new BatchItemFinalizeForm();
        form.setShipmentNumbers(List.of("order001"));
        batchSndService.placeOrQueueTPLDeliveries(1L, form);

        Mockito.verify(delyvaService, Mockito.never()).placeOrderInOneBatchAsync(Mockito.eq(batch), any());
        Mockito.verify(lalamoveService, Mockito.never()).placeOrderInOneBatchAsync(Mockito.eq(batch), any());
    }

    @Test(expected = ResourceNotFoundException.class)
    public void handleManualShipmentStatusUpdate_withInvalidOrderNumber_shouldThrow() {
        Mockito.when(shipmentRepository.findByNumber(anyString())).thenReturn(null);

        AdminShipmentStatusUpdateForm form = new AdminShipmentStatusUpdateForm("found_address", null);
        batchSndService.handleManualShipmentStatusUpdate("H111", form);
    }

    @Test(expected = BadRequestException.class)
    public void handleManualShipmentStatusUpdate_withInvalidFormStatus_shouldThrowBadRequest() {
        AdminShipmentStatusUpdateForm form = new AdminShipmentStatusUpdateForm("unknown_job_states", null);
        batchSndService.handleManualShipmentStatusUpdate("H111", form);
    }

    @Test
    public void handleManualShipmentStatusUpdate_foundAddress_shouldChangeJobState_shouldPublishWebhook() {
        deliverySlot = slotFactory.createLongerDeliverySlot(stockLocation, admin, LocalDateTime.now(), 1);
        deliveryShift = shiftFactory.createDriverShifts(stockLocation, deliverySlot.getStartTime(), 1, 5, 1, admin).get(0);
        deliveryShift.setId(2L);

        Shipment shipment = createShipment(shoppingSlot, 2, "order-001");
        Batch dBatch = batchFactory.createBatch(admin, shipment, deliverySlot, Batch.Type.DELIVERY, Job.State.DELIVERING);
        shipment.setJobs(dBatch.getJobs());
        Job dJob = dBatch.getJobs().get(0);

        Mockito.when(batchRepository.fetchById(dBatch.getId())).thenReturn(dBatch);
        Mockito.when(shipmentRepository.findByNumber(shipment.getNumber())).thenReturn(shipment);
        Mockito.when(shipmentService.findByNumber(shipment.getNumber())).thenReturn(shipment);
        Mockito.when(batchMapper.batchToBatchPresenter(dBatch)).thenReturn(new BatchPresenter());

        AdminShipmentStatusUpdateForm form = new AdminShipmentStatusUpdateForm("found_address", null);
        batchSndService.handleManualShipmentStatusUpdate(shipment.getNumber(), form);

        Mockito.verify(jobSndService, Mockito.times(1)).changeJobState(dJob, Job.State.FOUND_ADDRESS);
        Mockito.verify(webhookPublisherService, Mockito.times(1))
                .publish(eq(WebhookType.ARRIVE_SHIPMENT), any(), anyString(), eq(shipment.getTenant().getId()));
    }

    @Test(expected = EntityNotFoundException.class)
    public void failShopping_whenShipmentNotFound_shouldRaiseException() throws Exception {
        shoppingSlot = slotFactory.createLongerDeliverySlot(stockLocation, admin, LocalDateTime.now(), 1);
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);
        Mockito.when(shipmentRepository.findByNumber(shipment1.getNumber())).thenReturn(null);

        File file1 = new File("src/test/resources/fixtures/receipt1.jpg");
        FileInputStream input1 = new FileInputStream(file1);
        MockMultipartFile multipartFile = new MockMultipartFile("evidence", file1.getName(), "image/jpeg", IOUtils.toByteArray(input1));
        input1.close();

        CancellationForm cancellationForm = new CancellationForm();
        cancellationForm.setEvidence(multipartFile);
        cancellationForm.setReason("Out of stock");

        batchSndService.failShopping(shipment1.getNumber(), cancellationForm);
    }

    @Test
    public void failShopping_shouldCallShipmentCancel_andPublishWebhook() throws Exception {
        shoppingSlot = slotFactory.createLongerDeliverySlot(stockLocation, admin, LocalDateTime.now(), 1);
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);
        Mockito.when(shipmentRepository.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentMapper.shipmentToShipmentPresenter(shipment1)).thenReturn(new ShipmentPresenter());
        Mockito.when(mockMapper.convertValue(any(), eq(JsonNode.class))).thenReturn(null);

        File file1 = new File("src/test/resources/fixtures/receipt1.jpg");
        FileInputStream input1 = new FileInputStream(file1);
        MockMultipartFile multipartFile = new MockMultipartFile("evidence", file1.getName(), "image/jpeg", IOUtils.toByteArray(input1));
        input1.close();

        CancellationForm cancellationForm = new CancellationForm();
        cancellationForm.setEvidence(multipartFile);
        cancellationForm.setReason("Out of stock");

        batchSndService.failShopping(shipment1.getNumber(), cancellationForm);
        Mockito.verify(shipmentService, Mockito.times(1)).cancelByShopper(shipment1, cancellationForm, cancellationForm.getReason());
        Mockito.verify(webhookPublisherService, Mockito.times(1)).publish(eq(WebhookType.CANCEL_BY_SHOPPER), any(ObjectNode.class), any(String.class), eq(shipment1.getTenant().getId()));
    }

    @Test
    public void pending_shouldCallPendingDelivery_andTriggerFoundAddressToFTS() {
        setupForViewActiveBatchesTest(stockLocation);
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Shipment shipment2 = createShipment(shoppingSlot, 5, "order-002");
        Batch deliveryBatch = createBatch(List.of(shipment1, shipment2), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        addJobState(deliveryBatch, Job.State.DELIVERING);

        shipment1.setJobs(deliveryBatch.getJobs());
        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);


        batchSndService.pending("order-001");

        Mockito.verify(jobSndService).pendingDelivery(shipment1.getDeliveryJob().get());
        Mockito.verify(fleetTrackingService, never()).syncFleetTracking("order-001", FleetTrackingService.EventName.FOUND_ADDRESS);
    }

    @Test
    public void continueDelivery_shouldNotTriggerDeliveringToFTS() {
        setupForViewActiveBatchesTest(stockLocation);
        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Shipment shipment2 = createShipment(shoppingSlot, 5, "order-002");
        Batch deliveryBatch = createBatch(List.of(shipment1, shipment2), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        addJobState(deliveryBatch, Job.State.DELIVERING);
        for (int i = 0; i < deliveryBatch.getJobs().size(); i++) {
            deliveryBatch.getJobs().get(i).updateFlags(Job.FLAG_IS_PENDING, "true");
            String pendingTimestamp = String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli());
            deliveryBatch.getJobs().get(i).updateFlags(Job.FLAG_PENDING_TIMESTAMP, pendingTimestamp);
        }

        shipment1.setJobs(deliveryBatch.getJobs());
        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);

        batchSndService.continueDelivery("order-001");

        Mockito.verify(fleetTrackingService, never()).syncFleetTracking("order-001", FleetTrackingService.EventName.DELIVERING);
        Mockito.verify(batchRepository, times(1)).fetchById(anyLong());
    }

    @Test
    public void cancelJobByShipment_shoppingJobStarted_shouldEligibleToCancel() throws Exception {
        Slot shoppingSlot = slotFactory.createSlot(stockLocation, admin);
        Shipment shipment = shipmentFactory.createShipmentWithBatch(shoppingSlot, admin, "order-001");
        shipment.getShoppingJob().get().setState(Job.State.STARTED);
        shipment.getDeliveryJob().get().setState(Job.State.INITIAL);
        when(shipmentRepository.findByNumber(shipment.getNumber())).thenAnswer(new Answer(){
            private int count = 0;

            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                if (count == 0)
                    return shipment;

                count++;
                return invocationOnMock.getArguments()[0];
            }
        });

        Shipment result = batchSndService.cancelJobByShipment(shipment.getNumber());
        Batch batchResult = result.getShoppingJob().get().getBatch();
        Assert.assertNull(batchResult.getUser());
        verify(shipmentRepository, times(2)).findByNumber(anyString());
        verify(jobSndService, times(2)).cancelJob(any(Job.class), anyBoolean());
    }

    @Test
    public void cancelJobByShipment_shoppingJobInitial_shouldEligibleToCancel() throws Exception {
        Slot shoppingSlot = slotFactory.createSlot(stockLocation, admin);
        Shipment shipment = shipmentFactory.createShipmentWithBatch(shoppingSlot, admin, "order-001");
        shipment.getShoppingJob().get().setState(Job.State.INITIAL);
        shipment.getDeliveryJob().get().setState(Job.State.INITIAL);
        when(shipmentRepository.findByNumber(shipment.getNumber())).thenAnswer(new Answer(){
            private int count = 0;

            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                if (count == 0)
                    return shipment;

                count++;
                return invocationOnMock.getArguments()[0];
            }
        });

        Shipment result = batchSndService.cancelJobByShipment(shipment.getNumber());
        Batch batchResult = result.getShoppingJob().get().getBatch();
        Assert.assertNull(batchResult.getUser());
        verify(shipmentRepository, times(2)).findByNumber(anyString());
        verify(jobSndService, times(2)).cancelJob(any(Job.class), anyBoolean());
    }

    @Test
    public void cancelJobByShipment_shoppingJobFinished_shouldCancelJustDeliveryJob() throws Exception {
        Slot shoppingSlot = slotFactory.createSlot(stockLocation, admin);
        Shipment shipment = shipmentFactory.createShipmentWithBatch(shoppingSlot, admin, "order-001");
        shipment.getShoppingJob().get().setState(Job.State.FINISHED);
        shipment.getDeliveryJob().get().setState(Job.State.INITIAL);
        when(shipmentRepository.findByNumber(shipment.getNumber())).thenAnswer(new Answer(){
            private int count = 0;

            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                if (count == 0)
                    return shipment;

                count++;
                return invocationOnMock.getArguments()[0];
            }
        });

        Shipment result = batchSndService.cancelJobByShipment(shipment.getNumber());
        Batch batchResult = result.getShoppingJob().get().getBatch();
        Assert.assertNull(batchResult.getUser());
        verify(shipmentRepository, times(2)).findByNumber(anyString());
        verify(jobSndService, times(1)).cancelJob(any(Job.class), anyBoolean());

    }

    @Test
    public void test_finishV3_shouldTriggerDriverAutoAssignment() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV3Form form = new DeliveryInfoV3Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV3FormToDeliveryInfo(any(DeliveryInfoV3Form.class))).thenReturn(deliveryInfo);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(true);

        batchSndService.finishV3(shipment1.getNumber(), form);

        Mockito.verify(driverAutoAssignmentService).publishAutoAssignmentEvent(anyString(), anyString(), any());
    }

    @Test
    public void test_finishV3_whenShiftIsNull_shouldSetShiftIdToZeroBeforeTriggerDriverAutoAssignment() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV3Form form = new DeliveryInfoV3Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);
        dBatch.setShift(null);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV3FormToDeliveryInfo(any(DeliveryInfoV3Form.class))).thenReturn(deliveryInfo);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(true);

        batchSndService.finishV3(shipment1.getNumber(), form);

        Mockito.verify(driverAutoAssignmentService).publishAutoAssignmentEvent(anyString(), eq("0"), any());
    }

    @Test
    public void test_finishV3_whenConfigIsFalse_shouldNotTriggerDriverAutoAssignment() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV3Form form = new DeliveryInfoV3Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "false");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV3FormToDeliveryInfo(any(DeliveryInfoV3Form.class))).thenReturn(deliveryInfo);

        batchSndService.finishV3(shipment1.getNumber(), form);

        Mockito.verify(driverAutoAssignmentService, Mockito.never()).publishAutoAssignmentEvent(anyString(), anyString(), any());
    }

    @Test
    public void test_finishV3_whenIsNotTheLastShipmentInBatch_shouldNotTriggerDriverAutoAssignment() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV3Form form = new DeliveryInfoV3Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV3FormToDeliveryInfo(any(DeliveryInfoV3Form.class))).thenReturn(deliveryInfo);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(false);

        batchSndService.finishV3(shipment1.getNumber(), form);

        Mockito.verify(driverAutoAssignmentService, Mockito.never()).publishAutoAssignmentEvent(anyString(), anyString(), any());
    }

    @Test
    public void test_finishV3_ranger_shouldTriggerDriverAutoAssignment() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV3Form form = new DeliveryInfoV3Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.RANGER, true, 1L);

        List<Job> jobs = new ArrayList<>(sBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV3FormToDeliveryInfo(any(DeliveryInfoV3Form.class))).thenReturn(deliveryInfo);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(true);

        batchSndService.finishV3(shipment1.getNumber(), form);

        Mockito.verify(driverAutoAssignmentService).publishAutoAssignmentEvent(anyString(), anyString(), any());
    }

    @Test
    public void test_finishV3_onDemand_shouldNotTriggerDriverAutoAssignment() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV3Form form = new DeliveryInfoV3Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        Cluster cluster = stockLocation.getCluster();
        cluster.setId(1L);
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.ON_DEMAND_SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.ON_DEMAND_DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV3FormToDeliveryInfo(any(DeliveryInfoV3Form.class))).thenReturn(deliveryInfo);

        batchSndService.finishV3(shipment1.getNumber(), form);

        Mockito.verify(driverAutoAssignmentService, Mockito.never()).publishAutoAssignmentEvent(anyString(), anyString(), any());
    }

    @Test
    public void test_finishV3_whenLocationAccuracyIsProvided_shouldSaveTheInfo() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV3Form form = new DeliveryInfoV3Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        DeliveryInfoV3Form.LocationAccuracy locationAccuracy = new DeliveryInfoV3Form.LocationAccuracy();
        locationAccuracy.setIsAddressIncorrect(true);
        locationAccuracy.setIncorrectAddressReasons(Arrays.asList(
                DeliveryInfoV3Form.LocationAccuracy.WrongLocationReasonEnum.DELIVERY_LOCATION,
                DeliveryInfoV3Form.LocationAccuracy.WrongLocationReasonEnum.DELIVERY_INSTRUCTION
                ));
        form.setLocationAccuracy(locationAccuracy);

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV3FormToDeliveryInfo(any(DeliveryInfoV3Form.class))).thenReturn(deliveryInfo);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(true);

        batchSndService.finishV3(shipment1.getNumber(), form);

        Assert.assertTrue(shipment1.isAddressIncorrect());
        Assert.assertTrue(shipment1.getIncorrectLocationReasons().contains(DeliveryInfoV3Form.LocationAccuracy.WrongLocationReasonEnum.DELIVERY_LOCATION.toString()));
        Assert.assertTrue(shipment1.getIncorrectLocationReasons().contains(DeliveryInfoV3Form.LocationAccuracy.WrongLocationReasonEnum.DELIVERY_INSTRUCTION.toString()));
        Assert.assertFalse(shipment1.getIncorrectLocationReasons().contains(DeliveryInfoV3Form.LocationAccuracy.WrongLocationReasonEnum.ADDRESS_DETAIL.toString()));
    }

    @Test
    public void test_finishV3_whenAddressIsCorrect_shouldNotSaveIncorrectAddressReasons() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV3Form form = new DeliveryInfoV3Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        DeliveryInfoV3Form.LocationAccuracy locationAccuracy = new DeliveryInfoV3Form.LocationAccuracy();
        locationAccuracy.setIsAddressIncorrect(false);
        locationAccuracy.setIncorrectAddressReasons(Arrays.asList(
                DeliveryInfoV3Form.LocationAccuracy.WrongLocationReasonEnum.DELIVERY_LOCATION,
                DeliveryInfoV3Form.LocationAccuracy.WrongLocationReasonEnum.DELIVERY_INSTRUCTION
        ));
        form.setLocationAccuracy(locationAccuracy);

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV3FormToDeliveryInfo(any(DeliveryInfoV3Form.class))).thenReturn(deliveryInfo);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(true);

        batchSndService.finishV3(shipment1.getNumber(), form);

        Assert.assertFalse(shipment1.isAddressIncorrect());
        Assert.assertEquals("", shipment1.getIncorrectLocationReasons());
    }

    @Test
    public void test_finishV3_whenAddressIsIncorrect_andReasonIsNotProvided_shouldThrowBadRequestException() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV3Form form = new DeliveryInfoV3Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        DeliveryInfoV3Form.LocationAccuracy locationAccuracy = new DeliveryInfoV3Form.LocationAccuracy();
        locationAccuracy.setIsAddressIncorrect(true);
        form.setLocationAccuracy(locationAccuracy);

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV3FormToDeliveryInfo(any(DeliveryInfoV3Form.class))).thenReturn(deliveryInfo);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(true);

        expectedEx.expect(BadRequestException.class);
        expectedEx.expectMessage("Provide at least one reason when address is incorrect");

        batchSndService.finishV3(shipment1.getNumber(), form);

    }

    @Test
    public void test_finishV3_whenAddressIsIncorrect_andReasonIsEmpty_shouldThrowBadRequestException() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV3Form form = new DeliveryInfoV3Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        DeliveryInfoV3Form.LocationAccuracy locationAccuracy = new DeliveryInfoV3Form.LocationAccuracy();
        locationAccuracy.setIsAddressIncorrect(true);
        locationAccuracy.setIncorrectAddressReasons(new ArrayList<>());
        form.setLocationAccuracy(locationAccuracy);

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV3FormToDeliveryInfo(any(DeliveryInfoV3Form.class))).thenReturn(deliveryInfo);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(true);

        expectedEx.expect(BadRequestException.class);
        expectedEx.expectMessage("Provide at least one reason when address is incorrect");

        batchSndService.finishV3(shipment1.getNumber(), form);

    }

    @Test
    public void test_finishV3_whenAddressIsCorrect_andReasonIsNull_shouldProcess() throws Exception {
        setupForViewActiveBatchesTest(stockLocation);
        DeliveryInfoV3Form form = new DeliveryInfoV3Form();
        form.setLat(-6.351816);
        form.setLon(106.896186);
        form.setCashAmount(100000.0);
        form.setReceiver("Receiver");
        form.setAgeConsent(false);

        DeliveryInfoV3Form.LocationAccuracy locationAccuracy = new DeliveryInfoV3Form.LocationAccuracy();
        locationAccuracy.setIsAddressIncorrect(false);
        form.setLocationAccuracy(locationAccuracy);

        Shipment shipment1 = createShipment(shoppingSlot, 5, "order-001");
        Batch sBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.SHOPPING, true, 1L);
        Batch dBatch = createBatch(List.of(shipment1), shoppingSlot, Batch.Type.DELIVERY, true, 1L);

        List<Job> jobs = new ArrayList<>();
        jobs.addAll(sBatch.getJobs());
        jobs.addAll(dBatch.getJobs());

        shipment1.setJobs(jobs);

        Mockito.when(shipmentService.findByNumber(shipment1.getNumber())).thenReturn(shipment1);
        Mockito.when(shipmentRepository.save(any(Shipment.class))).thenReturn(shipment1);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        Mockito.when(deliveryInfoMapper.deliveryInfoV3FormToDeliveryInfo(any(DeliveryInfoV3Form.class))).thenReturn(deliveryInfo);
        Mockito.when(batchRepository.isTheLastShipmentInBatch(anyLong(), anyString(), anyList())).thenReturn(true);

        batchSndService.finishV3(shipment1.getNumber(), form);

        Assert.assertFalse(shipment1.isAddressIncorrect());
        Assert.assertEquals("", shipment1.getIncorrectLocationReasons());
    }

    @Test
    public void test_topUpLezCashBalance() throws Exception {
        User sysAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, admin.getTenant());
        Slot slot = slotFactory.createSlot(stockLocation, admin);
        Batch batch = batchFactory.createBatch(admin, slot, Batch.Type.SHOPPING);
        batch.setId(1L);
        Shipment shipment = shipmentFactory.createShipment(slot, admin, "order001", "order001");

        Mockito.when(batchRepository.fetchById(anyLong())).thenReturn(batch);
        Mockito.when(shipmentRepository.findByNumberIn(anyList())).thenReturn(List.of(shipment));
        Mockito.when(userRepository.findByRolesContainingAndTenantId(any(), anyLong())).thenReturn(sysAdmin);

        BatchItemFinalizeForm form = new BatchItemFinalizeForm();
        form.setShipmentNumbers(List.of("order001"));

        try {
            lezCashService.topUpAsync(batch.getId());
        } catch (Exception e) {
            System.out.println("FAILED" + e.getMessage());
        }
    }
}
