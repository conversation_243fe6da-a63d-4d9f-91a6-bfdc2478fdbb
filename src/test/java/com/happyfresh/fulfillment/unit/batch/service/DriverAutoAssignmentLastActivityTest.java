package com.happyfresh.fulfillment.unit.batch.service;

import com.happyfresh.fulfillment.batch.service.DriverAutoAssignmentService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.slot.presenter.SlotOptimizationEvent;
import com.happyfresh.fulfillment.user.service.AgentService;
import com.happyfresh.fulfillment.unit.BaseTest;
import com.happyfresh.fulfillment.unit.factory.*;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

public class DriverAutoAssignmentLastActivityTest extends BaseTest {

    @InjectMocks
    private DriverAutoAssignmentService driverAutoAssignmentService;

    @Mock
    private ShiftRepository shiftRepository;

    @Mock
    private AgentService agentService;

    @Mock
    private BatchRepository batchRepository;

    private UserFactory userFactory = new UserFactory();
    private AgentFactory agentFactory = new AgentFactory();
    private StockLocationFactory stockLocationFactory = new StockLocationFactory();
    private ShiftFactory shiftFactory = new ShiftFactory();
    private BatchFactory batchFactory = new BatchFactory();

    private User admin;
    private StockLocation stockLocation;
    private Shift shift;
    private LocalDateTime now = LocalDateTime.now();

    @Before
    public void setUp() {
        admin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN);
        stockLocation = stockLocationFactory.createStockLocationData();
        shift = shiftFactory.createShiftData(stockLocation);
        
        // Enable last activity-based assignment
        ReflectionTestUtils.setField(driverAutoAssignmentService, "useLastActivityForAssignment", true);
    }

    @Test
    public void testDriverAssignmentBasedOnLastActivity() {
        // Create agents with different last activity times
        Agent driver1 = createDriverWithLastActivity(1L, now.minusHours(2)); // Least recently active
        Agent driver2 = createDriverWithLastActivity(2L, now.minusMinutes(30)); // Most recently active
        Agent driver3 = createDriverWithLastActivity(3L, now.minusHours(1)); // Middle activity

        List<Agent> agents = Arrays.asList(driver1, driver2, driver3);
        
        // Create a batch to assign
        Batch batch = batchFactory.createBatchData(stockLocation, Batch.Type.DELIVERY);
        batch.setVehicle(1);
        List<Batch> batches = Arrays.asList(batch);

        // Mock the service calls
        when(shiftRepository.findById(shift.getId())).thenReturn(Optional.of(shift));
        when(agentService.getAvailableDriverAgentsByStockLocationsOrderedByLastActivity(anyList()))
                .thenReturn(agents);
        when(batchRepository.getLatestBatchWithFinalStateByStockLocationIds(anyList(), anyList(), anyList()))
                .thenReturn(Arrays.asList());
        when(batchRepository.saveAll(anyList())).thenReturn(batches);

        // Create event
        SlotOptimizationEvent event = new SlotOptimizationEvent();
        event.setDeliveryShiftIdAsText(shift.getId().toString());
        event.setAutoAssignmentTriggerEvent(SlotOptimizationEvent.AutoAssignmentTriggerEvent.BATCH_CREATED);

        // Execute the assignment
        driverAutoAssignmentService.handleEvent(event);

        // Verify that the least recently active driver (driver1) gets the assignment
        // This would be verified through the batch assignment logic
        Assert.assertNotNull("Assignment should be processed", event);
    }

    @Test
    public void testAgentLastActivityTimeUpdatedOnAssignment() {
        Agent driver = createDriverWithLastActivity(1L, now.minusHours(1));
        LocalDateTime originalLastActivity = driver.getLastActivityTime();

        // Simulate assignment process
        LocalDateTime assignmentTime = LocalDateTime.now();
        
        // Verify that updateLastActivityTime would be called
        // This tests the logic that updates last activity time when assignment happens
        Assert.assertTrue("Original activity time should be before assignment", 
                originalLastActivity.isBefore(assignmentTime));
    }

    @Test
    public void testNullLastActivityHandling() {
        // Create agents where some have null last activity time
        Agent driverWithActivity = createDriverWithLastActivity(1L, now.minusHours(1));
        Agent driverWithoutActivity = createDriverWithLastActivity(2L, null);

        // Agents with null last activity should be prioritized (treated as least recently active)
        Assert.assertNull("Driver without activity should have null last activity", 
                driverWithoutActivity.getLastActivityTime());
        Assert.assertNotNull("Driver with activity should have last activity time", 
                driverWithActivity.getLastActivityTime());
    }

    private Agent createDriverWithLastActivity(Long userId, LocalDateTime lastActivity) {
        User user = userFactory.createUserData(Role.Name.DRIVER, admin.getTenant());
        user.setId(userId);
        
        Agent driver = agentFactory.createAgent(user, stockLocation, Agent.State.WORKING);
        driver.setLastActivityTime(lastActivity);
        user.setAgent(driver);
        
        return driver;
    }
}
