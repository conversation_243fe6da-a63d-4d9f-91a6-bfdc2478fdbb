package com.happyfresh.fulfillment.unit.batch.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.batch.bean.autoassignment.FinalScore;
import com.happyfresh.fulfillment.batch.bean.autoassignment.log.AgentAutoAssignmentLog;
import com.happyfresh.fulfillment.batch.bean.autoassignment.log.AutoAssignmentLog;
import com.happyfresh.fulfillment.batch.bean.autoassignment.log.FinalScoreAutoAssignmentLog;
import com.happyfresh.fulfillment.batch.service.BaseAutoAssignmentService;
import com.happyfresh.fulfillment.batch.service.BatchAvailabilityService;
import com.happyfresh.fulfillment.batch.service.ShopperAutoAssignmentService;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaMessage;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaTopicConfig;
import com.happyfresh.fulfillment.common.service.NotificationService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.SlotRepository;
import com.happyfresh.fulfillment.repository.StockLocationRepository;
import com.happyfresh.fulfillment.slot.presenter.SlotOptimizationEvent;
import com.happyfresh.fulfillment.slot.service.SlotOptimizationEventPublisherService;
import com.happyfresh.fulfillment.user.service.AgentService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ShopperAutoAssignmentServiceTest {

    @Mock
    private AgentService agentService;

    @Mock
    private BatchAvailabilityService batchAvailabilityService;

    @Mock
    private SlotRepository slotRepository;

    @Mock
    private BatchRepository batchRepository;

    @Mock
    private NotificationService notificationService;

    @InjectMocks
    ShopperAutoAssignmentService service;

    @Captor
    private ArgumentCaptor<List<Batch>> batchesCaptor;

    @Mock
    private KafkaMessage kafkaMessage;

    @Mock
    private Logger logger;

    @Mock
    private Logger abstractLogger;

    @Mock
    private StockLocationRepository stockLocationRepository;

    @Mock
    private SlotOptimizationEventPublisherService publisherService;

    private ObjectMapper mapper;

    User admin;
    StockLocation stockLocation;
    Slot slot;
    AgentFactory agentFactory;
    UserFactory userFactory;
    SlotFactory slotFactory;
    ShipmentFactory shipmentFactory;
    StockLocationFactory stockLocationFactory;
    BatchFactory batchFactory;
    LocalDateTime now;
    private Tenant tenant;
    private String context = "ShopperAutoAssignment";

    @Before
    public void setup() throws Exception {
        injectLogger();
        injectAbstractLogger();
        userFactory = new UserFactory();
        stockLocationFactory = new StockLocationFactory();
        slotFactory = new SlotFactory();
        agentFactory = new AgentFactory();
        userFactory = new UserFactory();
        shipmentFactory = new ShipmentFactory();
        batchFactory = new BatchFactory();

        admin = userFactory.createUserData(Role.Name.ADMIN);
        stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin).get(0);
        Map<String, String> preferences = stockLocation.getPreferences();
        preferences.put("enable_shopper_auto_assignment", "true");

        slot = slotFactory.createLongerDeliverySlot(stockLocation, admin, 0, 1, 1);
        slot.setStockLocation(stockLocation);
        slot.setId(1L);

        tenant = stockLocation.getTenant();
        now = LocalDateTime.now();

        mapper = new ObjectMapper();
        ReflectionTestUtils.setField(service, "mapper", mapper);
        ReflectionTestUtils.setField(service, BaseAutoAssignmentService.class, "mapper", mapper, ObjectMapper.class);
        ReflectionTestUtils.setField(service, BaseAutoAssignmentService.class, "context", context, String.class);
    }

    private void injectLogger() throws NoSuchFieldException, IllegalAccessException {
        Field loggerField = ShopperAutoAssignmentService.class.getDeclaredField("logger");
        loggerField.setAccessible(true);

        Field modifiersField = loggerField.getClass().getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(loggerField, loggerField.getModifiers() & ~Modifier.FINAL);

        loggerField.set(service, logger);
    }

    private void injectAbstractLogger() throws NoSuchFieldException, IllegalAccessException {
        Field loggerField = ShopperAutoAssignmentService.class.getSuperclass().getDeclaredField("logger");
        loggerField.setAccessible(true);

        Field modifiersField = loggerField.getClass().getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(loggerField, loggerField.getModifiers() & ~Modifier.FINAL);

        loggerField.set(service, abstractLogger);
    }

    private SlotOptimizationEvent createEvent(Long slotId) {
        SlotOptimizationEvent event = new SlotOptimizationEvent();
        event.setShoppingShiftIdAsText("1");
        event.setSlotIdAsText(slotId.toString());
        event.setEventId("Event-1231-231");
        event.setTimestamp(LocalDateTime.now().toString());
        event.setAutoAssignmentTriggerEvent(SlotOptimizationEvent.AutoAssignmentTriggerEvent.CLOCK_IN);
        return event;
    }

    private void verifyLog(Long stockLocationId, List<Agent> agents, List<Batch> batches, int finalScoreSize, int assignmentSize) {
        verifyLogStart(stockLocationId, agents, batches, true);
        verifyLogScoreCalculation(stockLocationId, agents, batches, finalScoreSize, true);
        verifyLogEnd(stockLocationId, agents, batches, assignmentSize, true);
    }

    private void verifyLogEnd(Long stockLocationId, List<Agent> agents, List<Batch> batches, int assignmentSize, boolean isCalled) {
        if (isCalled) {
            verify(abstractLogger).info(
                    "[{}] Finish stock_location_id: {}, agents count: {}, batches count: {}, assignments count: {}",
                    context,
                    stockLocationId,
                    agents.size(),
                    batches.size(),
                    assignmentSize
            );
        } else {
            verify(abstractLogger, never()).info(
                    eq("\"[{}] Finish stock_location_id: {}, agents count: {}, batches count: {}, assignments count: {}"),
                    anyString(), any(Long.class), anyInt(), anyInt(), anyInt()
            );
        }
    }

    private void verifyLogScoreCalculation(Long stockLocationId, List<Agent> agents, List<Batch> batches, int finalScoreSize, boolean isCalled) {
        if (isCalled) {
            verify(logger).info(
                    "[ShopperAutoAssignment] Calculate stock_location_id: {}, agents count: {}, batches count: {}, finalScores count: {}",
                    stockLocationId,
                    agents.size(),
                    batches.size(),
                    finalScoreSize
            );
        } else {
            verify(logger, never()).info(
                    eq("[ShopperAutoAssignment] Calculate stock_location_id: {}, agents count: {}, batches count: {}, finalScores count: {}"),
                    any(Long.class), anyInt(), anyInt(), anyInt()
            );
        }
    }

    private void verifyLogStart(Long stockLocationId, List<Agent> agents, List<Batch> batches, boolean isCalled) {
        if (isCalled) {
            verify(abstractLogger).info(
                    "[{}] Start stock_location_id: {}, agents count: {}, batches count: {}",
                    context,
                    stockLocationId,
                    agents.size(),
                    batches.size()

            );
        } else {
            verify(abstractLogger, never()).info(
                    eq("[{}] Start stock_location_id: {}, agents count: {}, batches count: {}"),
                    anyString(),
                    any(Long.class),
                    anyInt(),
                    anyInt()

            );
        }
    }

    @Test
    public void handleEvent_shouldAssignAgentsToBatches() {
        List<Agent> agents = setupAgents(Arrays.asList(
                now.minusMinutes(10),
                now.minusMinutes(120)
        ));
        List<User> shoppers = agents.stream().map(Agent::getUser).collect(Collectors.toList());
        User shopper1 = shoppers.get(0);
        shopper1.setEmail("<EMAIL>");
        User shopper2 = shoppers.get(1);
        shopper2.setEmail("<EMAIL>");

        List<Batch> batches = setupBatches(Arrays.asList(
                now.plusMinutes(120),
                now.plusMinutes(60)
        ));
        Batch batchA = batches.get(0);
        Batch batchB = batches.get(1);

        batches.sort(Comparator.comparing(Batch::getStartTime));

        Mockito.when(slotRepository.findById(slot.getId()))
                .thenReturn(Optional.of(slot));
        Mockito.when(agentService.getAvailableShopperAgents(stockLocation))
                .thenReturn(agents);
        Mockito.when(batchAvailabilityService.getUnassignedShoppingBatchesByNLimit(stockLocation, agents.size()))
                .thenReturn(batches);

        SlotOptimizationEvent event = createEvent(slot.getId());
        service.handleEvent(event);

        Mockito.verify(batchRepository).saveAll(batchesCaptor.capture());
        List<Batch> capturedBatches = batchesCaptor.getValue();
        capturedBatches.sort(Comparator.comparing(Batch::getStartTime));
        Batch firstBatch = capturedBatches.get(0);
        Batch secondBatch = capturedBatches.get(1);

        Assert.assertEquals(2, capturedBatches.size());

        Assert.assertEquals((long) batchB.getId(), (long) firstBatch.getId());
        Assert.assertEquals((long) shopper2.getId(), (long) firstBatch.getUser().getId());
        Assert.assertTrue(firstBatch.isAutoAssigned());
        Assert.assertNotNull(firstBatch.getFlags().get(Batch.FLAG_AUTO_ASSIGNMENT_TIMESTAMP));

        Assert.assertEquals((long) batchA.getId(), (long) secondBatch.getId());
        Assert.assertEquals((long) shopper1.getId(), (long) secondBatch.getUser().getId());
        Assert.assertTrue(secondBatch.isAutoAssigned());
        Assert.assertNotNull(secondBatch.getFlags().get(Batch.FLAG_AUTO_ASSIGNMENT_TIMESTAMP));

        Mockito.verify(notificationService, times(1))
                .sendPushNotificationForNewShoppingAssignment(shopper1.getId(), tenant.getId());
        Mockito.verify(notificationService, times(1))
                .sendPushNotificationForNewShoppingAssignment(shopper2.getId(), tenant.getId());

        verifyLog(
                stockLocation.getId(),
                agents,
                batches,
                2,
                2
        );
    }

    @Test
    public void handleEvent_withAgentsCountMoreThanBatchCount() {
        List<Agent> agents = setupAgents(Arrays.asList(
                now.minusMinutes(10),
                now.minusMinutes(120)
        ));
        List<User> shoppers = agents.stream().map(Agent::getUser).collect(Collectors.toList());
        User shopper1 = shoppers.get(0);
        shopper1.setEmail("<EMAIL>");
        User shopper2 = shoppers.get(1);
        shopper2.setEmail("<EMAIL>");

        List<Batch> batches = setupBatches(List.of(
                now.plusMinutes(120)
        ));
        Batch batchA = batches.get(0);

        batches.sort(Comparator.comparing(Batch::getStartTime));

        Mockito.when(slotRepository.findById(slot.getId()))
                .thenReturn(Optional.of(slot));
        Mockito.when(agentService.getAvailableShopperAgents(stockLocation))
                .thenReturn(agents);
        Mockito.when(batchAvailabilityService.getUnassignedShoppingBatchesByNLimit(stockLocation, agents.size()))
                .thenReturn(batches);

        SlotOptimizationEvent event = createEvent(slot.getId());
        service.handleEvent(event);

        Mockito.verify(batchRepository).saveAll(batchesCaptor.capture());
        List<Batch> capturedBatches = batchesCaptor.getValue();
        capturedBatches.sort(Comparator.comparing(Batch::getStartTime));
        Batch firstBatch = capturedBatches.get(0);

        Assert.assertEquals(1, capturedBatches.size());
        Assert.assertEquals((long) batchA.getId(), (long) firstBatch.getId());
        Assert.assertEquals((long) shopper2.getId(), (long) firstBatch.getUser().getId());
        Assert.assertEquals("true", firstBatch.getFlags().get(Batch.FLAG_AUTO_ASSIGNED));

        Mockito.verify(notificationService, times(1))
                .sendPushNotificationForNewShoppingAssignment(shopper2.getId(), tenant.getId());

        verifyLog(
                stockLocation.getId(),
                agents,
                batches,
                2,
                1
        );
    }

    @Test
    public void handleEvent_withAgentsCountLessThanBatchCount() {
        List<Agent> agents = setupAgents(List.of(
                now.minusMinutes(10),
                now.minusMinutes(30)
        ));
        List<User> shoppers = agents.stream().map(Agent::getUser).collect(Collectors.toList());
        User shopper1 = shoppers.get(0);
        shopper1.setEmail("<EMAIL>");
        User shopper2 = shoppers.get(1);
        shopper2.setEmail("<EMAIL>");

        List<Batch> batches = setupBatches(List.of(
                now.plusMinutes(60),
                now.plusMinutes(120),
                now.plusMinutes(30)
        ));
        Batch batchA = batches.get(0);
        Batch batchB = batches.get(1);
        Batch batchC = batches.get(2);

        batches.sort(Comparator.comparing(Batch::getStartTime));

        Mockito.when(slotRepository.findById(slot.getId()))
                .thenReturn(Optional.of(slot));
        Mockito.when(agentService.getAvailableShopperAgents(stockLocation))
                .thenReturn(agents);
        Mockito.when(batchAvailabilityService.getUnassignedShoppingBatchesByNLimit(stockLocation, agents.size()))
                .thenReturn(batches);

        SlotOptimizationEvent event = createEvent(slot.getId());
        service.handleEvent(event);

        Mockito.verify(batchRepository).saveAll(batchesCaptor.capture());
        List<Batch> capturedBatches = batchesCaptor.getValue();
        capturedBatches.sort(Comparator.comparing(Batch::getStartTime));
        Batch firstBatch = capturedBatches.get(0);
        Batch secondBatch = capturedBatches.get(1);

        Assert.assertEquals(2, capturedBatches.size());
        Assert.assertEquals((long) batchC.getId(), (long) firstBatch.getId());
        Assert.assertEquals((long) shopper2.getId(), (long) firstBatch.getUser().getId());
        Assert.assertEquals((long) batchA.getId(), (long) secondBatch.getId());
        Assert.assertEquals((long) shopper1.getId(), (long) secondBatch.getUser().getId());
        Assert.assertEquals("true", firstBatch.getFlags().get(Batch.FLAG_AUTO_ASSIGNED));
        Assert.assertEquals("true", secondBatch.getFlags().get(Batch.FLAG_AUTO_ASSIGNED));
        Assert.assertNull(batchB.getFlags().get(Batch.FLAG_AUTO_ASSIGNED));
        Assert.assertFalse(batchB.isAutoAssigned());

        Mockito.verify(notificationService, times(1))
                .sendPushNotificationForNewShoppingAssignment(shopper1.getId(), tenant.getId());
        Mockito.verify(notificationService, times(1))
                .sendPushNotificationForNewShoppingAssignment(shopper2.getId(), tenant.getId());

        verifyLog(
                stockLocation.getId(),
                agents,
                batches,
                2,
                2
        );
    }

    @Test
    public void handleEvent_withNoAvailableAgents_shouldNotCheckAvailableBatches() {
        List<Agent> agents = new ArrayList<>();

        Mockito.when(slotRepository.findById(slot.getId()))
                .thenReturn(Optional.of(slot));
        Mockito.when(agentService.getAvailableShopperAgents(stockLocation))
                .thenReturn(agents);

        SlotOptimizationEvent event = createEvent(slot.getId());
        service.handleEvent(event);

        Mockito.verify(batchAvailabilityService, Mockito.never()).getUnassignedShoppingBatchesByNLimit(any(StockLocation.class), anyInt());
        Mockito.verify(batchRepository, Mockito.never()).saveAll(anyList());
        Mockito.verify(notificationService, times(0))
                .sendPushNotificationForNewShoppingAssignment(any(), any());

        verifyLogStart(stockLocation.getId(), agents, new ArrayList<>(), true);
        verifyLogScoreCalculation(null, null, null, 0, false);
        verifyLogEnd(null, null, null, 0, false);
    }

    @Test
    public void handleEvent_whenNoAvailableBatch_shouldDoNothing() {
        List<Agent> agents = setupAgents(Arrays.asList(
                now.minusMinutes(10),
                now.minusMinutes(120)
        ));
        List<Batch> batches = new ArrayList<>();

        Mockito.when(slotRepository.findById(slot.getId()))
                .thenReturn(Optional.of(slot));
        Mockito.when(agentService.getAvailableShopperAgents(stockLocation))
                .thenReturn(agents);
        Mockito.when(batchAvailabilityService.getUnassignedShoppingBatchesByNLimit(stockLocation, agents.size()))
                .thenReturn(batches);

        SlotOptimizationEvent event = createEvent(slot.getId());
        service.handleEvent(event);

        Mockito.verify(batchRepository, Mockito.never()).saveAll(anyList());
        Mockito.verify(notificationService, times(0))
                .sendPushNotificationForNewShoppingAssignment(any(), any());

        verifyLogStart(stockLocation.getId(), agents, batches, true);
        verifyLogScoreCalculation(null, null, null, 0, false);
        verifyLogEnd(null, null, null, 0, false);
    }

    @Test
    public void handleEvent_whenStockLocationNotEnableShopperAutoAssignment_shouldDoNothing() {

        Map<String, String> preferences = stockLocation.getPreferences();
        preferences.put("enable_shopper_auto_assignment", "false");

        Mockito.when(slotRepository.findById(slot.getId()))
                .thenReturn(Optional.of(slot));

        SlotOptimizationEvent event = createEvent(slot.getId());
        service.handleEvent(event);

        Mockito.verify(batchRepository, Mockito.never()).saveAll(anyList());
        Mockito.verify(agentService, Mockito.never()).getAvailableShopperAgents(any(StockLocation.class));
        Mockito.verify(batchAvailabilityService, Mockito.never()).getUnassignedShoppingBatchesByNLimit(any(StockLocation.class), anyInt());
        Mockito.verify(notificationService, times(0))
                .sendPushNotificationForNewShoppingAssignment(any(), any());

        verifyLogStart(null, null, null, false);
        verifyLogScoreCalculation(null, null, null, 0, false);
        verifyLogEnd(null, null, null, 0, false);
    }

    @Test
    public void handleEvent_whenStockLocationEnableShopperAutoAssignmentIsNotSet_shouldDoNothing() {

        Map<String, String> preferences = stockLocation.getPreferences();
        preferences.remove("enable_shopper_auto_assignment");

        Mockito.when(slotRepository.findById(slot.getId()))
                .thenReturn(Optional.of(slot));

        SlotOptimizationEvent event = createEvent(slot.getId());
        service.handleEvent(event);

        Mockito.verify(batchRepository, Mockito.never()).saveAll(anyList());
        Mockito.verify(agentService, Mockito.never()).getAvailableShopperAgents(any(StockLocation.class));
        Mockito.verify(batchAvailabilityService, Mockito.never()).getUnassignedShoppingBatchesByNLimit(any(StockLocation.class), anyInt());
        Mockito.verify(notificationService, times(0))
                .sendPushNotificationForNewShoppingAssignment(any(), any());

        verifyLogStart(null, null, null, false);
        verifyLogScoreCalculation(null, null, null, 0, false);
        verifyLogEnd(null, null, null, 0, false);
    }

    @Test
    public void handleEvent_whenStockLocationIsNull_shouldDoNothing() {

        Mockito.when(slotRepository.findById(slot.getId()))
                .thenReturn(Optional.empty());

        SlotOptimizationEvent event = createEvent(slot.getId());
        service.handleEvent(event);

        Mockito.verify(batchRepository, Mockito.never()).saveAll(anyList());
        Mockito.verify(agentService, Mockito.never()).getAvailableShopperAgents(any(StockLocation.class));
        Mockito.verify(batchAvailabilityService, Mockito.never()).getUnassignedShoppingBatchesByNLimit(any(StockLocation.class), anyInt());
        Mockito.verify(notificationService, times(0))
                .sendPushNotificationForNewShoppingAssignment(any(), any());

        verifyLogStart(null, null, null, false);
        verifyLogScoreCalculation(null, null, null, 0, false);
        verifyLogEnd(null, null, null, 0, false);
    }

    private List<Agent> setupAgents(List<LocalDateTime> lastActivityTimes) {
        List<Agent> agents = new ArrayList<>();
        for (int i = 0; i < lastActivityTimes.size(); i++) {
            User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());
            shopper.setId((long) i + 1);
            Agent agent = agentFactory.createAgent(shopper, stockLocation, Agent.State.WORKING);
            agent.setLastActivityTime(lastActivityTimes.get(i));
            agents.add(agent);
        }
        return agents;
    }

    private List<Batch> setupBatches(List<LocalDateTime> batchStartTimes) {
        List<Batch> batches = new ArrayList<>();
        for (int i = 0; i < batchStartTimes.size(); i++) {
            long id = i + 1;
            Shipment shipment = shipmentFactory.createShipment(slot, admin, "H" + id, "H" + id, Shipment.State.READY);
            Batch batch = batchFactory.createBatch(admin, null, shipment, slot, Batch.Type.SHOPPING, Job.State.INITIAL);
            batch.setId(id);
            batch.setStartTime(batchStartTimes.get(i));
            batch.setEndTime(batch.getStartTime().plusMinutes(60));
            batches.add(batch);
        }
        return batches;
    }

    @Test
    public void publishAutoAssignmentEvent_whenPublisherThrowException_shouldNotPublish() throws JsonProcessingException {
        stockLocation.setId(1L);
        stockLocation.getCluster().setId(3L);
        when(stockLocationRepository.findById(1L)).thenReturn(Optional.of(stockLocation));
        doThrow(new JsonProcessingException("JSON Processing Exception"){})
                .when(publisherService).publish(anyString(), any(SlotOptimizationEvent.class), anyBoolean());

        service.publishAutoAssignmentEvent(stockLocation.getCluster().getId().toString(),
                "", stockLocation.getId(), SlotOptimizationEvent.AutoAssignmentTriggerEvent.SLOT_OPTIMIZATION);

        verify(logger).error("[ShopperAutoAssignmentService] Failed create auto assignment kafka message for slot id {} or stock location id {}",
                "", 1L);
    }

    @Test
    public void publishAutoAssignmentEvent_whenCreateMessageSuccess_shouldPublishWithMessage() throws JsonProcessingException {
        stockLocation.setId(1L);
        when(stockLocationRepository.findById(1L)).thenReturn(Optional.of(stockLocation));
        Cluster cluster = stockLocation.getCluster();
        cluster.setId(3L);

        service.publishAutoAssignmentEvent(cluster.getId().toString(),
                "", stockLocation.getId(), SlotOptimizationEvent.AutoAssignmentTriggerEvent.SHOPPING_FINISHED);

        ArgumentCaptor<SlotOptimizationEvent> eventCaptor = ArgumentCaptor.forClass(SlotOptimizationEvent.class);
        verify(publisherService, times(1)).publish(eq("3"), eventCaptor.capture(), anyBoolean());
        SlotOptimizationEvent eventCaptorValue = eventCaptor.getValue();
        Assert.assertEquals("1", eventCaptorValue.getStockLocationIdAsText());
        Assert.assertEquals(SlotOptimizationEvent.AUTO_ASSIGNMENT_SHOPPER_EVENT, eventCaptorValue.getEventType());
    }

    @Test
    public void publishAutoAssignmentEvent_whenStockLocationNull_shouldDoNothing() {
        stockLocation.setId(1L);
        stockLocation.getCluster().setId(3L);
        when(stockLocationRepository.findById(1L)).thenReturn(Optional.empty());

        service.publishAutoAssignmentEvent(stockLocation.getCluster().getId().toString(),
                "", stockLocation.getId(), SlotOptimizationEvent.AutoAssignmentTriggerEvent.SHOPPING_FINISHED);
        Mockito.verify(kafkaMessage, never()).publish(anyString(), anyString(), anyString());
    }

    @Test
    public void publishAutoAssignmentEvent_whenStockLocationConfigPreferenceNotEligible_shouldDoNothing() {
        stockLocation.setId(1L);
        stockLocation.getCluster().setId(3L);
        Map<String, String> preferences = new HashMap<>();
        preferences.put("enable_shopper_auto_assignment", "false");
        stockLocation.setPreferences(preferences);
        when(stockLocationRepository.findById(1L)).thenReturn(Optional.of(stockLocation));
        service.publishAutoAssignmentEvent(stockLocation.getCluster().getId().toString(),
                "", stockLocation.getId(), SlotOptimizationEvent.AutoAssignmentTriggerEvent.SHOPPING_FINISHED);
        Mockito.verify(kafkaMessage, never()).publish(KafkaTopicConfig.SLOT_OPTIMIZATION_PROCESSING_TOPIC, stockLocation.getCluster().getId().toString(), "Test message");
    }

    @Test
    public void publishAutoAssignmentEvent_whenSlotIdNotEmptyAndStockLocationEligible_shouldPublishWithMessage() {
        stockLocation.setId(1L);
        stockLocation.getCluster().setId(3L);
        Map<String, String> preferences = new HashMap<>();
        preferences.put("enable_shopper_auto_assignment", "true");
        stockLocation.setPreferences(preferences);
        slot.setStockLocation(stockLocation);
        when(slotRepository.findById(1L)).thenReturn(Optional.of(slot));
        service.publishAutoAssignmentEvent(stockLocation.getCluster().getId().toString(),
                slot.getId().toString(), null, SlotOptimizationEvent.AutoAssignmentTriggerEvent.SHOPPING_FINISHED);
        Mockito.verify(kafkaMessage, never()).publish(KafkaTopicConfig.SLOT_OPTIMIZATION_PROCESSING_TOPIC, stockLocation.getCluster().getId().toString(), "Test message");
    }

    @Test
    public void handleEvent_withStockLocationNull_shouldDoNothing() {
        SlotOptimizationEvent event = createEvent(slot.getId());
        event.setSlotIdAsText("");
        event.setStockLocationIdAsText("");
        service.handleEvent(event);
        Mockito.verify(agentService, never()).getAvailableShopperAgents(any(StockLocation.class));

        verifyLogStart(null, null, null, false);
        verifyLogScoreCalculation(null, null, null, 0, false);
        verifyLogEnd(null, null, null, 0, false);
    }

    @Test
    public void handleEvent_withStockLocationIdParam_shouldAssignAgentsToBatches() {
        List<Agent> agents = setupAgents(Arrays.asList(
                now.minusMinutes(10),
                now.minusMinutes(120)
        ));
        List<User> shoppers = agents.stream().map(Agent::getUser).collect(Collectors.toList());
        User shopper1 = shoppers.get(0);
        shopper1.setEmail("<EMAIL>");
        User shopper2 = shoppers.get(1);
        shopper2.setEmail("<EMAIL>");

        List<Batch> batches = setupBatches(Arrays.asList(
                now.plusMinutes(120),
                now.plusMinutes(60)
        ));
        Batch batchA = batches.get(0);
        Batch batchB = batches.get(1);

        batches.sort(Comparator.comparing(Batch::getStartTime));

        Mockito.when(stockLocationRepository.findById(stockLocation.getId()))
                .thenReturn(Optional.of(stockLocation));
        Mockito.when(agentService.getAvailableShopperAgents(stockLocation))
                .thenReturn(agents);
        Mockito.when(batchAvailabilityService.getUnassignedShoppingBatchesByNLimit(stockLocation, agents.size()))
                .thenReturn(batches);

        SlotOptimizationEvent event = createEvent(slot.getId());
        event.setSlotIdAsText("");
        event.setStockLocationIdAsText(stockLocation.getId().toString());
        service.handleEvent(event);

        Mockito.verify(batchRepository).saveAll(batchesCaptor.capture());
        List<Batch> capturedBatches = batchesCaptor.getValue();
        capturedBatches.sort(Comparator.comparing(Batch::getStartTime));
        Batch firstBatch = capturedBatches.get(0);
        Batch secondBatch = capturedBatches.get(1);

        Assert.assertEquals(2, capturedBatches.size());
        Assert.assertEquals((long) batchB.getId(), (long) firstBatch.getId());
        Assert.assertEquals((long) shopper2.getId(), (long) firstBatch.getUser().getId());
        Assert.assertEquals((long) batchA.getId(), (long) secondBatch.getId());
        Assert.assertEquals((long) shopper1.getId(), (long) secondBatch.getUser().getId());

        verifyLog(
                stockLocation.getId(),
                agents,
                batches,
                2,
                2
        );
    }

    @Test
    public void testAutoAssignmentLogMapToJson() throws JsonProcessingException {
        List<AutoAssignmentLog> agentLog = new ArrayList<>();

        AutoAssignmentLog log1 = new AgentAutoAssignmentLog(
                "userId1",
                "userEmail1",
                "lastActivity1"
        );
        agentLog.add(log1);

        AutoAssignmentLog log2 = new AgentAutoAssignmentLog(
                "userId2",
                "userEmail2",
                "lastActivity2"
        );
        agentLog.add(log2);

        ObjectMapper mapper = new ObjectMapper();

        String s = mapper.writeValueAsString(agentLog);

        assertEquals("[{\"userId\":\"userId1\",\"userEmail\":\"userEmail1\",\"agentLastActivityTime\":\"lastActivity1\"},{\"userId\":\"userId2\",\"userEmail\":\"userEmail2\",\"agentLastActivityTime\":\"lastActivity2\"}]", s);
    }

    @Test
    public void testFinalScoreMapToJson() throws JsonProcessingException {
        List<FinalScore> finalScores = new ArrayList<>();

        User user1 = new User();
        user1.setId(1L);
        user1.setEmail("<EMAIL>");
        Agent agent1 = new Agent();
        agent1.setUser(user1);
        FinalScore score1 = new FinalScore(agent1, 10d);

        User user2 = new User();
        user2.setId(2L);
        user2.setEmail("<EMAIL>");
        Agent agent2 = new Agent();
        agent2.setUser(user2);
        FinalScore score2 = new FinalScore(agent2, 20d);

        finalScores.add(score1);
        finalScores.add(score2);

        List<AutoAssignmentLog> scoreLog = new ArrayList<>();
        for(FinalScore score : finalScores) {
            AutoAssignmentLog log = new FinalScoreAutoAssignmentLog(
                    score.getAgent().getUser().getId().toString(),
                    score.getAgent().getUser().getEmail(),
                    score.getScore().toString()
            );
            scoreLog.add(log);
        }

        ObjectMapper mapper = new ObjectMapper();
        String s = mapper.writeValueAsString(scoreLog);
        assertEquals("[{\"userId\":\"1\",\"userEmail\":\"<EMAIL>\",\"score\":\"10.0\"},{\"userId\":\"2\",\"userEmail\":\"<EMAIL>\",\"score\":\"20.0\"}]", s);

    }

}
