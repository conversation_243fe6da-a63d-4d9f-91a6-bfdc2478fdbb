package com.happyfresh.fulfillment.unit.slot.availability;

import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.entity.Agent;
import com.happyfresh.fulfillment.entity.Role;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.integrationTest.test.factory.AgentFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.repository.AgentRepository;
import com.happyfresh.fulfillment.slot.bean.availability.onDemand.OnDemandStratoEarliestAvailableSelection;
import com.happyfresh.fulfillment.slot.model.FleetDeliveredJobCount;
import com.happyfresh.fulfillment.user.service.AgentClockInActivityService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;

@RunWith(MockitoJUnitRunner.class)
public class OnDemandStratoEarliestAvailableSelectionTest {

    private StockLocationFactory stockLocationFactory;

    private AgentFactory agentFactory;

    private UserFactory userFactory;

    @Mock
    private AgentRepository agentRepository;

    @Mock
    private AgentClockInActivityService agentClockInActivityService;

    private User admin;
    private StockLocation stockLocation;
    private ZoneId zoneId;

    @Before
    public void setup() {
        userFactory = new UserFactory();
        stockLocationFactory = new StockLocationFactory();
        agentFactory = new AgentFactory();

        admin = userFactory.createUserData(Role.Name.ADMIN);
        stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin).get(0);
        zoneId = ZoneId.of(stockLocation.getState().getTimeZone());
    }

    private Optional<Agent> getSelectedAgent(Map<Agent, LocalDateTime> driversWithArrivalTime) {
        OnDemandStratoEarliestAvailableSelection selection = new OnDemandStratoEarliestAvailableSelection(
                driversWithArrivalTime,
                agentRepository,
                agentClockInActivityService,
                zoneId);
        return selection.select();
    }

    private List<Agent> setupOnDemandAgent(int n) {
        List<Agent> agents = new ArrayList<>();
        for (int i = 0; i < n; i++) {
            User odRanger = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, admin.getTenant());
            Agent agent = agentFactory.createAgent(odRanger, stockLocation, Agent.State.WORKING);
            agents.add(agent);
        }
        return agents;
    }

    @Test
    public void shouldReturnNull_whenArrivalTimesIsEmpty() {
        Map<Agent, LocalDateTime> driversWithArrivalTime = new HashMap<>();
        Optional<Agent> selectedAgent = getSelectedAgent(driversWithArrivalTime);
        Assert.assertFalse(selectedAgent.isPresent());
    }

    @Test
    public void shouldReturnAgent_withEarliestArrivalTime() {
        List<Agent> agents = setupOnDemandAgent(3);
        LocalDateTime now = LocalDateTime.now();

        Map<Agent, LocalDateTime> driversWithArrivalTime = new HashMap<>();
        driversWithArrivalTime.put(agents.get(0), now);
        driversWithArrivalTime.put(agents.get(1), now.plusMinutes(10));
        driversWithArrivalTime.put(agents.get(2), now.minusMinutes(20));
        Optional<Agent> selectedAgent = getSelectedAgent(driversWithArrivalTime);

        Assert.assertTrue(selectedAgent.isPresent());
        Assert.assertEquals(agents.get(2).getId(), selectedAgent.get().getId());
    }

    @Test
    public void shouldTieBreak_agentsWithSameArrivalTime_chooseLeastDeliveredJobInCurrentDay() {
        List<Agent> agents = setupOnDemandAgent(3);
        LocalDateTime now = LocalDateTime.now();

        Map<Agent, LocalDateTime> driversWithArrivalTime = new HashMap<>();
        driversWithArrivalTime.put(agents.get(0), now);
        driversWithArrivalTime.put(agents.get(1), now);
        driversWithArrivalTime.put(agents.get(2), now.plusMinutes(20));
        // Mock repo call
        LocalDateTime currentDayStart = DateTimeUtil.getTodayStoreStartInUTC(zoneId);
        LocalDateTime currentDayEnd = currentDayStart.plusDays(1);
        Long user1Id = agents.get(0).getUser().getId();
        Long user2Id = agents.get(1).getUser().getId();
        // agent with 0 delivered job won't be returned
        List<FleetDeliveredJobCount> repoResponse = Collections.singletonList(new FleetDeliveredJobCount(user1Id, 3L));
        Mockito.when(agentRepository.countDeliveredOnDemandJob(anyList(), eq(currentDayStart), eq(currentDayEnd)))
                .thenReturn(repoResponse);

        Optional<Agent> selectedAgent = getSelectedAgent(driversWithArrivalTime);

        Assert.assertTrue(selectedAgent.isPresent());
        Assert.assertEquals(agents.get(1).getId(), selectedAgent.get().getId());
    }

    @Test
    public void shouldTieBreak_agentsWithSameArrivalTime_withSameDeliveredJobs_chooseEarliestClockInTime() {
        List<Agent> agents = setupOnDemandAgent(3);
        LocalDateTime now = LocalDateTime.now();
        // same arrival time
        Map<Agent, LocalDateTime> driversWithArrivalTime = new HashMap<>();
        driversWithArrivalTime.put(agents.get(0), now);
        driversWithArrivalTime.put(agents.get(1), now);
        driversWithArrivalTime.put(agents.get(2), now.plusMinutes(20));
        // same delivered jobs count on current day
        List<FleetDeliveredJobCount> counts = new ArrayList<>();
        counts.add(new FleetDeliveredJobCount(agents.get(0).getUser().getId(), 2L));
        counts.add(new FleetDeliveredJobCount(agents.get(1).getUser().getId(), 2L));
        Mockito.when(agentRepository.countDeliveredOnDemandJob(anyList(), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(counts);
        // mock agent clock in time
        Mockito.when(agentClockInActivityService.getClockInTime(agents.get(0)))
                .thenReturn(LocalDateTime.now().minusHours(2));
        Mockito.when(agentClockInActivityService.getClockInTime(agents.get(1)))
                .thenReturn(LocalDateTime.now().minusHours(4));

        Optional<Agent> selectedAgent = getSelectedAgent(driversWithArrivalTime);

        Assert.assertTrue(selectedAgent.isPresent());
        Assert.assertEquals(agents.get(1).getId(), selectedAgent.get().getId());
    }

    @Test
    public void shouldTieBreak_agentsWithSameArrivalTime_withSameDeliveredJobs_earliestClockInTimeNotFound_chooseRandom() {
        List<Agent> agents = setupOnDemandAgent(3);
        LocalDateTime now = LocalDateTime.now();
        // same arrival time
        Map<Agent, LocalDateTime> driversWithArrivalTime = new HashMap<>();
        driversWithArrivalTime.put(agents.get(0), now);
        driversWithArrivalTime.put(agents.get(1), now);
        driversWithArrivalTime.put(agents.get(2), now.plusMinutes(20));
        // same delivered jobs count on current day
        List<FleetDeliveredJobCount> counts = new ArrayList<>();
        counts.add(new FleetDeliveredJobCount(agents.get(0).getUser().getId(), 2L));
        counts.add(new FleetDeliveredJobCount(agents.get(1).getUser().getId(), 2L));
        Mockito.when(agentRepository.countDeliveredOnDemandJob(anyList(), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(counts);
        // agent clock in time not found
        Mockito.when(agentClockInActivityService.getClockInTime(any(Agent.class))).thenReturn(null);

        Optional<Agent> selectedAgent = getSelectedAgent(driversWithArrivalTime);

        List<Long> expectedAgentIds = Arrays.asList(agents.get(0).getId(), agents.get(1).getId());
        Assert.assertTrue(selectedAgent.isPresent());
        Assert.assertTrue(expectedAgentIds.contains(selectedAgent.get().getId()));
    }

    @Test
    public void shouldTieBreak_agentsWithSameArrivalTime_emptyResponseDeliveredJobInCurrentDay_chooseRandom() {
        List<Agent> agents = setupOnDemandAgent(3);
        LocalDateTime now = LocalDateTime.now();

        Map<Agent, LocalDateTime> driversWithArrivalTime = new HashMap<>();
        driversWithArrivalTime.put(agents.get(0), now);
        driversWithArrivalTime.put(agents.get(1), now);
        driversWithArrivalTime.put(agents.get(2), now.plusMinutes(20));
        // failed repo call
        Mockito.when(agentRepository.countDeliveredOnDemandJob(anyList(), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(new ArrayList<>());
        // agent clock in time not found
        Mockito.when(agentClockInActivityService.getClockInTime(any(Agent.class))).thenReturn(null);

        Optional<Agent> selectedAgent = getSelectedAgent(driversWithArrivalTime);

        List<Long> expectedAgentIds = Arrays.asList(agents.get(0).getId(), agents.get(1).getId());
        Assert.assertTrue(selectedAgent.isPresent());
        Assert.assertTrue(expectedAgentIds.contains(selectedAgent.get().getId()));
    }

    @Test
    public void shouldTieBreak_agentsWithSameArrivalTime_withSameDeliveredJobs_withSameClockInTime_chooseRandom() {
        List<Agent> agents = setupOnDemandAgent(3);
        LocalDateTime now = LocalDateTime.now();
        // same arrival time
        Map<Agent, LocalDateTime> driversWithArrivalTime = new HashMap<>();
        driversWithArrivalTime.put(agents.get(0), now);
        driversWithArrivalTime.put(agents.get(1), now);
        driversWithArrivalTime.put(agents.get(2), now.plusMinutes(20));
        // same delivered jobs count on current day
        List<FleetDeliveredJobCount> counts = new ArrayList<>();
        counts.add(new FleetDeliveredJobCount(agents.get(0).getUser().getId(), 2L));
        counts.add(new FleetDeliveredJobCount(agents.get(1).getUser().getId(), 2L));
        Mockito.when(agentRepository.countDeliveredOnDemandJob(anyList(), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(counts);
        // mock agent clock in time
        Mockito.when(agentClockInActivityService.getClockInTime(agents.get(0)))
                .thenReturn(LocalDateTime.now().minusHours(2));
        Mockito.when(agentClockInActivityService.getClockInTime(agents.get(1)))
                .thenReturn(LocalDateTime.now().minusHours(2));

        Optional<Agent> selectedAgent = getSelectedAgent(driversWithArrivalTime);
        List<Long> expectedAgentIds = Arrays.asList(agents.get(0).getId(), agents.get(1).getId());
        Assert.assertTrue(selectedAgent.isPresent());
        Assert.assertTrue(expectedAgentIds.contains(selectedAgent.get().getId()));
    }

}
