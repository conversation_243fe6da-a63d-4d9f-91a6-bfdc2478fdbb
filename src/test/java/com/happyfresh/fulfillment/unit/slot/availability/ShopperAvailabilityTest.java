package com.happyfresh.fulfillment.unit.slot.availability;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.happyfresh.fulfillment.common.service.CoralogixAPIService;
import com.happyfresh.fulfillment.common.service.SegmentIOService;
import com.happyfresh.fulfillment.common.service.VRPService;
import com.happyfresh.fulfillment.common.util.WebRequestLogger;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.Slot;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.integrationTest.test.factory.ShipmentFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.SlotFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.slot.bean.SlotAvailabilityContext;
import com.happyfresh.fulfillment.slot.bean.availability.AbstractAvailabilityChain;
import com.happyfresh.fulfillment.slot.bean.availability.ShopperAvailability;
import com.happyfresh.fulfillment.slot.service.SlotReservedService;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;

import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;

@RunWith(MockitoJUnitRunner.class)
public class ShopperAvailabilityTest {

    private SlotAvailabilityContext context;

    private AbstractAvailabilityChain chain;

    private Slot slot;

    private Slot slot2;

    @Mock
    private VRPService vrpService;

    @Mock
    private ShipmentRepository shipmentRepository;

    @Mock
    private SlotReservedService slotReservedService;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private CoralogixAPIService coralogixAPIService;

    @Before
    public void setup() throws JsonProcessingException {
        UserFactory userFactory = new UserFactory();
        User user = userFactory.createUserData();

        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);

        SlotFactory slotFactory = new SlotFactory();
        slot = slotFactory.createSlot(stockLocation, user);
        slot.setId(RandomUtils.nextLong());
        slot2 = slotFactory.createSlot(stockLocation, user);
        slot2.setId(RandomUtils.nextLong());

        ShipmentFactory shipmentFactory = new ShipmentFactory();
        Shipment shipment = shipmentFactory.createShipment(slot, user);

        doNothing().when(coralogixAPIService).sendLog(any(), any(), any(), any(), any(), (Map)any());
        doReturn(coralogixAPIService).when(applicationContext).getBean(CoralogixAPIService.class);

        context = new SlotAvailabilityContext();
        context.setShipment(shipment);
        context.setApplicationContext(applicationContext);
    }

    @Test
    public void expectFalseIfShopperIsNotAvailable() {
        //Mockito.when(applicationContext.getBean(WebRequestLogger.class)).thenReturn(new WebRequestLogger());
        chain = new ShopperAvailability(context, vrpService, shipmentRepository, slotReservedService, applicationContext);
        slot2.setShopperCount(-1);

        Assert.assertEquals(false, chain.isAvailable(slot2));
    }
}
