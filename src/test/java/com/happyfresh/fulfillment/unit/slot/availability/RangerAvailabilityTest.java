package com.happyfresh.fulfillment.unit.slot.availability;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.model.RoundTripTime;
import com.happyfresh.fulfillment.common.service.CoralogixAPIService;
import com.happyfresh.fulfillment.common.service.DistanceService;
import com.happyfresh.fulfillment.common.service.SegmentIOService;
import com.happyfresh.fulfillment.common.service.VRPService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.slot.bean.SlotAvailabilityContext;
import com.happyfresh.fulfillment.slot.bean.availability.AbstractAvailabilityChain;
import com.happyfresh.fulfillment.slot.bean.availability.RangerAvailability;
import com.happyfresh.fulfillment.slot.model.BatchCount;
import com.happyfresh.fulfillment.slot.model.BatchCountId;
import com.happyfresh.fulfillment.slot.service.SlotReservedService;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;

@RunWith(MockitoJUnitRunner.class)
public class RangerAvailabilityTest {
    private SlotAvailabilityContext context;

    private AbstractAvailabilityChain chain;

    private User user;

    private Slot slot;

    private Slot slot2;

    @Mock
    private VRPService vrpService;

    @Mock
    private DistanceService distanceService;

    @Mock
    private ShipmentRepository shipmentRepository;

    @Mock
    private BatchRepository batchRepository;

    @Mock
    private SlotReservedService slotReservedService;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private CoralogixAPIService coralogixAPIService;

    @Before
    public void setup() throws JsonProcessingException {
        UserFactory userFactory = new UserFactory();
        user = userFactory.createUserData();

        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);
        stockLocation.setType(StockLocation.Type.SPECIAL);
        stockLocation.setMaxDeliveryHandover(5);
        stockLocation.setShopperAveragePickingTimePerUniqItem(10);
        stockLocation.setShopperQueueReplacementTime(5);

        SlotFactory slotFactory = new SlotFactory();
        slot = slotFactory.createSlot(stockLocation, user);
        slot.setType(Slot.Type.ONE_HOUR);
        slot.setId(RandomUtils.nextLong());
        slot.setDriverCount(2);
        slot.setStartTime(LocalDateTime.now().plusHours(1).withMinute(0).withSecond(0).withNano(0));
        slot.setEndTime(LocalDateTime.now().withHour(2).withMinute(0).withSecond(0).withNano(0));

        slot2 = slotFactory.createSlot(stockLocation, user);
        slot2.setType(Slot.Type.ONE_HOUR);
        slot2.setId(RandomUtils.nextLong());
        slot2.setDriverCount(2);
        slot2.setStartTime(LocalDateTime.now().plusHours(2).withMinute(0).withSecond(0).withNano(0));
        slot2.setEndTime(LocalDateTime.now().plusHours(3).withMinute(0).withSecond(0).withNano(0));

        ShipmentFactory shipmentFactory = new ShipmentFactory();
        Shipment shipment = shipmentFactory.createShipment(null, user);

        ItemFactory itemFactory = new ItemFactory();
        List<Item> items = itemFactory.createItems(shipment, user, 2);
        shipment.setItems(items);

        doNothing().when(coralogixAPIService).sendLog(any(), any(), any(), any(), any(), (Map)any());
        doReturn(coralogixAPIService).when(applicationContext).getBean(CoralogixAPIService.class);

        context = new SlotAvailabilityContext();
        context.setStockLocation(stockLocation);
        context.setShipment(shipment);
        context.setSlots(Lists.newArrayList(slot,slot2));
        context.setDeliveryDurations(ImmutableMap.of());
        context.setClusteredSlots(ImmutableMap.of(
                slot.getStartTime(), Lists.newArrayList(slot),
                slot2.getStartTime(), Lists.newArrayList(slot2)
        ));
        context.setApplicationContext(applicationContext);
    }

    @Test
    public void expectTrueIfStoreIsNotSpecial() {
        StockLocation stockLocation = context.getStockLocation();
        stockLocation.setType(StockLocation.Type.ORIGINAL);
        context.setStockLocation(stockLocation);

        chain = new RangerAvailability(context, vrpService, distanceService, shipmentRepository, batchRepository, slotReservedService, applicationContext);
        Assert.assertEquals(true, chain.isAvailable(slot));
    }

    @Test
    public void expectFalseIfDriverIsNotAvailable() {
        slot.setDriverCount(-1);

        chain = new RangerAvailability(context, vrpService, distanceService, shipmentRepository, batchRepository, slotReservedService, applicationContext);
        Assert.assertEquals(false, chain.isAvailable(slot));
    }

    @Test
    public void expectFalseIfWiderRangerIsNotAvailable() {
        Mockito.when(distanceService.getRoundTripTime(context.getShipment(), context.getStockLocation(), context.isAvoidTolls())).thenReturn(new RoundTripTime(1000l, 1000l));
        Mockito.when(distanceService.getRoundTripTime(context.getShipment(), context.getStockLocation(), context.isAvoidTolls())).thenReturn(new RoundTripTime(1000l, 1000l));

        BatchCountId key = new BatchCountId(slot.getStartTime(), Batch.Type.RANGER);
        BatchCount batchCount = new BatchCount(key,0,2);
        context.setBatchCounts(ImmutableMap.of(key, batchCount));

        chain = new RangerAvailability(context, vrpService, distanceService, shipmentRepository, batchRepository, slotReservedService, applicationContext);
        Assert.assertEquals(false, chain.isAvailable(slot));

        key = new BatchCountId(slot.getStartTime(), Batch.Type.RANGER);
        batchCount = new BatchCount(key,1,1);
        BatchCountId key2 = new BatchCountId(slot2.getStartTime(), Batch.Type.RANGER);
        BatchCount batchCount2 = new BatchCount(key2,2,0);
        context.setBatchCounts(ImmutableMap.of(
                key, batchCount,
                key2, batchCount2
        ));

        chain = new RangerAvailability(context, vrpService, distanceService, shipmentRepository, batchRepository, slotReservedService, applicationContext);
        Assert.assertEquals(false, chain.isAvailable(slot2));
    }

    @Test
    public void expectTrueIfWiderRangerIsAvailable() {
        Mockito.when(distanceService.getRoundTripTime(context.getShipment(), context.getStockLocation(), context.isAvoidTolls())).thenReturn(new RoundTripTime(2400l, 1000l));

        BatchCountId key = new BatchCountId(slot.getStartTime(), Batch.Type.RANGER);
        BatchCount batchCount = new BatchCount(key,1,0);
        BatchCountId key2 = new BatchCountId(slot2.getStartTime(), Batch.Type.RANGER);
        BatchCount batchCount2 = new BatchCount(key2,0,1);
        context.setBatchCounts(ImmutableMap.of(
                key, batchCount,
                key2, batchCount2
        ));

        chain = new RangerAvailability(context, vrpService, distanceService, shipmentRepository, batchRepository, slotReservedService, applicationContext);
        Assert.assertEquals(true, chain.isAvailable(slot2));

        key = new BatchCountId(slot.getStartTime(), Batch.Type.RANGER);
        batchCount = new BatchCount(key,0,1);
        key2 = new BatchCountId(slot2.getStartTime(), Batch.Type.RANGER);
        batchCount2 = new BatchCount(key2,1,0);
        context.setBatchCounts(ImmutableMap.of(
                key, batchCount,
                key2, batchCount2
        ));
        chain = new RangerAvailability(context, vrpService, distanceService, shipmentRepository, batchRepository, slotReservedService, applicationContext);
        Assert.assertEquals(true, chain.isAvailable(slot2));
    }

    @Test
    public void expectTrueIfRangerCountIsAboveThanBatchCountForWiderRanger() {
        Mockito.when(distanceService.getRoundTripTime(context.getShipment(), context.getStockLocation(), context.isAvoidTolls())).thenReturn(new RoundTripTime(2400l, 1000l));

        BatchCountId key = new BatchCountId(slot2.getStartTime(), Batch.Type.RANGER);
        BatchCount batchCount = new BatchCount(key,0,1);
        context.setBatchCounts(ImmutableMap.of(key, batchCount));

        chain = new RangerAvailability(context, vrpService, distanceService, shipmentRepository, batchRepository, slotReservedService, applicationContext);
        Assert.assertEquals(true, chain.isAvailable(slot2));

        batchCount = new BatchCount(key,1,0);
        context.setBatchCounts(ImmutableMap.of(key, batchCount));

        chain = new RangerAvailability(context, vrpService, distanceService, shipmentRepository, batchRepository, slotReservedService, applicationContext);
        Assert.assertEquals(true, chain.isAvailable(slot2));
    }

    @Test
    public void expectTrueIfRangerCountIsAboveThanBatchCountForNonWiderRanger() {
        Mockito.when(distanceService.getRoundTripTime(context.getShipment(), context.getStockLocation(), context.isAvoidTolls())).thenReturn(new RoundTripTime(600l, 1000l));

        BatchCountId key = new BatchCountId(slot.getStartTime(), Batch.Type.RANGER);
        BatchCount batchCount = new BatchCount(key,0,1);
        context.setBatchCounts(ImmutableMap.of(key, batchCount));

        chain = new RangerAvailability(context, vrpService, distanceService, shipmentRepository, batchRepository, slotReservedService, applicationContext);
        Assert.assertEquals(true, chain.isAvailable(slot));

        batchCount = new BatchCount(key,1,0);
        context.setBatchCounts(ImmutableMap.of(key, batchCount));

        chain = new RangerAvailability(context, vrpService, distanceService, shipmentRepository, batchRepository, slotReservedService, applicationContext);
        Assert.assertEquals(true, chain.isAvailable(slot));
    }

    @Test
    public void expectFalseIfRangerCountIsBelowThanBatchCountForNonWiderRanger() {
        Mockito.when(distanceService.getRoundTripTime(context.getShipment(), context.getStockLocation(), context.isAvoidTolls())).thenReturn(new RoundTripTime(600l, 1000l));
        //Mockito.when(applicationContext.getBean(WebRequestLogger.class)).thenReturn(new WebRequestLogger());

        BatchCountId key = new BatchCountId(slot.getStartTime(), Batch.Type.RANGER);
        BatchCount batchCount = new BatchCount(key,2,0);
        context.setBatchCounts(ImmutableMap.of(key, batchCount));

        chain = new RangerAvailability(context, vrpService, distanceService, shipmentRepository, batchRepository, slotReservedService, applicationContext);
        Assert.assertEquals(false, chain.isAvailable(slot));

    }
}
