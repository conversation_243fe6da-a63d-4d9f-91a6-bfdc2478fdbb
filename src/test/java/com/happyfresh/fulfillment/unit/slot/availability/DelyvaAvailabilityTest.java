package com.happyfresh.fulfillment.unit.slot.availability;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.service.CoralogixAPIService;
import com.happyfresh.fulfillment.common.service.DistanceService;
import com.happyfresh.fulfillment.common.service.SegmentIOService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.shipment.service.ShipmentService;
import com.happyfresh.fulfillment.slot.bean.SlotAvailabilityContext;
import com.happyfresh.fulfillment.slot.bean.availability.AbstractAvailabilityChain;
import com.happyfresh.fulfillment.slot.bean.availability.DelyvaAvailability;
import com.happyfresh.fulfillment.slot.service.SlotReservedService;
import com.happyfresh.fulfillment.tpl.delyva.model.DelyvaPrice;
import com.happyfresh.fulfillment.tpl.delyva.presenter.DelyvaGetQuotationPresenter;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DelyvaAvailabilityTest {

    private SlotAvailabilityContext context;

    private AbstractAvailabilityChain chain;

    private Slot slot;

    private Item item;

    private StockLocation stockLocation;

    private Shipment shipment;

    private Country country;

    @Mock
    private DistanceService distanceService;

    @Mock
    private ShipmentRepository shipmentRepository;

    @Mock
    private SlotReservedService slotReservedService;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private ShipmentService shipmentService;

    @Mock
    private CoralogixAPIService coralogixAPIService;

    @Before
    public void setup() throws JsonProcessingException {

        UserFactory userFactory = new UserFactory();
        User user = userFactory.createUserData();

        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user);
        stockLocation = stockLocations.get(0);
        stockLocation.getCluster().setSlotType(Slot.Type.LONGER_DELIVERY);

        country = new Country();
        country.setName("Indonesia");
        country.setIsoName("ID");
        country.setPreferences(new HashMap<String, String>() {{
            put("allow_ewallet_for_tpl", "true");
        }});
        country.setCreatedAt(LocalDateTime.now());
        country.setCreatedBy(user.getId());
        country.setTenant(user.getTenant());

        SlotFactory slotFactory = new SlotFactory();
        slot = slotFactory.createSlot(stockLocation, user);
        slot.setId(RandomUtils.nextLong());
        slot.setDriverCount(2);
        slot.setStartTime(LocalDateTime.now().withHour(8).withMinute(0).withSecond(0).withNano(0));
        slot.setEndTime(LocalDateTime.now().withHour(9).withMinute(0).withSecond(0).withNano(0));

        ShipmentFactory shipmentFactory = new ShipmentFactory();
        shipment = shipmentFactory.createShipment(slot, user);
        shipment.setOrderTotal(new BigDecimal(400000));

        CategoryFactory categoryFactory = new CategoryFactory();
        Category category = categoryFactory.createCategory(user);

        ItemFactory itemFactory = new ItemFactory();
        item = itemFactory.createItem(category, null, user);

        item.setConsideredAsAlcohol(false);
        shipment.setItems(Lists.newArrayList(item));

        doNothing().when(coralogixAPIService).sendLog(any(), any(), any(), any(), any(), (Map)any());
        doReturn(coralogixAPIService).when(applicationContext).getBean(CoralogixAPIService.class);

        context = new SlotAvailabilityContext();
        context.setShipment(shipment);
        context.setCountry(country);
        context.setStockLocation(stockLocation);
        context.setApplicationContext(applicationContext);

        DelyvaPrice delyvaPrice = new DelyvaPrice();
        delyvaPrice.setAmount(10);
        delyvaPrice.setCurrency("MYR");

        DelyvaGetQuotationPresenter.ServiceDetail serviceDetail = new DelyvaGetQuotationPresenter.ServiceDetail();
        serviceDetail.setCode("HFPK-ODD-KV-PROD");
        serviceDetail.setName("Pickupp Same Day");
        serviceDetail.setCollectCash(0);

        DelyvaGetQuotationPresenter.Service delyvaService = new DelyvaGetQuotationPresenter.Service();
        delyvaService.setPrice(delyvaPrice);
        delyvaService.setService(serviceDetail);

        context.setSelectedDelyvaService(delyvaService);

        this.shipmentService = new ShipmentService();

        when(shipmentRepository.findByNumber(shipment.getNumber())).thenReturn(shipment);
        ReflectionTestUtils.setField(shipmentService, "shipmentRepository", shipmentRepository);
    }

    @Test
    public void whenDelyvaIsDisabled_shouldReturnFalse() {
        chain = new DelyvaAvailability(context, slotReservedService, shipmentService, distanceService, applicationContext);
        stockLocation.setEnableDelyva(false);

        Assert.assertFalse(chain.isAvailable(slot));
    }

    @Test
    public void whenThereIsRestrictedItem_shouldReturnFalse() {
        chain = new DelyvaAvailability(context, slotReservedService, shipmentService, distanceService, applicationContext);
        stockLocation.setEnableDelyva(true);
        item.setConsideredAsGeRestrictedProduct(true);

        Assert.assertFalse(chain.isAvailable(slot));
    }

    @Test
    public void whenShipmentIsReadyAndPaymentCOD_shouldReturnFalse() {
        chain = new DelyvaAvailability(context, slotReservedService, shipmentService, distanceService, applicationContext);
        stockLocation.setEnableDelyva(true);
        item.setConsideredAsGeRestrictedProduct(false);

        shipment.setState(Shipment.State.READY);
        shipment.setOrderPaymentMethod(Shipment.COD_PAYMENT_TYPE);

        Assert.assertFalse(chain.isAvailable(slot));
    }

    @Test
    public void whenShipmentIsPaymentEWalletAndTPLNotAllowedEWallet_shouldReturnFalse() {
        chain = new DelyvaAvailability(context, slotReservedService, shipmentService, distanceService, applicationContext);
        stockLocation.setEnableDelyva(true);
        item.setConsideredAsGeRestrictedProduct(false);

        shipment.setState(Shipment.State.READY);
        shipment.setOrderPaymentMethod(Shipment.E_WALLET_PAYMENT_TYPE);

        country.setPreferences(new HashMap<String, String>() {{
            put("allow_ewallet_for_tpl", "false");
        }});

        Assert.assertFalse(chain.isAvailable(slot));
    }

    @Test
    public void whenShipmentPaymentIsEWalletAndTPLAllowedEWallet_shouldReturnTrue() {
        chain = new DelyvaAvailability(context, slotReservedService, shipmentService, distanceService, applicationContext);
        stockLocation.setEnableDelyva(true);
        item.setConsideredAsGeRestrictedProduct(false);

        shipment.setState(Shipment.State.READY);
        shipment.setOrderPaymentMethod(Shipment.E_WALLET_PAYMENT_TYPE);

        country.setPreferences(new HashMap<String, String>() {{
            put("allow_ewallet_for_tpl", "true");
        }});

        Assert.assertTrue(chain.isAvailable(slot));
    }

    @Test
    public void whenTPLNotAllowedEWalletAndShipmentPaymentIsNotEWallet_shouldReturnTrue() {
        chain = new DelyvaAvailability(context, slotReservedService, shipmentService, distanceService, applicationContext);
        stockLocation.setEnableDelyva(true);
        item.setConsideredAsGeRestrictedProduct(false);

        shipment.setState(Shipment.State.READY);
        shipment.setOrderPaymentMethod(Shipment.CC_PAYMENT_TYPE);

        country.setPreferences(new HashMap<String, String>() {{
            put("allow_ewallet_for_tpl", "false");
        }});

        Assert.assertTrue(chain.isAvailable(slot));
    }

    @Test
    public void whenDelyvaServicesIsEmpty_shouldReturnFalse() {
        chain = new DelyvaAvailability(context, slotReservedService, shipmentService, distanceService, applicationContext);
        stockLocation.setEnableDelyva(true);
        item.setConsideredAsGeRestrictedProduct(false);

        shipment.setState(Shipment.State.READY);
        shipment.setOrderPaymentMethod(Shipment.CC_PAYMENT_TYPE);

        context.setSelectedDelyvaService(null);

        Assert.assertFalse(chain.isAvailable(slot));
    }

    @Test
    public void whenAllConditionsMet_shouldReturnTrue() {
        chain = new DelyvaAvailability(context, slotReservedService, shipmentService, distanceService, applicationContext);
        stockLocation.setEnableDelyva(true);
        item.setConsideredAsGeRestrictedProduct(false);

        shipment.setState(Shipment.State.READY);
        shipment.setOrderPaymentMethod(Shipment.CC_PAYMENT_TYPE);

        Assert.assertTrue(chain.isAvailable(slot));
        Assert.assertEquals(Slot.FleetType.PICKUPP, slot.getFleetType());
        Assert.assertFalse(slot.isAllowCod());
        Assert.assertTrue(slot.isAllowEwallet());
        Assert.assertEquals(Slot.Type.LONGER_DELIVERY, stockLocation.getCluster().getSlotType());
    }

    @Test
    public void whenAllConditionsMet_borzoBike_shouldReturnTrue_andSetVehicleTypeFourWheels() {
        DelyvaGetQuotationPresenter.ServiceDetail serviceDetail = new DelyvaGetQuotationPresenter.ServiceDetail();
        serviceDetail.setCode("BRZB-PROD");
        serviceDetail.setName("Borzo Bike");
        serviceDetail.setCollectCash(0);
        DelyvaGetQuotationPresenter.Service delyvaService = new DelyvaGetQuotationPresenter.Service();
        delyvaService.setPrice(new DelyvaPrice(10, "MYR"));
        delyvaService.setService(serviceDetail);

        context.setSelectedDelyvaService(delyvaService);

        chain = new DelyvaAvailability(context, slotReservedService, shipmentService, distanceService, applicationContext);
        stockLocation.setEnableDelyva(true);
        item.setConsideredAsGeRestrictedProduct(false);

        shipment.setState(Shipment.State.READY);
        shipment.setOrderPaymentMethod(Shipment.CC_PAYMENT_TYPE);

        Assert.assertTrue(chain.isAvailable(slot));
        Assert.assertEquals(Slot.FleetType.BORZO_BIKE, slot.getFleetType());
        Assert.assertEquals(Slot.FleetVehicleType.TWO_WHEELS, slot.getVehicleType());
        Assert.assertFalse(slot.isAllowCod());
        Assert.assertTrue(slot.isAllowEwallet());
        Assert.assertEquals(Slot.Type.LONGER_DELIVERY, stockLocation.getCluster().getSlotType());
    }

    @Test
    public void whenAllConditionsMet_borzoCar_shouldReturnTrue_andSetVehicleTypeFourWheels() {
        DelyvaGetQuotationPresenter.ServiceDetail serviceDetail = new DelyvaGetQuotationPresenter.ServiceDetail();
        serviceDetail.setCode("BRZC-PROD");
        serviceDetail.setName("Borzo Car");
        serviceDetail.setCollectCash(0);
        DelyvaGetQuotationPresenter.Service delyvaService = new DelyvaGetQuotationPresenter.Service();
        delyvaService.setPrice(new DelyvaPrice(10, "MYR"));
        delyvaService.setService(serviceDetail);

        context.setSelectedDelyvaService(delyvaService);

        chain = new DelyvaAvailability(context, slotReservedService, shipmentService, distanceService, applicationContext);
        stockLocation.setEnableDelyva(true);
        item.setConsideredAsGeRestrictedProduct(false);

        shipment.setState(Shipment.State.READY);
        shipment.setOrderPaymentMethod(Shipment.CC_PAYMENT_TYPE);

        Assert.assertTrue(chain.isAvailable(slot));
        Assert.assertEquals(Slot.FleetType.BORZO_CAR, slot.getFleetType());
        Assert.assertEquals(Slot.FleetVehicleType.FOUR_WHEELS, slot.getVehicleType());
        Assert.assertFalse(slot.isAllowCod());
        Assert.assertTrue(slot.isAllowEwallet());
        Assert.assertEquals(Slot.Type.LONGER_DELIVERY, stockLocation.getCluster().getSlotType());
    }

    @Test
    public void whenOrderClientTypeIsLineMan_shouldReturnFalse() {
        chain = new DelyvaAvailability(context, slotReservedService, shipmentService, distanceService, applicationContext);
        stockLocation.setEnableDelyva(true);
        item.setConsideredAsGeRestrictedProduct(false);
        shipment.setOrderClientType(ShipmentService.LINEMAN);

        Assert.assertFalse(chain.isAvailable(slot));
    }

    @Test
    public void whenException_shouldReturnFalse() {
        shipmentService = Mockito.mock(ShipmentService.class);
        when(shipmentService.isEligibleForDelyva(any(Shipment.class), any(StockLocation.class), any(DelyvaGetQuotationPresenter.Service.class))).thenThrow(RuntimeException.class);

        chain = new DelyvaAvailability(context, slotReservedService, shipmentService, distanceService, applicationContext);
        stockLocation.setEnableDelyva(true);

        Assert.assertFalse(chain.isAvailable(slot));
        String unavailableReason = context.getSlotIdAndUnavailableReasonMap().get(slot.getId());
        Assert.assertEquals(unavailableReason, Slot.UnavailableReason.FAILED_CHAIN.toString());

    }
}
