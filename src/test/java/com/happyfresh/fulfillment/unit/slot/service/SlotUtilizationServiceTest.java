package com.happyfresh.fulfillment.unit.slot.service;

import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.entity.Role;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.slot.service.SlotUtilizationService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;

@RunWith(MockitoJUnitRunner.class)
public class SlotUtilizationServiceTest {

    @InjectMocks
    private SlotUtilizationService slotUtilizationService;

    private StockLocation stockLocation;

    @Before
    public void setup(){
        UserFactory userFactory = new UserFactory();
        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        User admin = userFactory.createUserData(Role.Name.ADMIN);
        stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin).get(0);
        stockLocation.setId(1L);
    }

    @Test
    public void getShoppingUtilization_whenStockLocationHFS_shouldReturn0(){
        stockLocation.setEnabler(StockLocation.Enabler.HFC);
        stockLocation.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);

        String storeTimeZone = stockLocation.getState().getTimeZone();
        ZoneId storeZoneId = ZoneId.of(storeTimeZone);
        LocalDateTime startOfDate = DateTimeUtil.getStoreStartInUTC(LocalDate.now(), storeZoneId);
        LocalDateTime endOfDate = startOfDate.plusDays(1);
        double result = slotUtilizationService.getShoppingUtilization(stockLocation, startOfDate, endOfDate, startOfDate, endOfDate);
        double expected = 0;
        Assert.assertEquals(expected, result, 0);
    }

    @Test
    public void getShoppingUtilization_whenStockLocationIsSpecial_shouldReturn0(){
        stockLocation.setType(StockLocation.Type.SPECIAL);

        String storeTimeZone = stockLocation.getState().getTimeZone();
        ZoneId storeZoneId = ZoneId.of(storeTimeZone);
        LocalDateTime startOfDate = DateTimeUtil.getStoreStartInUTC(LocalDate.now(), storeZoneId);
        LocalDateTime endOfDate = startOfDate.plusDays(1);
        double result = slotUtilizationService.getShoppingUtilization(stockLocation, startOfDate, endOfDate, startOfDate, endOfDate);
        double expected = 0;
        Assert.assertEquals(expected, result, 0);
    }

    @Test
    public void getDeliveryUtilization_whenStockLocationEnabledTplDelivery_shouldReturn0(){
        stockLocation.setTplEnabled(true);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableLalamove(false);
        stockLocation.setEnableDelyva(false);

        String storeTimeZone = stockLocation.getState().getTimeZone();
        ZoneId storeZoneId = ZoneId.of(storeTimeZone);
        LocalDateTime startOfDate = DateTimeUtil.getStoreStartInUTC(LocalDate.now(), storeZoneId);
        LocalDateTime endOfDate = startOfDate.plusDays(1);
        double result = slotUtilizationService.getDeliveryUtilization(stockLocation, startOfDate, endOfDate, startOfDate, endOfDate);
        double expected = 0;
        Assert.assertEquals(expected, result, 0);

        stockLocation.setTplEnabled(false);
        stockLocation.setEnableGrabExpress(true);
        stockLocation.setEnableLalamove(false);
        stockLocation.setEnableDelyva(false);
        result = slotUtilizationService.getDeliveryUtilization(stockLocation, startOfDate, endOfDate, startOfDate, endOfDate);
        Assert.assertEquals(expected, result, 0);

        stockLocation.setTplEnabled(false);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableLalamove(true);
        stockLocation.setEnableDelyva(false);
        result = slotUtilizationService.getDeliveryUtilization(stockLocation, startOfDate, endOfDate, startOfDate, endOfDate);
        Assert.assertEquals(expected, result, 0);

        stockLocation.setTplEnabled(false);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableLalamove(false);
        stockLocation.setEnableDelyva(true);
        result = slotUtilizationService.getDeliveryUtilization(stockLocation, startOfDate, endOfDate, startOfDate, endOfDate);
        Assert.assertEquals(expected, result, 0);
    }

}
