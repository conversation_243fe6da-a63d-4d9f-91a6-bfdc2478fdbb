package com.happyfresh.fulfillment.unit.lalamove.service.api;

import com.happyfresh.fulfillment.common.config.CustomCircuitBreakerConfig;
import com.happyfresh.fulfillment.common.property.LalamoveProperty;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.common.NoResetRequestExpectationManager;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.lalamove.presenter.LalamoveDriverDetailPresenter;
import com.happyfresh.fulfillment.lalamove.service.api.LalamoveApiGetDriverDetailsRequest;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.springframework.test.web.client.match.MockRestRequestMatchers.*;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

@RunWith(MockitoJUnitRunner.class)
public class LalamoveApiGetDriverDetailsTest {
    public static final String BASE_URL = "https://sandbox-rest.lalamove.com";
    public static final String URI = BASE_URL + "/v2/orders/";
    private MockRestServiceServer mockServer;
    private LalamoveApiGetDriverDetailsRequest request;
    private String orderId;
    private String driverId;
    private State state;

    @Before
    public void setup() {
        LalamoveProperty lalamoveProperty = new LalamoveProperty();
        Map<String,String> apiKeys = new HashMap<>();
        apiKeys.put("id", "123456");
        apiKeys.put("my", "122256");
        apiKeys.put("th", "122156");
        Map<String,String> apiSecrets = new HashMap<>();
        apiSecrets.put("id", "8jaskjdnjaH8uy2eiu123");
        apiSecrets.put("my", "8jaskjddsSEFuy2eiu123");
        apiSecrets.put("th", "8ZCetjdnjaH8uy2eiu123");
        lalamoveProperty.setApiKeys(apiKeys);
        lalamoveProperty.setApiSecrets(apiSecrets);
        lalamoveProperty.setBaseUrl(BASE_URL);

        RestTemplate template = new RestTemplate();
        mockServer = MockRestServiceServer.bindTo(template).build(new NoResetRequestExpectationManager());

        User user = new UserFactory().createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);
        state = stockLocation.getState();

        orderId = "849979813488758784";
        driverId = "84997981";
        request = new LalamoveApiGetDriverDetailsRequest(orderId, driverId, state);
        request.setLalamoveProperty(lalamoveProperty);
        request.setRestTemplate(template);
        CircuitBreakerRegistry circuitBreakerRegistry = new CustomCircuitBreakerConfig().circuitBreakerRegistry();
        request.setCircuitBreakerRegistry(circuitBreakerRegistry);
    }

    @Test
    public void shouldCallApi() {
        mockServer.expect(ExpectedCount.once(), requestTo(URI + orderId + "/drivers/" + driverId))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess("{ \"name\": \"David\", \"phone\": \"08321432123\", \"plateNumber\": \"B2314UG\", \"photo\": \"https://www.youtube.com/watch?v=dQw4w9WgXcQ\" }", MediaType.APPLICATION_JSON));
        request.makeRequest();
        mockServer.verify();
    }

    @Test
    public void shouldRespondWithOptional() {
        mockServer.expect(ExpectedCount.once(), requestTo(URI + orderId + "/drivers/" + driverId))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess("{ \"name\": \"David\", \"phone\": \"08321432123\", \"plateNumber\": \"B2314UG\", \"photo\": \"https://www.youtube.com/watch?v=dQw4w9WgXcQ\" }", MediaType.APPLICATION_JSON));

        Optional<LalamoveDriverDetailPresenter> maybePresenter = request.makeRequest();
        mockServer.verify();

        Assert.assertTrue(maybePresenter.isPresent());
        Assert.assertEquals("David", maybePresenter.get().getName());
        Assert.assertEquals("08321432123", maybePresenter.get().getPhone());
        Assert.assertEquals("B2314UG", maybePresenter.get().getPlateNumber());
        Assert.assertEquals("https://www.youtube.com/watch?v=dQw4w9WgXcQ", maybePresenter.get().getPhoto());
    }

    @Test
    public void shouldCallApi_withLlmMarketHeader_whenStatePreferencesExists() {
        state.setPreferences(new HashMap<String, String>(){{
            put("lalamove_city_code", "ID_JKT");
        }});
        request.setState(state);

        mockServer.expect(ExpectedCount.once(), requestTo(URI + orderId + "/drivers/" + driverId))
                .andExpect(method(HttpMethod.GET))
                .andExpect(header("X-LLM-Market", "ID_JKT"))
                .andRespond(withSuccess("{ \"name\": \"David\", \"phone\": \"08321432123\", \"plateNumber\": \"B2314UG\", \"photo\": \"https://www.youtube.com/watch?v=dQw4w9WgXcQ\" }", MediaType.APPLICATION_JSON));

        request.makeRequest();
        mockServer.verify();
    }

    @Test
    public void shouldCallApi_withoutLlmMarketHeader_whenStatePreferencesBlank() {
        state.setPreferences(new HashMap<String, String>(){{
            put("lalamove_city_code", "");
        }});
        request.setState(state);

        mockServer.expect(ExpectedCount.once(), requestTo(URI + orderId + "/drivers/" + driverId))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess("{ \"name\": \"David\", \"phone\": \"08321432123\", \"plateNumber\": \"B2314UG\", \"photo\": \"https://www.youtube.com/watch?v=dQw4w9WgXcQ\" }", MediaType.APPLICATION_JSON));

        request.makeRequest();
        mockServer.verify();
    }

    @Test
    public void withResponseErrorShouldReturnOptionalEmpty() throws Exception{
        mockServer.expect(ExpectedCount.once(), requestTo(URI + orderId + "/drivers/" + driverId))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withStatus(HttpStatus.valueOf(404))
                );

        Optional<LalamoveDriverDetailPresenter> maybePresenter = request.makeRequest();

        mockServer.verify();
        Assert.assertFalse(maybePresenter.isPresent());
    }
}
