package com.happyfresh.fulfillment.unit.lalamove.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.property.LalamoveProperty;
import com.happyfresh.fulfillment.common.service.WebhookPublisherService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.lalamove.mapper.LalamoveDeliveryMapper;
import com.happyfresh.fulfillment.lalamove.service.LalamoveDeliveryMessagingService;
import com.happyfresh.fulfillment.repository.LalamoveDeliveryRepository;
import com.happyfresh.fulfillment.unit.PowerMockBase;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.List;
import java.util.Optional;

import static org.mockito.Mockito.*;

public class LalamoveDeliveryMessagingServiceTest extends PowerMockBase {

    @Mock
    private Logger logger;

    @InjectMocks
    private LalamoveDeliveryMessagingService lalamoveDeliveryMessagingService;

    @Mock
    private LalamoveProperty lalamoveProperty;

    @Mock
    private LalamoveDeliveryMapper lalamoveDeliveryMapper;

    @Mock
    private WebhookPublisherService webhookPublisherService;

    @Mock
    private LalamoveDeliveryRepository lalamoveDeliveryRepository;

    private LalamoveDelivery delivery;
    private User user;
    private Shipment shipment;
    private ObjectMapper mapper;

    @Before
    public void setup() throws Exception {
        UserFactory userFactory = new UserFactory();
        user = userFactory.createUserData();

        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setAddress1("Jl. Penuh Kenangan");
        stockLocation.getCluster().setSlotType(Slot.Type.LONGER_DELIVERY);

        SlotFactory slotFactory = new SlotFactory();
        Slot slot = slotFactory.createSlot(stockLocation, user);
        ShipmentFactory shipmentFactory = new ShipmentFactory();
        shipment = shipmentFactory.createShipmentWithBatch(slot, user);

        setupLalamoveDelivery();

        Field loggerField = lalamoveDeliveryMessagingService.getClass().getDeclaredField("logger");
        loggerField.setAccessible(true);

        Field modifiersField = loggerField.getClass().getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(loggerField, loggerField.getModifiers() & ~Modifier.FINAL);

        loggerField.set(lalamoveDeliveryMessagingService, logger);

        mapper = new ObjectMapper();
        ReflectionTestUtils.setField(lalamoveDeliveryMessagingService, "mapper", mapper);

    }

    private void setupLalamoveDelivery(){
        LalamoveDeliveryFactory factory = new LalamoveDeliveryFactory();
        delivery = factory.create(shipment, user);
    }

    @Test
    public void logSentMessage() {
        lalamoveDeliveryMessagingService.logSentMessage(1L);
        verify(logger, atLeastOnce()).info(anyString(), anyLong());
    }

    @Test
    public void logReceivedMessage() {
        lalamoveDeliveryMessagingService.logReceivedMessage(1L);
        verify(logger, atLeastOnce()).info(anyString(), anyLong());
    }

    @Test
    public void logEndMessage() {
        lalamoveDeliveryMessagingService.logEndOfUpdateTasks(1L);
        verify(logger, atLeastOnce()).info(anyString(), anyLong());
    }

    @Test
    public void publishSynchronizeWebhook(){
        lalamoveDeliveryMessagingService.publishSynchronizeWebhook(delivery);
        verify(webhookPublisherService, timeout(1)).publish(eq(WebhookType.LALAMOVE_SYNCHRONIZE), any(), anyString(), eq(shipment.getTenant().getId()));
    }

    @Test
    public void publishFailedBookingWebhookUsingShipmentParameter() {
        Mockito.doReturn(Optional.of(delivery)).when(lalamoveDeliveryRepository).findByShipment(any());
        lalamoveDeliveryMessagingService.publishFailedBookingWebhook(shipment, "Unexpected Error");
        verify(lalamoveDeliveryRepository, times(1)).delete(delivery.getId());
        verify(webhookPublisherService, times(1)).publish(eq(WebhookType.LALAMOVE_BOOKING_ERROR_SWITCH_TO_HF), any(),
                anyString(), eq(shipment.getTenant().getId()));
    }

    @Test
    public void publishFailedBookingWebhookUsingLalamoveDeliveryParameter() {
        lalamoveDeliveryMessagingService.publishFailedBookingWebhook(delivery, "Unexpected Error");
        verify(lalamoveDeliveryRepository, times(1)).delete(delivery.getId());
        verify(webhookPublisherService, times(1)).publish(eq(WebhookType.LALAMOVE_BOOKING_ERROR_SWITCH_TO_HF), any(),
                anyString(), eq(shipment.getTenant().getId()));
    }

    @Test
    public void publishFailedBookingWebhookMapperIOException() throws IOException{
        ObjectMapper objMapper = Mockito.mock(ObjectMapper.class);
        Mockito.doThrow(new IOException("test")).when(objMapper).readTree(anyString());
        ReflectionTestUtils.setField(lalamoveDeliveryMessagingService, "mapper", objMapper);

        lalamoveDeliveryMessagingService.publishFailedBookingWebhook(delivery, "Unexpected Error");
        verify(lalamoveDeliveryRepository, times(1)).delete(delivery.getId());
        verify(webhookPublisherService, times(1)).publish(eq(WebhookType.LALAMOVE_BOOKING_ERROR_SWITCH_TO_HF), any(),
                anyString(), eq(shipment.getTenant().getId()));
        Mockito.verify(logger, Mockito.atLeastOnce()).error(anyString(), anyString());
    }

}
