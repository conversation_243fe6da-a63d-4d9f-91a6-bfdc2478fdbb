package com.happyfresh.fulfillment.unit.lalamove.service.api.v3;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.config.CustomCircuitBreakerConfig;
import com.happyfresh.fulfillment.common.property.LalamoveProperty;
import com.happyfresh.fulfillment.entity.Role;
import com.happyfresh.fulfillment.entity.State;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.integrationTest.test.common.NoResetRequestExpectationManager;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.lalamove.form.v3.LalamoveV3PlaceOrderForm;
import com.happyfresh.fulfillment.lalamove.presenter.v3.LalamoveV3OrderPresenter;
import com.happyfresh.fulfillment.lalamove.service.api.v3.LalamoveApiV3GetOrderDetailRequest;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static java.nio.file.Files.readAllBytes;
import static java.nio.file.Paths.get;
import static org.junit.Assert.*;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

@RunWith(MockitoJUnitRunner.class)
public class LalamoveApiV3GetOrderDetailRequestTest {
    public static final String BASE_URL = "https://sandbox-rest.lalamove.com";
    public static final String URI = BASE_URL + "/v3/orders/";
    private MockRestServiceServer mockServer;
    private LalamoveApiV3GetOrderDetailRequest request;
    private State state;
    private LalamoveV3PlaceOrderForm form;
    private ObjectMapper mapper;
    private RestTemplate restTemplate;
    private LalamoveProperty lalamoveProperty;
    private CircuitBreakerRegistry circuitBreakerRegistry;
    private String externalOrderId;

    @Before
    public void setup() throws IOException {
        Map<String,String> apiKeys = new HashMap<>();
        apiKeys.put("id", "123456");
        apiKeys.put("my", "122256");
        apiKeys.put("th", "122156");
        Map<String,String> apiSecrets = new HashMap<>();
        apiSecrets.put("id", "8jaskjdnjaH8uy2eiu123");
        apiSecrets.put("my", "8jaskjddsSEFuy2eiu123");
        apiSecrets.put("th", "8ZCetjdnjaH8uy2eiu123");
        lalamoveProperty = new LalamoveProperty();
        lalamoveProperty.setApiKeys(apiKeys);
        lalamoveProperty.setApiSecrets(apiSecrets);
        lalamoveProperty.setBaseUrl(BASE_URL);

        restTemplate = new RestTemplate();
        mockServer = MockRestServiceServer.bindTo(restTemplate).build(new NoResetRequestExpectationManager());

        mapper = new ObjectMapper();
        User user = new UserFactory().createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);
        state = stockLocation.getState();

        externalOrderId = "44333223343221";
        request = new LalamoveApiV3GetOrderDetailRequest(state, externalOrderId);
        request.setLalamoveProperty(lalamoveProperty);
        request.setRestTemplate(restTemplate);
        circuitBreakerRegistry = new CustomCircuitBreakerConfig().circuitBreakerRegistry();
        request.setCircuitBreakerRegistry(circuitBreakerRegistry);
    }

    private String getResponseFromResourceFile(String fileName) throws IOException {
        return new String(readAllBytes(get("src", "test", "resources", "fixtures", fileName)));
    }

    private void assertResponse(LalamoveV3OrderPresenter expectedResponse, LalamoveV3OrderPresenter presenter) {
        LalamoveV3OrderPresenter.Data responseData = presenter.getData();
        LalamoveV3OrderPresenter.Data expectedResponseData = expectedResponse.getData();

        assertEquals(expectedResponseData.getOrderId(), responseData.getOrderId());
        assertEquals(expectedResponseData.getDriverId(), responseData.getDriverId());
        assertEquals(expectedResponseData.getQuotationId(), responseData.getQuotationId());
        assertEquals(expectedResponseData.getShareLink(), responseData.getShareLink());
        assertEquals(expectedResponseData.getStatus(), responseData.getStatus());
        assertEquals(expectedResponseData.getStops().get(1).getPod().getStatus(), responseData.getStops().get(1).getPod().getStatus());

        LalamoveV3OrderPresenter.PriceBreakdown expectedPriceBreakdown = expectedResponseData.getPriceBreakdown();
        LalamoveV3OrderPresenter.PriceBreakdown priceBreakdown = responseData.getPriceBreakdown();

        assertEquals(expectedPriceBreakdown.getBase(), priceBreakdown.getBase());
        assertEquals(expectedPriceBreakdown.getCurrency(), priceBreakdown.getCurrency());
        assertEquals(expectedPriceBreakdown.getSpecialRequests(), priceBreakdown.getSpecialRequests());
        assertEquals(expectedPriceBreakdown.getDiscountCap(), priceBreakdown.getDiscountCap());
        assertEquals(expectedPriceBreakdown.getTotal(), priceBreakdown.getTotal());
        assertEquals(expectedPriceBreakdown.getSurcharge(), priceBreakdown.getSurcharge());
        assertEquals(expectedPriceBreakdown.getMinimumSurcharge(), priceBreakdown.getMinimumSurcharge());
        assertEquals(expectedPriceBreakdown.getVat(), priceBreakdown.getVat());
        assertEquals(expectedPriceBreakdown.getTotalExcludePriorityFee(), priceBreakdown.getTotalExcludePriorityFee());

    }

    @Test
    public void shouldCallApi() throws Exception {
        String response = getResponseFromResourceFile("lalamove_v3_place_order_response_success.json");
        mockServer.expect(ExpectedCount.once(), requestTo(URI + externalOrderId))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));
        request.makeRequest();
        mockServer.verify();
    }

    @Test
    public void shouldRespondWithOptional() throws IOException {
        String response = getResponseFromResourceFile("lalamove_v3_place_order_response_success.json");
        LalamoveV3OrderPresenter expectedResponse = mapper.readValue(response, LalamoveV3OrderPresenter.class);

        mockServer.expect(ExpectedCount.once(), requestTo(URI + externalOrderId))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));

        Optional<LalamoveV3OrderPresenter> maybePresenter = request.makeRequest();
        mockServer.verify();

        assertTrue(maybePresenter.isPresent());
        LalamoveV3OrderPresenter presenter = maybePresenter.get();
        assertNotNull(presenter.getData());
        assertResponse(expectedResponse, presenter);
    }

    @Test
    public void withResponseSuccessWithError_shouldReturnNullData() {
        String response = "{\"errors\":[{\"id\":\"ERR_ORDER_NOT_FOUND\",\"message\":\"Order not found.\"}]}";
        mockServer.expect(ExpectedCount.once(), requestTo(URI + externalOrderId))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON)
                        .body(response));

        Optional<LalamoveV3OrderPresenter> maybePresenter = request.makeRequest();
        mockServer.verify();

        Assert.assertTrue(maybePresenter.isPresent());

        LalamoveV3OrderPresenter orderPresenter = maybePresenter.get();
        Assert.assertNotNull(orderPresenter.getErrors());
        Assert.assertEquals(1, orderPresenter.getErrors().size());
        Assert.assertEquals("ERR_ORDER_NOT_FOUND", orderPresenter.getErrors().get(0).getId());
        Assert.assertEquals("Order not found.", orderPresenter.getErrors().get(0).getMessage());

        Assert.assertNull(maybePresenter.get().getData());
    }

    @Test
    public void withResponseOtherThanSuccess_shouldReturnOptionalEmpty() {
        mockServer.expect(ExpectedCount.once(), requestTo(URI + externalOrderId))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withStatus(HttpStatus.valueOf(404))
                        .contentType(MediaType.APPLICATION_JSON)
                        .body("{ \"message\": \"UNKNOWN_ERROR\"  }")
                );
        Optional<LalamoveV3OrderPresenter> maybePresenter = request.makeRequest();
        mockServer.verify();
        Assert.assertFalse(maybePresenter.isPresent());
    }

}
