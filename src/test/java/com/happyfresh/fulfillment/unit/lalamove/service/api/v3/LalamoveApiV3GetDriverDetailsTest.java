package com.happyfresh.fulfillment.unit.lalamove.service.api.v3;

import com.happyfresh.fulfillment.common.config.CustomCircuitBreakerConfig;
import com.happyfresh.fulfillment.common.property.LalamoveProperty;
import com.happyfresh.fulfillment.entity.Role;
import com.happyfresh.fulfillment.entity.State;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.integrationTest.test.common.NoResetRequestExpectationManager;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.lalamove.presenter.v3.LalamoveV3DriverDetailPresenter;
import com.happyfresh.fulfillment.lalamove.service.api.v3.LalamoveApiV3GetDriverDetailsRequest;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.springframework.test.web.client.match.MockRestRequestMatchers.*;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

@RunWith(MockitoJUnitRunner.class)
public class LalamoveApiV3GetDriverDetailsTest {
    public static final String BASE_URL = "https://sandbox-rest.lalamove.com";
    public static final String URI = BASE_URL + "/v3/orders/";
    private MockRestServiceServer mockServer;
    private LalamoveApiV3GetDriverDetailsRequest request;
    private String orderId;
    private String driverId;
    private State state;
    private String successResponse;

    @Before
    public void setup() {
        LalamoveProperty lalamoveProperty = new LalamoveProperty();
        Map<String, String> apiKeys = new HashMap<>();
        apiKeys.put("id", "123456");
        apiKeys.put("my", "122256");
        apiKeys.put("th", "122156");
        Map<String, String> apiSecrets = new HashMap<>();
        apiSecrets.put("id", "8jaskjdnjaH8uy2eiu123");
        apiSecrets.put("my", "8jaskjddsSEFuy2eiu123");
        apiSecrets.put("th", "8ZCetjdnjaH8uy2eiu123");
        lalamoveProperty.setApiKeys(apiKeys);
        lalamoveProperty.setApiSecrets(apiSecrets);
        lalamoveProperty.setBaseUrl(BASE_URL);

        RestTemplate template = new RestTemplate();
        mockServer = MockRestServiceServer.bindTo(template).build(new NoResetRequestExpectationManager());

        User user = new UserFactory().createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);
        state = stockLocation.getState();

        orderId = "120730409394";
        driverId = "79977";
        request = new LalamoveApiV3GetDriverDetailsRequest(orderId, driverId, state);
        request.setLalamoveProperty(lalamoveProperty);
        request.setRestTemplate(template);
        CircuitBreakerRegistry circuitBreakerRegistry = new CustomCircuitBreakerConfig().circuitBreakerRegistry();
        request.setCircuitBreakerRegistry(circuitBreakerRegistry);

        successResponse = "{\"data\":{\"driverId\":\"79977\",\"name\":\"TestDriver 88884\",\"phone\":\"+6288888884\",\"plateNumber\":\"**751947*\",\"photo\":\"https://www.happyfresh.id/\"}}";
    }

    @Test
    public void shouldCallApi() {
        mockServer.expect(ExpectedCount.once(), requestTo(URI + orderId + "/drivers/" + driverId))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess(successResponse, MediaType.APPLICATION_JSON));
        request.makeRequest();
        mockServer.verify();
    }

    @Test
    public void shouldRespondWithOptional() {
        mockServer.expect(ExpectedCount.once(), requestTo(URI + orderId + "/drivers/" + driverId))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess(successResponse, MediaType.APPLICATION_JSON));

        Optional<LalamoveV3DriverDetailPresenter> maybePresenter = request.makeRequest();
        mockServer.verify();

        Assert.assertTrue(maybePresenter.isPresent());
        Assert.assertEquals("TestDriver 88884", maybePresenter.get().getData().getName());
        Assert.assertEquals("+6288888884", maybePresenter.get().getData().getPhone());
        Assert.assertEquals("**751947*", maybePresenter.get().getData().getPlateNumber());
        Assert.assertEquals("https://www.happyfresh.id/", maybePresenter.get().getData().getPhoto());
    }

    @Test
    public void shouldCallApi_withMarketHeader_whenStatePreferencesExists() {
        state.setPreferences(new HashMap<String, String>() {{
            put("lalamove_city_code", "ID_JKT");
        }});
        request.setState(state);

        mockServer.expect(ExpectedCount.once(), requestTo(URI + orderId + "/drivers/" + driverId))
                .andExpect(method(HttpMethod.GET))
                .andExpect(header("Market", "ID"))
                .andRespond(withSuccess(successResponse, MediaType.APPLICATION_JSON));

        request.makeRequest();
        mockServer.verify();
    }

    @Test
    public void shouldCallApi_withoutLlmMarketHeader_whenStatePreferencesBlank() {
        state.setPreferences(new HashMap<String, String>() {{
            put("lalamove_city_code", "");
        }});
        request.setState(state);

        mockServer.expect(ExpectedCount.once(), requestTo(URI + orderId + "/drivers/" + driverId))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess(successResponse, MediaType.APPLICATION_JSON));

        request.makeRequest();
        mockServer.verify();
    }

    @Test
    public void withResponseErrorShouldReturnOptionalEmpty() throws Exception {
        String response = "{\"errors\":[{\"id\":\"ERR_DRIVER_NOT_FOUND\",\"message\":\"Driver not found.\"}]}";
        mockServer.expect(ExpectedCount.once(), requestTo(URI + orderId + "/drivers/" + driverId))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withStatus(HttpStatus.valueOf(404)).contentType(MediaType.APPLICATION_JSON)
                        .body(response));

        Optional<LalamoveV3DriverDetailPresenter> maybePresenter = request.makeRequest();

        mockServer.verify();
        Assert.assertFalse(maybePresenter.isPresent());
    }
}
