package com.happyfresh.fulfillment.unit.lalamove.scheduler;

import com.happyfresh.fulfillment.lalamove.scheduler.LalamoveServiceTypeFetcherScheduler;
import com.happyfresh.fulfillment.lalamove.service.LalamoveService;
import net.javacrumbs.shedlock.core.LockAssert;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class LalamoveServiceTypeFetcherSchedulerTest {

    @InjectMocks
    private LalamoveServiceTypeFetcherScheduler scheduler;

    @Mock
    private LalamoveService lalamoveService;

    @Rule
    public ExpectedException expectedEx = ExpectedException.none();

    @Test
    public void fetchServiceTypes_withLockAssertTrue_shouldCallService() {
        LockAssert.TestHelper.makeAllAssertsPass(true);
        scheduler.fetchServiceTypes();
        verify(lalamoveService, times(1)).updateOrCreateServiceTypesForAllTenants();
    }

    @Test
    public void fetchServiceTypes_withLockAssertFalse_shouldNotCallService() {
        LockAssert.TestHelper.makeAllAssertsPass(false);

        expectedEx.expect(IllegalStateException.class);
        expectedEx.expectMessage("The task is not locked.");

        scheduler.fetchServiceTypes();

        verify(lalamoveService, never()).updateOrCreateServiceTypesForAllTenants();
    }

}
