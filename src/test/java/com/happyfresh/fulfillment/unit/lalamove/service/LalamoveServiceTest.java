package com.happyfresh.fulfillment.unit.lalamove.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.happyfresh.fulfillment.batch.exception.SwitchToHFFailedException;
import com.happyfresh.fulfillment.batch.service.BatchService;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.service.JedisLockService;
import com.happyfresh.fulfillment.common.service.WebhookPublisherService;
import com.happyfresh.fulfillment.common.service.radar.RadarApiService;
import com.happyfresh.fulfillment.enabler.service.StratoWebhookService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.lalamove.exception.LalamoveException;
import com.happyfresh.fulfillment.lalamove.mapper.LalamoveOrderDetailMapper;
import com.happyfresh.fulfillment.lalamove.model.LalamoveDriverDetail;
import com.happyfresh.fulfillment.lalamove.model.LalamoveOrderDetail;
import com.happyfresh.fulfillment.lalamove.model.LalamoveQuotedTotalFee;
import com.happyfresh.fulfillment.lalamove.model.LalamoveServiceTypeEnum;
import com.happyfresh.fulfillment.lalamove.presenter.LalamoveOrderDetailPresenter;
import com.happyfresh.fulfillment.lalamove.presenter.v3.LalamoveV3ErrorResponse;
import com.happyfresh.fulfillment.lalamove.presenter.LalamoveDriverDetailPresenter;
import com.happyfresh.fulfillment.lalamove.presenter.LalamoveGetQuotationPresenter;
import com.happyfresh.fulfillment.lalamove.presenter.LalamovePlaceOrderPresenter;
import com.happyfresh.fulfillment.lalamove.presenter.v3.LalamoveV3DriverDetailPresenter;
import com.happyfresh.fulfillment.lalamove.presenter.v3.LalamoveV3GetCityInfoPresenter;
import com.happyfresh.fulfillment.lalamove.presenter.v3.LalamoveV3GetQuotationPresenter;
import com.happyfresh.fulfillment.lalamove.presenter.v3.LalamoveV3OrderPresenter;
import com.happyfresh.fulfillment.lalamove.service.LalamoveDeliveryMessagingService;
import com.happyfresh.fulfillment.lalamove.service.LalamoveDeliveryTrackingService;
import com.happyfresh.fulfillment.lalamove.service.LalamoveService;
import com.happyfresh.fulfillment.lalamove.service.api.LalamoveApiService;
import com.happyfresh.fulfillment.lalamove.service.api.v3.LalamoveApiV3Service;
import com.happyfresh.fulfillment.repository.*;
import org.assertj.core.util.Lists;
import org.junit.*;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mapstruct.factory.Mappers;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.test.util.ReflectionTestUtils;

import javax.persistence.EntityNotFoundException;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class LalamoveServiceTest {

    @Mock
    private Logger logger;

    @Mock
    private ShipmentRepository shipmentRepository;

    @Mock
    private LalamoveApiService lalamoveApiService;

    @Mock
    private LalamoveApiV3Service lalamoveApiV3Service;

    @Mock
    private RoleRepository roleRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private JedisLockService jedisLockService;

    @Mock
    private LalamoveDeliveryRepository lalamoveDeliveryRepository;

    @Mock
    private LalamoveServiceTypeRepository serviceTypeRepository;

    @Mock
    private WebhookPublisherService webhookPublisherService;

    @Mock
    private LalamoveDeliveryTrackingService lalamoveDeliveryTrackingService;

    @Mock
    private LalamoveDeliveryMessagingService lalamoveDeliveryMessagingService;

    @Mock
    private AbstractAuthenticationToken abstractAuthenticationToken;

    @Captor
    private ArgumentCaptor<LalamoveDelivery> lalamoveDeliveryArgumentCaptor;

    @Captor
    private ArgumentCaptor<List<LalamoveServiceType>> serviceTypeCaptor;

    @Mock
    private TenantRepository tenantRepository;

    @Mock
    private TransactionHelper transactionHelper;

    @Mock
    private CountryRepository countryRepository;

    @Mock
    private BatchRepository batchRepository;

    @Mock
    private JobRepository jobRepository;

    @Mock
    private RadarApiService radarApiService;

    @Mock
    private StratoWebhookService stratoWebhookService;

    @InjectMocks
    private LalamoveService lalamoveService;

    @Mock
    private BatchService batchService;

    @Rule
    public ExpectedException expectedEx = ExpectedException.none();

    private ObjectMapper mapper;
    private List<StockLocation> stockLocations;
    private StockLocation stockLocation;
    private Country country;
    private User user;
    private User shopper;
    private Slot shoppingSlot;
    private Slot deliverySlot;
    private Shipment shipment;
    private LalamoveDelivery lalamoveDelivery;
    private List<LalamoveServiceType> serviceTypes;
    private List<LalamoveServiceType> serviceTypesV3;
    private User userSystemAdmin;
    private LalamoveGetQuotationPresenter getQuotationPresenter;
    private LalamoveV3GetQuotationPresenter v3GetQuotationPresenter;
    private LalamoveV3GetCityInfoPresenter cityInfoPresenter;
    private LalamovePlaceOrderPresenter placeOrderPresenter;
    private LalamoveV3OrderPresenter placeOrderV3Presenter;
    private Batch shoppingBatch;
    private Batch deliveryBatch;
    private BatchFactory batchFactory;
    private LalamoveDeliveryFactory factory;
    private Tenant tenant;

    @Before
    public void setup() throws IOException, InterruptedException, NoSuchFieldException, IllegalAccessException {
        mapper = new ObjectMapper();

        setupUser();
        setupStockLocation();
        setupServiceType();
        setupSlot();
        setupShipment();
        setupLalamoveDelivery();
        setupBatch();

        mapLalamoveApiResponse();
        mockClasses();

        injectLogger();

        setupMapper();
    }

    private void setupMapper() {
        LalamoveOrderDetailMapper orderDetailMapper = Mappers.getMapper(LalamoveOrderDetailMapper.class);
        ReflectionTestUtils.setField(lalamoveService, "orderDetailMapper", orderDetailMapper);
    }

    private void injectLogger() throws NoSuchFieldException, IllegalAccessException {
        Field loggerField = LalamoveService.class.getDeclaredField("logger");
        loggerField.setAccessible(true);

        Field modifiersField = loggerField.getClass().getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(loggerField, loggerField.getModifiers() & ~Modifier.FINAL);

        loggerField.set(lalamoveService, logger);
    }

    private void setupBatch() {
        batchFactory = new BatchFactory();
        shoppingBatch = batchFactory.createBatch(user, shipment, shoppingSlot, Batch.Type.SHOPPING);
        shoppingBatch.setUser(shopper);
        deliveryBatch = batchFactory.createBatch(user, shipment, shoppingSlot, Batch.Type.DELIVERY);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.LALAMOVE);

        shipment.setJobs(Stream.concat(shoppingBatch.getJobs().stream(), deliveryBatch.getJobs().stream())
                .collect(Collectors.toList()));
    }

    private void mockClasses() throws InterruptedException {
        Role systemAdminRole = userSystemAdmin.getRoles().get(0);
        when(roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, stockLocation.getTenant().getId()))
                .thenReturn(systemAdminRole);
        when(userRepository.findByRolesContainingAndTenantId(systemAdminRole, stockLocation.getTenant().getId()))
                .thenReturn(userSystemAdmin);
        when(lalamoveApiService.getQuotation(eq(shipment), any(LocalDateTime.class), any(LalamoveServiceTypeEnum.class),
                any(User.class), any(Country.class), eq(stockLocation))).thenReturn(getQuotationPresenter);

        when(lalamoveApiService.placeOrder(eq(shipment), any(LocalDateTime.class), any(LalamoveServiceTypeEnum.class),
                any(User.class), any(Country.class), eq(stockLocation), eq(getQuotationPresenter))).thenReturn(placeOrderPresenter);
        when(shipmentRepository.findLalamoveShipmentsByShoppingBatch(shoppingBatch.getId())).thenReturn(Collections.singletonList(shipment));
        when(shipmentRepository.findByNumber(shipment.getNumber())).thenReturn(shipment);

        when(applicationContext.getBean(JedisLockService.class)).thenReturn(jedisLockService);
        when(jedisLockService.lock(anyString())).thenReturn(true);

        when(lalamoveDeliveryRepository.findByShipment(shipment)).thenReturn(Optional.of(lalamoveDelivery));
        when(serviceTypeRepository.findAllV2ByCountryIdOrderByMaxWeightAsc(country.getId())).thenReturn(serviceTypes);
        when(serviceTypeRepository.findAllActiveV3ByCityCode("JKT")).thenReturn(serviceTypesV3);
        when(lalamoveDeliveryRepository.save(any(LalamoveDelivery.class))).thenReturn(lalamoveDelivery);

        when(lalamoveApiV3Service.getQuotation(eq(shipment), any(LocalDateTime.class),
                eq(stockLocation), any(LalamoveServiceTypeEnum.class), any())).thenReturn(v3GetQuotationPresenter);
        when(lalamoveApiV3Service.placeOrder(eq(shipment),
                any(User.class), any(Country.class), eq(stockLocation), eq(v3GetQuotationPresenter))).thenReturn(placeOrderV3Presenter);
    }

    private void setupServiceType() {
        LalamoveServiceTypeFactory lalamoveServiceTypeFactory = new LalamoveServiceTypeFactory();
        serviceTypes = lalamoveServiceTypeFactory.createServiceTypeMotorAndMPV(user, stockLocation);
        serviceTypesV3 = lalamoveServiceTypeFactory.createServiceTypeMotorAndMPVV3(user, stockLocation, "JKT");
    }

    private void setupLalamoveDelivery() {
        factory = new LalamoveDeliveryFactory();
        lalamoveDelivery = factory.create(shipment, user);
        lalamoveDelivery.setExternalOrderId("EXTERNAL_ID");
        lalamoveDelivery.setCreatedAt(LocalDateTime.now());
        lalamoveDelivery.setUpdatedAt(LocalDateTime.now());
        lalamoveDelivery.setPrice(new BigDecimal("5000.0"));
        lalamoveDelivery.setInitialPrice(new BigDecimal("5000.0"));
        lalamoveDelivery.setInitialServiceType("SERVICE-CODE");
        lalamoveDelivery.setServiceType("SERVICE-CODE");
        lalamoveDelivery.setId(1L);

        shipment.setTplDeliveries(Collections.singletonList(lalamoveDelivery));
    }

    private void setupShipment() {
        ShipmentFactory shipmentFactory = new ShipmentFactory();
        shipment = shipmentFactory.createShipment(shoppingSlot, user);

        ItemFactory itemFactory = new ItemFactory();
        List<Item> items = itemFactory.createItems(shipment, user, 1);
        shipment.setItems(items);
    }

    private void setupSlot() {
        SlotFactory slotFactory = new SlotFactory();
        LocalDateTime today = LocalDateTime.now();
        List<Slot> shoppingSlots = slotFactory.createLongerDeliverySlots(stockLocation, user, 1, 1, today);
        shoppingSlot = shoppingSlots.get(0);

        List<Slot> deliverySlots = slotFactory.createLongerDeliverySlots(stockLocation, user, 1, 1, shoppingSlot.getEndTime().plusMinutes(30));
        deliverySlot = deliverySlots.get(0);
    }

    private void setupStockLocation() {
        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user);
        stockLocation = stockLocations.get(0);
        stockLocation.getCluster().setSlotType(Slot.Type.LONGER_DELIVERY);
        stockLocation.setShopperAveragePickingTimePerUniqItem(2);
        stockLocation.setMaxShoppingVolume(200);

        country = stockLocation.getState().getCountry();

        configLalamoveInStockLocation(true, true, "1500");
    }

    private void configLalamoveInStockLocation(boolean enableLalamove, boolean enableLalamoveDeliveryFee, String lalamoveFlatFee) {
        stockLocation.setEnableLalamove(enableLalamove);
        stockLocation.setPreferences(new HashMap<String, String>() {{
            put("enable_lalamove_delivery_fee", String.valueOf(enableLalamoveDeliveryFee));
            put("lalamove_flat_service_fee", lalamoveFlatFee);
            put("grab_express_offset_time", "3000");
        }});
    }

    private void setupUser() {
        UserFactory userFactory = new UserFactory();
        user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        tenant = user.getTenant(); tenant.setId(1L);
        userSystemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, tenant);
        shopper = userFactory.createUserData(Role.Name.SHOPPER, tenant);
    }

    private String getResponseFromResourceFile(String fileName) throws IOException {
        return new String(Files.readAllBytes(Paths.get("src", "test", "resources", "fixtures", fileName)));
    }

    private void mapLalamoveApiResponse() throws IOException {
        getQuotationPresenter = mapper.readValue("{ \"totalFee\": \"16000\", \"totalFeeCurrency\": \"IDR\" }", LalamoveGetQuotationPresenter.class);
        placeOrderPresenter = mapper.readValue("{ \"customerOrderId\": \"52342\", \"orderRef\": \"LM9342\" }", LalamovePlaceOrderPresenter.class);

        String v3GetQuotationResponse = getResponseFromResourceFile("lalamove_v3_get_quotation_response_success.json");
        v3GetQuotationPresenter = mapper.readValue(v3GetQuotationResponse, LalamoveV3GetQuotationPresenter.class);

        String placeOrderV3Response = getResponseFromResourceFile("lalamove_v3_place_order_response_success.json");
        placeOrderV3Presenter = mapper.readValue(placeOrderV3Response, LalamoveV3OrderPresenter.class);

        String cityInfoResponse = getResponseFromResourceFile("lalamove_v3_get_city_info_response_success.json");
        cityInfoPresenter = mapper.readValue(cityInfoResponse, LalamoveV3GetCityInfoPresenter.class);
    }

    private void verifyDeliveryStatus(LalamoveDelivery.Status status) {
        verify(lalamoveDeliveryRepository).save(lalamoveDeliveryArgumentCaptor.capture());
        LalamoveDelivery savedLalamoveDelivery = lalamoveDeliveryArgumentCaptor.getValue();
        assertEquals(status, savedLalamoveDelivery.getStatus());
    }

    private void verifyDeliveryStatus(LalamoveDelivery.Status status, BigDecimal price) {
        verify(lalamoveDeliveryRepository).save(lalamoveDeliveryArgumentCaptor.capture());
        LalamoveDelivery savedLalamoveDelivery = lalamoveDeliveryArgumentCaptor.getValue();
        assertEquals(status, savedLalamoveDelivery.getStatus());
        assertEquals(price, savedLalamoveDelivery.getPrice());
    }

    @Test
    public void whenGetDeliveryQuotation_shouldReturnCorrectFee() {
        Double fee = lalamoveService.getDeliveryQuotation(serviceTypes.get(0), shipment, stockLocation);
        assertEquals(Double.valueOf(16000), fee);
    }

    @Test
    public void whenGetDeliveryQuotation_lalamoveV3_shouldReturnCorrectFee() {
        Map<String, String> tenantPreferences = country.getTenant().getPreferences();
        tenantPreferences.put("enable_lalamove_api_v3", "true");
        country.getTenant().setPreferences(tenantPreferences);
        Double fee = lalamoveService.getDeliveryQuotation(serviceTypes.get(0), shipment, stockLocation);
        assertEquals(Double.valueOf(31900), fee);
    }

    @Test
    public void whenGetDeliveryQuotation_lalamoveV3_andExceptionGetServiceTypeEnum_shouldReturnZeroFee() {
        Map<String, String> tenantPreferences = country.getTenant().getPreferences();
        tenantPreferences.put("enable_lalamove_api_v3", "true");
        country.getTenant().setPreferences(tenantPreferences);
        serviceTypes.get(0).setKey("4x4");
        Double fee = lalamoveService.getDeliveryQuotation(serviceTypes.get(0), shipment, stockLocation);
        assertEquals(Double.valueOf(0), fee);
        verify(logger).error("LalamoveServiceType {} not found in LalamoveServiceTypeEnum.java for shipment {}", "4x4", shipment.getNumber());
    }

    @Test
    public void whenGetDeliveryQuotation_andRoleIsNull_shouldReturnZeroFee() {
        when(roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, stockLocation.getTenant().getId()))
                .thenReturn(null);
        verify(lalamoveApiService, never()).getQuotation(eq(shipment), any(LocalDateTime.class), eq(serviceTypes.get(0).getServiceTypeEnum()),
                eq(userSystemAdmin), eq(stockLocation.getState().getCountry()), eq(stockLocation));

        Double fee = lalamoveService.getDeliveryQuotation(serviceTypes.get(0), shipment, stockLocation);
        assertEquals(Double.valueOf(0), fee);
    }

    @Test
    public void whenGetDeliveryQuotation_andQuotationIsNull_shouldReturnZeroFee() {
        when(lalamoveApiService.getQuotation(eq(shipment), any(LocalDateTime.class), eq(serviceTypes.get(0).getServiceTypeEnum()),
                eq(userSystemAdmin), eq(stockLocation.getState().getCountry()), eq(stockLocation))).thenReturn(null);

        Double fee = lalamoveService.getDeliveryQuotation(serviceTypes.get(0), shipment, stockLocation);
        assertEquals(Double.valueOf(0), fee);

        getQuotationPresenter.setMessage("Error fetching quotation");
        when(lalamoveApiService.getQuotation(eq(shipment), any(LocalDateTime.class), eq(serviceTypes.get(0).getServiceTypeEnum()),
                eq(userSystemAdmin), eq(stockLocation.getState().getCountry()), eq(stockLocation))).thenReturn(getQuotationPresenter);

        fee = lalamoveService.getDeliveryQuotation(serviceTypes.get(0), shipment, stockLocation);
        assertEquals(Double.valueOf(0), fee);
    }

    @Test
    public void whenGetDeliveryQuotation_lalamoveV3_andQuotationIsNull_shouldReturnZeroFee() {
        Map<String, String> tenantPreferences = country.getTenant().getPreferences();
        tenantPreferences.put("enable_lalamove_api_v3", "true");
        country.getTenant().setPreferences(tenantPreferences);

        when(lalamoveApiV3Service.getQuotation(eq(shipment), any(LocalDateTime.class), eq(stockLocation),
                any(LalamoveServiceTypeEnum.class), any())).thenReturn(null);

        Double fee = lalamoveService.getDeliveryQuotation(serviceTypes.get(0), shipment, stockLocation);
        assertEquals(Double.valueOf(0), fee);

        LalamoveV3ErrorResponse error = getErrorResponse();
        v3GetQuotationPresenter.setErrors(Lists.newArrayList(error));
        when(lalamoveApiV3Service.getQuotation(eq(shipment), any(LocalDateTime.class), eq(stockLocation),
                any(LalamoveServiceTypeEnum.class), any())).thenReturn(v3GetQuotationPresenter);

        fee = lalamoveService.getDeliveryQuotation(serviceTypes.get(0), shipment, stockLocation);
        assertEquals(Double.valueOf(0), fee);
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_shouldPlaceOrderToLalamove() {
        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verifyDeliveryStatus(LalamoveDelivery.Status.ORDER_PLACED);
        verify(webhookPublisherService, never()).publish(eq(WebhookType.LALAMOVE_BOOKING_ERROR), any(ObjectNode.class), anyString(), anyLong());
        verify(lalamoveDeliveryTrackingService).track(eq("Lalamove Update Log"), any(LalamoveDelivery.class));
        verify(lalamoveDeliveryMessagingService).queueUpdateJob(anyLong());
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_whenDisableLalamoveFee_shouldUseDDf() {

        Map<String, String> preferences = stockLocation.getPreferences();
        preferences.put("enable_lalamove_delivery_fee", "false");

        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verifyDeliveryStatus(LalamoveDelivery.Status.ORDER_PLACED, shipment.getCost());
        verify(webhookPublisherService, never()).publish(eq(WebhookType.LALAMOVE_BOOKING_ERROR), any(ObjectNode.class), anyString(), anyLong());
        verify(lalamoveDeliveryTrackingService).track(eq("Lalamove Update Log"), any(LalamoveDelivery.class));
        verify(lalamoveDeliveryMessagingService).queueUpdateJob(anyLong());
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_andLalamoveStatusIsNotInitial_shouldDoNothing() {
        lalamoveDelivery.setStatus(LalamoveDelivery.Status.ORDER_PLACED);
        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verify(webhookPublisherService, never()).publish(eq(WebhookType.LALAMOVE_BOOKING_ERROR), any(ObjectNode.class), anyString(), anyLong());
        verify(lalamoveDeliveryTrackingService, never()).track(eq("Lalamove Update Log"), any(LalamoveDelivery.class));
        verify(lalamoveDeliveryMessagingService, never()).queueUpdateJob(anyLong());
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_andShopperIsNull_shouldLogError() {
        shoppingBatch.setUser(null);
        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verify(logger).error("[Lalamove][PlaceOrderInOneBatch] Shopper not found for started batch " + shoppingBatch.getId());
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_andFailToPlaceOrder_shouldSendWebhookBookingError() {
        placeOrderPresenter.setMessage("Fail to place order");
        when(lalamoveApiService.placeOrder(eq(shipment), any(LocalDateTime.class), any(LalamoveServiceTypeEnum.class),
                any(User.class), any(Country.class), eq(stockLocation), eq(getQuotationPresenter))).thenReturn(placeOrderPresenter);
        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verifyDeliveryStatus(LalamoveDelivery.Status.ORDER_PLACED);
        verify(webhookPublisherService).publish(eq(WebhookType.LALAMOVE_BOOKING_ERROR), any(ObjectNode.class), anyString(), any());
        verify(lalamoveDeliveryTrackingService).track(eq("Lalamove Update Log"), any(LalamoveDelivery.class));
        verify(lalamoveDeliveryMessagingService).queueUpdateJob(anyLong());
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_andExceptionOccur_shouldLogError() throws InterruptedException {
        when(jedisLockService.lock(anyString())).thenThrow(InterruptedException.class);
        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verify(logger).error(eq("Lalamove Place Order async failed"), any(Exception.class));
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_andFailToGetQuotation_shouldNotSetStatusToOrderPlaced() {
        when(lalamoveApiService.getQuotation(eq(shipment), any(LocalDateTime.class), any(LalamoveServiceTypeEnum.class),
                any(User.class), any(Country.class), eq(stockLocation))).thenReturn(null);
        when(lalamoveApiService.placeOrder(eq(shipment), any(LocalDateTime.class), any(LalamoveServiceTypeEnum.class),
                any(User.class), any(Country.class), eq(stockLocation), any())).thenReturn(null);

        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verifyDeliveryStatus(LalamoveDelivery.Status.INITIAL);
        verify(webhookPublisherService, never()).publish(eq(WebhookType.LALAMOVE_BOOKING_ERROR), any(ObjectNode.class), anyString(), any());
        verify(lalamoveDeliveryTrackingService, never()).track(eq("Lalamove Update Log"), any(LalamoveDelivery.class));
        verify(lalamoveDeliveryMessagingService).queueUpdateJob(anyLong());
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_andNoAvailableServiceType_shouldSwitchToHF() {
        when(serviceTypeRepository.findAllV2ByCountryIdOrderByMaxWeightAsc(country.getId())).thenReturn(Collections.emptyList());
        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verify(lalamoveApiService, never()).placeOrder(eq(shipment), any(LocalDateTime.class), any(LalamoveServiceTypeEnum.class),
                any(User.class), any(Country.class), eq(stockLocation), any());
        verify(lalamoveDeliveryMessagingService).publishFailedBookingWebhook(shipment, "Failed to book lalamove");
        // Radar service disabled
        verify(radarApiService, Mockito.never()).createDeliveryGeofence(shipment, shipment.getSlot().getStockLocation().getState().getCountry());
    }

    @Test
    public void whenSwitchLalamoveToHF_andNotInCancelableStatus_shouldNotSendCancelToLalamove() {
        lalamoveService.switchLalamoveToHF(shipment);

        verifyDeliveryStatus(LalamoveDelivery.Status.CANCELED);
        verify(lalamoveApiService, never()).cancelBooking(any(State.class), anyString());
        verify(lalamoveDeliveryMessagingService).publishSynchronizeWebhook(any(LalamoveDelivery.class));
        // Radar service disabled
        verify(radarApiService, Mockito.never()).createDeliveryGeofence(shipment, shipment.getSlot().getStockLocation().getState().getCountry());
    }

    @Test
    public void whenSwitchLalamoveToHF_andHasCancelableStatus_shouldSendCancelToLalamove() {
        lalamoveDelivery.setStatus(LalamoveDelivery.Status.ORDER_PLACED);
        lalamoveService.switchLalamoveToHF(shipment);

        verifyDeliveryStatus(LalamoveDelivery.Status.CANCELED);
        verify(lalamoveApiService).cancelBooking(shipment.getSlot().getStockLocation().getState(), lalamoveDelivery.getExternalOrderId());
        verify(lalamoveDeliveryMessagingService).publishSynchronizeWebhook(any(LalamoveDelivery.class));
        // Radar service disabled
        verify(radarApiService, Mockito.never()).createDeliveryGeofence(shipment, shipment.getSlot().getStockLocation().getState().getCountry());
    }

    @Test(expected = LalamoveException.class)
    public void whenSwitchLalamoveToHF_andDeliveryJobIsNotLalamove_shouldThrowException() {
        shipment.getDeliveryJob().get().getBatch().setTplType(Batch.TplType.DELYVA);
        lalamoveService.switchLalamoveToHF(shipment);
    }

    @Test(expected = LalamoveException.class)
    public void whenSwitchLalamoveToHF_andLalamoveDeliveryIsNull_shouldThrowException() {
        shipment.setTplDeliveries(Collections.emptyList());
        lalamoveService.switchLalamoveToHF(shipment);
    }

    @Test(expected = LalamoveException.class)
    public void whenSwitchLalamoveToHF_andShipmentNotExists_shouldThrowException() {
        when(shipmentRepository.findByNumber(shipment.getNumber())).thenReturn(null);
        lalamoveService.switchLalamoveToHF(shipment);
    }

    @Test
    public void whenPlaceOrderByDelivery_shouldPlaceOrderToLalamove() {
        lalamoveService.placeOrderByDelivery(lalamoveDelivery);

        verifyDeliveryStatus(LalamoveDelivery.Status.ORDER_PLACED);
        verify(webhookPublisherService, never()).publish(eq(WebhookType.LALAMOVE_BOOKING_ERROR), any(ObjectNode.class), anyString(), anyLong());
        verify(lalamoveDeliveryTrackingService).track(eq("Lalamove Update Log"), any(LalamoveDelivery.class));
        verify(lalamoveDeliveryMessagingService).queueUpdateJob(anyLong());
    }

    @Test(expected = EntityNotFoundException.class)
    public void whenPlaceOrderByDelivery_andShopperNotAssignedToBatch_shouldThrowException() {
        shoppingBatch.setUser(null);
        lalamoveService.placeOrderByDelivery(lalamoveDelivery);
    }

    @Test(expected = EntityNotFoundException.class)
    public void whenPlaceOrderByDelivery_andShoppingJobIsNotPresent_shouldThrowException() {
        shipment.setJobs(Collections.emptyList());
        lalamoveService.placeOrderByDelivery(lalamoveDelivery);
    }

    @Test
    public void whenPlaceOrderByDelivery_andNoAvailableServiceType_shouldSwitchToHF() {
        when(serviceTypeRepository.findAllV2ByCountryIdOrderByMaxWeightAsc(country.getId())).thenReturn(Collections.emptyList());
        lalamoveService.placeOrderByDelivery(lalamoveDelivery);

        verify(lalamoveApiService, never()).placeOrder(eq(shipment), any(LocalDateTime.class), any(LalamoveServiceTypeEnum.class),
                any(User.class), any(Country.class), eq(stockLocation), any());
        verify(lalamoveDeliveryMessagingService).publishFailedBookingWebhook(shipment, "Failed to book lalamove");
        // Radar service disabled
        verify(radarApiService, Mockito.never()).createDeliveryGeofence(shipment, shipment.getSlot().getStockLocation().getState().getCountry());
    }

    @Test
    public void whenCancelLalamoveBooking_andNotInCancelableStatus_shouldNotSendCancelToLalamove() {
        lalamoveService.cancelLalamoveBooking(shipment, lalamoveDelivery);

        verifyDeliveryStatus(LalamoveDelivery.Status.SHIPMENT_CANCELLED);
        verify(lalamoveApiService, never()).cancelBooking(any(State.class), anyString());
        verify(lalamoveDeliveryTrackingService).track(eq("Lalamove Update Log"), any(LalamoveDelivery.class));
    }

    @Test
    public void whenCancelLalamoveBooking_andHasCancelableStatus_shouldSendCancelToLalamove() {
        lalamoveDelivery.setStatus(LalamoveDelivery.Status.ORDER_PLACED);
        lalamoveService.cancelLalamoveBooking(shipment, lalamoveDelivery);

        verifyDeliveryStatus(LalamoveDelivery.Status.SHIPMENT_CANCELLED);
        verify(lalamoveApiService).cancelBooking(shipment.getSlot().getStockLocation().getState(), lalamoveDelivery.getExternalOrderId());
        verify(lalamoveDeliveryTrackingService).track(eq("Lalamove Update Log"), any(LalamoveDelivery.class));
    }

    @Test
    public void whenCancelLalamoveBooking_lalamoveV3_andNotInCancelableStatus_shouldNotSendCancelToLalamove() {
        configureLalamoveV3();
        lalamoveService.cancelLalamoveBooking(shipment, lalamoveDelivery);

        verifyDeliveryStatus(LalamoveDelivery.Status.SHIPMENT_CANCELLED);
        verify(lalamoveApiService, never()).cancelBooking(any(State.class), anyString());
        verify(lalamoveApiV3Service, never()).cancelBooking(any(State.class), anyString());
        verify(lalamoveDeliveryTrackingService).track(eq("Lalamove Update Log"), any(LalamoveDelivery.class));
    }

    @Test
    public void whenCancelLalamoveBooking_lalamoveV3_andHasCancelableStatus_shouldSendCancelToLalamove() {
        configureLalamoveV3();
        lalamoveDelivery.setStatus(LalamoveDelivery.Status.ORDER_PLACED);
        lalamoveService.cancelLalamoveBooking(shipment, lalamoveDelivery);

        verifyDeliveryStatus(LalamoveDelivery.Status.SHIPMENT_CANCELLED);
        verify(lalamoveApiService, never()).cancelBooking(any(State.class), anyString());
        verify(lalamoveApiV3Service).cancelBooking(shipment.getSlot().getStockLocation().getState(), lalamoveDelivery.getExternalOrderId());
        verify(lalamoveDeliveryTrackingService).track(eq("Lalamove Update Log"), any(LalamoveDelivery.class));
    }

    @Test
    public void whenSendStratoDriverDetail_andIsFulfilledByStrato_shouldSendWebhook() {
        stockLocation.setEnabler(StockLocation.Enabler.HYPERMART);
        stockLocation.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);

        lalamoveService.sendStratoDriverDetail(lalamoveDelivery);
        verify(stratoWebhookService).sendDeliveryBookedWebhook(shipment.getOrderNumber());
    }

    @Test
    public void whenSendStratoDriverDetail_andIsNotFulfilledByStrato_shouldNotSendWebhook() {
        stockLocation.setEnabler(StockLocation.Enabler.HYPERMART);
        stockLocation.setEnablerPlatform(StockLocation.EnablerPlatform.JUBELIO);

        lalamoveService.sendStratoDriverDetail(lalamoveDelivery);
        verify(stratoWebhookService, never()).sendDeliveryBookedWebhook(shipment.getOrderNumber());
    }

    @Test
    public void whenGetServiceTypeByEnum_shouldReturnCorrectServiceType() {
        LalamoveServiceTypeEnum typeEnum = LalamoveServiceTypeEnum.MOTORCYCLE;
        LalamoveServiceType filteredType = serviceTypes.stream().filter(p -> p.getKey().equals(typeEnum.toString())
                && p.getCountry() == country).collect(Collectors.toList()).get(0);

        when(serviceTypeRepository.findV2ByKeyAndCountryId(typeEnum.toString(), country.getId())).thenReturn(filteredType);

        LalamoveServiceType getType = lalamoveService.getServiceTypeByEnum(typeEnum, country, stockLocation);
        assertEquals(filteredType, getType);
    }

    @Test
    public void whenGetServiceTypeByEnum_v3_shouldReturnCorrectServiceType() {
        LalamoveServiceTypeEnum typeEnum = LalamoveServiceTypeEnum.MOTORCYCLE;
        LalamoveServiceType filteredType = serviceTypes.stream().filter(p -> p.getKey().equals(typeEnum.toString())
                && p.getCountry() == country).collect(Collectors.toList()).get(0);

        when(serviceTypeRepository.findV3ByKeyAndCityCode(typeEnum.toString(), "JKT")).thenReturn(filteredType);

        configureLalamoveV3();

        LalamoveServiceType getType = lalamoveService.getServiceTypeByEnum(typeEnum, country, stockLocation);
        assertEquals(filteredType, getType);
    }

    @Test
    public void whenGetLargestServiceTypeByCountry_shouldReturnCorrectServiceType() {
        LalamoveServiceType filteredType = serviceTypes.stream().filter(p -> p.getCountry() == country).collect(Collectors.toList()).get(0);

        when(serviceTypeRepository.findFirstV2ByCountryIdOrderByMaxWeightDesc(country.getId())).thenReturn(filteredType);

        LalamoveServiceType getType = lalamoveService.getLargestServiceTypeByCountry(country, stockLocation);
        assertEquals(filteredType, getType);
    }

    @Test
    public void whenGetLargestServiceTypeByCountry_v3_shouldReturnCorrectServiceType() {
        LalamoveServiceType filteredType = serviceTypes.stream().filter(p -> p.getCountry() == country).collect(Collectors.toList()).get(0);

        when(serviceTypeRepository.findFirstV3ByCityCodeOrderByMaxWeightDesc("JKT")).thenReturn(filteredType);

        configureLalamoveV3();

        LalamoveServiceType getType = lalamoveService.getLargestServiceTypeByCountry(country, stockLocation);
        assertEquals(filteredType, getType);
    }

    @Test
    public void whenGetPendingDeliveries_shouldReturnCorrectList() {
        when(lalamoveDeliveryRepository.findAllPendingBookingByShopperId(eq(shopper.getId()), any(LocalDateTime.class))).thenReturn(Collections.singletonList(lalamoveDelivery));
        when(userRepository.findById(shopper.getId())).thenReturn(Optional.of(shopper));

        List<LalamoveDelivery> deliveryList = lalamoveService.getPendingDeliveries(shopper.getId());
        assertEquals(1, deliveryList.size());
        assertEquals(lalamoveDelivery, deliveryList.get(0));
    }

    @Test
    public void whenGetPendingDeliveries_andShopperNotExists_shouldReturnEmptyList() {
        when(userRepository.findById(shopper.getId())).thenReturn(Optional.empty());

        List<LalamoveDelivery> deliveryList = lalamoveService.getPendingDeliveries(shopper.getId());
        assertEquals(0, deliveryList.size());
    }

    @Test(expected = SwitchToHFFailedException.class)
    public void whenSwitchLalamoveBatchToHF_andDeliveryJobIsNotPresend_shouldReturnException() {
        shipment.setJobs(Collections.emptyList());
        lalamoveService.switchLalamoveBatchToHF(shipment);
    }

    @Test(expected = SwitchToHFFailedException.class)
    public void whenSwitchLalamoveBatchToHF_andDeliveryBatchIsNotLalamove_shouldReturnException() {
        shipment.getDeliveryJob().get().getBatch().setTplType(Batch.TplType.DELYVA);
        lalamoveService.switchLalamoveBatchToHF(shipment);
    }

    @Test
    public void whenSwitchLalamoveBatchToHF_andDeliveryJobIsFinished_shouldDoNothing() {
        shipment.getDeliveryJob().get().setState(Job.State.FINISHED);
        lalamoveService.switchLalamoveBatchToHF(shipment);

        verify(lalamoveDeliveryMessagingService, never()).publishFailedBookingWebhook(any(Shipment.class), anyString());
        verify(radarApiService, never()).createDeliveryGeofence(any(Shipment.class), any(Country.class));
    }

    @Test
    public void adminSwitchToHF() {
        lalamoveDelivery.setStatus(LalamoveDelivery.Status.ORDER_PLACED);

        lalamoveService.adminSwitchToHF(shipment, shipment.getDeliveryJob().get());
        verify(lalamoveApiService, atLeastOnce()).cancelBooking(any(State.class), anyString());
        verify(lalamoveDeliveryRepository, atLeastOnce()).save(any(LalamoveDelivery.class));
        verify(lalamoveDeliveryMessagingService, atLeastOnce()).publishSynchronizeWebhook(any(LalamoveDelivery.class));
        verify(batchService, atLeastOnce()).switchBatchToHF(any(Job.class));
        // Radar service disabled
        verify(radarApiService, never()).createDeliveryGeofence(any(Shipment.class), any(Country.class));
    }

    @Test
    public void adminSwitchToHF_whenDeliveryJobNotLalamove() {
        Batch nonLalamoveDeliveryBatch = batchFactory.createBatch(user, shipment, shoppingSlot, Batch.Type.DELIVERY);
        nonLalamoveDeliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        nonLalamoveDeliveryBatch.setTplType(Batch.TplType.DELYVA);
        shipment.setJobs(Stream.concat(shoppingBatch.getJobs().stream(), nonLalamoveDeliveryBatch.getJobs().stream())
                .collect(Collectors.toList()));

        expectedEx.expect(SwitchToHFFailedException.class);
        expectedEx.expectMessage("Batch fleet type not Lalamove");

        lalamoveService.adminSwitchToHF(shipment, shipment.getDeliveryJob().get());
        verify(lalamoveApiService, never()).cancelBooking(any(State.class), anyString());
        verify(lalamoveDeliveryRepository, never()).save(any(LalamoveDelivery.class));
        verify(lalamoveDeliveryMessagingService, never()).publishSynchronizeWebhook(any(LalamoveDelivery.class));
        verify(batchService, never()).switchBatchToHF(any(Job.class));
        verify(radarApiService, never()).createDeliveryGeofence(any(Shipment.class), any(Country.class));
    }

    @Test
    public void adminSwitchToHF_whenStatusNotAllowed_shouldDoUpdateBatchRecord() {
        lalamoveDelivery.setStatus(LalamoveDelivery.Status.INITIAL);

        lalamoveService.adminSwitchToHF(shipment, shipment.getDeliveryJob().get());
        verify(lalamoveApiService, never()).cancelBooking(any(State.class), anyString());
        verify(lalamoveDeliveryRepository, atLeastOnce()).save(any(LalamoveDelivery.class));
        verify(lalamoveDeliveryMessagingService, atLeastOnce()).publishSynchronizeWebhook(any(LalamoveDelivery.class));
        verify(batchService, atLeastOnce()).switchBatchToHF(any(Job.class));
        // Radar service disabled
        verify(radarApiService, never()).createDeliveryGeofence(any(Shipment.class), any(Country.class));
    }

    @Test
    public void adminSwitchToHF_whenLalamoveDeliveryNull_shouldDoUpdateBatchRecord() {
        shipment.setTplDeliveries(Collections.emptyList());

        lalamoveService.adminSwitchToHF(shipment, shipment.getDeliveryJob().get());
        verify(lalamoveApiService, never()).cancelBooking(any(State.class), anyString());
        verify(lalamoveDeliveryRepository, never()).save(any(LalamoveDelivery.class));
        verify(lalamoveDeliveryMessagingService, never()).publishSynchronizeWebhook(any(LalamoveDelivery.class));
        verify(batchService, atLeastOnce()).switchBatchToHF(any(Job.class));
        // Radar service disabled
        verify(radarApiService, never()).createDeliveryGeofence(any(Shipment.class), any(Country.class));
    }

    private void configureLalamoveV3() {
        Map<String, String> preferences = tenant.getPreferences();
        preferences.put("enable_lalamove_api_v3", String.valueOf(true));

        State state = stockLocation.getState();
        state.setPreferences(new HashMap<>() {{
            put("lalamove_city_code", "JKT");
        }});
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_enableV3_shouldPlaceOrderToLalamove() {
        configureLalamoveV3();

        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verifyDeliveryStatus(LalamoveDelivery.Status.ORDER_PLACED);
        verify(webhookPublisherService, never()).publish(eq(WebhookType.LALAMOVE_BOOKING_ERROR), any(ObjectNode.class), anyString(), anyLong());
        verify(lalamoveDeliveryTrackingService).track(eq("Lalamove Update Log"), any(LalamoveDelivery.class));
        verify(lalamoveDeliveryMessagingService).queueUpdateJob(anyLong());
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_enableV3_whenDisableLalamoveFee_shouldUseDDf() {
        configureLalamoveV3();

        Map<String, String> preferences = stockLocation.getPreferences();
        preferences.put("enable_lalamove_delivery_fee", "false");

        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verifyDeliveryStatus(LalamoveDelivery.Status.ORDER_PLACED, shipment.getCost());
        verify(webhookPublisherService, never()).publish(eq(WebhookType.LALAMOVE_BOOKING_ERROR), any(ObjectNode.class), anyString(), anyLong());
        verify(lalamoveDeliveryTrackingService).track(eq("Lalamove Update Log"), any(LalamoveDelivery.class));
        verify(lalamoveDeliveryMessagingService).queueUpdateJob(anyLong());
    }


    @Test
    public void whenPlaceOrderInOneBatchAsync_enableV3_andLalamoveStatusIsNotInitial_shouldDoNothing() {
        configureLalamoveV3();

        lalamoveDelivery.setStatus(LalamoveDelivery.Status.ORDER_PLACED);
        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verify(webhookPublisherService, never()).publish(eq(WebhookType.LALAMOVE_BOOKING_ERROR), any(ObjectNode.class), anyString(), anyLong());
        verify(lalamoveDeliveryTrackingService, never()).track(eq("Lalamove Update Log"), any(LalamoveDelivery.class));
        verify(lalamoveDeliveryMessagingService, never()).queueUpdateJob(anyLong());
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_enableV3_andShopperIsNull_shouldLogError() {
        configureLalamoveV3();

        shoppingBatch.setUser(null);
        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verify(logger).error("[Lalamove][PlaceOrderInOneBatch] Shopper not found for started batch " + shoppingBatch.getId());
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_enableV3_andFailToPlaceOrder_shouldSendWebhookBookingError() {
        configureLalamoveV3();

        LalamoveV3ErrorResponse error = getErrorResponse();

        placeOrderV3Presenter.setErrors(Lists.newArrayList(error));

        when(lalamoveApiV3Service.placeOrder(eq(shipment),
                any(User.class), any(Country.class), eq(stockLocation), eq(v3GetQuotationPresenter))).thenReturn(placeOrderV3Presenter);
        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verifyDeliveryStatus(LalamoveDelivery.Status.ORDER_PLACED);
        verify(webhookPublisherService).publish(eq(WebhookType.LALAMOVE_BOOKING_ERROR), any(ObjectNode.class), anyString(), any());
        verify(lalamoveDeliveryTrackingService).track(eq("Lalamove Update Log"), any(LalamoveDelivery.class));
        verify(lalamoveDeliveryMessagingService).queueUpdateJob(anyLong());
    }

    private LalamoveV3ErrorResponse getErrorResponse() {
        LalamoveV3ErrorResponse error = new LalamoveV3ErrorResponse();
        error.setMessage("Fail to place order.");
        error.setId("ERR_ORDER");
        return error;
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_enableV3_andExceptionOccur_shouldLogError() throws InterruptedException {
        configureLalamoveV3();

        when(jedisLockService.lock(anyString())).thenThrow(InterruptedException.class);
        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verify(logger).error(eq("Lalamove Place Order async failed"), any(Exception.class));
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_enableV3_andFailToGetQuotation_shouldNotSetStatusToOrderPlaced() {
        configureLalamoveV3();

        when(lalamoveApiV3Service.getQuotation(eq(shipment), any(LocalDateTime.class), eq(stockLocation),
                any(LalamoveServiceTypeEnum.class), any())).thenReturn(null);
        when(lalamoveApiV3Service.placeOrder(eq(shipment),
                any(User.class), any(Country.class), eq(stockLocation), any())).thenReturn(null);

        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verifyDeliveryStatus(LalamoveDelivery.Status.INITIAL);
        verify(webhookPublisherService, never()).publish(eq(WebhookType.LALAMOVE_BOOKING_ERROR), any(ObjectNode.class), anyString(), any());
        verify(lalamoveDeliveryTrackingService, never()).track(eq("Lalamove Update Log"), any(LalamoveDelivery.class));
        verify(lalamoveDeliveryMessagingService).queueUpdateJob(anyLong());
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_enableV3_andNoAvailableServiceType_shouldSwitchToHF() {
        configureLalamoveV3();

        when(serviceTypeRepository.findAllActiveV3ByCityCode("JKT")).thenReturn(Collections.emptyList());
        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verify(lalamoveApiV3Service, never()).placeOrder(eq(shipment),
                any(User.class), any(Country.class), eq(stockLocation), any());
        verify(lalamoveDeliveryMessagingService).publishFailedBookingWebhook(shipment, "Failed to book lalamove");
        // Radar service disabled
        verify(radarApiService, Mockito.never()).createDeliveryGeofence(shipment, shipment.getSlot().getStockLocation().getState().getCountry());
    }

    @Test
    public void whenPlaceOrderInOneBatchAsync_enableV3_andExceptionGetServiceTypeEnum_shouldSwitchToHF() {
        configureLalamoveV3();

        serviceTypesV3.stream().forEach(lalamoveServiceType -> lalamoveServiceType.setKey("4x4"));
        lalamoveService.placeOrderInOneBatchAsync(shoppingBatch, abstractAuthenticationToken);

        verify(lalamoveApiV3Service, never()).placeOrder(eq(shipment),
                any(User.class), any(Country.class), eq(stockLocation), any());
        verify(lalamoveDeliveryMessagingService).publishFailedBookingWebhook(shipment, "Failed to book lalamove");
        // Radar service disabled
        verify(radarApiService, Mockito.never()).createDeliveryGeofence(shipment, shipment.getSlot().getStockLocation().getState().getCountry());
        verify(logger).error("LalamoveServiceType 4x4 not found in LalamoveServiceTypeEnum.java for shipment " + shipment.getNumber());
    }

    @Test
    public void getDriverDetail_whenEnableV3_andPresenterIsPresent_shouldReturnDriverDetail() throws IOException {
        configureLalamoveV3();

        String response = "{\"data\":{\"driverId\":\"79977\",\"name\":\"TestDriver 88884\",\"phone\":\"+6288888884\",\"plateNumber\":\"**751947*\",\"photo\":\"https://www.happyfresh.id/\"}}";
        LalamoveV3DriverDetailPresenter presenter = mapper.readValue(response, LalamoveV3DriverDetailPresenter.class);

        when(lalamoveApiV3Service.getDriverDetail(any(State.class), anyString(), anyString()))
                .thenReturn(Optional.of(presenter));
        Optional<LalamoveDriverDetail> result = lalamoveService.getDriverDetail(stockLocation.getState(), "120730409394", 79971L);
        assertTrue(result.isPresent());
        assertEquals("TestDriver 88884", result.get().getName());
        assertEquals("+6288888884", result.get().getPhone());
        assertEquals("**751947*", result.get().getPlateNumber());
        assertEquals("https://www.happyfresh.id/", result.get().getPhoto());
    }

    @Test
    public void getDriverDetail_whenEnableV3_andPresenterIsNotPresent_shouldReturnNotPresent() throws IOException {
        configureLalamoveV3();

        when(lalamoveApiV3Service.getDriverDetail(any(State.class), anyString(), anyString()))
                .thenReturn(Optional.empty());
        Optional<LalamoveDriverDetail> result = lalamoveService.getDriverDetail(stockLocation.getState(), "120730409394", 79971L);
        assertFalse(result.isPresent());
    }

    @Test
    public void getDriverDetail_whenDisableV3_andPresenterIsPresent_shouldReturnDriverDetail() throws IOException {
        String response = "{\"name\":\"TestDriver 88884\",\"phone\":\"+6288888884\",\"plateNumber\":\"**751947*\",\"photo\":\"https://www.happyfresh.id/\"}}";
        LalamoveDriverDetailPresenter presenter = mapper.readValue(response, LalamoveDriverDetailPresenter.class);

        when(lalamoveApiService.getDriverDetail(any(State.class), anyString(), anyLong()))
                .thenReturn(Optional.of(presenter));
        Optional<LalamoveDriverDetail> result = lalamoveService.getDriverDetail(stockLocation.getState(), "120730409394", 79971L);
        assertTrue(result.isPresent());
        assertEquals("TestDriver 88884", result.get().getName());
        assertEquals("+6288888884", result.get().getPhone());
        assertEquals("**751947*", result.get().getPlateNumber());
        assertEquals("https://www.happyfresh.id/", result.get().getPhoto());
    }

    @Test
    public void getDriverDetail_whenDisableV3_andPresenterIsNotPresent_shouldReturnNotPresent() throws IOException {
        when(lalamoveApiService.getDriverDetail(any(State.class), anyString(), anyLong()))
                .thenReturn(Optional.empty());
        Optional<LalamoveDriverDetail> result = lalamoveService.getDriverDetail(stockLocation.getState(), "120730409394", 79971L);
        assertFalse(result.isPresent());
    }

    @Test
    public void getOrderDetail_shouldMapLalamoveOrderDetailV2_toOrderDetail() {
        LalamoveQuotedTotalFee fee = new LalamoveQuotedTotalFee();
        fee.setCurrency("IDR");
        fee.setAmount("10000");
        LalamoveOrderDetailPresenter orderDetailV2Presenter = new LalamoveOrderDetailPresenter();
        orderDetailV2Presenter.setStatus("DELIVERING");
        orderDetailV2Presenter.setDriverId(10001L);
        orderDetailV2Presenter.setPrice(fee);

        when(lalamoveApiService.getOrderDetail(any(), anyString())).thenReturn(Optional.of(orderDetailV2Presenter));

        Optional<LalamoveOrderDetail> optOrderDetail = lalamoveService.getOrderDetail(stockLocation.getState(), "xxx");
        Assert.assertTrue(optOrderDetail.isPresent());
        LalamoveOrderDetail lalamoveOrderDetail = optOrderDetail.get();
        Assert.assertEquals(orderDetailV2Presenter.getDriverId(), lalamoveOrderDetail.getDriverId());
        Assert.assertEquals(orderDetailV2Presenter.getStatus(), lalamoveOrderDetail.getStatus());
        Assert.assertEquals(orderDetailV2Presenter.getPrice().getAmount(), lalamoveOrderDetail.getPrice());
        Assert.assertEquals(orderDetailV2Presenter.getPrice().getCurrency(), lalamoveOrderDetail.getCurrency());
    }

    @Test
    public void getOrderDetail_v2_whenLalamoveReturnEmpty_shouldReturnOptionalEmpty() {

        when(lalamoveApiService.getOrderDetail(any(), anyString())).thenReturn(Optional.empty());

        Optional<LalamoveOrderDetail> optOrderDetail = lalamoveService.getOrderDetail(stockLocation.getState(), "xxx");
        Assert.assertTrue(optOrderDetail.isEmpty());
    }

    @Test
    public void getOrderDetail_withV3Enabled_shouldMapLalamoveOrderDetailV3_toOrderDetail() {
        configureLalamoveV3();
        when(lalamoveApiV3Service.getOrderDetail(any(), anyString())).thenReturn(Optional.of(placeOrderV3Presenter));

        Optional<LalamoveOrderDetail> optOrderDetail = lalamoveService.getOrderDetail(stockLocation.getState(), "xxx");
        Assert.assertTrue(optOrderDetail.isPresent());
        LalamoveOrderDetail lalamoveOrderDetail = optOrderDetail.get();
        Assert.assertEquals(Long.valueOf(placeOrderV3Presenter.getData().getDriverId()), lalamoveOrderDetail.getDriverId());
        Assert.assertEquals(placeOrderV3Presenter.getData().getStatus(), lalamoveOrderDetail.getStatus());
        Assert.assertEquals(placeOrderV3Presenter.getData().getPriceBreakdown().getTotal(), lalamoveOrderDetail.getPrice());
        Assert.assertEquals(placeOrderV3Presenter.getData().getPriceBreakdown().getCurrency(), lalamoveOrderDetail.getCurrency());
    }

    @Test
    public void getOrderDetail_withV3Enabled_andEmptyDriverId_shouldMapLalamoveOrderDetailV3_toOrderDetail_withNullDriverId() {
        configureLalamoveV3();
        placeOrderV3Presenter.getData().setDriverId("");
        when(lalamoveApiV3Service.getOrderDetail(any(), anyString())).thenReturn(Optional.of(placeOrderV3Presenter));

        Optional<LalamoveOrderDetail> optOrderDetail = lalamoveService.getOrderDetail(stockLocation.getState(), "xxx");
        Assert.assertTrue(optOrderDetail.isPresent());
        LalamoveOrderDetail lalamoveOrderDetail = optOrderDetail.get();
        Assert.assertNull(lalamoveOrderDetail.getDriverId());
        Assert.assertEquals(placeOrderV3Presenter.getData().getStatus(), lalamoveOrderDetail.getStatus());
        Assert.assertEquals(placeOrderV3Presenter.getData().getPriceBreakdown().getTotal(), lalamoveOrderDetail.getPrice());
        Assert.assertEquals(placeOrderV3Presenter.getData().getPriceBreakdown().getCurrency(), lalamoveOrderDetail.getCurrency());
    }

    @Test
    public void getOrderDetail_withV3Enabled_whenLalamoveReturnEmpty_shouldReturnOptionalEmpty() {
        configureLalamoveV3();
        when(lalamoveApiV3Service.getOrderDetail(any(), anyString())).thenReturn(Optional.empty());

        Optional<LalamoveOrderDetail> optOrderDetail = lalamoveService.getOrderDetail(stockLocation.getState(), "xxx");
        Assert.assertTrue(optOrderDetail.isEmpty());
    }

    @Test
    public void updateOrCreateServiceTypesForAllTenants_whenV3Enabled_whenNotExistOnDB_shouldCallV3Api_andCreateNew() {
        configureLalamoveV3();

        Mockito.doCallRealMethod().when(transactionHelper).withNewTransaction(Mockito.any(Runnable.class));
        when(tenantRepository.findAll()).thenReturn(List.of(tenant));
        when(countryRepository.findByIsoNameInAndTenantId(anyList(), eq(tenant.getId()))).thenReturn(List.of(country));
        when(lalamoveApiV3Service.getCityInfo(country)).thenReturn(Optional.of(cityInfoPresenter));
        when(serviceTypeRepository.findAllV3ByCountry(country)).thenReturn(new ArrayList<>()); // not exist

        lalamoveService.updateOrCreateServiceTypesForAllTenants();

        verify(lalamoveApiV3Service, times(1)).getCityInfo(country);
        verify(serviceTypeRepository, times(1)).saveAll(serviceTypeCaptor.capture());

        List<LalamoveServiceType> serviceTypes = serviceTypeCaptor.getValue();
        assertEquals(20, serviceTypes.size());
    }

    @Test
    public void updateOrCreateServiceTypesForAllTenants_whenV3Disabled_shouldAlsoCreate() {
        Map<String, String> tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("enable_lalamove_api_v3", "false");
        tenant.setPreferences(tenantPreferences);

        Mockito.doCallRealMethod().when(transactionHelper).withNewTransaction(Mockito.any(Runnable.class));
        when(tenantRepository.findAll()).thenReturn(List.of(tenant));
        when(countryRepository.findByIsoNameInAndTenantId(anyList(), eq(tenant.getId()))).thenReturn(List.of(country));
        when(lalamoveApiV3Service.getCityInfo(country)).thenReturn(Optional.of(cityInfoPresenter));
        when(serviceTypeRepository.findAllV3ByCountry(country)).thenReturn(new ArrayList<>()); // not exist

        lalamoveService.updateOrCreateServiceTypesForAllTenants();

        verify(serviceTypeRepository, times(1)).saveAll(serviceTypeCaptor.capture());
    }

    @Test
    public void updateOrCreateServiceTypesForAllTenants_whenNotExistOnDB_shouldCreate_setInactiveAndV3() {
        configureLalamoveV3();

        // Simplify API response, Bandung: Motorcycle
        LalamoveV3GetCityInfoPresenter.Data cityInfoBandung = cityInfoPresenter.getData().stream()
                .filter(d -> d.getLocode().equals("ID BDO"))
                .findFirst().orElseThrow();
        LalamoveV3GetCityInfoPresenter.Service motor = cityInfoBandung.getServices().stream()
                .filter(s -> s.getKey().equals("MOTORCYCLE"))
                .findFirst().orElseThrow();
        assertEquals(6, motor.getSpecialRequests().size());
        cityInfoBandung.setServices(List.of(motor));
        cityInfoPresenter.getData().clear();
        cityInfoPresenter.setData(List.of(cityInfoBandung));

        Mockito.doCallRealMethod().when(transactionHelper).withNewTransaction(Mockito.any(Runnable.class));
        when(tenantRepository.findAll()).thenReturn(List.of(tenant));
        when(countryRepository.findByIsoNameInAndTenantId(anyList(), eq(tenant.getId()))).thenReturn(List.of(country));
        when(lalamoveApiV3Service.getCityInfo(country)).thenReturn(Optional.of(cityInfoPresenter));
        when(serviceTypeRepository.findAllV3ByCountry(country)).thenReturn(new ArrayList<>()); // not exist

        lalamoveService.updateOrCreateServiceTypesForAllTenants();

        verify(lalamoveApiV3Service, times(1)).getCityInfo(country);
        verify(serviceTypeRepository, times(1)).saveAll(serviceTypeCaptor.capture());

        List<LalamoveServiceType> capturedServiceTypes = serviceTypeCaptor.getValue();
        LalamoveServiceType captMotor = capturedServiceTypes.stream()
                .filter(s -> s.getKey().equals("MOTORCYCLE"))
                .findFirst().orElseThrow();
        assertEquals(1, capturedServiceTypes.size());
        assertEquals(6, captMotor.getAvailableSpecialRequests().length);
        assertFalse(captMotor.getActive());
        assertEquals("3", captMotor.getVersion());
        assertEquals("ID BDO", captMotor.getCityCode());
        LalamoveV3GetCityInfoPresenter.Service.Dimensions dimensions = motor.getDimensions();
        assertEquals(dimensions.getHeight().getValueInCm().orElseThrow(), captMotor.getMaxHeight());
        assertEquals(dimensions.getLength().getValueInCm().orElseThrow(), captMotor.getMaxLength());
        assertEquals(dimensions.getWidth().getValueInCm().orElseThrow(), captMotor.getMaxWidth());
        assertEquals(Double.valueOf(motor.getLoad().getValue()), captMotor.getMaxWeight());
        assertEquals("ID BDO", captMotor.getCityCode());
    }

    @Test
    public void updateOrCreateServiceTypesForAllTenants_whenExistOnDB_shouldUpdate() {
        configureLalamoveV3();

        // Simplify API response, Bandung: Motorcycle & MPV
        LalamoveV3GetCityInfoPresenter.Data cityInfoBandung = cityInfoPresenter.getData().stream()
                .filter(d -> d.getLocode().equals("ID BDO"))
                .findFirst().orElseThrow();
        LalamoveV3GetCityInfoPresenter.Service motor = cityInfoBandung.getServices().stream()
                .filter(s -> s.getKey().equals("MOTORCYCLE"))
                .findFirst().orElseThrow();
        LalamoveV3GetCityInfoPresenter.Service mpv = cityInfoBandung.getServices().stream()
                .filter(s -> s.getKey().equals("MPV"))
                .findFirst().orElseThrow();
        assertEquals(6, motor.getSpecialRequests().size());
        assertEquals(6, mpv.getSpecialRequests().size());
        cityInfoBandung.setServices(List.of(motor, mpv));
        cityInfoPresenter.getData().clear();
        cityInfoPresenter.setData(List.of(cityInfoBandung));

        // setup existing Bandung service types: Motorcycle & MPV
        LalamoveServiceTypeFactory serviceTypeFactory = new LalamoveServiceTypeFactory();
        LalamoveServiceType serviceTypeMotor = serviceTypeFactory.createMotorcycleServiceTypeV3(country, user, "ID BDO");
        LalamoveServiceType serviceTypeMpv = serviceTypeFactory.createMPVServiceTypeV3(country, user, "ID BDO");
        assertEquals(3, serviceTypeMotor.getAvailableSpecialRequests().length);
        assertEquals(3, serviceTypeMpv.getAvailableSpecialRequests().length);

        Mockito.doCallRealMethod().when(transactionHelper).withNewTransaction(Mockito.any(Runnable.class));
        when(tenantRepository.findAll()).thenReturn(List.of(tenant));
        when(countryRepository.findByIsoNameInAndTenantId(anyList(), eq(tenant.getId()))).thenReturn(List.of(country));
        when(lalamoveApiV3Service.getCityInfo(country)).thenReturn(Optional.of(cityInfoPresenter));
        when(serviceTypeRepository.findAllV3ByCountry(country))
                .thenReturn(List.of(serviceTypeMotor, serviceTypeMpv));

        lalamoveService.updateOrCreateServiceTypesForAllTenants();

        verify(lalamoveApiV3Service, times(1)).getCityInfo(country);
        verify(serviceTypeRepository, times(1)).saveAll(serviceTypeCaptor.capture());

        List<LalamoveServiceType> capturedServiceTypes = serviceTypeCaptor.getValue();
        assertEquals(2, capturedServiceTypes.size());
        LalamoveServiceType captMotor = capturedServiceTypes.stream()
                .filter(s -> s.getKey().equals("MOTORCYCLE"))
                .findFirst().orElseThrow();
        LalamoveServiceType captMpv = capturedServiceTypes.stream()
                .filter(s -> s.getKey().equals("MPV"))
                .findFirst().orElseThrow();
        assertEquals(6, captMotor.getAvailableSpecialRequests().length);
        assertEquals(6, captMpv.getAvailableSpecialRequests().length);
    }

    @Test
    public void updateOrCreateServiceTypesForAllTenants_whenUnknownDimensionUnit_shouldNotCreate() {
        configureLalamoveV3();

        // Simplify API response, Bandung: Motorcycle
        LalamoveV3GetCityInfoPresenter.Data cityInfoBandung = cityInfoPresenter.getData().stream()
                .filter(d -> d.getLocode().equals("ID BDO"))
                .findFirst().orElseThrow();
        LalamoveV3GetCityInfoPresenter.Service motor = cityInfoBandung.getServices().stream()
                .filter(s -> s.getKey().equals("MOTORCYCLE"))
                .findFirst().orElseThrow();
        LalamoveV3GetCityInfoPresenter.Service.Dimensions.Dimension unknownUnitDimension = new LalamoveV3GetCityInfoPresenter.Service.Dimensions.Dimension();
        unknownUnitDimension.setValue("1.5");
        unknownUnitDimension.setUnit("feet");
        motor.getDimensions().setLength(unknownUnitDimension);
        motor.getDimensions().setHeight(unknownUnitDimension);
        motor.getDimensions().setWidth(unknownUnitDimension);
        cityInfoBandung.setServices(List.of(motor));
        cityInfoPresenter.getData().clear();
        cityInfoPresenter.setData(List.of(cityInfoBandung));

        Mockito.doCallRealMethod().when(transactionHelper).withNewTransaction(Mockito.any(Runnable.class));
        when(tenantRepository.findAll()).thenReturn(List.of(tenant));
        when(countryRepository.findByIsoNameInAndTenantId(anyList(), eq(tenant.getId()))).thenReturn(List.of(country));
        when(lalamoveApiV3Service.getCityInfo(country)).thenReturn(Optional.of(cityInfoPresenter));
        when(serviceTypeRepository.findAllV3ByCountry(country)).thenReturn(new ArrayList<>()); // not exist

        lalamoveService.updateOrCreateServiceTypesForAllTenants();

        verify(lalamoveApiV3Service, times(1)).getCityInfo(country);
        verify(serviceTypeRepository, never()).saveAll(serviceTypeCaptor.capture());
        verify(logger).error(eq("Failed to convert dimension to cm when creating LalamoveServiceType. Service Type: {}, City Code: {}"), eq("MOTORCYCLE"), eq("ID BDO"), any(NoSuchElementException.class));
    }

    @Test
    public void updateOrCreateServiceTypesForAllTenants_whenDimensionUnitIsMeter_shouldCorrectlyConvertDimensionToCm() {
        configureLalamoveV3();

        // Simplify API response, Bandung: Motorcycle
        LalamoveV3GetCityInfoPresenter.Data cityInfoBandung = cityInfoPresenter.getData().stream()
                .filter(d -> d.getLocode().equals("ID BDO"))
                .findFirst().orElseThrow();
        LalamoveV3GetCityInfoPresenter.Service motor = cityInfoBandung.getServices().stream()
                .filter(s -> s.getKey().equals("MOTORCYCLE"))
                .findFirst().orElseThrow();
        LalamoveV3GetCityInfoPresenter.Service.Dimensions.Dimension dimensionInM = new LalamoveV3GetCityInfoPresenter.Service.Dimensions.Dimension();
        dimensionInM.setValue("1.5");
        dimensionInM.setUnit("m");
        motor.getDimensions().setLength(dimensionInM);
        motor.getDimensions().setHeight(dimensionInM);
        motor.getDimensions().setWidth(dimensionInM);
        cityInfoBandung.setServices(List.of(motor));
        cityInfoPresenter.getData().clear();
        cityInfoPresenter.setData(List.of(cityInfoBandung));

        Mockito.doCallRealMethod().when(transactionHelper).withNewTransaction(Mockito.any(Runnable.class));
        when(tenantRepository.findAll()).thenReturn(List.of(tenant));
        when(countryRepository.findByIsoNameInAndTenantId(anyList(), eq(tenant.getId()))).thenReturn(List.of(country));
        when(lalamoveApiV3Service.getCityInfo(country)).thenReturn(Optional.of(cityInfoPresenter));
        when(serviceTypeRepository.findAllV3ByCountry(country)).thenReturn(new ArrayList<>()); // not exist

        lalamoveService.updateOrCreateServiceTypesForAllTenants();

        verify(serviceTypeRepository, times(1)).saveAll(serviceTypeCaptor.capture());

        List<LalamoveServiceType> capturedServiceTypes = serviceTypeCaptor.getValue();
        assertEquals(1, capturedServiceTypes.size());
        LalamoveServiceType captMotor = capturedServiceTypes.stream()
                .filter(s -> s.getKey().equals("MOTORCYCLE"))
                .findFirst().orElseThrow();
        Double dimensionInCm = dimensionInM.getValueInCm().orElseThrow();
        assertEquals(dimensionInCm, captMotor.getMaxHeight());
        assertEquals(dimensionInCm, captMotor.getMaxLength());
        assertEquals(dimensionInCm, captMotor.getMaxWidth());
    }

    @Test
    public void updateOrCreateServiceTypesForAllTenants_whenExistOnDB_butNotOnApi_shouldIgnore() {
        configureLalamoveV3();

        // Simplify API response, Bandung: Motorcycle
        LalamoveV3GetCityInfoPresenter.Data cityInfoBandung = cityInfoPresenter.getData().stream()
                .filter(d -> d.getLocode().equals("ID BDO"))
                .findFirst().orElseThrow();
        LalamoveV3GetCityInfoPresenter.Service motor = cityInfoBandung.getServices().stream()
                .filter(s -> s.getKey().equals("MOTORCYCLE"))
                .findFirst().orElseThrow();
        assertEquals(6, motor.getSpecialRequests().size());
        cityInfoBandung.setServices(List.of(motor));
        cityInfoPresenter.getData().clear();
        cityInfoPresenter.setData(List.of(cityInfoBandung));

        // setup existing Bandung service types: Motorcycle
        LalamoveServiceTypeFactory serviceTypeFactory = new LalamoveServiceTypeFactory();
        LalamoveServiceType bandungMotor = serviceTypeFactory.createMotorcycleServiceTypeV3(country, user, "ID BDO");
        assertEquals(3, bandungMotor.getAvailableSpecialRequests().length);

        // setup existing Makassar (unknown) service types: Motorcycle
        LalamoveServiceType makassarMotor = serviceTypeFactory.createMotorcycleServiceTypeV3(country, user, "ID MKS");
        assertEquals(3, makassarMotor.getAvailableSpecialRequests().length);

        Mockito.doCallRealMethod().when(transactionHelper).withNewTransaction(Mockito.any(Runnable.class));
        when(tenantRepository.findAll()).thenReturn(List.of(tenant));
        when(countryRepository.findByIsoNameInAndTenantId(anyList(), eq(tenant.getId()))).thenReturn(List.of(country));
        when(lalamoveApiV3Service.getCityInfo(country)).thenReturn(Optional.of(cityInfoPresenter));
        when(serviceTypeRepository.findAllV3ByCountry(country))
                .thenReturn(List.of(bandungMotor, makassarMotor));

        lalamoveService.updateOrCreateServiceTypesForAllTenants();

        verify(lalamoveApiV3Service, times(1)).getCityInfo(country);
        verify(serviceTypeRepository, times(1)).saveAll(serviceTypeCaptor.capture());

        List<LalamoveServiceType> capturedServiceTypes = serviceTypeCaptor.getValue();
        assertEquals(1, capturedServiceTypes.size());
        LalamoveServiceType captMotor = capturedServiceTypes.stream()
                .filter(s -> s.getKey().equals("MOTORCYCLE"))
                .findFirst().orElseThrow();
        assertEquals(6, captMotor.getAvailableSpecialRequests().length);
    }

    @Test
    public void updateOrCreateServiceTypesForAllTenants_withApiCallReturnsErrors_shouldIgnore() {
        configureLalamoveV3();

        LalamoveV3ErrorResponse e1 = new LalamoveV3ErrorResponse();
        e1.setId("ERR_INVALID_MARKET");
        e1.setMessage("Please provide valid market.");
        cityInfoPresenter.setErrors(List.of(e1));

        Mockito.doCallRealMethod().when(transactionHelper).withNewTransaction(Mockito.any(Runnable.class));
        when(tenantRepository.findAll()).thenReturn(List.of(tenant));
        when(countryRepository.findByIsoNameInAndTenantId(anyList(), eq(tenant.getId()))).thenReturn(List.of(country));
        when(lalamoveApiV3Service.getCityInfo(country)).thenReturn(Optional.of(cityInfoPresenter));

        lalamoveService.updateOrCreateServiceTypesForAllTenants();

        verify(lalamoveApiV3Service, times(1)).getCityInfo(country);
        verify(serviceTypeRepository, never()).saveAll(serviceTypeCaptor.capture());
    }

    @Test
    public void updateOrCreateServiceTypesForAllTenants_withApiCallReturns500_shouldIgnore() {
        configureLalamoveV3();

        Mockito.doCallRealMethod().when(transactionHelper).withNewTransaction(Mockito.any(Runnable.class));
        when(tenantRepository.findAll()).thenReturn(List.of(tenant));
        when(countryRepository.findByIsoNameInAndTenantId(anyList(), eq(tenant.getId()))).thenReturn(List.of(country));
        when(lalamoveApiV3Service.getCityInfo(country)).thenReturn(Optional.empty());

        lalamoveService.updateOrCreateServiceTypesForAllTenants();

        verify(lalamoveApiV3Service, times(1)).getCityInfo(country);
        verify(serviceTypeRepository, never()).saveAll(serviceTypeCaptor.capture());
    }
}
