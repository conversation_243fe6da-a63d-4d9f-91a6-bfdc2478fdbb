package com.happyfresh.fulfillment.unit.lalamove.service;

import com.amazonaws.util.Base16Lower;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.property.LalamoveProperty;
import com.happyfresh.fulfillment.common.service.JedisLockService;
import com.happyfresh.fulfillment.common.util.WebRequestLogger;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.lalamove.form.v3.LalamoveV3WebhookForm;
import com.happyfresh.fulfillment.lalamove.service.LalamoveDeliveryUpdateService;
import com.happyfresh.fulfillment.lalamove.service.LalamoveWebhookService;
import com.happyfresh.fulfillment.repository.LalamoveDeliveryRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.nio.file.Files.readAllBytes;
import static java.nio.file.Paths.get;
import static org.mockito.ArgumentMatchers.*;

@RunWith(MockitoJUnitRunner.class)
public class LalamoveWebhookServiceTest {
    private static final String V3_EVENT_VERSION = "v3";

    @InjectMocks
    private LalamoveWebhookService lalamoveWebhookService;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private JedisLockService jedisLockService;

    @Mock
    private LalamoveDeliveryUpdateService lalamoveDeliveryUpdateService;

    @Spy
    private Logger logger;

    @Mock
    private LalamoveDeliveryRepository lalamoveDeliveryRepository;

    private User user;
    private Shipment shipment;
    private LalamoveDelivery delivery;
    private ObjectMapper mapper;
    private LalamoveProperty lalamoveProperty;

    @Before
    public void setup() throws Exception {
        injectLogger();
        setupLalamoveProperty();
        setupMapper();

        UserFactory userFactory = new UserFactory();
        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        SlotFactory slotFactory = new SlotFactory();

        user = userFactory.createUserData();

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setAddress1("Jl. Penuh Kenangan");
        stockLocation.getCluster().setSlotType(Slot.Type.LONGER_DELIVERY);

        Slot slot = slotFactory.createSlot(stockLocation, user);
        ShipmentFactory shipmentFactory = new ShipmentFactory();
        shipment = shipmentFactory.createShipmentWithBatch(slot, user);

        setupLalamoveDelivery();
    }

    private void setupLalamoveDelivery() {
        LalamoveDeliveryFactory factory = new LalamoveDeliveryFactory();
        delivery = factory.create(shipment, user);
        delivery.setExternalOrderId("externalId");
        delivery.setStatus(LalamoveDelivery.Status.ORDER_PLACED);
    }

    private void setupLalamoveProperty(){
        Map<String,String> apiKeys = new HashMap<>();
        apiKeys.put("id", "pk_test_93cb722cb4b0b47cc9debec02bda9dcc");
        apiKeys.put("my", "pk_test_93cb722cb4b0b47cc9debec02bda9dcc");
        apiKeys.put("th", "pk_test_93cb722cb4b0b47cc9debec02bda9dcc");
        Map<String,String> apiSecrets = new HashMap<>();
        apiSecrets.put("id", "sk_test_2lGxRcJ/iZewftBqqk0vns008H30+MYmw1gqbzRXJtNNKZlxdjyBuxIvCjjkn8LX");
        apiSecrets.put("my", "sk_test_2lGxRcJ/iZewftBqqk0vns008H30+MYmw1gqbzRXJtNNKZlxdjyBuxIvCjjkn8LX");
        apiSecrets.put("th", "sk_test_2lGxRcJ/iZewftBqqk0vns008H30+MYmw1gqbzRXJtNNKZlxdjyBuxIvCjjkn8LX");
        lalamoveProperty = new LalamoveProperty();
        lalamoveProperty.setApiKeys(apiKeys);
        lalamoveProperty.setApiSecrets(apiSecrets);
        ReflectionTestUtils.setField(lalamoveWebhookService, "lalamoveProperty", lalamoveProperty);
    }

    private void setupMapper(){
        mapper = new ObjectMapper();
        ReflectionTestUtils.setField(lalamoveWebhookService, "mapper", mapper);
    }

    private String getResponseFromResourceFileAsString(String fileName) throws IOException {
        return new String(readAllBytes(get("src", "test", "resources", "fixtures", fileName)));
    }

    private LalamoveV3WebhookForm getLalamoveV3WebhookForm(String fileName, String eventVersion) throws IOException {
        String json = getResponseFromResourceFileAsString(fileName);
        LalamoveV3WebhookForm form = mapper.readValue(json, LalamoveV3WebhookForm.class);
        form.setTimestamp(LocalDateTime.now().plusHours(2).toEpochSecond(ZoneOffset.UTC));
        form.setEventVersion(eventVersion);
        form.setSignature(generateSignature(form));
        return form;
    }

    private String generateSignature(LalamoveV3WebhookForm form){
        final String apiSecret = lalamoveProperty.getApiSecrets().get("id");
        final String httpMethod = "POST";
        final String path = LalamoveV3WebhookForm.WEBHOOK_PATH;
        try {
            String body = mapper.writer().writeValueAsString(form.getData());
            String rawSignature = form.getTimestamp() + "\r\n" + httpMethod + "\r\n" +
                    path + "\r\n\r\n" + body;

            Mac sha256HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(apiSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256HMAC.init(secretKey);

            return Base16Lower.encodeAsString(sha256HMAC.doFinal(rawSignature.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "";
    }

    private void injectLogger() throws Exception {
        Field loggerField = lalamoveWebhookService.getClass().getDeclaredField("logger");
        loggerField.setAccessible(true);
        Field modifiersField = loggerField.getClass().getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(loggerField, loggerField.getModifiers() & ~Modifier.FINAL);
        loggerField.set(lalamoveWebhookService, logger);
    }

    @Test
    public void invalidSignature() throws IOException {
        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_assigning_driver.json", V3_EVENT_VERSION);
        form.setSignature("d8adfadf49fd2f66540bbabf899fd97a84b392db1c3019b95c0dc531c7d3c27e");
        Assert.assertFalse(lalamoveWebhookService.isValidSignature(form, "ID", null));
    }

    @Test
    public void signatureEmpty() throws IOException {
        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_assigning_driver.json", V3_EVENT_VERSION);
        form.setSignature("");
        Assert.assertFalse(lalamoveWebhookService.isValidSignature(form, "ID", null));
    }

    @Test
    public void validateSignature_npeMapper() throws IOException {
        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_assigning_driver.json", V3_EVENT_VERSION);
        ReflectionTestUtils.setField(lalamoveWebhookService, "mapper", null);
        lalamoveWebhookService.isValidSignature(form, "ID", null);
        Mockito.verify(logger, Mockito.times(1)).error(eq("Error when validate lalamove webhook signature."), (Throwable) any());
    }

    @Test
    public void validateSignature_valid() throws IOException {
        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_assigning_driver.json", V3_EVENT_VERSION);
        Assert.assertTrue(lalamoveWebhookService.isValidSignature(form, "ID", null));
    }

    @Test
    public void processData_invalidEventVersion() throws IOException, InterruptedException {
        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_assigning_driver.json", "v2");
        Mockito.when(applicationContext.getBean(JedisLockService.class)).thenReturn(jedisLockService);
        Mockito.when(jedisLockService.lock("lalamove_webhook_" + form.getEventId())).thenReturn(true);

        lalamoveWebhookService.processData(form);
        Mockito.verify(logger, Mockito.times(1)).info(eq("Webhook event version is {}, not processed in v3 handle class"), anyString());
    }

    @Test
    public void processData_whenLalamoveDeliveryNotPresent() throws IOException, InterruptedException {
        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_assigning_driver.json", V3_EVENT_VERSION);
        Mockito.when(applicationContext.getBean(JedisLockService.class)).thenReturn(jedisLockService);
        Mockito.when(jedisLockService.lock("lalamove_webhook_" + form.getEventId())).thenReturn(true);
        Mockito.when(lalamoveDeliveryRepository.findByExternalOrderId(form.getData().getOrder().getOrderId())).thenReturn(Optional.empty());
        lalamoveWebhookService.processData(form);
        Mockito.verify(logger, Mockito.times(1)).info(eq("External order id {} on hff_lalamove_delivery table has not updatable status or empty delivery record"), anyString());
    }

    @Test
    public void processData_whenLalamoveDeliveryStatusNotUpdatable() throws IOException, InterruptedException {
        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_assigning_driver.json", V3_EVENT_VERSION);
        Mockito.when(applicationContext.getBean(JedisLockService.class)).thenReturn(jedisLockService);
        Mockito.when(jedisLockService.lock("lalamove_webhook_" + form.getEventId())).thenReturn(true);
        delivery.setStatus(LalamoveDelivery.Status.COMPLETED);
        Mockito.when(lalamoveDeliveryRepository.findByExternalOrderId(form.getData().getOrder().getOrderId()))
                .thenReturn(Optional.of(delivery));
        lalamoveWebhookService.processData(form);
        Mockito.verify(logger, Mockito.times(1)).info(eq("External order id {} on hff_lalamove_delivery table has not updatable status or empty delivery record"), anyString());
    }

    @Test
    public void processData_shouldSkipProcess_whenGotPastMessage() throws IOException, InterruptedException {
        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_assigning_driver.json", "v3");
        form.setTimestamp(LocalDateTime.now().minusHours(2).toEpochSecond(ZoneOffset.UTC));
        form.setSignature(generateSignature(form));
        Mockito.when(applicationContext.getBean(JedisLockService.class)).thenReturn(jedisLockService);
        Mockito.when(jedisLockService.lock("lalamove_webhook_" + form.getEventId())).thenReturn(true);
        delivery.setStatus(LalamoveDelivery.Status.ASSIGNING_DRIVER);
        Mockito.when(lalamoveDeliveryRepository.findByExternalOrderId(form.getData().getOrder().getOrderId()))
                .thenReturn(Optional.of(delivery));
        lalamoveWebhookService.processData(form);
        Mockito.verify(logger, Mockito.times(1)).info(eq("Skip webhook order id {}, event type {}, webhookTs {}, latestStatusChangedAt {}"), anyString(),
                anyString(), any(), any());
    }

    @Test
    public void processData_whenEventTypeNotHandled() throws IOException, InterruptedException {
        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_assigning_driver.json", V3_EVENT_VERSION);
        form.setEventType(LalamoveV3WebhookForm.ORDER_REPLACED);
        Mockito.when(applicationContext.getBean(JedisLockService.class)).thenReturn(jedisLockService);
        Mockito.when(jedisLockService.lock("lalamove_webhook_" + form.getEventId())).thenReturn(true);
        lalamoveWebhookService.processData(form);
        Mockito.verify(logger, Mockito.times(1)).info(eq("Webhook event type {} for event id {} not processed because it has not been handled"), anyString(), anyString());
    }

    @Test
    public void processData_whenAllValidationPass_shouldContinueToUpdateHandler() throws IOException, InterruptedException {
        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_assigning_driver.json", V3_EVENT_VERSION);
        form.setEventType(LalamoveV3WebhookForm.ORDER_STATUS_CHANGED);
        Mockito.when(applicationContext.getBean(JedisLockService.class)).thenReturn(jedisLockService);
        Mockito.when(jedisLockService.lock("lalamove_webhook_" + form.getEventId())).thenReturn(true);
        delivery.setStatus(LalamoveDelivery.Status.ORDER_PLACED);
        Mockito.when(lalamoveDeliveryRepository.findByExternalOrderId(form.getData().getOrder().getOrderId()))
                .thenReturn(Optional.of(delivery));
        lalamoveWebhookService.processData(form);
        Mockito.verify(lalamoveDeliveryUpdateService, Mockito.times(1)).handleLalamoveWebhookEventType(any(LalamoveV3WebhookForm.class), any(LalamoveDelivery.class));
    }

    @Test
    public void processData_jedisLock() throws IOException, InterruptedException {
        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_assigning_driver.json", V3_EVENT_VERSION);
        Mockito.when(applicationContext.getBean(JedisLockService.class)).thenReturn(jedisLockService);
        Mockito.when(jedisLockService.lock("lalamove_webhook_" + form.getEventId())).thenReturn(false);
        WebRequestLogger webRequestLogger = Mockito.mock(WebRequestLogger.class);
        Mockito.when(applicationContext.getBean(WebRequestLogger.class)).thenReturn(webRequestLogger);
        lalamoveWebhookService.processData(form);
        Mockito.verify(webRequestLogger, Mockito.times(1)).error(any(), eq("Acquire redis lock timeout"));
    }

    @Test
    public void processData_whenJedisThrowException() throws IOException, InterruptedException {
        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_assigning_driver.json", V3_EVENT_VERSION);
        Mockito.when(applicationContext.getBean(JedisLockService.class)).thenReturn(jedisLockService);
        Mockito.when(jedisLockService.lock("lalamove_webhook_" + form.getEventId())).thenThrow(new NullPointerException());
        lalamoveWebhookService.processData(form);
        Mockito.verify(logger, Mockito.times(1)).error(eq("Error when processing lalamove webhook "), (Throwable) any());
    }

    @Test
    public void processData_whenEventTypeNotHandled_andWalletBalanceChangedEvent_shouldNotProcess() throws IOException, InterruptedException {
        String webhook = "{\"apiKey\":\"pk_test_93cb722cb4b0b47cc9debec02bda9dcc\",\"timestamp\":1657081053,\"signature\":\"cf41a80590c295c79aaec3dcc9ffbb644f8c2c32bd09ec117c96e02d217165b4\",\"eventId\":\"BDD5C83D-9D65-16CD-0553-E3799A4E87AB\",\"eventType\":\"WALLET_BALANCE_CHANGED\",\"eventVersion\":\"v3\",\"data\":{\"balance\":{\"currency\":\"THB\",\"amount\":\"21819\"},\"updatedAt\":\"2022-07-06T11:17.00Z\"}}";
        LalamoveV3WebhookForm form = mapper.readValue(webhook, LalamoveV3WebhookForm.class);
        Mockito.when(applicationContext.getBean(JedisLockService.class)).thenReturn(jedisLockService);
        Mockito.when(jedisLockService.lock("lalamove_webhook_" + form.getEventId())).thenReturn(true);
        lalamoveWebhookService.processData(form);
        Mockito.verify(logger, Mockito.times(1)).info(eq("Webhook event type {} for event id {} not processed because it has not been handled"), anyString(), anyString());
        Mockito.verify(logger, Mockito.never()).error(eq("Error when processing lalamove webhook "), any(NullPointerException.class));
    }

}
