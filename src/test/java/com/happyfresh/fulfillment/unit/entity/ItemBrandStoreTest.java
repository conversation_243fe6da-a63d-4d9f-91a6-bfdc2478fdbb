package com.happyfresh.fulfillment.unit.entity;

import com.happyfresh.fulfillment.entity.Item;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ItemBrandStoreTest {

    private Item item;

    @Before
    public void setUp() {
        item = new Item();
    }

    @Test
    public void shouldCorrectly_getSkuWithOmittedSuffix() {
        item.setSku("091238091283-ID");
        Assert.assertEquals("091238091283", item.getSkuWithOmittedSuffix());
        item.setSku("0239091283-MY");
        Assert.assertEquals("0239091283", item.getSkuWithOmittedSuffix());
        item.setSku("1823091230192y30912-TH");
        Assert.assertEquals("1823091230192y30912", item.getSkuWithOmittedSuffix());

        item.setSku("091238091283-id");
        Assert.assertEquals("091238091283", item.getSkuWithOmittedSuffix());
        item.setSku("091238091283-my");
        Assert.assertEquals("091238091283", item.getSkuWithOmittedSuffix());
        item.setSku("091238091283-th");
        Assert.assertEquals("091238091283", item.getSkuWithOmittedSuffix());

        item.setSku("120939012830-MY-1203981290");
        Assert.assertEquals("120939012830-MY-1203981290", item.getSkuWithOmittedSuffix());
        item.setSku("091283091-id-12391290");
        Assert.assertEquals("091283091-id-12391290", item.getSkuWithOmittedSuffix());
        item.setSku("091283091-TH-12391290");
        Assert.assertEquals("091283091-TH-12391290", item.getSkuWithOmittedSuffix());
    }
}
