package com.happyfresh.fulfillment.unit.entity;

import org.junit.Assert;
import org.junit.Test;

import java.time.DayOfWeek;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class StockLocationBrandStoreTest extends BaseStockLocationTest {

    @Test
    public void shouldCorrectly_getOperationalDays() {
        // With valid string
        preferences.put("operational_days", "monday|tuesday|Wednesday|THURSDAY|friDay|Saturday");
        stockLocation.setPreferences(preferences);
        Assert.assertEquals(
                Arrays.asList(DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY, DayOfWeek.FRIDAY, DayOfWeek.SATURDAY),
                stockLocation.getOperationalDays()
        );

        // with invalid strings
        preferences.put("operational_days", "mondai|");
        stockLocation.setPreferences(preferences);
        Assert.assertEquals(new ArrayList<>(), stockLocation.getOperationalDays());
    }
}
