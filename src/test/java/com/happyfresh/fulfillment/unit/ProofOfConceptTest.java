package com.happyfresh.fulfillment.unit;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.util.CurrencyUtil;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.CountryFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import org.apache.commons.codec.binary.Hex;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.time.*;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class ProofOfConceptTest {

    @Test
    public void round_up() throws Exception {
        String json = "{\"id\":1,\"amount\": 6.69 }";
        ObjectMapper mapper = new ObjectMapper();
        JsonNode node = mapper.readTree(json);

        assertEquals(BigDecimal.valueOf(6.69), BigDecimal.valueOf(node.get("amount").asDouble(0.0)));
    }

    @Test
    public void datetime_conversion() {
        String datetime = "2020-08-03T04:53:36.707";
        LocalDateTime result = LocalDateTime.parse(datetime);
        assertNotNull(result);

        datetime = "2020-08-03T04:53:36";
        result = LocalDateTime.parse(datetime);
        assertNotNull(result);

        datetime = "2020-08-03T04:53";
        result = LocalDateTime.parse(datetime);
        assertNotNull(result);

        datetime = "2021-11-05T11:59:53.722Z";
        result = DateTimeUtil.spreeStringToLocalDateTime(datetime);
        assertNotNull(result);
    }

    @Test
    public void test_normalCase() {
        ZoneId zone = ZoneId.of("Asia/Jakarta");
        ZoneId UTCZone = ZoneId.of("UTC");
        // 2021-04-16 10:00 WIB --> 2021-04-16 03:00 UTC
        LocalDateTime now = LocalDateTime.of(2021, 4, 16, 3, 0);
        // 2021-04-17 00:00 WIB --> 2021-04-16 17:00 UTC
        LocalDateTime expected = LocalDateTime.of(2021, 4, 16, 17, 0); // + 1 day, start of day
        //LocalDateTime localEndOfDay = now.toLocalDate().atStartOfDay().atZone(zone).withZoneSameInstant(UTCZone).toLocalDateTime().plusDays(1);
        LocalDateTime localEndOfDay = now.atZone(UTCZone).withZoneSameInstant(zone)
                .withHour(0).withMinute(0)
                .plusDays(1)
                .withZoneSameInstant(UTCZone).toLocalDateTime();
        assertEquals(expected, localEndOfDay);
    }

    @Test
    public void test_edgeCase() {
        ZoneId zone = ZoneId.of("Asia/Jakarta");
        ZoneId UTCZone = ZoneId.of("UTC");

        // 2021-04-16 01:00 WIB --> 2021-04-15 18:00 UTC
        LocalDateTime now = LocalDateTime.of(2021, 4, 15, 18, 0);
        // 2021-04-17 00:00 WIB --> 2021-04-16 17:00 UTC
        LocalDateTime expected = LocalDateTime.of(2021, 4, 16, 17, 0); // + 1 day, start of day

        ZonedDateTime storeZoneStartDateTime = now.toLocalDate().atStartOfDay().atZone(zone);
        ZonedDateTime utcZoneStartDateTime = storeZoneStartDateTime.withZoneSameInstant(UTCZone);
        LocalDateTime utcLocalStartDateTime = utcZoneStartDateTime.toLocalDateTime();

        LocalDateTime localEndOfDay = now.atZone(UTCZone).withZoneSameInstant(zone)
                .withHour(0).withMinute(0)
                .plusDays(1)
                .withZoneSameInstant(UTCZone).toLocalDateTime();

        //LocalDateTime localEndOfDay = now.toLocalDate().atStartOfDay().atZone(zone).withZoneSameInstant(UTCZone).toLocalDateTime().plusDays(1);
        assertEquals(expected, localEndOfDay);
    }

    @Test
    public void test_sameDay() {
        ZoneId zone = ZoneId.of("Asia/Jakarta");
        ZoneId UTCZone = ZoneId.of("UTC");
        // 2021-04-16 10:00 WIB --> 2021-04-16 03:00 UTC
        LocalDateTime dateTime1 = LocalDateTime.of(2021, 4, 16, 3, 0);
        // 2021-04-16 06:00 WIB --> 2021-04-15 23:00 UTC
        LocalDateTime dateTime2 = LocalDateTime.of(2021, 4, 15, 23, 0);
        assertTrue(DateTimeUtil.isSameLocalDate(dateTime1, dateTime2, zone));
    }

    @Test
    public void test_HMAC() throws Exception {
        String eventId = "60d99ecfa4cc4600764b843e";
        String signature = "02dca6e3b84b3c94472ed017c571b60607802e48";
        String token = "8b9476a5f83e50975b3bae5994c4c4cecbf5374b";


        SecretKeySpec signingKey = new SecretKeySpec(token.getBytes("UTF-8"), "HmacSHA1");
        Mac mac = Mac.getInstance("HmacSHA1");
        mac.init(signingKey);

        byte[] hmacByte = mac.doFinal(eventId.getBytes("UTF-8"));
        assertEquals(signature, Hex.encodeHexString(hmacByte));
        //assertEquals(signature, String.format("%032x", new BigInteger(1, hmacByte)));
    }

    @Test
    public void test_storeOperatingHour() {
        ZoneId zone = ZoneId.of("Asia/Jakarta");
        ZoneId UTCZone = ZoneId.of("UTC");

        LocalDateTime localStartOfDay = LocalDateTime.now().atZone(UTCZone).withZoneSameInstant(zone)
                .withHour(0).withMinute(0)
                .withZoneSameInstant(UTCZone).toLocalDateTime();
        LocalDateTime localEndOfDay = localStartOfDay.plusDays(1);

        // Today 01:00 WIB --> Yesterday date 18:00 UTC
        LocalDateTime storeOpen = LocalDateTime.of(localStartOfDay.toLocalDate(), LocalTime.of(18,0));
        // Today 21:00 WIB --> Today date 14:00 UTC
        LocalDateTime storeClose = LocalDateTime.of(localEndOfDay.toLocalDate(), LocalTime.of(14,0));

        assertTrue(storeOpen.isBefore(storeClose));
    }

    @Test
    public void currencyUtil_TH() {
        String iso = "TH";
        BigDecimal price = BigDecimal.valueOf(120.32);

        String result = CurrencyUtil.format(price, iso);
        assertEquals("฿120", result);
    }

    @Test
    public void currencyUtil_ID() {
        String iso = "ID";
        BigDecimal price = BigDecimal.valueOf(120300);

        String result = CurrencyUtil.format(price, iso);
        assertEquals("Rp120.300", result);
    }

    @Test
    public void currencyUtil_MY() {
        String iso = "MY";
        BigDecimal price = BigDecimal.valueOf(200.13);

        String result = CurrencyUtil.format(price, iso);
        assertEquals("RM200.13", result);
    }

    @Test
    public void test_getTodayStartOfTheDayInUTC() {
        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        UserFactory userFactory = new UserFactory();
        CountryFactory countryFactory = new CountryFactory();

        User admin = userFactory.createUserData(Role.Name.ADMIN);

        Country countryID = countryFactory.createCountry(admin);
        StockLocation stockLocationID = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin, Slot.Type.LONGER_DELIVERY, countryID).get(0);

        ZoneId UTCZone = ZoneId.of("UTC");

        LocalDateTime todayStartOfTheDayInUTC_ID = DateTimeUtil.getTodayStartOfTheDayInUTC(stockLocationID);

        LocalDateTime localStartOfDayID = LocalDateTime.now().atZone(UTCZone).withZoneSameInstant(ZoneId.of(stockLocationID.getState().getTimeZone()))
                .withHour(0).withMinute(0).withSecond(0).withNano(0)
                .withZoneSameInstant(UTCZone).toLocalDateTime();
        Assert.assertEquals(localStartOfDayID, todayStartOfTheDayInUTC_ID);


        Country countryMY = countryFactory.createCountryMY(admin);
        StockLocation stockLocationMY = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin, Slot.Type.LONGER_DELIVERY, countryMY).get(0);
        stockLocationMY.getState().setTimeZone("Asia/Kuala_Lumpur");

        LocalDateTime todayStartOfTheDayInUTC_MY = DateTimeUtil.getTodayStartOfTheDayInUTC(stockLocationMY);

        LocalDateTime localStartOfDayMY = LocalDateTime.now().atZone(UTCZone).withZoneSameInstant(ZoneId.of(stockLocationMY.getState().getTimeZone()))
                .withHour(0).withMinute(0).withSecond(0).withNano(0)
                .withZoneSameInstant(UTCZone).toLocalDateTime();
        Assert.assertEquals(localStartOfDayMY, todayStartOfTheDayInUTC_MY);


        Country countryTH = countryFactory.createCountryTH(admin);
        StockLocation stockLocationTH = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin, Slot.Type.LONGER_DELIVERY, countryTH).get(0);
        stockLocationTH.getState().setTimeZone("Asia/Bangkok");

        LocalDateTime todayStartOfTheDayInUTC_TH = DateTimeUtil.getTodayStartOfTheDayInUTC(stockLocationTH);

        LocalDateTime localStartOfDayTH = LocalDateTime.now().atZone(UTCZone).withZoneSameInstant(ZoneId.of(stockLocationTH.getState().getTimeZone()))
                .withHour(0).withMinute(0).withSecond(0).withNano(0)
                .withZoneSameInstant(UTCZone).toLocalDateTime();
        Assert.assertEquals(localStartOfDayTH, todayStartOfTheDayInUTC_TH);

    }

}
