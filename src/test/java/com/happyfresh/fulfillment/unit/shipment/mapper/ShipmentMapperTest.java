package com.happyfresh.fulfillment.unit.shipment.mapper;

import com.happyfresh.fulfillment.batch.form.DeliveryInfoV3Form;
import com.happyfresh.fulfillment.common.property.S3Property;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.grabExpress.mapper.GrabExpressDeliveryMapperImpl;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.lalamove.mapper.LalamoveDeliveryMapperImpl;
import com.happyfresh.fulfillment.packaging.mapper.ShipmentPackagingMapperImpl;
import com.happyfresh.fulfillment.shipment.mapper.DeliveryPhotoMapperImpl;
import com.happyfresh.fulfillment.shipment.mapper.JobMapperImpl;
import com.happyfresh.fulfillment.shipment.mapper.ShipmentMapper;
import com.happyfresh.fulfillment.shipment.mapper.ShipmentMapperImpl;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentPresenter;
import com.happyfresh.fulfillment.slot.mappper.SlotMapperImpl;
import com.happyfresh.fulfillment.stockLocation.mapper.CountryMapperImpl;
import com.happyfresh.fulfillment.tpl.delyva.mapper.DelyvaDeliveryMapperImpl;
import com.happyfresh.fulfillment.user.mapper.UserMapperImpl;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

import static org.junit.Assert.*;


@ContextConfiguration(classes = ShipmentMapperTest.SpringTestConfig.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class ShipmentMapperTest {

    @Configuration
    @ComponentScan(basePackageClasses = {
            ShipmentMapperImpl.class,
            DeliveryPhotoMapperImpl.class,
            S3Property.class,
            UserMapperImpl.class,
            JobMapperImpl.class,
            CountryMapperImpl.class,
            ShipmentPackagingMapperImpl.class,
            SlotMapperImpl.class,
            GrabExpressDeliveryMapperImpl.class,
            LalamoveDeliveryMapperImpl.class,
            DelyvaDeliveryMapperImpl.class
    })
    public static class SpringTestConfig {
    }

    @Autowired
    private ShipmentMapper mapper;

    private Slot slot;
    private User admin;
    private ShipmentFactory shipmentFactory;
    private ItemFactory itemFactory;
    private SlotFactory slotFactory;
    private Slot rangerSlot;
    private StockLocation stockLocation;
    private StockLocation rangerStockLocation;

    @Before
    public void setup() {
        UserFactory userFactory = new UserFactory();
        admin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN);
        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, admin);
        stockLocation = stockLocations.get(0);
        rangerStockLocation = stockLocations.get(1);
        rangerStockLocation.setType(StockLocation.Type.SPECIAL);
        stockLocation.setMaxDeliveryHandover(20);
        slotFactory = new SlotFactory();
        slot = slotFactory.createSlot(stockLocation, admin);
        rangerSlot = slotFactory.createSlot(rangerStockLocation, admin);

        shipmentFactory = new ShipmentFactory();
        itemFactory = new ItemFactory();

    }

    private Shipment createShipment(Slot slot, int itemCount, String orderNumber, boolean isRanger, boolean isTpl, Batch.TplType tplType, boolean isHasBeenPending) {
        Shipment shipment = null;
        if (!isRanger) {
            shipment = shipmentFactory.createShipmentWithBatch(slot, admin, orderNumber);
            shipment.getJobs().forEach(job -> job.getBatch().setStockLocation(stockLocation));
            shipment.getJobs().forEach(job -> {
                job.getBatch().setStartTime(LocalDateTime.now());
                job.getBatch().setEndTime(LocalDateTime.now().plusMinutes(30L));
                if (job.isDelivery())
                    if (isTpl) {
                        job.getBatch().setDeliveryType(Batch.DeliveryType.TPL);
                        job.getBatch().setTplType(tplType);
                    } else {
                        job.getBatch().setDeliveryType(Batch.DeliveryType.NORMAL);
                        job.updateFlags("is_pending", String.valueOf(isHasBeenPending));
                    }
                if (job.isShopping())
                    job.getBatch().setShoppingType(Batch.ShoppingType.NORMAL);
            });

            shipment.getDeliveryJob().get().setDeliverySequence(1);
            shipment.getDeliveryJob().get().setDeliveryTime(LocalDateTime.now());
            shipment.getDeliveryJob().get().setState(Job.State.STARTED);
            shipment.getDeliveryJob().get().setId(2L);
            shipment.getShoppingJob().get().setState(Job.State.STARTED);
            shipment.getShoppingJob().get().setId(1L);
            DeliveryInfo deliveryInfo = new DeliveryInfo();
            deliveryInfo.setShipment(shipment);
            deliveryInfo.setId(1L);
            deliveryInfo.setCashAmount(BigDecimal.valueOf(10000));
            deliveryInfo.setLat(shipment.getAddressLat());
            deliveryInfo.setLon(shipment.getAddressLon());
            deliveryInfo.setReceiver("Receiver");
            deliveryInfo.setSignatureUrl("www.signature.com");
            deliveryInfo.setTenant(shipment.getTenant());
            deliveryInfo.setCreatedAt(LocalDateTime.now());
            deliveryInfo.setCreatedBy(admin.getId());

            shipment.setDeliveryInfos(List.of(deliveryInfo));
        } else {
            shipment = shipmentFactory.createShipmentWithRangerBatch(rangerSlot, admin, "order-002");
            shipment.getJobs().forEach(job -> job.getBatch().setStockLocation(rangerStockLocation));
            shipment.getJobs().forEach(job -> {
                job.getBatch().setStartTime(LocalDateTime.now());
                job.getBatch().setEndTime(LocalDateTime.now().plusMinutes(30L));
                job.getBatch().setDeliveryType(Batch.DeliveryType.NORMAL);
            });

            shipment.getRangerJob().get().setDeliverySequence(1);
            shipment.getRangerJob().get().setDeliveryTime(LocalDateTime.now());
            shipment.getRangerJob().get().setState(Job.State.STARTED);
            shipment.getRangerJob().get().setId(1L);
            shipment.getRangerJob().get().updateFlags("is_pending", String.valueOf(isHasBeenPending));
        }
        shipment.setId(1L);
        shipment.setOrderCustomerMemberTier("silver");
        shipment.setItems(createItems(itemCount, shipment));
        shipment.updateFlags(Shipment.FLAG_IS_ADDRESS_INCORRECT, "false");
        return shipment;
    }

    private Shipment createOnDemandShipment() {
        Shipment shipment = shipmentFactory.createShipmentWithOnDemandRangerBatch(rangerSlot, admin, "order-1");
        shipment.getJobs().forEach(job -> job.getBatch().setStockLocation(rangerStockLocation));
        shipment.getJobs().forEach(job -> {
            job.getBatch().setStartTime(LocalDateTime.now());
            job.getBatch().setEndTime(LocalDateTime.now().plusMinutes(30L));
            job.getBatch().setDeliveryType(Batch.DeliveryType.NORMAL);
        });

        Job job = shipment.getOnDemandRangerJob().orElseThrow();
        job.setDeliverySequence(1);
        job.setDeliveryTime(LocalDateTime.now());
        job.setState(Job.State.STARTED);
        job.setId(1L);

        shipment.setId(1L);
        shipment.setOrderCustomerMemberTier("silver");
        shipment.setItems(createItems(10, shipment));
        return shipment;
    }

    private Shipment createOnDemandDeliveryShipment() {
        Shipment shipment = shipmentFactory.createShipmentWithOnDemandDeliveryBatch(rangerSlot, admin, "order-1");
        shipment.getJobs().forEach(job -> {
            Batch b = job.getBatch();
            b.setStockLocation(rangerStockLocation);
            b.setStartTime(LocalDateTime.now());
            b.setEndTime(LocalDateTime.now().plusMinutes(30L));
            b.setDeliveryType(Batch.DeliveryType.NORMAL);
        });

        Job sjob = shipment.getShoppingJob().orElseThrow();
        sjob.setState(Job.State.FINISHED);
        sjob.setId(1L);
        Job job = shipment.getOnDemandDeliveryOrRangerJob().orElseThrow();
        job.setDeliverySequence(1);
        job.setDeliveryTime(LocalDateTime.now());
        job.setState(Job.State.STARTED);
        job.setId(1L);

        shipment.setId(1L);
        shipment.setOrderCustomerMemberTier("silver");
        shipment.setItems(createItems(10, shipment));
        return shipment;
    }

    private Shipment createOnDemandShoppingShipment() {
        Shipment shipment = shipmentFactory.createShipmentWithOnDemandShoppingBatch(rangerSlot, admin, "order-1");
        shipment.getJobs().forEach(job -> {
            Batch b = job.getBatch();
            b.setStockLocation(rangerStockLocation);
            b.setStartTime(LocalDateTime.now());
            b.setEndTime(LocalDateTime.now().plusMinutes(30L));
            b.setDeliveryType(Batch.DeliveryType.NORMAL);
        });

        Job sjob = shipment.getShoppingJob().orElseThrow();
        sjob.setState(Job.State.STARTED);
        sjob.setId(1L);
        Job job = shipment.getOnDemandDeliveryOrRangerJob().orElseThrow();
        job.setDeliverySequence(1);
        job.setDeliveryTime(LocalDateTime.now());
        job.setState(Job.State.STARTED);
        job.setId(1L);

        shipment.setId(1L);
        shipment.setOrderCustomerMemberTier("silver");
        shipment.setItems(createItems(10, shipment));
        return shipment;
    }

    private List<Item> createItems(int itemCount, Shipment shipment) {
        List<Item> items = itemFactory.createItems(shipment, admin, itemCount);
        int i = 0;
        for (Item item : items) {
            if (i % 2 == 0) {
                item.setFoundQty(new Random().nextInt(2));
                item.setOosQty(item.getRequestedQty() - item.getFoundQty());
            }
            if (i == items.size() - 1)
                item.setReplacedItem(items.get(0));
            i++;
        }
        return items;
    }

    private void verifyResult(Shipment shipment, ShipmentPresenter presenter,
                              String fleetType, String deliveryTipe, boolean hasBeenPending, boolean hasBeenContinue) {
        assertEquals(shipment.getId(), presenter.getId());
        assertEquals(shipment.getOrderNumber(), presenter.getOrderNumber());
        assertEquals(shipment.getNumber(), presenter.getNumber());
        assertEquals(shipment.getOrderCustomerMemberTier(), presenter.getOrderCustomerMemberTier());
        assertEquals(fleetType, presenter.getFleetType());
        assertEquals(deliveryTipe, presenter.getDeliveryType());
        assertEquals(shipment.getState().name(), presenter.getState());
        assertEquals(shipment.getTrackingUrl(), presenter.getTrackingUrl());
        assertEquals(hasBeenPending, presenter.isHasBeenPending());
        assertEquals(hasBeenContinue, presenter.isHasBeenContinue());
        assertEquals(shipment.isAddressIncorrect(), presenter.isAddressIncorrect());
    }

    @Test
    public void shipmentToShipmentPresenter_shouldReturnPresenter() {
        Shipment shipment = createShipment(slot, 10, "Order-001", false, false, null, true);
        shipment.getDeliveryJob().stream().forEach(job -> {
            String continueTimestamp = String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli());
            job.updateFlags(Job.FLAG_CONTINUE_TIMESTAMP, continueTimestamp);
        });

        ShipmentPresenter presenter = mapper.shipmentToShipmentPresenter(shipment);

        verifyResult(shipment, presenter, "hf", "normal", true, true);
        assertEquals(0, presenter.getIncorrectAddressReasons().size());
    }

    @Test
    public void shipmentToShipmentPresenter_ranger_shouldReturnPresenter() {
        Shipment shipment = createShipment(rangerSlot, 10, "Order-001", true, false, null, true);
        shipment.getRangerJob().stream().forEach(job -> {
            String continueTimestamp = String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli());
            job.updateFlags(Job.FLAG_CONTINUE_TIMESTAMP, continueTimestamp);
        });
        ShipmentPresenter presenter = mapper.shipmentToShipmentPresenter(shipment);

        verifyResult(shipment, presenter, "hf", "normal", true, true);
    }

    @Test
    public void shipmentToShipmentPresenter_onDemandRanger_shouldReturnOnDemandRangerJobField() {
        Shipment shipment = createOnDemandShipment();
        ShipmentPresenter presenter = mapper.shipmentToShipmentPresenter(shipment);

        assertEquals(shipment.getId(), presenter.getId());
        assertNotNull(presenter.getOnDemandRangerJob());
        assertEquals("started", presenter.getOnDemandRangerJob().getState());
    }

    @Test
    public void shipmentToShipmentPresenter_onDemandDelivery_shouldReturnOnDemandDeliveryJobField() {
        Shipment shipment = createOnDemandDeliveryShipment();
        ShipmentPresenter presenter = mapper.shipmentToShipmentPresenter(shipment);

        assertEquals(shipment.getId(), presenter.getId());
        assertNotNull(presenter.getOnDemandDeliveryJob());
        assertEquals("started", presenter.getOnDemandDeliveryJob().getState());
    }

    @Test
    public void shipmentToShipmentPresenter_onDemandShopping_shouldReturnOnDemandDeliveryJobField() {
        Shipment shipment = createOnDemandShoppingShipment();
        ShipmentPresenter presenter = mapper.shipmentToShipmentPresenter(shipment);

        assertEquals(shipment.getId(), presenter.getId());
        assertNotNull(presenter.getOnDemandDeliveryJob());
        assertEquals("started", presenter.getOnDemandDeliveryJob().getState());
    }

    @Test
    public void shipmentToShipmentPresenter_tpl_delyva_shouldReturnPresenter() {
        Shipment shipment = createShipment(slot, 10, "Order-001", false, true, Batch.TplType.DELYVA, false);
        ShipmentPresenter presenter = mapper.shipmentToShipmentPresenter(shipment);

        verifyResult(shipment, presenter, "delyva", "tpl", false, false);
    }

    @Test
    public void shipmentToShipmentPresenter_tpl_ge_shouldReturnPresenter() {
        Shipment shipment = createShipment(slot, 10, "Order-001", false, true, Batch.TplType.GRAB_EXPRESS, false);
        ShipmentPresenter presenter = mapper.shipmentToShipmentPresenter(shipment);

        verifyResult(shipment, presenter, "ge", "tpl", false, false);
    }

    @Test
    public void shipmentToShipmentPresenter_tpl_lalamove_shouldReturnPresenter() {
        Shipment shipment = createShipment(slot, 10, "Order-001", false, true, Batch.TplType.LALAMOVE, false);
        ShipmentPresenter presenter = mapper.shipmentToShipmentPresenter(shipment);

        verifyResult(shipment, presenter, "lalamove", "tpl", false, false);
    }

    @Test
    public void shipmentToShipmentPresenter_tpl_default_shouldReturnPresenter() {
        Shipment shipment = createShipment(slot, 10, "Order-001", false, true, Batch.TplType.DEFAULT, false);
        ShipmentPresenter presenter = mapper.shipmentToShipmentPresenter(shipment);

        verifyResult(shipment, presenter, "tpl", "tpl", false, false);
    }

    @Test
    public void shipmentToShipmentPresenter_whenShipmentIsNull_shouldReturnNull() {
        ShipmentPresenter presenter = mapper.shipmentToShipmentPresenter(null);

        assertNull(presenter);
    }

    @Test
    public void shipmentToShipmentPresenter_whenIncorrectAddressReasonsNotEmpty_shouldReturnListString(){
        Shipment shipment = createShipment(slot, 10, "Order-001", false, false, null, true);
        shipment.getDeliveryJob().stream().forEach(job -> {
            String continueTimestamp = String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli());
            job.updateFlags(Job.FLAG_CONTINUE_TIMESTAMP, continueTimestamp);
        });
        shipment.updateFlags(Shipment.FLAG_IS_ADDRESS_INCORRECT, "true");
        String reasons = Lists.newArrayList(DeliveryInfoV3Form.LocationAccuracy.WrongLocationReasonEnum.DELIVERY_LOCATION,
                        DeliveryInfoV3Form.LocationAccuracy.WrongLocationReasonEnum.ADDRESS_DETAIL,
                        DeliveryInfoV3Form.LocationAccuracy.WrongLocationReasonEnum.DELIVERY_INSTRUCTION).stream()
                .map(DeliveryInfoV3Form.LocationAccuracy.WrongLocationReasonEnum::toString)
                .collect(Collectors.joining(","));
        shipment.updateFlags(Shipment.FLAG_LOCATION_INCORRECT_REASONS, reasons);

        ShipmentPresenter presenter = mapper.shipmentToShipmentPresenter(shipment);
        verifyResult(shipment, presenter, "hf", "normal", true, true);
        assertEquals(shipment.getIncorrectLocationReasons(), presenter.getIncorrectAddressReasons().stream().collect(Collectors.joining(",")));
    }

    @Test
    public void shipmentToShipmentPresenter_whenNullTrackingUrl_shouldReturnEmptyString(){
        Shipment shipment = createOnDemandShoppingShipment();
        shipment.setTrackingUrl(null);
        ShipmentPresenter presenter = mapper.shipmentToShipmentPresenter(shipment);

        assertEquals(shipment.getId(), presenter.getId());
        assertNotNull(presenter.getTrackingUrl());
        assertEquals("", presenter.getTrackingUrl());
    }

}
