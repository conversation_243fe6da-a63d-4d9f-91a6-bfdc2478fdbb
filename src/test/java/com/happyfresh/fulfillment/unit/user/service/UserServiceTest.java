package com.happyfresh.fulfillment.unit.user.service;


import com.happyfresh.fulfillment.common.exception.type.InvalidSndRolesException;
import com.happyfresh.fulfillment.common.security.UserPrincipal;
import com.happyfresh.fulfillment.common.security.token.ClientAuthenticationToken;
import com.happyfresh.fulfillment.common.security.token.UserAuthenticationToken;
import com.happyfresh.fulfillment.common.util.ApplicationUtil;
import com.happyfresh.fulfillment.entity.Role;
import com.happyfresh.fulfillment.entity.Tenant;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.entity.UserRole;
import com.happyfresh.fulfillment.repository.RoleRepository;
import com.happyfresh.fulfillment.repository.TenantRepository;
import com.happyfresh.fulfillment.repository.UserRepository;
import com.happyfresh.fulfillment.repository.UserRoleRepository;
import com.happyfresh.fulfillment.unit.PowerMockBase;
import com.happyfresh.fulfillment.user.form.RoleForm;
import com.happyfresh.fulfillment.user.form.UserForm;
import com.happyfresh.fulfillment.user.service.UserService;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.util.ReflectionTestUtils;

import javax.persistence.EntityNotFoundException;
import java.security.NoSuchAlgorithmException;
import java.time.Clock;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@PrepareForTest({LocalDateTime.class, ApplicationUtil.class})
public class UserServiceTest extends PowerMockBase {

    @Mock
    private UserRepository userRepository;

    @Mock
    private RoleRepository roleRepository;

    @Mock
    private UserRoleRepository userRoleRepository;

    @Mock
    private TenantRepository tenantRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @InjectMocks
    UserService userService;


    private User createUser() {
        Clock clock = Clock.fixed(Instant.parse("2021-10-10T00:00:00.00Z"), ZoneId.of("UTC"));

        User user = new User();
        user.setFirstName("john");
        user.setLastName("doe");
        user.setPhone("085622221111");
        user.setEmail("<EMAIL>");
        user.setPassword("UserPassword");
        user.setToken(RandomStringUtils.randomAlphabetic(10));
        user.setCreatedAt(LocalDateTime.now(clock));
        user.setCreatedBy(1L);
        user.setIsActive(true);
        user.setToken("token");
        user.setProfilePictureUrl("https://img.hostname.net/my-pic.jpg");
        return user;
    }

    private Role createRole(String name) {
        Clock clock = Clock.fixed(Instant.parse("2021-10-10T00:00:00.00Z"), ZoneId.of("UTC"));

        Role role = new Role();
        role.setName(Role.Name.valueOf(name.toUpperCase()));
        role.setCreatedAt(LocalDateTime.now(clock));
        role.setCreatedBy(1L);

        return role;
    }

    private UserRole createUserRole(User user, Role role) {
        Clock clock = Clock.fixed(Instant.parse("2021-10-10T00:00:00.00Z"), ZoneId.of("UTC"));

        UserRole userRole = new UserRole();
        userRole.setUser(user);
        userRole.setRole(role);
        userRole.setCreatedBy(1L);
        userRole.setCreatedAt(LocalDateTime.now(clock));

        return userRole;
    }

    private void mockRandomKeyGenerator(){
        PowerMockito.mockStatic(ApplicationUtil.class);
        try {
            Mockito.when(ApplicationUtil.generateRandomKey()).thenReturn("token");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testGetAuthenticationTokenTokenNotFound() {
        String userToken = "userToken";
        String tenantToken = "tenantToken";

        Mockito.when(tenantRepository.findByToken(tenantToken)).thenReturn(null);
        Assert.assertNull(userService.getAuthenticationToken(userToken, tenantToken));
    }

    @Test
    public void testGetAuthenticationTokenTenantToken() {
        String userToken = "";
        String tenantToken = "tenantToken";

        Tenant tenant = new Tenant();

        Mockito.when(tenantRepository.findByToken(tenantToken)).thenReturn(tenant);
        AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(userToken, tenantToken);
        UserPrincipal userPrincipal = (UserPrincipal) authenticationToken.getPrincipal();

        Assert.assertNotNull(authenticationToken);
        Assert.assertTrue(authenticationToken.isAuthenticated());
        Assert.assertTrue(authenticationToken instanceof ClientAuthenticationToken);
        Assert.assertNull(userPrincipal.getUser());
        Assert.assertNotNull(userPrincipal.getTenant());
    }

    @Test
    public void testGetAuthenticationTokenUserNotFoundOrInActive() {
        String userToken = "userToken";
        String tenantToken = "tenantToken";
        Long tenantId = 1L;

        Tenant tenant = new Tenant();
        tenant.setId(tenantId);

        User user = createUser();
        user.setIsActive(false);

        Mockito.when(tenantRepository.findByToken(tenantToken)).thenReturn(tenant);
        Mockito.when(userRepository.findByTokenAndTenantId(userToken, tenantId)).thenReturn(null);

        AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(userToken, tenantToken);
        Assert.assertNull(authenticationToken);

        Mockito.when(userRepository.findByTokenAndTenantId(userToken, tenantId)).thenReturn(user);

        authenticationToken = userService.getAuthenticationToken(userToken, tenantToken);
        Assert.assertNull(authenticationToken);
    }

    @Test
    public void testGetAuthenticationTokenUserCredential() {
        String userToken = "userToken";
        String tenantToken = "tenantToken";
        Long tenantId = 1L;

        Tenant tenant = new Tenant();
        tenant.setId(tenantId);

        Role role = new Role();
        role.setName(Role.Name.ADMIN);

        List<Role> roles = new ArrayList<>();
        roles.add(role);

        User user = createUser();
        user.setIsActive(true);
        user.setRoles(roles);

        Mockito.when(tenantRepository.findByToken(tenantToken)).thenReturn(tenant);
        Mockito.when(userRepository.findByTokenAndTenantId(userToken, tenantId)).thenReturn(user);


        AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(userToken, tenantToken);

        UserPrincipal userPrincipal = (UserPrincipal) authenticationToken.getPrincipal();

        Assert.assertTrue(authenticationToken instanceof UserAuthenticationToken);
        Assert.assertTrue(authenticationToken.isAuthenticated());
        Assert.assertNotNull(authenticationToken);
        Assert.assertNull(authenticationToken.getCredentials());
        Assert.assertEquals(user, userPrincipal.getUser());
        Assert.assertNotNull(userPrincipal.getTenant());
    }

    @Test
    public void testFindOrCreateFindByEmail() {
        String email = "<EMAIL>";
        UserForm userForm = new UserForm();
        userForm.setEmail(email);
        User user = createUser();

        Mockito.when(userRepository.findByEmail(email)).thenReturn(user);
        User result = null;
        try {
            result = userService.findOrCreate(userForm);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        Assert.assertEquals(result, user);
    }

    @Test
    public void testFindOrCreateSuccess() {
        mockRandomKeyGenerator();

        User user = createUser();
        user.setIsNewlyCreated(false);
        user.setIsActive(true);
        String token = user.getToken();
        RoleForm roleForm = new RoleForm();
        roleForm.setName("SHOPPER");
        List<RoleForm> roles = new ArrayList<>();
        roles.add(roleForm);

        Mockito.when(passwordEncoder.encode(Mockito.anyString())).thenReturn(user.getPassword());
        Mockito.when(userRepository.findByToken(Mockito.anyString()))
                .thenReturn(user)
                .thenReturn(null)
                .thenReturn(user);
        Mockito.when(userRepository.save(Mockito.any(User.class))).then(AdditionalAnswers.returnsFirstArg());

        UserForm userForm = new UserForm();
        userForm.setEmail(user.getEmail());
        userForm.setFirstName(user.getFirstName());
        userForm.setLastName(user.getLastName());
        userForm.setPassword(user.getPassword());
        userForm.setPhone(user.getPhone());
        userForm.setIsActive(true);
        userForm.setProfilePictureUrl(user.getProfilePictureUrl());
        userForm.setPassword(user.getPassword());
        userForm.setRoles(roles);

        User result = null;
        try {
            result = userService.findOrCreate(userForm);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }

        Mockito.verify(userRoleRepository, Mockito.times(1)).save(Mockito.any(UserRole.class));
        Mockito.verify(userRepository, Mockito.times(2)).save(Mockito.any(User.class));

        Assert.assertNotNull(result);
        Assert.assertEquals(userForm.getEmail(), result.getEmail());
        Assert.assertEquals(token, result.getToken());
        Assert.assertEquals(true, result.getIsNewlyCreated());
        Assert.assertEquals(userForm.getProfilePictureUrl(), result.getProfilePictureUrl());
        Assert.assertEquals(userForm.getPassword(), result.getPassword());
    }

    @Test
    public void testFindOrCreateNoPassword() {
        mockRandomKeyGenerator();

        UserForm userForm = new UserForm();
        userForm.setPassword("");
        userForm.setRoles(new ArrayList<>());

        Mockito.when(userRepository.save(Mockito.any(User.class))).then(AdditionalAnswers.returnsFirstArg());

        User result = null;
        try {
            result = userService.findOrCreate(userForm);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }

        Mockito.verify(userRepository, Mockito.times(3)).save(Mockito.any(User.class));
        Assert.assertNotNull(result);
    }

    @Test
    public void testFindOrCreateRoleNotMatch() {
        mockRandomKeyGenerator();
        UserForm userForm = new UserForm();
        RoleForm roleForm = new RoleForm();
        roleForm.setName("random");
        List<RoleForm> roles = new ArrayList<>();
        roles.add(roleForm);
        userForm.setRoles(roles);

        Mockito.when(userRepository.save(Mockito.any(User.class))).then(AdditionalAnswers.returnsFirstArg());

        User result = null;
        try {
            result = userService.findOrCreate(userForm);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }

        Mockito.verify(userRoleRepository, Mockito.times(0)).save(Mockito.any(UserRole.class));
        Assert.assertNotNull(result);
    }

    @Test(expected = EntityNotFoundException.class)
    public void testUpdateNotFound(){
        UserForm userForm = new UserForm();
        userService.update(userForm);
    }

    @Test
    public void testUpdateSuccess(){
        final String phoneNumber = "+621234567890";
        UserForm userForm = new UserForm();
        userForm.setIsActive(true);
        userForm.setEmail("<EMAIL>");
        userForm.setPhone(phoneNumber);
        User user = createUser();
        Mockito.when(userRepository.findByEmail(Mockito.anyString())).thenReturn(user);
        Mockito.when(userRepository.save(Mockito.any(User.class))).then(AdditionalAnswers.returnsFirstArg());
        User result = userService.update(userForm);
        Assert.assertNotNull(result);
        Assert.assertTrue(result.getIsActive());
        Assert.assertEquals(phoneNumber, result.getPhone());
    }

    @Test
    public void testUpdateRoleWithValidSndRole() {
        User user = createUser();
        UserForm userForm = new UserForm();
        RoleForm shopperRoleForm = new RoleForm();
        RoleForm backOfficeRoleForm = new RoleForm();

        Role shopperRole = createRole("shopper");
        Role backOfficeRole = createRole("back_office");
        shopperRoleForm.setName("shopper");
        backOfficeRoleForm.setName("back_office");
        UserRole shopperUserRole = createUserRole(user, shopperRole);
        UserRole backOfficeUserRole = createUserRole(user, backOfficeRole);

        userForm.setEmail(user.getEmail());
        userForm.setRoles(Arrays.asList(shopperRoleForm, backOfficeRoleForm));

        Mockito.when(userRepository.findByEmail(Mockito.anyString())).thenReturn(user);
        Mockito.when(userRepository.save(Mockito.any(User.class))).then(AdditionalAnswers.returnsFirstArg());
        Mockito.when(roleRepository.findByName(shopperRole.getName())).thenReturn(shopperRole);
        Mockito.when(roleRepository.findByName(backOfficeRole.getName())).thenReturn(backOfficeRole);
        Mockito.when(userRoleRepository.save(Mockito.any(UserRole.class)))
                .thenReturn(shopperUserRole)
                .thenReturn(backOfficeUserRole);
        User result = userService.update(userForm);
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getUserRoles());
        Assert.assertEquals(userForm.getRoles().size(), result.getUserRoles().size());
    }

    @Test(expected = InvalidSndRolesException.class)
    public void testUpdateRoleWithInvalidSndRole() {
        User user = createUser();
        UserForm userForm = new UserForm();
        RoleForm adminRoleForm = new RoleForm();
        adminRoleForm.setName("admin");
        List<RoleForm> roles = new ArrayList<>();
        roles.add(adminRoleForm);

        userForm.setEmail(user.getEmail());
        userForm.setRoles(roles);

        Mockito.when(userRepository.findByEmail(Mockito.anyString())).thenReturn(user);
        Mockito.when(userRepository.save(Mockito.any(User.class))).then(AdditionalAnswers.returnsFirstArg());

        userService.update(userForm);
    }

    @Test
    public void testUpdateRoleWithEmptyRole() {
        User user = createUser();
        UserForm userForm = new UserForm();
        userForm.setEmail(user.getEmail());
        userForm.setRoles(new ArrayList<>());

        Mockito.when(userRepository.findByEmail(Mockito.anyString())).thenReturn(user);
        Mockito.when(userRepository.save(Mockito.any(User.class))).then(AdditionalAnswers.returnsFirstArg());
        User result = userService.update(userForm);
        Mockito.verify(userRoleRepository, Mockito.times(0)).save(Mockito.any(UserRole.class));
        Assert.assertNotNull(result);
        Assert.assertNull(result.getUserRoles());
    }

    @Test
    public void testUpdateWithoutRoles() {
        User user = createUser();
        UserForm userForm = new UserForm();
        userForm.setEmail(user.getEmail());
        userForm.setRoles(null);

        Mockito.when(userRepository.findByEmail(Mockito.anyString())).thenReturn(user);
        Mockito.when(userRepository.save(Mockito.any(User.class))).then(AdditionalAnswers.returnsFirstArg());
        User result = userService.update(userForm);
        Mockito.verify(userRoleRepository, Mockito.times(0)).save(Mockito.any(UserRole.class));
        Assert.assertNotNull(result);
        Assert.assertNull(result.getUserRoles());
    }

    @Test
    public void testBlockUserNotFound(){
        Mockito.when(userRepository.findByEmail(Mockito.anyString())).thenReturn(null);
        userService.block("<EMAIL>", 1);
        Mockito.verify(userRoleRepository, Mockito.times(0)).save(Mockito.any(UserRole.class));
    }

    @Test
    public void testBlockSuccess(){
        LocalDateTime now = LocalDateTime.now();

        User user = createUser();
        Mockito.when(userRepository.findByEmail(Mockito.anyString())).thenReturn(user);

        userService.block("<EMAIL>", 1);

        Mockito.verify(userRepository, Mockito.times(1)).save(Mockito.any(User.class));
        Assert.assertEquals(now.plusSeconds(1).withNano(0), user.getBlockedUntil().withNano(0));
    }

    @Test
    public void testIsBlockedEmailNotFound(){
        Mockito.when(userRepository.findByEmail(Mockito.anyString())).thenReturn(null);
        boolean result = userService.isBlocked("<EMAIL>");
        Assert.assertFalse(result);
    }

    @Test
    public void testIsBlockedUserIsBlocked(){
        User user = createUser();
        user.setBlockedUntil(LocalDateTime.now().plusHours(1));
        Mockito.when(userRepository.findByEmail(Mockito.anyString())).thenReturn(user);
        boolean result = userService.isBlocked("<EMAIL>");
        Assert.assertTrue(result);
    }

    @Test
    public void testUserExistsByEmail() {
        Mockito.when(userRepository.existsByEmail(Mockito.anyString())).thenReturn(true);
        boolean result = userService.userExistsByEmail("<EMAIL>");
        Assert.assertTrue(result);

        Mockito.when(userRepository.existsByEmail(Mockito.anyString())).thenReturn(false);
        result = userService.userExistsByEmail("<EMAIL>");
        Assert.assertFalse(result);
    }

    @Test
    public void testCheckValidUrl() {
        boolean result = ReflectionTestUtils.invokeMethod(userService, "checkValidUrl", "https://domain.com/img.jpg");
        Assert.assertTrue(result);
    }

    @Test
    public void testCheckValidUrlFailed() {
        boolean result = ReflectionTestUtils.invokeMethod(userService, "checkValidUrl", "random string");
        Assert.assertFalse(result);
    }

}
