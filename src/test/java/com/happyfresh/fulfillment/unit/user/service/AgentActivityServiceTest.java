package com.happyfresh.fulfillment.unit.user.service;

import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.AgentFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.repository.AgentActivityRepository;
import com.happyfresh.fulfillment.user.service.AgentActivityService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class AgentActivityServiceTest {

    @InjectMocks
    private AgentActivityService agentActivityService;

    @Mock
    private AgentActivityRepository agentActivityRepository;

    @Test
    public void createAgentActivity(){
        UserFactory userFactory = new UserFactory();
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        user.setId(1l);
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, user.getTenant());
        shopper.setId(2l);

        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);
        StockLocation stockLocation = stockLocations.get(0);

        AgentFactory agentFactory = new AgentFactory();
        Agent shopperAgent = agentFactory.createAgent(shopper, stockLocation, Agent.State.WORKING);
        agentActivityService.createAgentActivity(shopperAgent);
        Mockito.verify(agentActivityRepository, Mockito.atLeastOnce()).save(any(AgentActivity.class));
    }

}
