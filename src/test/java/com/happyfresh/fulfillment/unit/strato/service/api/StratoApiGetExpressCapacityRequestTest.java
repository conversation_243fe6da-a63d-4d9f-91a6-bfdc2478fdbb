package com.happyfresh.fulfillment.unit.strato.service.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.property.StratoProperty;
import com.happyfresh.fulfillment.enabler.form.StratoExpressCapacityForm;
import com.happyfresh.fulfillment.enabler.presenter.StratoExpressCapacityPresenter;
import com.happyfresh.fulfillment.enabler.service.api.StratoApiGetExpressCapacityRequest;
import com.happyfresh.fulfillment.integrationTest.test.common.NoResetRequestExpectationManager;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

@RunWith(MockitoJUnitRunner.class)
public class StratoApiGetExpressCapacityRequestTest {

    private MockRestServiceServer mockServer;
    private StratoApiGetExpressCapacityRequest request;

    @Before
    public void setup() {
        StratoProperty stratoProperty = new StratoProperty();
        stratoProperty.setBaseUrl("https://strato-test-url.io");

        RestTemplate template = new RestTemplate();
        mockServer = MockRestServiceServer.bindTo(template).build(new NoResetRequestExpectationManager());

        ObjectMapper mapper = new ObjectMapper();

        StratoExpressCapacityForm form = new StratoExpressCapacityForm("R123", 10, 5);
        request = new StratoApiGetExpressCapacityRequest(1l, form, mapper);
        request.setRestTemplate(template);
        request.setStratoProperty(stratoProperty);
    }

    @Test
    public void shouldCallApi() {
        String response = "{" +
                "\"earliest_pickup\":\"2021-07-18T22:00:00Z\", \"unavailable_reason\": null }";

        String url = "https://strato-test-url.io/store/1/express_capacity";
        mockServer.expect(ExpectedCount.once(), requestTo(url))
            .andExpect(method(HttpMethod.GET))
            .andRespond(withSuccess(response, MediaType.APPLICATION_JSON)); // dummy response

        request.makeRequest();
        mockServer.verify();
    }

    @Test
    public void shouldResponseWithOptionalPresenter() {
        String response = "{" +
                "\"earliest_pickup\":\"2021-07-18T22:00:00Z\", \"unavailable_reason\": null }";

        String url = "https://strato-test-url.io/store/1/express_capacity";
        mockServer.expect(ExpectedCount.once(), requestTo(url))
            .andExpect(method(HttpMethod.GET))
            .andRespond(withSuccess(response, MediaType.APPLICATION_JSON)); // dummy response

        Optional<StratoExpressCapacityPresenter> maybePresenter = request.makeRequest();
        mockServer.verify();

        Assert.assertTrue(maybePresenter.isPresent());
        StratoExpressCapacityPresenter presenter = maybePresenter.get();
        Assert.assertEquals(LocalDateTime.class, presenter.getEarliestPickup().getClass());
        Assert.assertEquals(LocalDateTime.of(2021,7,18,22,0,0), presenter.getEarliestPickup());
    }

    @Test
    public void withResponseErrorShouldReturnOptionalEmpty() throws Exception {
        String url = "https://strato-test-url.io/store/1/express_capacity";
        mockServer.expect(ExpectedCount.once(), requestTo(url))
            .andExpect(method(HttpMethod.GET))
            .andRespond(withStatus(HttpStatus.valueOf(409))
                .contentType(MediaType.APPLICATION_JSON)
                .body("{ \"message\": \"ERROR_STRATO\" }")
            );

        Optional<StratoExpressCapacityPresenter> maybePresenter = request.makeRequest();
        mockServer.verify();

        Assert.assertFalse(maybePresenter.isPresent());
    }

    @Test
    public void shouldResponseWithOptionalPresenter_caseEarliestPickupNull() {
        String response = "{\"earliest_pickup\":null, \"unavailable_reason\":\"NO_PICKER\" }";

        String url = "https://strato-test-url.io/store/1/express_capacity";
        mockServer.expect(ExpectedCount.once(), requestTo(url))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess(response, MediaType.APPLICATION_JSON)); // dummy response

        Optional<StratoExpressCapacityPresenter> maybePresenter = request.makeRequest();
        mockServer.verify();

        Assert.assertTrue(maybePresenter.isPresent());
        StratoExpressCapacityPresenter presenter = maybePresenter.get();
        Assert.assertNull(presenter.getEarliestPickup());
        Assert.assertEquals(StratoExpressCapacityPresenter.UnavailableReason.NO_PICKER.toString().toLowerCase(), presenter.getUnavailableReason().toLowerCase());
    }
}
