package com.happyfresh.fulfillment.unit.admin.mapper;

import com.happyfresh.fulfillment.admin.mapper.AdminOnDemandOrderMapper;
import com.happyfresh.fulfillment.admin.mapper.AdminOnDemandOrderMapperImpl_;
import com.happyfresh.fulfillment.admin.presenter.AdminOnDemandOrderPresenter;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.shipment.mapper.JobMapper;
import com.happyfresh.fulfillment.shipment.presenter.JobPresenter;
import com.happyfresh.fulfillment.user.mapper.UserMapper;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mapstruct.factory.Mappers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class AdminOnDemandOrderMapperTest {

    private User sysAdmin;
    private List<StockLocation> stockLocations;
    private StockLocation stockLocation;
    private UserFactory userFactory;
    private AgentFactory agentFactory;
    private Shipment shipment;

    private AdminOnDemandOrderMapper adminOnDemandOrderMapper = Mappers.getMapper(AdminOnDemandOrderMapper.class);

    @Mock
    private JobMapper jobMapper;

    private UserMapper userMapper = Mappers.getMapper(UserMapper.class);
    private AdminOnDemandOrderMapperImpl_ delegate = new AdminOnDemandOrderMapperImpl_();

    @Before
    public void setup(){
        ReflectionTestUtils.setField(adminOnDemandOrderMapper, "delegate", delegate);
        ReflectionTestUtils.setField(adminOnDemandOrderMapper, "jobMapper", jobMapper);
        ReflectionTestUtils.setField(adminOnDemandOrderMapper, "userMapper", userMapper);

        userFactory = new UserFactory();
        agentFactory = new AgentFactory();

        sysAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        sysAdmin.setId(1l);

        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, sysAdmin, Slot.Type.LONGER_DELIVERY);
        stockLocation = stockLocations.get(0);
        stockLocation.setId(1L);
        stockLocation.getCluster().setId(1L);

        SlotFactory slotFactory = new SlotFactory();
        Slot slot = slotFactory.createOnDemandSlot(stockLocation, sysAdmin);

        ShipmentFactory shipmentFactory = new ShipmentFactory();
        shipment = shipmentFactory.createShipmentWithOnDemandRangerBatch(slot, sysAdmin, "Order1");
        shipment.getJobs().forEach(job -> job.getBatch().setStockLocation(stockLocation));
        shipment.getJobs().forEach(job -> {
            job.getBatch().setStartTime(LocalDateTime.now());
            job.getBatch().setEndTime(LocalDateTime.now().plusMinutes(30L));
        });
    }

    @Test
    public void shouldMapWithProperFormat() {
        User user1 = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, sysAdmin.getTenant());
        user1.setId(3l);
        User user2 = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, sysAdmin.getTenant());
        user2.setId(4l);
        Job onDemandJob = shipment.getOnDemandDeliveryOrRangerJob().get();
        JobPresenter jobPresenter = new JobPresenter();
        jobPresenter.setNote("Test");
        jobPresenter.setJobType("ON_DEMAND_RANGER");
        Mockito.doReturn(jobPresenter).when(jobMapper).jobToJobPresenter(onDemandJob);

        AdminOnDemandOrderPresenter presenter = adminOnDemandOrderMapper.onDemandOrderToOnDemandOrderPresenter(shipment, Lists.newArrayList(user1, user2));
        Assert.assertEquals("Order1", presenter.getNumber());
        Assert.assertEquals(2, presenter.getAvailableUsers().size());
        Assert.assertEquals(1, presenter.getJobs().size());
        Assert.assertEquals("Test", presenter.getJobs().get(0).getNote());
        Assert.assertEquals("ON_DEMAND_RANGER", presenter.getJobs().get(0).getJobType());
    }
}
