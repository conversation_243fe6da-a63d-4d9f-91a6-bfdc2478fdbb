package com.happyfresh.fulfillment.unit.admin.mapper;

import com.happyfresh.fulfillment.admin.mapper.activebatch.AdminPlannedActualTimePresenterMapperImpl;
import com.happyfresh.fulfillment.grabExpress.mapper.GrabExpressDeliveryMapperImpl;
import com.happyfresh.fulfillment.lalamove.mapper.LalamoveDeliveryMapperImpl;
import com.happyfresh.fulfillment.tpl.delyva.mapper.DelyvaDeliveryMapperImpl;
import org.junit.runner.RunWith;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(classes = BaseAdminMapperTest.SpringTestConfig.class)
@RunWith(SpringJUnit4ClassRunner.class)
public abstract class BaseAdminMapperTest {
    @Configuration
    @ComponentScan(basePackageClasses = {
            AdminPlannedActualTimePresenterMapperImpl.class,
            GrabExpressDeliveryMapperImpl.class,
            LalamoveDeliveryMapperImpl.class,
            DelyvaDeliveryMapperImpl.class
    })
    public static class SpringTestConfig {
    }
}
