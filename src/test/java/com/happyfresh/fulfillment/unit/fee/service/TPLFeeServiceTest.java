package com.happyfresh.fulfillment.unit.fee.service;

import com.happyfresh.fulfillment.common.service.DistanceService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.entity.ddf.DDFMatrix;
import com.happyfresh.fulfillment.entity.ddf.SaverFeeMatrix;
import com.happyfresh.fulfillment.entity.ddf.TPLFeeMatrix;
import com.happyfresh.fulfillment.entity.ddf.TimeBasedFeeMatrix;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.DDFMatrixRepository;
import com.happyfresh.fulfillment.slot.bean.AppliedFee;
import com.happyfresh.fulfillment.slot.bean.DDFCalculationContext;
import com.happyfresh.fulfillment.slot.service.ddf.DDFCalculationServiceChain;
import com.happyfresh.fulfillment.slot.service.ddf.SaverFeeService;
import com.happyfresh.fulfillment.slot.service.ddf.TPLFeeService;
import com.happyfresh.fulfillment.slot.service.ddf.TimeBasedFeeService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.spy;

@RunWith(MockitoJUnitRunner.class)
public class TPLFeeServiceTest {

    private DDFCalculationServiceChain service;

    private DDFCalculationServiceChain.Builder serviceBuilder;

    private DDFCalculationContext context;

    @Mock
    private DDFMatrixRepository ddfMatrixRepository;

    @Mock
    private DistanceService distanceService;

    private List<Slot> slots;

    private List<StockLocation> stockLocations;

    private Shipment shipment;

    private User externalAdmin;

    @Before
    public void setUp() throws InstantiationException, IllegalAccessException {
        this.externalAdmin = new UserFactory().createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        MockitoAnnotations.initMocks(this.getClass());
        this.serviceBuilder = new DDFCalculationServiceChain.Builder();
        this.service = serviceBuilder
                .addServiceChain(TimeBasedFeeService.class)
                .addServiceChain(TPLFeeService.class)
                .addServiceChain(SaverFeeService.class)
                .build(StockLocation.DDFType.ORIGINAL);

        UserFactory userFactory = new UserFactory();
        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        SlotFactory slotFactory = new SlotFactory();

        User user = userFactory.createUserData();
        this.stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user, false);
        this.slots = slotFactory.createSlots(this.stockLocations, 6, 6, 14, 15, Slot.Type.ONE_HOUR, user);
        this.shipment = new ShipmentFactory().createShipment(this.slots.get(0), user, "Order-0", "Order-0", Shipment.State.READY, 7);
        List<Item> items = new ItemFactory().createItems(shipment, user, 1);
        this.shipment.setItems(items);

    }

    @Test
    public void testCompanyOrderIDR5000_AdditionalFeeShouldAddIDR5000() {
        this.setIsCompany();
        this.shipment.setOrderTotal(BigDecimal.valueOf(5000));
        AppliedFee fee = calculateFee();
        assertAdditionalBasedFee(fee, 5000D);
    }

    @Test
    public void testCompanyOrderIDR1000000_AdditionalFeeShouldAddIDR5000() {
        this.setIsCompany();
        this.shipment.setOrderTotal(BigDecimal.valueOf(1000000));
        AppliedFee fee = calculateFee();
        assertAdditionalBasedFee(fee, 5000D);
    }

    @Test
    public void testCompanyOrderIDR1000001_AdditionalFeeShouldAddIDR10000() {
        this.setIsCompany();
        this.shipment.setOrderTotal(BigDecimal.valueOf(1000001));
        AppliedFee fee = calculateFee();
        assertAdditionalBasedFee(fee, 10000D);
    }

    @Test
    public void testNotCompanyOrder_ShouldBeNormal() {
        this.setNotCompany();
        this.shipment.setOrderTotal(BigDecimal.valueOf(1000000));
        AppliedFee fee = calculateFee();
        assertAdditionalBasedFee(fee, 0D);
    }

    private void setIsCompany() {
        Company spyCompany = setupCompany();
        this.shipment.setOrderCompanyId(spyCompany.getExternalId());
    }

    private void setNotCompany() {
        this.shipment.setOrderCompanyId(null);
    }

    private void assertAdditionalBasedFee(AppliedFee fee, Double expectedValue) {
        Assert.assertEquals(BigDecimal.valueOf(expectedValue).floatValue(), fee.getDetail().getAdditionalBasedFee().floatValue(), 0);
    }

    private void assertSaverFee(AppliedFee fee, BigDecimal expectedValue) {
        Assert.assertEquals(expectedValue, fee.getDetail().getSaverFee());
    }

    private void assertTotal(AppliedFee fee, Double expectedValue) {
        Assert.assertEquals(BigDecimal.valueOf(expectedValue).floatValue(), fee.getTotal().floatValue(), 0);
    }

    private AppliedFee calculateFee() {
        this.setDDFContext();
        AppliedFee fee = this.service.calculateFee(this.context, this.slots.get(0));
        return fee;
    }

    private void setDDFContext() {
        Mockito.when(this.ddfMatrixRepository.findAllByStockLocationOrderByIdAsc(stockLocations.get(0))).thenReturn(this.getDDFMatrix());
        this.context = new DDFCalculationContext.Builder()
                .withStockLocation(stockLocations.get(0))
                .withShipmentContext(shipment, distanceService, 1, false)
                .withDDFMatrixConfig(ddfMatrixRepository)
                .build();
    }

    /**
     * Matrix setup
     * Col id = free
     * Row id = 0 reserved to threshold
     * Row id = 1 reserved to amount of applied threshold
     *
     * e.g
     * 0 | 0 = 1000000
     * 0 | 1 = 5000
     * 1 | 0 = 99999999
     * 1 | 1 = 10000
     * means every order <= IDR 1.000.000 will add fee IDR 5.000
     * otherwhise get additional fee IDR 10.000
     * with assumption no order will exceed 99.999.999
     *
     */
    private List<DDFMatrix> getDDFMatrix() {
        ZonedDateTime slotTime = this.slots.get(0).getStartTime().atZone(ZoneId.of("UTC"))
                .withZoneSameInstant(ZoneId.of(this.slots.get(0).getStockLocation().getState().getTimeZone()));
        List<DDFMatrix> ddfMatrix = Arrays.asList(
                new TimeBasedFeeMatrix(slotTime.getDayOfWeek().getValue(), slotTime.getHour(), BigDecimal.valueOf(1000D), stockLocations.get(0)),
                new TPLFeeMatrix(1, 0, BigDecimal.valueOf(1000000D), stockLocations.get(0)),
                new TPLFeeMatrix(1, 1, BigDecimal.valueOf(5000D), stockLocations.get(0)),
                new TPLFeeMatrix(2, 0, BigDecimal.valueOf(99000000D), stockLocations.get(0)),
                new TPLFeeMatrix(2, 1, BigDecimal.valueOf(10000D), stockLocations.get(0)),
                new SaverFeeMatrix(0, 0,  BigDecimal.valueOf(0.5D), stockLocations.get(0)),
                new SaverFeeMatrix(1, 1,  BigDecimal.valueOf(0.3D), stockLocations.get(0)),
                new SaverFeeMatrix(2, 2,  BigDecimal.valueOf(3), stockLocations.get(0))
        );
        return ddfMatrix;
    }

    private Company setupCompany() {
        Company company = new CompanyFactory().create(this.externalAdmin);
        // Assign DdfCompany rule to Company, Store DDF
        DdfCompany ddfCompany1 = new DdfCompanyFactory().create(company, true, this.externalAdmin);
        ddfCompany1.setLowerVolumeLimit(0);
        ddfCompany1.setUpperVolumeLimit(10);
        // Assign DdfCompany rule to Company, Company DDF
        DdfCompany ddfCompany2 = new DdfCompanyFactory().create(company, false, this.externalAdmin);
        ddfCompany2.setLowerVolumeLimit(11);
        ddfCompany2.setUpperVolumeLimit(50);
        ddfCompany2.setMinimumFee(BigDecimal.valueOf(1000));
        ddfCompany2.setFixedFee(BigDecimal.valueOf(1000));
        ddfCompany2.setMinimumFeeKm(5D);
        ddfCompany2.setCostPerKm(BigDecimal.valueOf(1000));
        // Assign DdfCompany rule to Company, Company DDF
        DdfCompany ddfCompany3 = new DdfCompanyFactory().create(company, false, this.externalAdmin);
        ddfCompany3.setLowerVolumeLimit(51);
        ddfCompany3.setUpperVolumeLimit(100);
        ddfCompany3.setMinimumFee(BigDecimal.valueOf(2000));
        ddfCompany3.setFixedFee(BigDecimal.valueOf(2000));
        ddfCompany3.setMinimumFeeKm(5D);
        ddfCompany3.setCostPerKm(BigDecimal.valueOf(2000));

        company.setDdfCompanyList(Arrays.asList(ddfCompany1, ddfCompany2, ddfCompany3));
        return spy(company); // Return as Mockito Spy
    }
}
