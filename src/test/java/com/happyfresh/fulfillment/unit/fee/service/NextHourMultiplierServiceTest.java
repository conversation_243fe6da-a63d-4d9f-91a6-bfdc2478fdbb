package com.happyfresh.fulfillment.unit.fee.service;

import com.happyfresh.fulfillment.common.service.DistanceService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.entity.ddf.DDFMatrix;
import com.happyfresh.fulfillment.entity.ddf.NextHourMultiplierMatrix;
import com.happyfresh.fulfillment.entity.ddf.TimeBasedFeeMatrix;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.slot.bean.AppliedFee;
import com.happyfresh.fulfillment.slot.bean.DDFCalculationContext;
import com.happyfresh.fulfillment.slot.service.ddf.DDFCalculationServiceChain;
import com.happyfresh.fulfillment.slot.service.ddf.NextHourMultiplierService;
import com.happyfresh.fulfillment.slot.service.ddf.TimeBasedFeeService;
import com.happyfresh.fulfillment.repository.DDFMatrixRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.Clock;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class NextHourMultiplierServiceTest {

    private DDFCalculationServiceChain service;

    private DDFCalculationServiceChain.Builder serviceBuilder;

    private DDFCalculationContext context;

    @Mock
    private DDFMatrixRepository ddfMatrixRepository;

    @Mock
    private DistanceService distanceService;

    private List<Slot> slots;

    @Before
    public void setUp() throws InstantiationException, IllegalAccessException {
        MockitoAnnotations.initMocks(this.getClass());
        this.serviceBuilder = new DDFCalculationServiceChain.Builder();
        this.service = serviceBuilder.addServiceChain(TimeBasedFeeService.class).addServiceChain(NextHourMultiplierService.class).build(StockLocation.DDFType.ORIGINAL);

        UserFactory userFactory = new UserFactory();
        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        SlotFactory slotFactory = new SlotFactory();

        User user = userFactory.createUserData();
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, false);
        this.slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 15, Slot.Type.ONE_HOUR, user);

        ZonedDateTime slotTime = this.slots.get(0).getStartTime().atZone(ZoneId.of("UTC"))
                .withZoneSameInstant(ZoneId.of(this.slots.get(0).getStockLocation().getState().getTimeZone()));
        List<DDFMatrix> ddfMatrix = Arrays.asList(
                new TimeBasedFeeMatrix(slotTime.getDayOfWeek().getValue(), slotTime.getHour(), BigDecimal.valueOf(1000D), stockLocations.get(0)),
                new TimeBasedFeeMatrix(slotTime.getDayOfWeek().getValue(), slotTime.getHour(), BigDecimal.valueOf(1000D), stockLocations.get(1)),
                new NextHourMultiplierMatrix(BigDecimal.valueOf(0.5D), slots.get(0).getStockLocation())
        );

        Mockito.when(this.ddfMatrixRepository.findAllByStockLocationOrderByIdAsc(slots.get(0).getStockLocation())).thenReturn(ddfMatrix);

        Shipment shipment = new ShipmentFactory().createShipment(this.slots.get(0), user);
        List<Item> items = new ItemFactory().createItems(shipment, user, 1);
        shipment.setItems(items);

        this.context = new DDFCalculationContext.Builder()
                .withStockLocation(this.slots.get(0).getStockLocation())
                .withShipmentContext(shipment, distanceService, 1,false)
                .withDDFMatrixConfig(ddfMatrixRepository)
                .withClock(Clock.fixed(
                        this.slots.get(0).getStartTime().minusMinutes(30).atZone(ZoneId.of("UTC")).toInstant(),
                        ZoneId.of("UTC")
                ))
                .build();
    }

    @Test
    public void updateAppliedFeeContext() {
        AppliedFee fee = this.service.calculateFee(this.context, this.slots.get(0));

        Assert.assertEquals(0, BigDecimal.valueOf(1500).compareTo(fee.getTotal()));
        Assert.assertEquals(BigDecimal.valueOf(0.5), fee.getDetail().getNextHourMultiplier());
    }

    @Test
    public void return0IfNotExists() {
        AppliedFee fee = this.service.calculateFee(this.context, this.slots.get(2));

        Assert.assertEquals(0, BigDecimal.valueOf(1000).compareTo(fee.getTotal()));
        Assert.assertEquals(BigDecimal.valueOf(0), fee.getDetail().getNextHourMultiplier());
    }

}
