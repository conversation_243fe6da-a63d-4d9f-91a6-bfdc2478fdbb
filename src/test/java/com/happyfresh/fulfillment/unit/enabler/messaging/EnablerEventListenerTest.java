package com.happyfresh.fulfillment.unit.enabler.messaging;

import com.happyfresh.fulfillment.common.presenter.EnablerWebhookEvent;
import com.happyfresh.fulfillment.common.presenter.StratoEvent;
import com.happyfresh.fulfillment.enabler.messaging.EnablerEventListener;
import com.happyfresh.fulfillment.enabler.service.EnablerService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class EnablerEventListenerTest {

    @Mock
    private EnablerService enablerService;

    @InjectMocks
    private EnablerEventListener enablerEventListener;

    @Test
    public void listen_shouldCallHandleEvent_andPlaceOrderTPL() {
        EnablerWebhookEvent event = new EnablerWebhookEvent();
        enablerEventListener.listen(event);

        Mockito.verify(enablerService).handleEvent(event);
        Mockito.verify(enablerService).placeOrderTPL(event);
    }
}
