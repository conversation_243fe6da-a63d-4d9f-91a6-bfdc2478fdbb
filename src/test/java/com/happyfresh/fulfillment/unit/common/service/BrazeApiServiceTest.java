package com.happyfresh.fulfillment.unit.common.service;

import com.happyfresh.fulfillment.common.service.CoralogixAPIService;
import com.happyfresh.fulfillment.common.service.NotificationService;
import com.happyfresh.fulfillment.common.service.BrazeAPIService;
import com.happyfresh.fulfillment.common.service.SegmentIOService;
import com.happyfresh.fulfillment.common.util.ApplicationUtil;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressDeliveryContent;
import com.happyfresh.fulfillment.integrationTest.test.common.NoResetRequestExpectationManager;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.repository.TenantRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

@RunWith(MockitoJUnitRunner.class)
public class BrazeApiServiceTest {

    @InjectMocks
    private BrazeAPIService brazeAPIService;

    @InjectMocks
    private NotificationService notificationService;

    @Mock
    private GrabExpressDeliveryContent grabExpressDeliveryContent;

    @Mock
    private CoralogixAPIService coralogixAPIService;

    @Mock
    private TenantRepository tenantRepository;

    private User shopper;
    private User driver;
    private Tenant tenant;
    private Map<String, String> tenantPreferences;
    private String url;
    private RestTemplate restTemplate;
    private MockRestServiceServer mockServer;

    @Before
    public void setup(){
        UserFactory userFactory = new UserFactory();

        shopper = userFactory.createUserData(Role.Name.SHOPPER);
        shopper.setId(1L);
        driver = userFactory.createUserData(Role.Name.DRIVER, shopper.getTenant());
        driver.setId(2L);
        tenant = shopper.getTenant();
        tenant.setId(1L);
        tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("notification_provider", Tenant.NotificationProvider.BRAZE.toString());
        tenantPreferences.put("braze_api_key", "BRAZE_API_KEY");
        tenantPreferences.put("braze_api_url_key", "https://braze.localhost");
        tenantPreferences.put("braze_campaign_id_new_shopping_job_assignment", "CAMPAIGN_ID");
        tenant.setPreferences(tenantPreferences);

        url = tenant.getBrazeApiUrlKey() + ApplicationUtil.BRAZE_TRIGGER_CAMPAIGN_MAIN_PATH;

        restTemplate = new RestTemplate();
        brazeAPIService.setRestTemplate(restTemplate);
        Whitebox.setInternalState(notificationService, "brazeAPIService", brazeAPIService);
        mockServer = MockRestServiceServer.bindTo(restTemplate).build(new NoResetRequestExpectationManager());
    }

    @Test
    public void sendPushNotificationForNewShoppingAssignment_shouldCallBrazeAPI(){
        Mockito.when(tenantRepository.getOne(tenant.getId()))
                .thenReturn(tenant);

        mockServer.expect(ExpectedCount.once(), requestTo(url))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON));

        notificationService.sendPushNotificationForNewShoppingAssignment(shopper.getId(), tenant.getId());
        mockServer.verify();
    }

    @Test
    public void sendPushNotificationForNewShoppingAssignment_withNoKeyAndCampaignId_shouldNotCallBrazeAPI(){
        Mockito.when(tenantRepository.getOne(tenant.getId()))
                .thenReturn(tenant);

        tenantPreferences.remove("braze_api_key");
        tenantPreferences.remove("braze_campaign_id_new_shopping_job_assignment");
        tenant.setPreferences(tenantPreferences);

        mockServer.expect(ExpectedCount.never(), requestTo(url))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON));

        notificationService.sendPushNotificationForNewShoppingAssignment(shopper.getId(), tenant.getId());
        mockServer.verify();
    }

    @Test
    public void sendPushNotificationForNewDeliveryOrRangerAssignment_shouldCallBrazeAPI(){
        Mockito.when(tenantRepository.getOne(tenant.getId()))
                .thenReturn(tenant);

        tenantPreferences.put("braze_campaign_id_new_delivery_or_ranger_job_assignment","true");
        tenant.setPreferences(tenantPreferences);

        mockServer.expect(ExpectedCount.once(), requestTo(url))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON));

        notificationService.sendPushNotificationForNewDeliveryOrRangerAssignment(driver.getId(), tenant.getId());
        mockServer.verify();
    }

    @Test
    public void sendPushNotificationForNewDeliveryOrRangerAssignment_withNoKeyAndCampaignId_shouldNotCallBrazeAPI(){
        Mockito.when(tenantRepository.getOne(tenant.getId()))
                .thenReturn(tenant);

        tenantPreferences.remove("braze_api_key");
        tenant.setPreferences(tenantPreferences);

        mockServer.expect(ExpectedCount.never(), requestTo(url))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON));

        notificationService.sendPushNotificationForNewDeliveryOrRangerAssignment(shopper.getId(), tenant.getId());
        mockServer.verify();
    }

    @Test
    public void isDisableShoppingNotification_shouldReturnFalse(){
        tenant.setPreferences(tenantPreferences);

        boolean result = notificationService.isDisableShoppingNotification(tenant);
        Assert.assertFalse(result);
    }

    @Test
    public void isDisableShoppingNotification_withNoKeyAndCampaignId_shouldReturnTrue(){
        tenantPreferences.remove("braze_api_key");
        tenant.setPreferences(tenantPreferences);

        boolean result = notificationService.isDisableShoppingNotification(tenant);
        Assert.assertTrue(result);
    }
}
