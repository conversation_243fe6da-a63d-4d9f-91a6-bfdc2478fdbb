package com.happyfresh.fulfillment.unit.common.service;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.graphhopper.jsprit.core.problem.VehicleRoutingProblem;
import com.graphhopper.jsprit.core.problem.solution.route.VehicleRoute;
import com.graphhopper.jsprit.core.util.VehicleRoutingTransportCostsMatrix;
import com.happyfresh.fulfillment.common.service.CostMatrixService;
import com.happyfresh.fulfillment.common.service.VRPService;
import com.happyfresh.fulfillment.common.tracking.SlotOptimizationEventTracker;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.slot.model.BatchRouteActivity;
import com.happyfresh.fulfillment.slot.model.RouteResult;
import com.happyfresh.fulfillment.slot.model.VRPParam;
import com.happyfresh.fulfillment.slot.model.VehicleBatchTime;
import com.happyfresh.fulfillment.slot.service.SlotReservedService;
import com.happyfresh.fulfillment.slot.util.VRPParamUtil;
import com.happyfresh.fulfillment.unit.PowerMockBase;
import com.happyfresh.fulfillment.unit.helper.ResultCaptor;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.context.ApplicationContext;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@PrepareForTest(VehicleRoutingProblem.Builder.class)
public class VRPServiceTest extends PowerMockBase {

    @Mock
    private BatchRepository batchRepository;

    @Mock
    private SlotReservedService slotReservedService;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private SlotOptimizationEventTracker slotOptimizationEventTracker;

    @Mock
    private CostMatrixService costMatrixService;

    @InjectMocks
    private VRPService vrpService;

    User user;
    StockLocation stockLocation;
    Country country;
    List<StockLocation> stockLocations;
    List<Slot> slots;
    Shift shopperShift;
    Shift driverShift;
    ShipmentFactory shipmentFactory;
    ItemFactory itemFactory;

    @Before
    public void setup() {
        UserFactory userFactory = new UserFactory();
        user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        user.setId(1l);

        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);
        stockLocation = stockLocations.get(0);
        stockLocation.setId(1L);

        country = stockLocation.getState().getCountry();

        SlotFactory slotFactory = new SlotFactory();
        slots = slotFactory.createLongerDeliverySlots(stockLocations.get(0), user, 1, 4);

        ShiftFactory shiftFactory = new ShiftFactory();
        shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slots.get(0).getStartTime(), slots.get(3).getEndTime().minusHours(1), 1);
        driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slots.get(3).getEndTime(), 1);
        shopperShift.setId(1l);
        driverShift.setId(2l);
        shipmentFactory = new ShipmentFactory();
        itemFactory = new ItemFactory();
    }

    private void setupShipments(Shift shopperShift, Shift driverShift, List<Shipment> shipments, int itemCount) {
        long counter = 1l;
        for (Shipment shipment : shipments) {
            Batch shoppingBatch = shipment.getShoppingJob().get().getBatch();
            shoppingBatch.setShift(shopperShift);
            Batch deliveryBatch = shipment.getDeliveryJob().get().getBatch();
            deliveryBatch.setShift(driverShift);

            List<Item> items = itemFactory.createItems(shipment, user, itemCount);
            shipment.setItems(items);
            shipment.setId(counter);
            counter++;
        }
    }

    @Test
    public void shopperLongerSolution_shouldPoolOrder_sameSlot(){
        Shipment shipment1 = shipmentFactory.createShipmentWithBatch(slots.get(0), user, "Order1");
        Shipment shipment2 = shipmentFactory.createShipmentWithBatch(slots.get(0), user, "Order2");
        Shipment shipment3 = shipmentFactory.createShipmentWithBatch(slots.get(1), user, "Order3");
        Shipment shipment4 = shipmentFactory.createShipmentWithBatch(slots.get(1), user, "Order4");
        Shipment shipment5 = shipmentFactory.createShipmentWithBatch(slots.get(2), user, "Order5");
        List<Shipment> modifiableCompleteShipments = Lists.newArrayList(shipment1, shipment2, shipment3, shipment4, shipment5);
        setupShipments(shopperShift, driverShift, modifiableCompleteShipments, 2);

        CostMatrixService costMatrixServiceReal = new CostMatrixService();
        VehicleRoutingTransportCostsMatrix shoppingCostMatrix = costMatrixServiceReal.shoppingCostMatrixLongerDelivery(stockLocation, modifiableCompleteShipments);
        Mockito.doReturn(shoppingCostMatrix).when(costMatrixService).shoppingCostMatrixLongerDelivery(stockLocation, modifiableCompleteShipments);

        VRPParam vrpParam = VRPParamUtil.buildLongerDeliveryParam(stockLocation, slots.get(0), new ArrayList<>(), ImmutableList.of(shopperShift), modifiableCompleteShipments, country.getAvoidTollOnDelivery(), stockLocation.getCluster().getMaxTerminationTime());
        List<RouteResult> routeResults = vrpService.shoppingLongerSlotSolution(vrpParam);
        assertEquals(3, routeResults.size());
    }

    @Test
    public void shopperLongerSolution_shouldNotPool_whenShoppingExceededMaximumTime(){
        Shipment shipment1 = shipmentFactory.createShipmentWithBatch(slots.get(0), user, "Order1");
        Shipment shipment2 = shipmentFactory.createShipmentWithBatch(slots.get(0), user, "Order2");
        Shipment shipment3 = shipmentFactory.createShipmentWithBatch(slots.get(1), user, "Order3");
        Shipment shipment4 = shipmentFactory.createShipmentWithBatch(slots.get(1), user, "Order4");
        Shipment shipment5 = shipmentFactory.createShipmentWithBatch(slots.get(2), user, "Order5");
        List<Shipment> modifiableCompleteShipments = Lists.newArrayList(shipment1, shipment2, shipment3, shipment4, shipment5);
        setupShipments(shopperShift, driverShift, modifiableCompleteShipments, 10);

        CostMatrixService costMatrixServiceReal = new CostMatrixService();
        VehicleRoutingTransportCostsMatrix shoppingCostMatrix = costMatrixServiceReal.shoppingCostMatrixLongerDelivery(stockLocation, modifiableCompleteShipments);
        Mockito.doReturn(shoppingCostMatrix).when(costMatrixService).shoppingCostMatrixLongerDelivery(stockLocation, modifiableCompleteShipments);

        Assert.assertTrue((shipment1.getItems().size() * stockLocation.getShopperAveragePickingTimePerUniqItem()) > stockLocation.getMaximumShoppingTime());

        VRPParam vrpParam = VRPParamUtil.buildLongerDeliveryParam(stockLocation, slots.get(0), new ArrayList<>(), ImmutableList.of(shopperShift), modifiableCompleteShipments, country.getAvoidTollOnDelivery(), stockLocation.getCluster().getMaxTerminationTime());
        List<RouteResult> routeResults = vrpService.shoppingLongerSlotSolution(vrpParam);
        assertEquals(5, routeResults.size());
    }

    @Test
    public void shopperLongerSolution_shouldNotPoolOrder_withEnabler(){
        Shipment shipment1 = shipmentFactory.createShipmentWithBatch(slots.get(0), user, "Order1");
        Shipment shipment2 = shipmentFactory.createShipmentWithBatch(slots.get(0), user, "Order2");
        Shipment shipment3 = shipmentFactory.createShipmentWithBatch(slots.get(1), user, "Order3");
        Shipment shipment4 = shipmentFactory.createShipmentWithBatch(slots.get(1), user, "Order4");
        List<Shipment> modifiableCompleteShipments = Lists.newArrayList(shipment1, shipment2, shipment3, shipment4);
        setupShipments(shopperShift, driverShift, modifiableCompleteShipments, 2);
        stockLocation.setEnabler(StockLocation.Enabler.HYPERMART);

        CostMatrixService costMatrixServiceReal = new CostMatrixService();
        VehicleRoutingTransportCostsMatrix shoppingCostMatrix = costMatrixServiceReal.shoppingCostMatrixLongerDelivery(stockLocation, modifiableCompleteShipments);
        Mockito.doReturn(shoppingCostMatrix).when(costMatrixService).shoppingCostMatrixLongerDelivery(stockLocation, modifiableCompleteShipments);

        VRPParam vrpParam = VRPParamUtil.buildLongerDeliveryParam(stockLocation, slots.get(0), new ArrayList<>(), ImmutableList.of(shopperShift), modifiableCompleteShipments, country.getAvoidTollOnDelivery(), stockLocation.getCluster().getMaxTerminationTime());
        List<RouteResult> routeResults = vrpService.shoppingLongerSlotSolution(vrpParam);
        assertEquals(4, routeResults.size());
    }

    @Test
    public void deliveryLongerSlotSolution_crossSlot_shouldUseSlotEndTimeAsDeliveryTimeEnd() throws Exception {
        Cluster cluster = stockLocation.getCluster();
        Map<String, String> clusterPreferences = cluster.getPreferences();
        clusterPreferences.put("enable_cross_slot_optimization", "true");
        cluster.setPreferences(clusterPreferences);
        stockLocation.setCluster(cluster);

        Slot slot1 = slots.get(0);
        Shipment shipment1 = shipmentFactory.createShipmentWithBatch(slot1, user, "Order1"); shipment1.setId(1L);
        Shipment shipment2 = shipmentFactory.createShipmentWithBatch(slots.get(1), user, "Order2"); shipment2.setId(2L);
        List<Shipment> modifiableCompleteShipments = Lists.newArrayList(shipment1, shipment2);
        driverShift.setCount(1);
        setupShipments(shopperShift, driverShift, modifiableCompleteShipments, 2);

        // mock others
        VehicleRoutingTransportCostsMatrix transportCostsMatrixMock = mock(VehicleRoutingTransportCostsMatrix.class);
        when(costMatrixService.deliveryCostMatrixLongerDelivery(eq(slot1), eq(modifiableCompleteShipments), anyList(), eq(true), eq(null)))
                .thenReturn(transportCostsMatrixMock);
        when(batchRepository.findAllLatestUnmodifiableVehicleBatch(anyList(), anyList(), anyList(), any(LocalDateTime.class), anyList()))
                .thenReturn(Lists.newArrayList(new VehicleBatchTime(driverShift.getId(), 1, driverShift.getStartTime())));
        when(applicationContext.getBean(SlotReservedService.class)).thenReturn(slotReservedService);
        when(applicationContext.getBean(SlotOptimizationEventTracker.class)).thenReturn(slotOptimizationEventTracker);
        LocalDateTime now = LocalDateTime.now();
        BatchRouteActivity dummyBatchRouteActivity = new BatchRouteActivity(Double.valueOf(DateTimeUtil.localDateTimeToEpochSecond(now)), Double.valueOf(DateTimeUtil.localDateTimeToEpochSecond(now.plusMinutes(10))), new LinkedList<>());
        when(slotReservedService.getDeliveryBatchesOfActivity(any(VehicleRoute.class)))
                .thenReturn(List.of(dummyBatchRouteActivity));

        // Setup VRP builder Spy
        PowerMockito.mockStatic(VehicleRoutingProblem.Builder.class);
        VehicleRoutingProblem.Builder vrpBuilderSpy = spy(VehicleRoutingProblem.Builder.class);
        when(VehicleRoutingProblem.Builder.newInstance()).thenReturn(vrpBuilderSpy);
        ResultCaptor<VehicleRoutingProblem> resultCaptor = new ResultCaptor<>();
        when(vrpBuilderSpy.build()).thenAnswer(resultCaptor);

        // Call
        VRPParam vrpParam = VRPParamUtil.buildLongerDeliveryParam(stockLocation, slot1, new ArrayList<>(), List.of(driverShift), modifiableCompleteShipments, country.getAvoidTollOnDelivery(), cluster.getMaxTerminationTime());
        vrpParam.setOptimizedBatchIdsWithinOffset(new ArrayList<>());
        vrpParam.setCrossSlotOptimization(true);
        vrpService.deliveryLongerSlotSolution(vrpParam);

        VehicleRoutingProblem vrp = resultCaptor.getResult();
        Map<String, com.graphhopper.jsprit.core.problem.job.Job> jobs = vrp.getJobs();
        com.graphhopper.jsprit.core.problem.job.Shipment vrpShipment1 = (com.graphhopper.jsprit.core.problem.job.Shipment) jobs.get(shipment1.getComplexId());
        com.graphhopper.jsprit.core.problem.job.Shipment vrpShipment2 = (com.graphhopper.jsprit.core.problem.job.Shipment) jobs.get(shipment2.getComplexId());
        Long slotEndEpoch1 = DateTimeUtil.localDateTimeToEpochSecond(shipment1.getSlot().getEndTime());
        Long slotEndEpoch2 = DateTimeUtil.localDateTimeToEpochSecond(shipment2.getSlot().getEndTime());
        double timeWindowEndEpoch1 = vrpShipment1.getDeliveryTimeWindow().getEnd();
        double timeWindowEndEpoch2 = vrpShipment2.getDeliveryTimeWindow().getEnd();
        Assert.assertEquals(Double.valueOf(slotEndEpoch1), Double.valueOf(timeWindowEndEpoch1));
        Assert.assertEquals(Double.valueOf(slotEndEpoch2), Double.valueOf(timeWindowEndEpoch2));
    }

    @Test
    public void deliveryLongerSlotSolution_nonCrossSlot_shouldUseSlotEndTimeMinusHandoverAsDeliveryTimeEnd() throws Exception {
        int maxDeliveryHandover = 10;
        stockLocation.setMaxDeliveryHandover(maxDeliveryHandover);
        Cluster cluster = stockLocation.getCluster();
        stockLocation.setCluster(cluster);

        Slot slot1 = slots.get(0);
        Shipment shipment1 = shipmentFactory.createShipmentWithBatch(slot1, user, "Order1"); shipment1.setId(1L);
        Shipment shipment2 = shipmentFactory.createShipmentWithBatch(slots.get(1), user, "Order2"); shipment2.setId(2L);
        List<Shipment> modifiableCompleteShipments = Lists.newArrayList(shipment1, shipment2);
        driverShift.setCount(1);
        setupShipments(shopperShift, driverShift, modifiableCompleteShipments, 2);

        // mock others
        VehicleRoutingTransportCostsMatrix transportCostsMatrixMock = mock(VehicleRoutingTransportCostsMatrix.class);
        when(costMatrixService.deliveryCostMatrixLongerDelivery(eq(slot1), eq(modifiableCompleteShipments), anyList(), eq(true), eq(null)))
                .thenReturn(transportCostsMatrixMock);
        when(batchRepository.findAllLatestUnmodifiableVehicleBatch(anyList(), anyList(), anyList(), any(LocalDateTime.class), anyList()))
                .thenReturn(Lists.newArrayList(new VehicleBatchTime(driverShift.getId(), 1, driverShift.getStartTime())));
        when(applicationContext.getBean(SlotReservedService.class)).thenReturn(slotReservedService);
        when(applicationContext.getBean(SlotOptimizationEventTracker.class)).thenReturn(slotOptimizationEventTracker);
        LocalDateTime now = LocalDateTime.now();
        BatchRouteActivity dummyBatchRouteActivity = new BatchRouteActivity(Double.valueOf(DateTimeUtil.localDateTimeToEpochSecond(now)), Double.valueOf(DateTimeUtil.localDateTimeToEpochSecond(now.plusMinutes(10))), new LinkedList<>());
        when(slotReservedService.getDeliveryBatchesOfActivity(any(VehicleRoute.class)))
                .thenReturn(List.of(dummyBatchRouteActivity));

        // Setup VRP builder Spy
        PowerMockito.mockStatic(VehicleRoutingProblem.Builder.class);
        VehicleRoutingProblem.Builder vrpBuilderSpy = spy(VehicleRoutingProblem.Builder.class);
        when(VehicleRoutingProblem.Builder.newInstance()).thenReturn(vrpBuilderSpy);
        ResultCaptor<VehicleRoutingProblem> resultCaptor = new ResultCaptor<>();
        when(vrpBuilderSpy.build()).thenAnswer(resultCaptor);

        // Call
        VRPParam vrpParam = VRPParamUtil.buildLongerDeliveryParam(stockLocation, slot1, new ArrayList<>(), List.of(driverShift), modifiableCompleteShipments, country.getAvoidTollOnDelivery(), cluster.getMaxTerminationTime());
        vrpParam.setOptimizedBatchIdsWithinOffset(new ArrayList<>());
        vrpParam.setCrossSlotOptimization(false);
        vrpService.deliveryLongerSlotSolution(vrpParam);

        int deliveryHandoverInSeconds = maxDeliveryHandover * 60;
        Assert.assertEquals(deliveryHandoverInSeconds, vrpParam.getMaxDeliveryHandoverInSeconds().intValue());

        VehicleRoutingProblem vrp = resultCaptor.getResult();
        Map<String, com.graphhopper.jsprit.core.problem.job.Job> jobs = vrp.getJobs();
        com.graphhopper.jsprit.core.problem.job.Shipment vrpShipment1 = (com.graphhopper.jsprit.core.problem.job.Shipment) jobs.get(shipment1.getComplexId());
        com.graphhopper.jsprit.core.problem.job.Shipment vrpShipment2 = (com.graphhopper.jsprit.core.problem.job.Shipment) jobs.get(shipment2.getComplexId());
        long slotEndEpoch1 = DateTimeUtil.localDateTimeToEpochSecond(shipment1.getSlot().getEndTime().minusSeconds(deliveryHandoverInSeconds));
        long slotEndEpoch2 = DateTimeUtil.localDateTimeToEpochSecond(shipment2.getSlot().getEndTime().minusSeconds(deliveryHandoverInSeconds));
        long timeWindowEndEpoch1 = Double.valueOf(vrpShipment1.getDeliveryTimeWindow().getEnd()).longValue();
        long timeWindowEndEpoch2 = Double.valueOf(vrpShipment2.getDeliveryTimeWindow().getEnd()).longValue();
        Assert.assertEquals(slotEndEpoch1, timeWindowEndEpoch1);
        Assert.assertEquals(slotEndEpoch2, timeWindowEndEpoch2);
    }

    @Test
    public void deliveryLongerSlotSolutionWithCrossSlot_callDeliveryLongerSlotSolution_shouldUseSlotEndTimeAsDeliveryTimeEnd() throws Exception {
        Cluster cluster = stockLocation.getCluster();
        Map<String, String> clusterPreferences = cluster.getPreferences();
        clusterPreferences.put("enable_cross_slot_optimization", "true");
        cluster.setPreferences(clusterPreferences);
        stockLocation.setCluster(cluster);

        Slot slot1 = slots.get(0);
        Shipment shipment1 = shipmentFactory.createShipmentWithBatch(slot1, user, "Order1"); shipment1.setId(1L);
        Shipment shipment2 = shipmentFactory.createShipmentWithBatch(slots.get(1), user, "Order2"); shipment2.setId(2L);
        List<Shipment> modifiableCompleteShipments = Lists.newArrayList(shipment1, shipment2);
        driverShift.setCount(1);
        setupShipments(shopperShift, driverShift, modifiableCompleteShipments, 2);

        // mock others
        VehicleRoutingTransportCostsMatrix transportCostsMatrixMock = mock(VehicleRoutingTransportCostsMatrix.class);
        when(costMatrixService.deliveryCostMatrixLongerDelivery(eq(slot1), eq(modifiableCompleteShipments), anyList(), eq(true), eq(null)))
                .thenReturn(transportCostsMatrixMock);
        when(batchRepository.findAllLatestUnmodifiableVehicleBatch(anyList(), anyList(), anyList(), any(LocalDateTime.class), anyList()))
                .thenReturn(Lists.newArrayList(new VehicleBatchTime(driverShift.getId(), 1, driverShift.getStartTime())));
        when(applicationContext.getBean(SlotReservedService.class)).thenReturn(slotReservedService);
        when(applicationContext.getBean(SlotOptimizationEventTracker.class)).thenReturn(slotOptimizationEventTracker);
        LocalDateTime now = LocalDateTime.now();
        BatchRouteActivity dummyBatchRouteActivity = new BatchRouteActivity(Double.valueOf(DateTimeUtil.localDateTimeToEpochSecond(now)), Double.valueOf(DateTimeUtil.localDateTimeToEpochSecond(now.plusMinutes(10))), new LinkedList<>());
        when(slotReservedService.getDeliveryBatchesOfActivity(any(VehicleRoute.class)))
                .thenReturn(List.of(dummyBatchRouteActivity));

        // Setup VRP builder Spy
        PowerMockito.mockStatic(VehicleRoutingProblem.Builder.class);
        VehicleRoutingProblem.Builder vrpBuilderSpy = spy(VehicleRoutingProblem.Builder.class);
        when(VehicleRoutingProblem.Builder.newInstance()).thenReturn(vrpBuilderSpy);
        ResultCaptor<VehicleRoutingProblem> resultCaptor = new ResultCaptor<>();
        when(vrpBuilderSpy.build()).thenAnswer(resultCaptor);

        VRPParam vrpParam = VRPParamUtil.buildLongerDeliveryParam(stockLocation, slot1, new ArrayList<>(), List.of(driverShift), modifiableCompleteShipments, country.getAvoidTollOnDelivery(), cluster.getMaxTerminationTime());
        vrpParam.setOptimizedBatchIdsWithinOffset(new ArrayList<>());
        vrpService.deliveryLongerSlotSolutionWithCrossSlot(vrpParam);

        VehicleRoutingProblem vrp = resultCaptor.getResult();
        Map<String, com.graphhopper.jsprit.core.problem.job.Job> jobs = vrp.getJobs();
        com.graphhopper.jsprit.core.problem.job.Shipment vrpShipment1 = (com.graphhopper.jsprit.core.problem.job.Shipment) jobs.get(shipment1.getComplexId());
        com.graphhopper.jsprit.core.problem.job.Shipment vrpShipment2 = (com.graphhopper.jsprit.core.problem.job.Shipment) jobs.get(shipment2.getComplexId());
        Long slotEndEpoch1 = DateTimeUtil.localDateTimeToEpochSecond(shipment1.getSlot().getEndTime());
        Long slotEndEpoch2 = DateTimeUtil.localDateTimeToEpochSecond(shipment2.getSlot().getEndTime());
        double timeWindowEndEpoch1 = vrpShipment1.getDeliveryTimeWindow().getEnd();
        double timeWindowEndEpoch2 = vrpShipment2.getDeliveryTimeWindow().getEnd();
        Assert.assertEquals(Double.valueOf(slotEndEpoch1), Double.valueOf(timeWindowEndEpoch1));
        Assert.assertEquals(Double.valueOf(slotEndEpoch2), Double.valueOf(timeWindowEndEpoch2));
    }

    @Test
    public void deliveryLongerSlotSolutionWithCrossSlot_crossSlotEnabled_shouldTrack() throws Exception {
        Cluster cluster = stockLocation.getCluster();
        Map<String, String> clusterPreferences = cluster.getPreferences();
        clusterPreferences.put("enable_cross_slot_optimization", "true");
        cluster.setPreferences(clusterPreferences);
        stockLocation.setCluster(cluster);

        Slot slot1 = slots.get(0);
        Shipment shipment1 = shipmentFactory.createShipmentWithBatch(slot1, user, "Order1"); shipment1.setId(1L);
        Shipment shipment2 = shipmentFactory.createShipmentWithBatch(slots.get(1), user, "Order2"); shipment2.setId(2L);
        List<Shipment> modifiableCompleteShipments = Lists.newArrayList(shipment1, shipment2);
        driverShift.setCount(1);
        setupShipments(shopperShift, driverShift, modifiableCompleteShipments, 2);

        // mock others
        VehicleRoutingTransportCostsMatrix transportCostsMatrixMock = mock(VehicleRoutingTransportCostsMatrix.class);
        when(costMatrixService.deliveryCostMatrixLongerDelivery(eq(slot1), eq(modifiableCompleteShipments), anyList(), eq(true), eq(null)))
                .thenReturn(transportCostsMatrixMock);
        when(batchRepository.findAllLatestUnmodifiableVehicleBatch(anyList(), anyList(), anyList(), any(LocalDateTime.class), anyList()))
                .thenReturn(Lists.newArrayList(new VehicleBatchTime(driverShift.getId(), 1, driverShift.getStartTime())));
        when(applicationContext.getBean(SlotReservedService.class)).thenReturn(slotReservedService);
        when(applicationContext.getBean(SlotOptimizationEventTracker.class)).thenReturn(slotOptimizationEventTracker);
        LocalDateTime now = LocalDateTime.now();
        BatchRouteActivity dummyBatchRouteActivity = new BatchRouteActivity(Double.valueOf(DateTimeUtil.localDateTimeToEpochSecond(now)), Double.valueOf(DateTimeUtil.localDateTimeToEpochSecond(now.plusMinutes(10))), new LinkedList<>());
        when(slotReservedService.getDeliveryBatchesOfActivity(any(VehicleRoute.class)))
                .thenReturn(List.of(dummyBatchRouteActivity));

        VRPParam vrpParam = VRPParamUtil.buildLongerDeliveryParam(stockLocation, slot1, new ArrayList<>(), List.of(driverShift), modifiableCompleteShipments, country.getAvoidTollOnDelivery(), cluster.getMaxTerminationTime());
        vrpParam.setOptimizedBatchIdsWithinOffset(new ArrayList<>());
        vrpService.deliveryLongerSlotSolutionWithCrossSlot(vrpParam);

        verify(slotOptimizationEventTracker, times(1)).track();
    }

    @Test
    public void deliveryLongerSlotSolutionWithCrossSlot_crossSlotDisabled_shouldNotCall_deliveryLongerSlotSolution() throws Exception {
        Cluster cluster = stockLocation.getCluster();
        Map<String, String> clusterPreferences = cluster.getPreferences();
        clusterPreferences.put("enable_cross_slot_optimization", "false");
        cluster.setPreferences(clusterPreferences);
        stockLocation.setCluster(cluster);

        Slot slot1 = slots.get(0);
        Shipment shipment1 = shipmentFactory.createShipmentWithBatch(slot1, user, "Order1"); shipment1.setId(1L);
        Shipment shipment2 = shipmentFactory.createShipmentWithBatch(slots.get(1), user, "Order2"); shipment2.setId(2L);
        List<Shipment> modifiableCompleteShipments = Lists.newArrayList(shipment1, shipment2);
        driverShift.setCount(1);
        setupShipments(shopperShift, driverShift, modifiableCompleteShipments, 2);

        VRPParam vrpParam = VRPParamUtil.buildLongerDeliveryParam(stockLocation, slot1, new ArrayList<>(), List.of(driverShift), modifiableCompleteShipments, country.getAvoidTollOnDelivery(), cluster.getMaxTerminationTime());
        vrpParam.setOptimizedBatchIdsWithinOffset(new ArrayList<>());
        vrpService.deliveryLongerSlotSolutionWithCrossSlot(vrpParam);

        verify(slotOptimizationEventTracker, never()).track();
    }

}
