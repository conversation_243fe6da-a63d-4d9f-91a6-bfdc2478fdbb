package com.happyfresh.fulfillment.unit.common.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaMessage;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaTopicConfig;
import com.happyfresh.fulfillment.common.service.CoralogixAPIService;
import com.happyfresh.fulfillment.common.service.RadarService;
import org.jobrunr.jobs.lambdas.JobLambda;
import org.jobrunr.scheduling.JobScheduler;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Map;

import static java.nio.file.Files.readAllBytes;
import static java.nio.file.Paths.get;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RadarServiceTest {

    @InjectMocks
    private RadarService radarService;

    @Mock
    private KafkaMessage kafkaMessage;

    @MockBean
    JobScheduler jobScheduler;

    @Mock
    private Logger logger;

    @Mock
    private CoralogixAPIService coralogixAPIService;

    @Mock
    private ApplicationContext applicationContext;

    @Before
    public void setup() throws Exception {
        injectLogger();

        Object mapper = new ObjectMapper();
        ReflectionTestUtils.setField(radarService, "mapper", mapper, ObjectMapper.class);

        doNothing().when(coralogixAPIService).sendLog(any(), any(), any(), any(), any(), (Map) any());
        doReturn(coralogixAPIService).when(applicationContext).getBean(CoralogixAPIService.class);
    }

    private void injectLogger() throws NoSuchFieldException, IllegalAccessException {
        Field loggerField = RadarService.class.getDeclaredField("logger");
        loggerField.setAccessible(true);

        Field modifiersField = loggerField.getClass().getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(loggerField, loggerField.getModifiers() & ~Modifier.FINAL);

        loggerField.set(radarService, logger);
    }

    @Test
    public void syncData_shouldScheduleJobRunrJob() {
        String body = "test body";
        radarService.syncData(body);
        
        // Verify JobRunr scheduling
        verify(jobScheduler).enqueue((JobLambda) any());
    }

    @Test
    public void processData_whenEnterGeofence_shouldTrackToSegment() throws IOException {
        String body = new String(readAllBytes(get("src", "test", "resources", "fixtures", "radar_dummy_webhook_supermarket_enter_geofence_payload.json")));
        /*Mockito.when(shipmentRepository.existsShipmentByNumberAndOngoingDeliveryBatchAndUser(anyString(), anyLong())).thenReturn(true);*/
        radarService.processData(body);
        verify(coralogixAPIService, times(2)).sendLog(any(), any(), any(), any(), any(), (Map) any());
    }

    @Test
    public void processData_whenExitGeofence_shouldTrackToSegment() throws IOException {
        String body = new String(readAllBytes(get("src", "test", "resources", "fixtures", "radar_dummy_webhook_supermarket_exit_geofence_payload.json")));
        radarService.processData(body);
        verify(coralogixAPIService, times(2)).sendLog(any(), any(), any(), any(), any(), (Map) any());
    }

    @Test
    public void processData_whenWarehouseTag_shouldPublishKafka() throws IOException {
        String body = new String(readAllBytes(get("src", "test", "resources", "fixtures", "radar_dummy_webhook_warehouse_enter_geofence_payload.json")));
        radarService.processData(body);
        verify(kafkaMessage, atLeastOnce()).publish(eq(KafkaTopicConfig.RADAR_EVENT_PROCESSING_TOPIC), anyString(), anyString());
        verify(coralogixAPIService, times(2)).sendLog(any(), any(), any(), any(), any(), (Map) any());
    }

    @Test
    public void processData_whenEventNotTrackToSegment_shouldDoNothing() throws IOException {
        String body = new String(readAllBytes(get("src", "test", "resources", "fixtures", "radar_dummy_webhook_unknown_event_payload.json")));
        radarService.processData(body);
        verify(kafkaMessage, never()).publish(eq(KafkaTopicConfig.RADAR_EVENT_PROCESSING_TOPIC), anyString(), anyString());
        verify(coralogixAPIService, never()).sendLog(any(), any(), any(), any(), any(), (Map) any());
    }

    @Test
    public void processData_whenException_shouldLogError() throws IOException {
        String body = new String(readAllBytes(get("src", "test", "resources", "fixtures", "radar_dummy_webhook_warehouse_enter_geofence_payload.json")));
        Mockito.doThrow(RuntimeException.class).when(kafkaMessage).publish(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
        radarService.processData(body);
        verify(coralogixAPIService, never()).sendLog(any(), any(), any(), any(), any(), (Map) any());
        Mockito.verify(logger, Mockito.atLeastOnce()).error(anyString(), (Throwable) any());
    }

    @Test
    public void processData_whenTripWithoutDestinationGeofenceTag_shouldNotTrackToSegment() throws IOException {
        String body = new String(readAllBytes(get("src", "test", "resources", "fixtures", "radar_dummy_webhook_trip_without_destinationGeofenceTag_payload.json")));
        radarService.processData(body);
        verify(coralogixAPIService, times(1)).sendLog(any(), any(), any(), any(), any(), (Map) any());
        Mockito.verify(logger, Mockito.never()).error(anyString(), (Throwable) any());
    }
}
