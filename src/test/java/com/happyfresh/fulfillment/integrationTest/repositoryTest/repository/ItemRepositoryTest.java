package com.happyfresh.fulfillment.integrationTest.repositoryTest.repository;

import com.happyfresh.fulfillment.entity.Category;
import com.happyfresh.fulfillment.entity.Item;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.integrationTest.repositoryTest.BaseRepositoryTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.CategoryFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.ItemFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.ShipmentFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.repository.ItemRepository;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;


public class ItemRepositoryTest extends BaseRepositoryTest {

    @Autowired
    ShipmentFactory shipmentFactory;

    @Autowired
    UserFactory userFactory;

    @Autowired
    ItemFactory itemFactory;

    @Autowired
    CategoryFactory categoryFactory;

    @Autowired
    ItemRepository itemRepository;

    private Item createItem() {
        User user = userFactory.createUserData();
        Shipment shipment = shipmentFactory.createShipment(user);
        Category category = categoryFactory.createCategory(user);
        return itemFactory.createItem(category, shipment, user);
    }

    @Test
    public void testCountFoundOrOosByShipment(){
        Item item = createItem();
        item.setFoundQty(1);
        item.setOosQty(1);
        itemFactory.save(item);

        int count = itemRepository.countFoundOrOosByShipment(item.getShipment());
        Assert.assertEquals(count, 1);
    }

    @Test
    public void testCountFoundOrOosByShipmentNotFound(){
        Item item = createItem();
        item.setFoundQty(0);
        item.setOosQty(0);
        itemFactory.save(item);

        int count = itemRepository.countFoundOrOosByShipment(item.getShipment());
        Assert.assertEquals(count, 0);
    }

    @Test
    public void testCountFoundOrOosByShipments() {
        Item item = createItem();
        item.setFoundQty(1);
        item.setOosQty(1);
        itemFactory.save(item);

        int count = itemRepository.countFoundOrOosByShipments(Arrays.asList(item.getShipment()));
        Assert.assertEquals(count, 1);
    }

    @Test
    public void testCountFoundOrOosByShipmentsNotFound() {
        Item item = createItem();
        item.setFoundQty(0);
        item.setOosQty(0);
        itemFactory.save(item);

        int count = itemRepository.countFoundOrOosByShipments(Arrays.asList(item.getShipment()));
        Assert.assertEquals(count, 0);
    }

    @Test
    public void testDeleteAllReplacementByShipment() {
        Item item = createItem();
        Item itemReplacement = createItem();
        item.setReplacedItem(itemReplacement);
        itemFactory.save(item);
        Long shipmentId = item.getShipment().getId();

        itemRepository.deleteAllReplacementByShipment(item.getShipment());
        List<Item> items = itemRepository.findAllByShipmentId(shipmentId);
        Assert.assertTrue(items.isEmpty());
    }

    @Test
    public void testDeleteAllReplacementByShipmentNoDeletion() {
        Item item = createItem();
        item.setReplacedItem(null);
        itemFactory.save(item);
        Long shipmentId = item.getShipment().getId();

        /* test if replacement item is null */
        itemRepository.deleteAllReplacementByShipment(item.getShipment());
        List<Item> items = itemRepository.findAllByShipmentId(shipmentId);
        Assert.assertFalse(items.isEmpty());

        /* test if shipment is not equal */
        Item itemReplacement = createItem();
        item.setReplacedItem(itemReplacement);
        itemFactory.save(item);

        User user = userFactory.createUserData();
        Shipment shipment = shipmentFactory.createShipment(user);
        itemRepository.deleteAllReplacementByShipment(shipment);
        items = itemRepository.findAllByShipmentId(shipmentId);
        Assert.assertFalse(items.isEmpty());

    }

    @Test
    public void testCountRequestedByShipments() {
        Item item = createItem();
        item.setReplacedItem(null);
        itemFactory.save(item);

        int count = itemRepository.countRequestedByShipments(Arrays.asList(item.getShipment()));
        Assert.assertTrue(count > 0);
    }

    @Test
    public void testCountRequestedByShipmentsNotFound() {
        Item item = createItem();
        Item itemReplacement = createItem();
        item.setReplacedItem(itemReplacement);
        itemFactory.save(item);

        int count = itemRepository.countRequestedByShipments(Arrays.asList(item.getShipment()));
        Assert.assertEquals(0, count);
    }

    @Test
    public void testCountByShipment() {
        Item item = createItem();
        item.setReplacedItem(null);
        itemFactory.save(item);

        int count = itemRepository.countByShipment(item.getShipment());
        Assert.assertTrue(count > 0);
    }

    @Test
    public void testCountByShipmentNotFound() {
        Item item = createItem();
        Item itemReplacement = createItem();
        item.setReplacedItem(itemReplacement);
        itemFactory.save(item);

        int count = itemRepository.countByShipment(item.getShipment());
        Assert.assertEquals(0, count);
    }

    @Test
    public void testQuantityBeforeShopperStarted(){
        Item item = createItem();
        item.setRequestedBeforeShopperStartedQty(0);
        itemFactory.save(item);

        int count = itemRepository.countAdditionalItemsByShipment(item.getShipment());
        Assert.assertEquals(count, 1);
    }
}
