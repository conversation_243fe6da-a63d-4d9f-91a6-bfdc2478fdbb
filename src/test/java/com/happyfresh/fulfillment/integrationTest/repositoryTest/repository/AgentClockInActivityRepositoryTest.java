package com.happyfresh.fulfillment.integrationTest.repositoryTest.repository;

import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.repositoryTest.BaseRepositoryTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.AgentFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.ShiftFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.repository.AgentClockInActivityRepository;
import com.happyfresh.fulfillment.repository.ShiftRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

public class AgentClockInActivityRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private AgentClockInActivityRepository agentClockInActivityRepository;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private AgentFactory agentFactory;

    @Autowired
    private ShiftRepository shiftRepository;
    private Agent driverAgent;
    private Shift driverShift;

    @Before
    public void setup() {
        databaseCleanUpService.truncate(Arrays.asList("hff_user", "hff_stock_location", "hff_shift", "hff_agent", "hff_agent_clock_in_activity", "hff_agent_activity"));
        User admin = userFactory.createUserData(Role.Name.ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        StockLocation stockLocation = stockLocations.get(0);

        List<Shift> driverShifts = shiftFactory.createDriverShifts(stockLocation,
                LocalDateTime.now(),
                2,
                8,
                4,
                admin
        );

        driverShift = shiftRepository.findAll().get(0);

        User driver = userFactory.createUserData(Role.Name.DRIVER, admin.getTenant());
        driverAgent = agentFactory.createAgent(driver, stockLocation, Agent.State.WORKING);
    }

    @Test
    public void testSaveDataWithShift() {

        createAgentClockInActivity(driverAgent, driverShift, 0);

        List<AgentClockInActivity> savedAgentClockInActivities = agentClockInActivityRepository.findAll();
        Assert.assertEquals(1, savedAgentClockInActivities.size());

        AgentClockInActivity agentClockInActivity = savedAgentClockInActivities.get(0);
        Assert.assertEquals(driverShift.getId(), agentClockInActivity.getShift().getId());
        Assert.assertEquals(Integer.valueOf(0), agentClockInActivity.getDuration());

    }

    @Test
    public void testSaveDataWithoutShift() {

        createAgentClockInActivity(driverAgent, null, 8);

        List<AgentClockInActivity> savedAgentClockInActivities = agentClockInActivityRepository.findAll();
        Assert.assertEquals(1, savedAgentClockInActivities.size());

        AgentClockInActivity agentClockInActivity = savedAgentClockInActivities.get(0);
        Assert.assertNull(agentClockInActivity.getShift());
        Assert.assertEquals(Integer.valueOf(8), agentClockInActivity.getDuration());

    }

    private void createAgentClockInActivity(Agent agent, Shift shift, int duration) {
        AgentClockInActivity agentClockInActivity = new AgentClockInActivity();
        agentClockInActivity.setUserId(agent.getUser().getId());
        agentClockInActivity.setAgentId(agent.getId());
        agentClockInActivity.setState(agent.getState());
        agentClockInActivity.setStockLocationId(agent.getStockLocation().getId());
        agentClockInActivity.setLat(agent.getLat());
        agentClockInActivity.setLon(agent.getLon());
        agentClockInActivity.setAccuracy(agent.getAccuracy());
        agentClockInActivity.setCreatedBy(agent.getCreatedBy());
        agentClockInActivity.setUpdatedBy(agent.getUpdatedBy());
        agentClockInActivity.setTenant(agent.getTenant());
        agentClockInActivity.setDuration(duration);
        if(shift != null)
            agentClockInActivity.setShift(shift);
        agentClockInActivityRepository.save(agentClockInActivity);
    }
}
