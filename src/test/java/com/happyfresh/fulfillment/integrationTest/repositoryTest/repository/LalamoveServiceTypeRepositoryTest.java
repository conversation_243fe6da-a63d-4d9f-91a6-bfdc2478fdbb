package com.happyfresh.fulfillment.integrationTest.repositoryTest.repository;

import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.repositoryTest.BaseRepositoryTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.CountryFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.LalamoveServiceTypeFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.lalamove.model.LalamoveServiceTypeEnum;
import com.happyfresh.fulfillment.repository.LalamoveServiceTypeRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class LalamoveServiceTypeRepositoryTest extends BaseRepositoryTest {

    @Autowired
    LalamoveServiceTypeRepository lalamoveServiceTypeRepository;

    @Autowired
    LalamoveServiceTypeFactory factory;

    @Autowired
    UserFactory userFactory;

    @Autowired
    private CountryFactory countryFactory;

    @Autowired
    StockLocationFactory stockLocationFactory;
    private StockLocation stockLocation;
    private Country country;
    private User admin;

    @Before
    public void setup() {
        databaseCleanUpService.truncate(List.of("hff_lalamove_service_type"));

        admin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN);
        this.stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin).get(0);
        country = stockLocation.getState().getCountry();
    }

    @Test
    public void saveAndRetrieve() {
        factory.createMotorcycleServiceType(country, admin);
        List<LalamoveServiceType> serviceTypes = lalamoveServiceTypeRepository.findAll();

        Assert.assertEquals(1, serviceTypes.size());
    }

    @Test
    public void findAllActiveV3ByCityCode_shouldReturnOnlyActiveAndVersion3() {
        LalamoveServiceType serviceType1 = factory.createMotorcycleServiceType(country, admin);
        serviceType1.setCityCode("JKT");
        serviceType1.setActive(true);
        serviceType1.setVersion("3");
        serviceType1.setAvailableSpecialRequests(new String[]{"COD", "DOOR_TO_DOOR", "EXTRA_HELPER"});
        serviceType1.setActiveSpecialRequests(new String[]{"DOOR_TO_DOOR"});
        LalamoveServiceType serviceType2 = factory.createVanServiceType(country, admin);
        serviceType2.setCityCode("JKT");
        serviceType2.setActive(false);
        serviceType2.setVersion("3");
        serviceType2.setAvailableSpecialRequests(new String[]{"COD","DOOR_TO_DOOR", "EXTRA_HELPER"});
        serviceType2.setActiveSpecialRequests(new String[]{"DOOR_TO_DOOR"});
        LalamoveServiceType serviceType3 = factory.createVanServiceType(country, admin);
        serviceType3.setCityCode("JKT");
        serviceType3.setActive(true);
        serviceType3.setVersion(null);
        serviceType3.setAvailableSpecialRequests(new String[]{"COD","DOOR_TO_DOOR", "EXTRA_HELPER"});
        serviceType3.setActiveSpecialRequests(new String[]{"DOOR_TO_DOOR"});
        lalamoveServiceTypeRepository.saveAll(List.of(serviceType1, serviceType2, serviceType3));

        List<LalamoveServiceType> serviceTypes = lalamoveServiceTypeRepository.findAllActiveV3ByCityCode("JKT");

        Assert.assertEquals(1, serviceTypes.size());
        Assert.assertArrayEquals(new String[]{"COD", "DOOR_TO_DOOR", "EXTRA_HELPER"}, serviceTypes.get(0).getAvailableSpecialRequests());
        Assert.assertArrayEquals(new String[]{"DOOR_TO_DOOR"}, serviceTypes.get(0).getActiveSpecialRequests());
    }

    @Test
    public void findAllActiveV3ByCountry_shouldReturnOnlyVersion3inOneCountry() {
        LalamoveServiceType serviceType1 = factory.createMotorcycleServiceType(country, admin);
        serviceType1.setCityCode("JKT");
        serviceType1.setActive(true);
        serviceType1.setVersion("3");
        serviceType1.setAvailableSpecialRequests(new String[]{"COD"});
        serviceType1.setActiveSpecialRequests(new String[]{"COD"});
        LalamoveServiceType serviceType2 = factory.createVanServiceType(country, admin);
        serviceType2.setCityCode("SUB");
        serviceType2.setActive(true);
        serviceType2.setVersion("3");
        serviceType2.setAvailableSpecialRequests(new String[]{"COD"});
        serviceType2.setActiveSpecialRequests(new String[]{"COD"});

        Country malaysia = countryFactory.createCountry(admin, "Malaysia", "MY", true);
        LalamoveServiceType serviceTypeMY = factory.createVanServiceType(malaysia, admin);
        serviceTypeMY.setCityCode("KL");
        serviceTypeMY.setActive(true);
        serviceTypeMY.setVersion(null);
        serviceTypeMY.setAvailableSpecialRequests(new String[]{"COD"});
        serviceTypeMY.setActiveSpecialRequests(new String[]{"COD"});
        lalamoveServiceTypeRepository.saveAll(List.of(serviceType1, serviceType2, serviceTypeMY));

        List<LalamoveServiceType> serviceTypes = lalamoveServiceTypeRepository.findAllV3ByCountry(country);

        Assert.assertEquals(2, serviceTypes.size());
        Assert.assertEquals(country.getId(), serviceTypes.get(0).getCountry().getId());
        Assert.assertEquals(country.getId(), serviceTypes.get(1).getCountry().getId());
    }

    @Test
    public void findAllV2ByKeyAndCountryId_shouldOnlyReturnVersion2OrNullVersionByKeyAndCountry() {
        LalamoveServiceType serviceType1 = factory.createMotorcycleServiceType(country, admin);
        serviceType1.setVersion("2");

        LalamoveServiceType serviceType2 = factory.createVanServiceType(country, admin);
        serviceType2.setVersion(null);

        LalamoveServiceType serviceType3 = factory.createVanServiceType(country, admin);
        serviceType3.setCityCode("JKT");
        serviceType3.setActive(true);
        serviceType3.setVersion("3");
        serviceType3.setAvailableSpecialRequests(new String[]{"COD","DOOR_TO_DOOR", "EXTRA_HELPER"});
        serviceType3.setActiveSpecialRequests(new String[]{"DOOR_TO_DOOR"});

        LalamoveServiceType serviceType4 = factory.createMotorcycleServiceType(country, admin);
        serviceType4.setCityCode("JKT");
        serviceType4.setActive(true);
        serviceType4.setVersion("3");
        serviceType4.setAvailableSpecialRequests(new String[]{"COD","DOOR_TO_DOOR", "EXTRA_HELPER"});
        serviceType4.setActiveSpecialRequests(new String[]{"DOOR_TO_DOOR"});

        lalamoveServiceTypeRepository.saveAll(List.of(serviceType1, serviceType2, serviceType3, serviceType4));

        LalamoveServiceType serviceType = lalamoveServiceTypeRepository.findV2ByKeyAndCountryId(LalamoveServiceTypeEnum.MOTORCYCLE.toString(), country.getId());
        Assert.assertEquals("MOTORCYCLE", serviceType.getKey());
        Assert.assertEquals("2", serviceType.getVersion());

        serviceType = lalamoveServiceTypeRepository.findV2ByKeyAndCountryId(LalamoveServiceTypeEnum.VAN.toString(), country.getId());
        Assert.assertEquals("VAN", serviceType.getKey());
        Assert.assertNull(serviceType.getVersion());
    }

    @Test
    public void findV3ByKeyAndCityCode_shouldOnlyReturnVersion3() {
        LalamoveServiceType serviceType1 = factory.createMotorcycleServiceType(country, admin);
        serviceType1.setVersion("2");

        LalamoveServiceType serviceType2 = factory.createVanServiceType(country, admin);
        serviceType2.setVersion(null);

        LalamoveServiceType serviceType3 = factory.createVanServiceType(country, admin);
        serviceType3.setCityCode("JKT");
        serviceType3.setActive(true);
        serviceType3.setVersion("3");
        serviceType3.setAvailableSpecialRequests(new String[]{"COD","DOOR_TO_DOOR", "EXTRA_HELPER"});
        serviceType3.setActiveSpecialRequests(new String[]{"DOOR_TO_DOOR"});

        LalamoveServiceType serviceType4 = factory.createMotorcycleServiceType(country, admin);
        serviceType4.setCityCode("JKT");
        serviceType4.setActive(true);
        serviceType4.setVersion("3");
        serviceType4.setAvailableSpecialRequests(new String[]{"COD","DOOR_TO_DOOR", "EXTRA_HELPER"});
        serviceType4.setActiveSpecialRequests(new String[]{"DOOR_TO_DOOR"});

        lalamoveServiceTypeRepository.saveAll(List.of(serviceType1, serviceType2, serviceType3, serviceType4));

        LalamoveServiceType serviceType = lalamoveServiceTypeRepository.findV3ByKeyAndCityCode(LalamoveServiceTypeEnum.MOTORCYCLE.toString(), "JKT");
        Assert.assertEquals("MOTORCYCLE", serviceType.getKey());
        Assert.assertEquals("3", serviceType.getVersion());
    }

    @Test
    public void findFirstV2ByCountryIdOrderByMaxWeightDesc_shouldReturnV2ServiceTypeWithBiggestMaxWeight() {
        LalamoveServiceType serviceType1 = factory.createMotorcycleServiceType(country, admin); // max weight: 20.0
        LalamoveServiceType serviceType2 = factory.createTruck175ServiceType(country, admin); // max weight: 800.0
        serviceType2.setVersion("3");
        LalamoveServiceType serviceType3 = factory.createMPVServiceType(country, admin); // max weight: 350.0


        lalamoveServiceTypeRepository.saveAll(List.of(serviceType1, serviceType2, serviceType3));

        LalamoveServiceType serviceType = lalamoveServiceTypeRepository.findFirstV2ByCountryIdOrderByMaxWeightDesc(country.getId());
        Assert.assertEquals("MPV", serviceType.getKey());
        Assert.assertNull(serviceType.getVersion());

    }

    @Test
    public void findAllV2ByCountryIdOrderByMaxWeightAsc_shouldOnlyReturnAllV2ServiceTypeOrderByMaxWeightAsc() {
        factory.createMotorcycleServiceType(country, admin); // max weight: 20.0
        factory.createTruck175ServiceType(country, admin); // max weight: 800.0
        LalamoveServiceType serviceType1 = factory.createMPVServiceType(country, admin); // max weight: 350.0
        serviceType1.setVersion("3");
        factory.createVanServiceType(country, admin); // max weight: 600.0


        lalamoveServiceTypeRepository.saveAll(List.of(serviceType1));

        List<LalamoveServiceType> serviceTypes = lalamoveServiceTypeRepository.findAllV2ByCountryIdOrderByMaxWeightAsc(country.getId());
        Assert.assertEquals(3, serviceTypes.size());
        Assert.assertEquals("MOTORCYCLE",serviceTypes.get(0).getKey());
        Assert.assertEquals("VAN",serviceTypes.get(1).getKey());
        Assert.assertEquals("TRUCK175",serviceTypes.get(2).getKey());

    }

    @Test
    public void findFirstV3ByCityCodeOrderByMaxWeightDesc_shouldReturnV3ServiceTypeWithBiggestMaxWeight() {
        LalamoveServiceType serviceType1 = factory.createMotorcycleServiceType(country, admin); // max weight: 20.0
        serviceType1.setVersion("3");
        serviceType1.setActive(true);
        serviceType1.setCityCode("JKT");
        LalamoveServiceType serviceType2 = factory.createTruck175ServiceType(country, admin); // max weight: 800.0
        LalamoveServiceType serviceType3 = factory.createMPVServiceType(country, admin); // max weight: 350.0


        lalamoveServiceTypeRepository.saveAll(List.of(serviceType1, serviceType2, serviceType3));

        LalamoveServiceType serviceType = lalamoveServiceTypeRepository.findFirstV3ByCityCodeOrderByMaxWeightDesc("JKT");
        Assert.assertEquals("MOTORCYCLE", serviceType.getKey());
        Assert.assertEquals("3", serviceType.getVersion());

    }

}
