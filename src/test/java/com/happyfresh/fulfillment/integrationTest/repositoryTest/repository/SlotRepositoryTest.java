package com.happyfresh.fulfillment.integrationTest.repositoryTest.repository;

import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.repositoryTest.BaseRepositoryTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.SlotRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;

public class SlotRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private SlotRepository slotRepository;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private BatchFactory batchFactory;
    private List<Slot> slots;
    private User admin;
    private Shift dShift;
    private Shift sShift;

    @Before
    public void setup() {
        admin = userFactory.createUserData(Role.Name.ADMIN);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin, StockLocation.Type.ORIGINAL);
        StockLocation stockLocation = stockLocations.get(0);

        User driver = userFactory.createUserData(Role.Name.DRIVER, admin.getTenant());
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        dShift = shiftFactory.createDriverShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, admin).get(0);
        sShift = shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, admin).get(0);

        slots = slotFactory.createLongerDeliverySlots(stockLocation, admin, 1, 4, LocalDateTime.now());
        slots.sort(Comparator.comparing(Slot::getEndTime).reversed());

    }

    @Test
    public void test_findLatestSlotByDriverShiftId() {
        Shipment shipment1 = shipmentFactory.createShipment(slots.get(0), admin);
        Batch dBatch1 = batchFactory.createBatch(admin, shipment1, slots.get(0), Batch.Type.DELIVERY);
        dBatch1.setShift(dShift);
        Batch sBatch1 = batchFactory.createBatch(admin, shipment1, slots.get(0), Batch.Type.SHOPPING);
        sBatch1.setShift(sShift);

        Shipment shipment2 = shipmentFactory.createShipment(slots.get(1), admin);
        Batch dBatch2 = batchFactory.createBatch(admin, shipment2, slots.get(1), Batch.Type.DELIVERY);
        dBatch2.setShift(dShift);
        Batch sBatch2 = batchFactory.createBatch(admin, shipment2, slots.get(1), Batch.Type.SHOPPING);
        sBatch2.setShift(sShift);

        Shipment shipment3 = shipmentFactory.createShipment(slots.get(2), admin);
        Batch dBatch3 = batchFactory.createBatch(admin, shipment3, slots.get(2), Batch.Type.DELIVERY);
        dBatch3.setShift(dShift);
        Batch sBatch3 = batchFactory.createBatch(admin, shipment3, slots.get(2), Batch.Type.SHOPPING);
        sBatch3.setShift(sShift);

        Shipment shipment4 = shipmentFactory.createShipment(slots.get(3), admin);
        Batch dBatch4 = batchFactory.createBatch(admin, shipment4, slots.get(3), Batch.Type.DELIVERY);
        dBatch4.setShift(dShift);
        Batch sBatch4 = batchFactory.createBatch(admin, shipment4, slots.get(3), Batch.Type.SHOPPING);
        sBatch4.setShift(sShift);

        batchRepository.saveAll(List.of(dBatch1, dBatch2, dBatch3, dBatch4, sBatch1, sBatch2, sBatch3, sBatch4));
        List<Object[]> objectSlots = slotRepository.findLastSlotAndRelatedShopperShiftByDriverShiftId(dShift.getId());

        Object[] objectSlot = objectSlots.get(0);

        Assert.assertEquals(slots.get(0).getId().longValue(), ((BigInteger) objectSlot[0]).longValue());
        Assert.assertEquals(dShift.getId().longValue(), ((BigInteger) objectSlot[1]).longValue());
        Assert.assertEquals(sShift.getId().longValue(), ((BigInteger) objectSlot[2]).longValue());
        Assert.assertEquals(slots.get(0).getStartTime(), ((java.sql.Timestamp) objectSlot[3]).toLocalDateTime());

    }

    @Test
    public void test_findLatestSlotByDriverShiftId_shouldReturnLatestSlotWithOrder() {

        Shipment shipment2 = shipmentFactory.createShipment(slots.get(1), admin);
        Batch dBatch2 = batchFactory.createBatch(admin, shipment2, slots.get(1), Batch.Type.DELIVERY);
        dBatch2.setShift(dShift);
        Batch sBatch2 = batchFactory.createBatch(admin, shipment2, slots.get(1), Batch.Type.SHOPPING);
        sBatch2.setShift(sShift);

        Shipment shipment3 = shipmentFactory.createShipment(slots.get(2), admin);
        Batch dBatch3 = batchFactory.createBatch(admin, shipment3, slots.get(2), Batch.Type.DELIVERY);
        dBatch3.setShift(dShift);
        Batch sBatch3 = batchFactory.createBatch(admin, shipment3, slots.get(2), Batch.Type.SHOPPING);
        sBatch3.setShift(sShift);

        Shipment shipment4 = shipmentFactory.createShipment(slots.get(3), admin);
        Batch dBatch4 = batchFactory.createBatch(admin, shipment4, slots.get(3), Batch.Type.DELIVERY);
        dBatch4.setShift(dShift);
        Batch sBatch4 = batchFactory.createBatch(admin, shipment4, slots.get(3), Batch.Type.SHOPPING);
        sBatch4.setShift(sShift);

        batchRepository.saveAll(List.of(dBatch2, dBatch3, dBatch4, sBatch2, sBatch3, sBatch4));
        List<Object[]> objectSlots = slotRepository.findLastSlotAndRelatedShopperShiftByDriverShiftId(dShift.getId());

        Object[] objectSlot = objectSlots.get(0);

        Assert.assertEquals(slots.get(1).getId().longValue(), ((BigInteger) objectSlot[0]).longValue());
        Assert.assertEquals(dShift.getId().longValue(), ((BigInteger) objectSlot[1]).longValue());
        Assert.assertEquals(sShift.getId().longValue(), ((BigInteger) objectSlot[2]).longValue());
        Assert.assertEquals(slots.get(1).getStartTime(), ((java.sql.Timestamp) objectSlot[3]).toLocalDateTime());

    }

    @Test
    public void test_ranger_findLatestSlotByDriverShiftId_shouldReturnZeroInShopperShiftId() {
        Shipment shipment1 = shipmentFactory.createShipment(slots.get(0), admin);
        Batch dBatch1 = batchFactory.createBatch(admin, shipment1, slots.get(0), Batch.Type.RANGER);
        dBatch1.setShift(dShift);

        Shipment shipment2 = shipmentFactory.createShipment(slots.get(1), admin);
        Batch dBatch2 = batchFactory.createBatch(admin, shipment2, slots.get(1), Batch.Type.RANGER);
        dBatch2.setShift(dShift);

        Shipment shipment3 = shipmentFactory.createShipment(slots.get(2), admin);
        Batch dBatch3 = batchFactory.createBatch(admin, shipment3, slots.get(2), Batch.Type.RANGER);
        dBatch3.setShift(dShift);

        Shipment shipment4 = shipmentFactory.createShipment(slots.get(3), admin);
        Batch dBatch4 = batchFactory.createBatch(admin, shipment4, slots.get(3), Batch.Type.RANGER);
        dBatch4.setShift(dShift);

        batchRepository.saveAll(List.of(dBatch1, dBatch2, dBatch3, dBatch4));
        List<Object[]> objectSlots = slotRepository.findLastSlotAndRelatedShopperShiftByDriverShiftId(dShift.getId());

        Object[] objectSlot = objectSlots.get(0);

        Assert.assertEquals(slots.get(0).getId().longValue(), ((BigInteger) objectSlot[0]).longValue());
        Assert.assertEquals(dShift.getId().longValue(), ((BigInteger) objectSlot[1]).longValue());
        Assert.assertEquals(0L, ((BigInteger) objectSlot[2]).longValue());
        Assert.assertEquals(slots.get(0).getStartTime(), ((java.sql.Timestamp) objectSlot[3]).toLocalDateTime());

    }
}
