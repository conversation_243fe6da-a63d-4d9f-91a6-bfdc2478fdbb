package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.happyfresh.fulfillment.batch.service.FileUploaderService;
import com.happyfresh.fulfillment.batch.service.ShopperAutoAssignmentService;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.CancelInfoRepository;
import com.happyfresh.fulfillment.repository.ChatRepository;
import com.happyfresh.fulfillment.repository.JobRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.slot.presenter.SlotOptimizationEvent;
import org.apache.commons.io.IOUtils;
import org.hamcrest.Matchers;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.any;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class FailShoppingTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private CancelInfoRepository cancelInfoRepository;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private ChatFactory chatFactory;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private ChatRepository chatRepository;

    @MockBean
    private ShopperAutoAssignmentService shopperAutoAssignmentService;

    @MockBean
    private FileUploaderService fileUploaderService;

    @Autowired
    private ShiftFactory shiftFactory;

    @BeforeClass
    public static void initProperty() {
        System.setProperty("kafka.listener.enabled","true");
    }

    @AfterClass
    public static void resetProperty() {
        System.setProperty("kafka.listener.enabled","false");
    }

    @Test
    public void shouldSuccessIfFailShopping() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER);
        User externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, shopper.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocations.get(0), LocalDateTime.now(), 5, 1, 3, 3, shopper);;

        File file1 = new File("src/test/resources/fixtures/receipt1.jpg");
        FileInputStream input1 = new FileInputStream(file1);
        MockMultipartFile multipartFile = new MockMultipartFile("evidence", file1.getName(), "image/jpeg", IOUtils.toByteArray(input1));
        input1.close();

        Shipment shipment = shipmentFactory.createShipment(slots.get(0), externalSystemAdmin, "H234567", "H234567", Shipment.State.READY);

        Batch shoppingBatch = batchFactory.createBatch(externalSystemAdmin, shipment, slots.get(0), Batch.Type.SHOPPING, Job.State.STARTED); // + start shopping job
        Batch deliveryBatch = batchFactory.createBatch(externalSystemAdmin, shipment, slots.get(0), Batch.Type.DELIVERY, Job.State.INITIAL);

        Chat chat = chatFactory.createChat(externalSystemAdmin, shipment, "http://yahoo.com", Chat.State.ACTIVE);

        Mockito.when(fileUploaderService.uploadAttachment(any(MultipartFile.class))).thenReturn("https://cdn.carbon.com/asd.png");
        mvc.perform(MockMvcRequestBuilders.multipart("/api/batches/"+ shoppingBatch.getId() + "/shipments/"+ shipment.getNumber() +"/fail_shopping")
                .file(multipartFile)
                .param("reason", "i have no reason")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        transactionHelper.withNewTransaction(() -> {
            CancelInfo cancelInfo = cancelInfoRepository.findAll().get(0);
            Assert.assertEquals(1, cancelInfoRepository.count());
            Assert.assertEquals("i have no reason", cancelInfo.getReason());
            Assert.assertNotNull(cancelInfo.getEvidenceUrl());

            Shipment shipment1 = shipmentRepository.findAll().get(0);
            Assert.assertEquals(Shipment.State.CANCELLED, shipment1.getState());
            Chat chat1 = chatRepository.findAll().get(0);
            Assert.assertEquals(Chat.State.CLOSED, chat1.getState());
            Assert.assertEquals(0, jobRepository.findAllByType(Job.Type.SHOPPER).size());
            Assert.assertEquals(0, jobRepository.findAllByType(Job.Type.DRIVER).size());
        });

    }

    @Test
    public void failShoppingwithNullParams_shouldFail() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER);
        User externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, shopper.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, shopper);

        Batch shoppingBatch = batchFactory.createBatch(externalSystemAdmin, shopper, slots.get(0), Batch.Type.SHOPPING); // + start shopping job

        Shipment shipment = shoppingBatch.getJobs().get(0).getShipment();
        Chat chat = chatFactory.createChat(externalSystemAdmin, shipment, "http://yahoo.com", Chat.State.ACTIVE);

        mvc.perform(MockMvcRequestBuilders.multipart("/api/batches/"+ shoppingBatch.getId() + "/shipments/"+ shipment.getNumber() +"/fail_shopping")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errors", Matchers.notNullValue()))
                .andExpect(jsonPath("$.errors[0].message", Matchers.not(Matchers.isEmptyOrNullString())));
    }

    @Test
    public void shouldSuccessIfChatNotInitialized() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER);
        User externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, shopper.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, shopper);

        File file1 = new File("src/test/resources/fixtures/receipt1.jpg");
        FileInputStream input1 = new FileInputStream(file1);
        MockMultipartFile multipartFile = new MockMultipartFile("evidence", file1.getName(), "image/jpeg", IOUtils.toByteArray(input1));
        input1.close();

        Shipment shipment = shipmentFactory.createShipment(slots.get(0), externalSystemAdmin, "H234567", "H234567", Shipment.State.READY);
        Batch shoppingBatch = batchFactory.createBatch(externalSystemAdmin, shipment, slots.get(0), Batch.Type.SHOPPING, Job.State.STARTED);
        Batch deliveryBatch = batchFactory.createBatch(externalSystemAdmin, shipment, slots.get(0), Batch.Type.DELIVERY, Job.State.INITIAL);

        mvc.perform(MockMvcRequestBuilders.multipart("/api/batches/"+ shoppingBatch.getId() + "/shipments/"+ shipment.getNumber() +"/fail_shopping")
                .file(multipartFile)
                .param("reason", "i have no reason")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
    }

    @Test
    public void whenFailShopping_shouldTriggerShopperAutoAssignment() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER);
        User externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, shopper.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocations.get(0), LocalDateTime.now(), 5, 1, 3, 3, shopper);;

        File file1 = new File("src/test/resources/fixtures/receipt1.jpg");
        FileInputStream input1 = new FileInputStream(file1);
        MockMultipartFile multipartFile = new MockMultipartFile("evidence", file1.getName(), "image/jpeg", IOUtils.toByteArray(input1));
        input1.close();

        Shipment shipment = shipmentFactory.createShipment(slots.get(0), externalSystemAdmin, "H234567", "H234567", Shipment.State.READY);

        Batch shoppingBatch = batchFactory.createBatch(externalSystemAdmin, shipment, slots.get(0), Batch.Type.SHOPPING, Job.State.STARTED); // + start shopping job
        Batch deliveryBatch = batchFactory.createBatch(externalSystemAdmin, shipment, slots.get(0), Batch.Type.DELIVERY, Job.State.INITIAL);

        Chat chat = chatFactory.createChat(externalSystemAdmin, shipment, "http://yahoo.com", Chat.State.ACTIVE);

        Mockito.when(fileUploaderService.uploadAttachment(any(MultipartFile.class))).thenReturn("https://cdn.carbon.com/asd.png");
        mvc.perform(MockMvcRequestBuilders.multipart("/api/batches/"+ shoppingBatch.getId() + "/shipments/"+ shipment.getNumber() +"/fail_shopping")
                        .file(multipartFile)
                        .param("reason", "i have no reason")
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        transactionHelper.withNewTransaction(() -> {
            CancelInfo cancelInfo = cancelInfoRepository.findAll().get(0);
            Assert.assertEquals(1, cancelInfoRepository.count());
            Assert.assertEquals("i have no reason", cancelInfo.getReason());
            Assert.assertNotNull(cancelInfo.getEvidenceUrl());

            Shipment shipment1 = shipmentRepository.findAll().get(0);
            Assert.assertEquals(Shipment.State.CANCELLED, shipment1.getState());
            Chat chat1 = chatRepository.findAll().get(0);
            Assert.assertEquals(Chat.State.CLOSED, chat1.getState());
            Assert.assertEquals(0, jobRepository.findAllByType(Job.Type.SHOPPER).size());
            Assert.assertEquals(0, jobRepository.findAllByType(Job.Type.DRIVER).size());
        });

        TimeUnit.SECONDS.sleep(1);
        Mockito.verify(shopperAutoAssignmentService).publishAutoAssignmentEvent(shoppingBatch.getStockLocation().getCluster().getId().toString(),
                shipment.getSlot().getId().toString(), null, SlotOptimizationEvent.AutoAssignmentTriggerEvent.SLOT_OPTIMIZATION);

    }

    @Test
    public void whenFailShopping_longerDelivery_shouldTriggerShopperAutoAssignment() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER);
        User externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, shopper.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocations.get(0), LocalDateTime.now(), 5, 1, 3, 3, shopper);;

        File file1 = new File("src/test/resources/fixtures/receipt1.jpg");
        FileInputStream input1 = new FileInputStream(file1);
        MockMultipartFile multipartFile = new MockMultipartFile("evidence", file1.getName(), "image/jpeg", IOUtils.toByteArray(input1));
        input1.close();

        Shipment shipment = shipmentFactory.createShipment(slots.get(0), externalSystemAdmin, "H234567", "H234567", Shipment.State.READY);

        Batch shoppingBatch = batchFactory.createBatch(externalSystemAdmin, shipment, slots.get(0), Batch.Type.SHOPPING, Job.State.STARTED); // + start shopping job
        Shift shopperShift = shiftFactory.createShift(stockLocations.get(0), externalSystemAdmin, Shift.Type.SHOPPER, slots.get(0).getStartTime().minusHours(3), slots.get(0).getStartTime().plusHours(8), 1);

        Batch deliveryBatch = batchFactory.createBatch(externalSystemAdmin, shipment, slots.get(0), Batch.Type.DELIVERY, Job.State.INITIAL);

        Chat chat = chatFactory.createChat(externalSystemAdmin, shipment, "http://yahoo.com", Chat.State.ACTIVE);

        Mockito.when(fileUploaderService.uploadAttachment(any(MultipartFile.class))).thenReturn("https://cdn.carbon.com/asd.png");
        mvc.perform(MockMvcRequestBuilders.multipart("/api/batches/"+ shoppingBatch.getId() + "/shipments/"+ shipment.getNumber() +"/fail_shopping")
                        .file(multipartFile)
                        .param("reason", "i have no reason")
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        transactionHelper.withNewTransaction(() -> {
            CancelInfo cancelInfo = cancelInfoRepository.findAll().get(0);
            Assert.assertEquals(1, cancelInfoRepository.count());
            Assert.assertEquals("i have no reason", cancelInfo.getReason());
            Assert.assertNotNull(cancelInfo.getEvidenceUrl());

            Shipment shipment1 = shipmentRepository.findAll().get(0);
            Assert.assertEquals(Shipment.State.CANCELLED, shipment1.getState());
            Chat chat1 = chatRepository.findAll().get(0);
            Assert.assertEquals(Chat.State.CLOSED, chat1.getState());
            Assert.assertEquals(0, jobRepository.findAllByType(Job.Type.SHOPPER).size());
            Assert.assertEquals(0, jobRepository.findAllByType(Job.Type.DRIVER).size());
        });

        TimeUnit.SECONDS.sleep(2);

        Mockito.verify(shopperAutoAssignmentService).publishAutoAssignmentEvent(shoppingBatch.getStockLocation().getCluster().getId().toString(),
                shipment.getSlot().getId().toString(), null, SlotOptimizationEvent.AutoAssignmentTriggerEvent.SLOT_OPTIMIZATION);
    }

}
