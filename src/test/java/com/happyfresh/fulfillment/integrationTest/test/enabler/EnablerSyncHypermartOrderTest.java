package com.happyfresh.fulfillment.integrationTest.test.enabler;

import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.messaging.activemq.Sender;
import com.happyfresh.fulfillment.common.presenter.EnablerWebhookEvent;
import com.happyfresh.fulfillment.common.service.EnablerEventPublisherService;
import com.happyfresh.fulfillment.common.service.WebhookPublisherService;
import com.happyfresh.fulfillment.enabler.presenter.hypermart.HypermartOrderStatusPresenter;
import com.happyfresh.fulfillment.enabler.presenter.hypermart.model.HypermartOrderDetail;
import com.happyfresh.fulfillment.enabler.scheduler.EnablerScheduler;
import com.happyfresh.fulfillment.enabler.service.api.hypermart.HypermartApiService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.repository.TenantRepository;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import com.happyfresh.fulfillment.stockLocation.service.StockLocationService;
import org.junit.*;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

public class EnablerSyncHypermartOrderTest extends BaseTest {

    @MockBean
    private HypermartApiService hypermartApiService;

    @Autowired
    private JobFactory jobFactory;

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private BatchFactory batchFactory;

    @SpyBean
    private EnablerEventPublisherService enablerEventPublisherService;

    @Autowired
    private EnablerScheduler enablerScheduler;

    @MockBean
    private Sender sender;

    @MockBean
    private WebhookPublisherService webhookPublisherService;

    @MockBean
    private StockLocationService stockLocationService;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private BatchRepository batchRepository;

    @MockBean
    private OrderService orderService;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private TenantRepository tenantRepository;

    private StockLocation.Enabler enabler = StockLocation.Enabler.HYPERMART;
    private List<Slot> slots;
    private User hypermartAdmin;
    private StockLocation enablerStockLocation;
    private User creator;
    private Tenant tenant;

    @BeforeClass
    public static void initProperty() {
        System.setProperty("scheduler.enabled", "true");
        System.setProperty("kafka.listener.enabled","true");
    }

    @AfterClass
    public static void resetProperty() {
        System.setProperty("scheduler.enabled", "false");
        System.setProperty("kafka.listener.enabled","false");
    }

    @Before
    public void setup() throws InterruptedException {
        Thread.sleep(2000);
        creator = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);

        tenant = creator.getTenant();
        Map<String, String> preferences = tenant.getPreferences();
        preferences.put("enable_hypermart_check_status_scheduler", "true");
        tenantRepository.save(tenant);

        User systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, creator.getTenant());
        hypermartAdmin = userFactory.createEnablerShopper(StockLocation.Enabler.HYPERMART, systemAdmin.getTenant());
        userFactory.createEnablerShopper(StockLocation.Enabler.HYPERMART, systemAdmin.getTenant());

        List<StockLocation> enablerStockLocations = stockLocationFactory.createNEnablerStockLocationsOnTheSameCluster(enabler, null, 3, hypermartAdmin, StockLocation.Type.ORIGINAL);
        enablerStockLocation = enablerStockLocations.get(0);
        slots = slotFactory.createLongerDeliverySlots(enablerStockLocation, creator, 2, 4, LocalDateTime.now());
        Thread.sleep(400);
    }

    private HypermartOrderStatusPresenter getHypermartOrderStatusPresenter(boolean isComplete, String message, Shipment shipment) {
        HypermartOrderStatusPresenter presenter = new HypermartOrderStatusPresenter();
        presenter.setErrorCode(200);
        presenter.setMessage("Success");
        if(isComplete){
            presenter.setOrderStatusDetail(getHypermartOrderDetail(shipment));
        }else{
            presenter.setErrorMsg(message);
        }
        return presenter;
    }

    private HypermartOrderDetail getHypermartOrderDetail(Shipment shipment) {
        HypermartOrderDetail orderDetail = new HypermartOrderDetail();
        orderDetail.setHappyFreshID(shipment.getOrderNumber());
        orderDetail.setOrderStatus("complete");
        List<HypermartOrderDetail.OrderItem> items = shipment.getItems().stream().map(item -> {
            HypermartOrderDetail.OrderItem orderItem = new HypermartOrderDetail.OrderItem();
            orderItem.setItemSku(item.getSku());
            orderItem.setQuantityOnHand(item.getRequestedQty());
            orderItem.setQuantityOutOfStock(0);
            return orderItem;
        }).collect(Collectors.toList());
        orderDetail.setResult(items);
        return orderDetail;
    }

    private void addJobState(Batch batch, Job.State state) {
        for (Job job : batch.getJobs()) {
            job.setState(state);
            jobFactory.save(job);
        }
    }

    private Batch createBatch(User user, List<Shipment> shipments, Slot slot, Batch.Type type) {
        Batch batch = batchFactory.createBatch(hypermartAdmin, shipments, slots.get(0), type);
        batch.setUser(user);
        batchFactory.save(batch);

        return batch;
    }

    @Test
    public void syncHypermartOrders_whenJobStillStarted_andHypermartOrderIsComplete_shouldUpdateToComplete() throws InterruptedException {
        Shipment shipment = shipmentFactory.createShipment(slots.get(0), creator, "order001", "order001", Shipment.State.READY, 5);
        List<Shipment> shipments = Lists.newArrayList(shipment);
        Batch sBatch = createBatch(hypermartAdmin, shipments, slots.get(0), Batch.Type.SHOPPING);
        Batch dBatch = createBatch(hypermartAdmin, shipments, slots.get(0), Batch.Type.DELIVERY);

        addJobState(sBatch, Job.State.STARTED);

        HypermartOrderStatusPresenter hypermartOrderStatusPresenter = getHypermartOrderStatusPresenter(true, null, shipment);

        Mockito.when(hypermartApiService.getOrderStatus(Mockito.any()))
                .thenReturn(Optional.of(hypermartOrderStatusPresenter));

        enablerScheduler.syncHypermartOrders();

        TimeUnit.SECONDS.sleep(5);
        verify(enablerEventPublisherService, atLeastOnce()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());
        Thread.sleep(1000);
        transactionHelper.withNewTransaction(() -> {
            Optional<Batch> optionalBatch = batchRepository.findById(sBatch.getId());
            Assert.assertTrue(optionalBatch.isPresent());
            Batch batch = optionalBatch.get();
            Job shoppingJob = batch.getJobs().stream().filter(Job::isShopping).findAny().get();
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());
        });
    }

    @Test
    public void syncHypermartOrders_whenJobStillStarted_andHypermartOrderIsCanceled_shouldUpdateToCancel() throws InterruptedException {
        Shipment shipment = shipmentFactory.createShipment(slots.get(0), creator, "order001", "order001", Shipment.State.READY, 5);
        List<Shipment> shipments = Lists.newArrayList(shipment);
        Batch sBatch = createBatch(hypermartAdmin, shipments, slots.get(0), Batch.Type.SHOPPING);
        Batch dBatch = createBatch(hypermartAdmin, shipments, slots.get(0), Batch.Type.DELIVERY);

        addJobState(sBatch, Job.State.STARTED);

        HypermartOrderStatusPresenter hypermartOrderStatusPresenter = getHypermartOrderStatusPresenter(false, HypermartApiService.MESSAGE_CANCELLED_ORDER, null);

        Mockito.when(hypermartApiService.getOrderStatus(Mockito.any()))
                .thenReturn(Optional.of(hypermartOrderStatusPresenter));

        enablerScheduler.syncHypermartOrders();

        TimeUnit.SECONDS.sleep(5);
        verify(enablerEventPublisherService, atLeastOnce()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());
        Thread.sleep(1000);
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByOrderNumber(shipment.getOrderNumber());
            Assert.assertEquals(Shipment.State.CANCELLED, shipment1.getState());
            Optional<Job> optionalJob = shipment1.getShoppingJob();
            Assert.assertFalse(optionalJob.isPresent());
        });
    }

    @Test
    public void syncHypermartOrders_whenJobStillStarted_andHypermartOrderIsOnGoing_shouldNotUpdateOrder() throws InterruptedException {
        Shipment shipment = shipmentFactory.createShipment(slots.get(0), creator, "order001", "order001", Shipment.State.READY, 5);
        List<Shipment> shipments = Lists.newArrayList(shipment);
        Batch sBatch = createBatch(hypermartAdmin, shipments, slots.get(0), Batch.Type.SHOPPING);
        Batch dBatch = createBatch(hypermartAdmin, shipments, slots.get(0), Batch.Type.DELIVERY);

        addJobState(sBatch, Job.State.STARTED);

        HypermartOrderStatusPresenter hypermartOrderStatusPresenter = getHypermartOrderStatusPresenter(false, HypermartApiService.MESSAGE_ONGOING_ORDER, null);

        Mockito.when(hypermartApiService.getOrderStatus(Mockito.any()))
                .thenReturn(Optional.of(hypermartOrderStatusPresenter));

        enablerScheduler.syncHypermartOrders();

        verify(enablerEventPublisherService, never()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());
        Thread.sleep(2000);
        transactionHelper.withNewTransaction(() -> {
            Optional<Batch> optionalBatch = batchRepository.findById(sBatch.getId());
            Assert.assertTrue(optionalBatch.isPresent());
            Batch batch = optionalBatch.get();
            Job shoppingJob = batch.getJobs().stream().filter(Job::isShopping).findAny().get();
            Assert.assertEquals(Job.State.STARTED, shoppingJob.getState());
        });
    }

    @Test
    public void syncHypermartOrders_whenJobStillStarted_andTenantDisableHypermartScheduler_shouldNotUpdateOrder() throws InterruptedException {
        tenant = creator.getTenant();
        Map<String, String> preferences = tenant.getPreferences();
        preferences.put("enable_hypermart_check_status_scheduler", "false");
        tenantRepository.save(tenant);

        Shipment shipment = shipmentFactory.createShipment(slots.get(0), creator, "order001", "order001", Shipment.State.READY, 5);
        List<Shipment> shipments = Lists.newArrayList(shipment);
        Batch sBatch = createBatch(hypermartAdmin, shipments, slots.get(0), Batch.Type.SHOPPING);
        Batch dBatch = createBatch(hypermartAdmin, shipments, slots.get(0), Batch.Type.DELIVERY);

        addJobState(sBatch, Job.State.STARTED);

        HypermartOrderStatusPresenter hypermartOrderStatusPresenter = getHypermartOrderStatusPresenter(true, null, shipment);

        Mockito.when(hypermartApiService.getOrderStatus(Mockito.any()))
                .thenReturn(Optional.of(hypermartOrderStatusPresenter));

        enablerScheduler.syncHypermartOrders();

        verify(enablerEventPublisherService, never()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());
        Thread.sleep(2000);
        transactionHelper.withNewTransaction(() -> {
            Optional<Batch> optionalBatch = batchRepository.findById(sBatch.getId());
            Assert.assertTrue(optionalBatch.isPresent());
            Batch batch = optionalBatch.get();
            Job shoppingJob = batch.getJobs().stream().filter(Job::isShopping).findAny().get();
            Assert.assertEquals(Job.State.STARTED, shoppingJob.getState());
        });
    }
}
