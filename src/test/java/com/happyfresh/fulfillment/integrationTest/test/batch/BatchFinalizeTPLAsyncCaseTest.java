package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.happyfresh.fulfillment.batch.service.BatchSndService;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.property.DelyvaProperty;
import com.happyfresh.fulfillment.common.property.LalamoveProperty;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.BatchHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.MockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.SetSlotHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.ShipmentHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.lalamove.service.LalamoveService;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.DelyvaDeliveryRepository;
import com.happyfresh.fulfillment.repository.LalamoveDeliveryRepository;
import com.happyfresh.fulfillment.shipment.service.ItemService;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import com.happyfresh.fulfillment.tpl.delyva.model.DelyvaStatusCode;
import org.elasticsearch.common.geo.GeoPoint;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static java.nio.file.Files.readAllBytes;
import static java.nio.file.Paths.get;
import static org.mockito.Mockito.any;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

public class BatchFinalizeTPLAsyncCaseTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private LalamoveServiceTypeFactory lalamoveServiceTypeFactory;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private SetSlotHelper slotHelper;

    @Autowired
    private ShipmentHelper shipmentHelper;

    @Autowired
    private BatchHelper batchHelper;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private LalamoveDeliveryRepository lalamoveDeliveryRepository;

    @Autowired
    private DelyvaDeliveryRepository delyvaDeliveryRepository;

    @MockBean
    private OrderService orderService;

    @Autowired
    private LalamoveProperty lalamoveProperty;

    @Autowired
    private DelyvaProperty delyvaProperty;

    @Autowired
    private MockServerHelper mockServerHelper;

    @SpyBean
    private LalamoveService lalamoveService;

    @SpyBean
    private BatchSndService batchSndService;

    @SpyBean
    private ItemService itemService;

    @Autowired
    private ShiftFactory shiftFactory;

    private User systemAdmin;
    private User admin;
    private User userAdmin;
    private User shopper;
    private StockLocation stockLocation;
    private Slot slot;
    private MockRestServiceServer mockServer;

    @Before
    public void setup() throws Exception {
        systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN);
        admin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, systemAdmin.getTenant());
        userAdmin = userFactory.createUserData(Role.Name.ADMIN, systemAdmin.getTenant());
        shopper = userFactory.createUserData(Role.Name.SHOPPER, systemAdmin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        stockLocation = stockLocations.get(0);

        slot = slotFactory.createSlot(stockLocation, admin, 0, 2);
        slot.setType(Slot.Type.LONGER_DELIVERY);
        slotFactory.save(slot);

        shiftFactory.createDriverShifts(stockLocation, LocalDateTime.now(), 2, 8, 1, admin);
        shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 2, 8, 1, admin);

        mockServer = mockServerHelper.buildMockServer();
    }

    private JSONObject setupItemShipment(long suffixId, String sku, int reqQty, double weight) throws Exception {
        JSONObject item = shipmentJsonObjectFactory.createItemObject(suffixId, sku, reqQty, weight, 60.0, 75.0, 105.0);
        item.put("average_weight", String.valueOf(weight));
        return item;
    }

    private JSONObject setupShipment(JSONObject itemObj) throws Exception {
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(itemObj);
        return shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta", itemsArray);
    }

    private void setupSlForLalamove() {
        stockLocation.setEnableLalamove(true);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<String, String>() {{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000");
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);
    }

    private void setupSlForDelyva() {
        stockLocation.setEnableLalamove(false);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocation.setEnableDelyva(true);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocationFactory.save(stockLocation);
    }

    private void setupLalamove() throws Exception {
        setupSlForLalamove();
        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);
        mockLalamoveAPIRequest();
    }

    private void setupDelyva() throws Exception {
        setupSlForDelyva();
        mockDelyvaAPIRequest();
        slot.setFleetType(Slot.FleetType.PICKUPP);
    }

    private void mockLalamoveAPIRequest() throws Exception {
        // Mock - Get Quotation Request
        String uri = lalamoveProperty.getBaseUrl();
        String responseBody = "{ \"totalFee\": \"35000\", \"totalFeeCurrency\": \"IDR\" }";
        mockServer = setupMockServer(mockServer, 2, HttpMethod.POST, uri + "/v2/quotations", responseBody);
        // Mock - Place Order Request
        responseBody = "{ \"customerOrderId\": \"DEPRECATED_LALAMOVE_ID\", \"orderRef\": \"LALAMOVE-EXTERNAL-ID\" }";
        mockServer = setupMockServer(mockServer, 1, HttpMethod.POST, uri + "/v2/orders", responseBody);
    }

    private void mockDelyvaAPIRequest() throws Exception {
        String uri = delyvaProperty.getBaseUrl();

        String getQuotationResponse = getResponseFromResourceFileAsString("delyva_get_quotation_response.json");
        mockServer = setupMockServer(mockServer, 1, HttpMethod.POST, uri + "/v1.0/service/instantQuote", getQuotationResponse);

        String createOrderResponse = getResponseFromResourceFileAsString("delyva_create_order_response.json");
        mockServer = setupMockServer(mockServer, 1, HttpMethod.POST, uri + "/v1.0/order", createOrderResponse);

    }

    private MockRestServiceServer setupMockServer(MockRestServiceServer mockServer, int expectedCount, HttpMethod method, String url, String responseBody) throws Exception {
        ExpectedCount count = expectedCount > 0 ? ExpectedCount.min(expectedCount) : ExpectedCount.never();
        mockServer.expect(count, requestTo(new URI(url)))
                .andExpect(method(method))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));
        return mockServer;
    }

    private String getResponseFromResourceFileAsString(String fileName) throws IOException {
        return new String(readAllBytes(get("src", "test", "resources", "fixtures", fileName)));
    }

    @Test
    public void finalizeOrder_withLalamove_caseUpdateItemQtyTransactionNotFinishYet() throws Exception {
        setupLalamove();

        JSONObject itemObj = setupItemShipment(1L, "SKU-0001-", 1, 300.0);
        JSONObject shipmentObj = setupShipment(itemObj);

        slotHelper.assertSetRegularSlotV2(true, shipmentObj, admin);
        shipmentHelper.adjustShipment(shipmentObj, admin);
        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        Thread.sleep(200);

        JSONArray itemFinalizeArray = new JSONArray();
        itemObj.put("id", 1L);
        itemObj.put("found_qty", 1);
        itemFinalizeArray.put(itemObj);
        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipmentObj.get("number"));
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemFinalizeArray);
        requestBody.put("shipment_numbers", shipmentsArray);

        Mockito.doAnswer(invocation -> {
            CompletableFuture<Void> async = CompletableFuture.runAsync(() -> {
                try {
                    TimeUnit.SECONDS.sleep(5);
                    invocation.callRealMethod();
                } catch (Throwable e) {
                    throw new RuntimeException(e);
                }
            });
            async.get();
            return null;
        }).when(itemService).save(any(), any(), any());

        String urlFinalize = String.format("/api/v3/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(urlFinalize)
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                        .content(requestBody.toString())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Thread.sleep(200);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("LALAMOVE-EXTERNAL-ID", lalamoveDelivery.getExternalOrderId());
            Assert.assertEquals(BigDecimal.valueOf(40000.0), lalamoveDelivery.getPrice()); // 35000 + 5000 (Addition)
            Assert.assertEquals("MPV", lalamoveDelivery.getServiceType());
            Assert.assertEquals(BigDecimal.valueOf(40000.0), lalamoveDelivery.getInitialPrice()); // 35000 + 5000 (Addition)
            Assert.assertEquals("MPV", lalamoveDelivery.getInitialServiceType());
        });

        mockServer.verify();
    }

    @Test
    public void finalizeOrder_withDelyva_caseUpdateItemQtyTransactionNotFinishYet() throws Exception {
        setupDelyva();

        JSONObject itemObj = setupItemShipment(1L, "SKU-0001-", 1, 300.0);
        JSONObject shipmentObj = setupShipment(itemObj);

        slotHelper.assertSetRegularSlotV2(true, shipmentObj, admin);
        shipmentHelper.adjustShipment(shipmentObj, admin);
        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        Thread.sleep(200);

        JSONArray itemFinalizeArray = new JSONArray();
        itemObj.put("id", 1L);
        itemObj.put("found_qty", 1);
        itemFinalizeArray.put(itemObj);
        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipmentObj.get("number"));
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemFinalizeArray);
        requestBody.put("shipment_numbers", shipmentsArray);

        Mockito.doAnswer(invocation -> {
            CompletableFuture<Void> async = CompletableFuture.runAsync(() -> {
                try {
                    TimeUnit.SECONDS.sleep(5);
                    invocation.callRealMethod();
                } catch (Throwable e) {
                    throw new RuntimeException(e);
                }
            });
            async.get();
            return null;
        }).when(itemService).save(any(), any(), any());

        String urlFinalize = String.format("/api/v3/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(urlFinalize)
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                        .content(requestBody.toString())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Thread.sleep(200);

        transactionHelper.withNewTransaction(() -> {
            DelyvaDelivery delivery = delyvaDeliveryRepository.findAll().get(0);
            Assert.assertEquals(DelyvaStatusCode.DRAFT, delivery.getStatus());
            Assert.assertEquals("0aa4189a-39f5-488c-9aaf-0f011073de76", delivery.getExternalId());
            Assert.assertEquals(BigDecimal.valueOf(8.0), delivery.getServiceFee());
            Assert.assertEquals("HFPK-ODD-KV-PROD", delivery.getServiceCode());
            Assert.assertEquals("Pickupp Express Delivery", delivery.getServiceName());
        });

        mockServer.verify();
    }
}
