package com.happyfresh.fulfillment.integrationTest.test.tpl;

import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.property.DelyvaProperty;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.BatchHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.MockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.ShipmentHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.DelyvaDeliveryRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import com.happyfresh.fulfillment.tpl.delyva.model.DelyvaStatusCode;
import com.happyfresh.fulfillment.tpl.delyva.service.DelyvaTrackingService;
import org.apache.commons.io.IOUtils;
import org.elasticsearch.common.geo.GeoPoint;
import org.hamcrest.Matchers;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;

import static java.nio.file.Files.readAllBytes;
import static java.nio.file.Paths.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class DelyvaCancelOrderTest extends BaseTest {

    private final String EXTERNAL_ID = "0aa4189a-39f5-488c-9aaf-0f011073de76";

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private CountryFactory countryFactory;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private MockServerHelper mockServerHelper;

    @Autowired
    private ShipmentHelper shipmentHelper;

    @Autowired
    private BatchHelper batchHelper;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private DelyvaDeliveryRepository delyvaDeliveryRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private DelyvaProperty delyvaProperty;

    @MockBean
    private OrderService orderService;

    @MockBean
    private DelyvaTrackingService delyvaTrackingService;

    private StockLocation stockLocation;
    private User user;
    private User shopper;
    private Slot slot;
    private String getQuotationResponse, createOrderResponse, cancelOrderResponse;

    public DelyvaCancelOrderTest() {
    }

    @Before
    public void setUp() throws InterruptedException, IOException {
        user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        userFactory.createUserData(Role.Name.SYSTEM_ADMIN, user.getTenant());
        shopper = userFactory.createUserData(Role.Name.SHOPPER, user.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnDdsCluster(2, user);
        stockLocation = stockLocations.get(0);

        LocalDateTime tomorrow10AM = LocalDateTime.now().plusDays(1).withHour(10).withMinute(0).withSecond(0).withNano(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 2, 3, tomorrow10AM);
        slots.sort(Comparator.comparing(Slot::getStartTime));
        slot = slots.get(0);

        getQuotationResponse = getResponseFromResourceFileAsString("delyva_get_quotation_response.json");
        createOrderResponse = getResponseFromResourceFileAsString("delyva_create_order_response.json");
        cancelOrderResponse = getResponseFromResourceFileAsString("delyva_cancel_order_response.json");
    }

    private void setupStockLocation(boolean enableDelyva, boolean enableDelyvaDeliveryFee, String delyvaFlatFee) {
        stockLocation.setEnableDelyva(enableDelyva);
        stockLocation.setPreferences(new HashMap<String, String>() {{
            put("enable_delyva_delivery_fee", String.valueOf(enableDelyvaDeliveryFee));
            put("delyva_flat_service_fee", delyvaFlatFee);
        }});
        stockLocationFactory.save(stockLocation);

        Country country = stockLocation.getState().getCountry();
        country.setIsoName("MY");
        country.setName("Malaysia");
        countryFactory.save(country);
    }

    private void checkAvailability(JSONObject shipmentObj) throws Exception {

        mvc.perform(MockMvcRequestBuilders.post("/api/v2/slots/available")
                        .header("X-Fulfillment-User-Token", user.getToken())
                        .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    private void setSlot(JSONObject shipmentObj, String orderNumber, boolean isSuccess) throws Exception {
        if (isSuccess) {
            mvc.perform(MockMvcRequestBuilders.post("/api/v2/slots/set_slot")
                            .header("X-Fulfillment-User-Token", user.getToken())
                            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(shipmentObj.toString()))
                    .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                    .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.order_number", Matchers.equalTo(orderNumber)));
        } else {
            mvc.perform(MockMvcRequestBuilders.post("/api/v2/slots/set_slot")
                            .header("X-Fulfillment-User-Token", user.getToken())
                            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(shipmentObj.toString()))
                    .andExpect(MockMvcResultMatchers.status().is4xxClientError());
        }
    }

    private void finalize(JSONObject shipmentObj, Batch shoppingBatch, Shipment shipment) throws Exception {
        JSONObject requestBody = generateFinalizeRequestBody(shipmentObj, shipment);

        String urlFinalize = String.format("/api/v3/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(urlFinalize)
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                        .content(requestBody.toString())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    private void cancelShipment(Shipment shipment) throws Exception {
        String shipmentNumber = shipment.getNumber();
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + shipmentNumber + "/cancel")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("X-Fulfillment-User-Token", user.getToken())
                        .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void failShopping(Batch shoppingBatch, Shipment shipment) throws Exception {
        mvc.perform(MockMvcRequestBuilders.multipart("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/fail_shopping")
                        .file(getFailShoppingUploadFile())
                        .param("reason", "This is a reason")
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
    }

    private JSONObject generateFinalizeRequestBody(JSONObject shipmentObj, Shipment shipment) throws JSONException {
        JSONObject itemObject = shipmentObj.getJSONArray("items").getJSONObject(0);
        JSONArray itemFinalizeArray = new JSONArray();
        itemObject.put("id", 1);
        itemObject.put("found_qty", 2);
        itemFinalizeArray.put(itemObject);
        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipment.getNumber());
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemFinalizeArray);
        requestBody.put("shipment_numbers", shipmentsArray);
        return requestBody;
    }

    private JSONObject setupShipment(Slot slot) throws Exception {
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), 1, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        JSONArray itemsArray = new JSONArray();
        JSONObject itemObject = shipmentJsonObjectFactory.createItemObject(1, "sku-", 2, 1.0, 10, 10, 10);
        itemObject.put("average_weight", "1.0");
        itemsArray.put(itemObject);
        shipmentObj.put("items", itemsArray);
        return shipmentObj;
    }

    private String getQuotationUrl() {
        return delyvaProperty.getBaseUrl() + "/v1.0/service/instantQuote";
    }

    private String getCreateOrderUrl() {
        return delyvaProperty.getBaseUrl() + "/v1.0/order";
    }

    private String getCancelOrderUrl(String externalId) {
        return delyvaProperty.getBaseUrl() + "/v1.0/order/" + externalId + "/cancel";
    }

    private String getResponseFromResourceFileAsString(String fileName) throws IOException {
        return new String(readAllBytes(get("src", "test", "resources", "fixtures", fileName)));
    }

    private MockMultipartFile getFailShoppingUploadFile() throws IOException {
        File file1 = new File("src/test/resources/fixtures/receipt1.jpg");
        FileInputStream input1 = new FileInputStream(file1);
        MockMultipartFile multipartFile = new MockMultipartFile("evidence", file1.getName(), "image/jpeg", IOUtils.toByteArray(input1));
        input1.close();

        return multipartFile;
    }

    @Test
    public void whenAdminRequestCancel_AndOrderIsDelyva_AndIsCancelableStatus_shouldSendCancelToDelyva() throws Exception {
        shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(1), slot.getStartTime().plusHours(5), 1);
        shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(6), 0);
        setupStockLocation(true, true, "0");

        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = mockServerHelper.mock(mockServer, 2, HttpMethod.POST, getQuotationUrl(), getQuotationResponse);
        mockServer = mockServerHelper.mock(mockServer, 1, HttpMethod.POST, getCreateOrderUrl(), createOrderResponse);
        mockServer = mockServerHelper.mock(mockServer, 1, HttpMethod.POST, getCancelOrderUrl(EXTERNAL_ID), cancelOrderResponse);

        JSONObject shipmentObj = setupShipment(slot);
        checkAvailability(shipmentObj);
        setSlot(shipmentObj, "Order-1", true);

        shipmentHelper.adjustShipment(shipmentObj, user);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);

        Shipment shipment = shipmentRepository.findByNumber("Order-1");
        finalize(shipmentObj, shoppingBatch, shipment);

        transactionHelper.withNewTransaction(() -> {
            DelyvaDelivery delivery = delyvaDeliveryRepository.findAll().get(0);
            Assert.assertEquals(DelyvaStatusCode.DRAFT, delivery.getStatus());
            Assert.assertEquals(EXTERNAL_ID, delivery.getExternalId());
            Assert.assertEquals(BigDecimal.valueOf(8.0), delivery.getServiceFee());
            Assert.assertEquals("HFPK-ODD-KV-PROD", delivery.getServiceCode());
            Assert.assertEquals("Pickupp Express Delivery", delivery.getServiceName());
        });

        cancelShipment(shipment);

        transactionHelper.withNewTransaction(() -> {
            DelyvaDelivery delivery = delyvaDeliveryRepository.findAll().get(0);
            Assert.assertEquals(DelyvaStatusCode.ORDER_CANCELLED, delivery.getStatus());
            Assert.assertEquals(EXTERNAL_ID, delivery.getExternalId());
        });

        Mockito.verify(delyvaTrackingService, Mockito.atLeastOnce()).track(Mockito.eq("Delyva Update Log"), Mockito.any(DelyvaDelivery.class));

        mockServer.verify();

    }

    @Test
    public void whenAdminRequestCancel_AndOrderIsDelyva_AndIsNotCancelableStatus_shouldNotSendCancelToDelyva() throws Exception {
        shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(1), slot.getStartTime().plusHours(5), 1);
        shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(6), 0);
        setupStockLocation(true, true, "0");

        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = mockServerHelper.mock(mockServer, 2, HttpMethod.POST, getQuotationUrl(), getQuotationResponse);
        mockServer = mockServerHelper.mock(mockServer, 1, HttpMethod.POST, getCreateOrderUrl(), createOrderResponse);
        mockServer = mockServerHelper.mock(mockServer, 0, HttpMethod.POST, getCancelOrderUrl(EXTERNAL_ID), cancelOrderResponse);

        JSONObject shipmentObj = setupShipment(slot);
        checkAvailability(shipmentObj);
        setSlot(shipmentObj, "Order-1", true);

        shipmentHelper.adjustShipment(shipmentObj, user);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);

        Shipment shipment = shipmentRepository.findByNumber("Order-1");
        finalize(shipmentObj, shoppingBatch, shipment);
        Thread.sleep(500L);
        transactionHelper.withNewTransaction(() -> {
            DelyvaDelivery delivery = delyvaDeliveryRepository.findAll().get(0);
            Assert.assertEquals(DelyvaStatusCode.DRAFT, delivery.getStatus());
            Assert.assertEquals(EXTERNAL_ID, delivery.getExternalId());
            Assert.assertEquals(BigDecimal.valueOf(8.0), delivery.getServiceFee());
            Assert.assertEquals("HFPK-ODD-KV-PROD", delivery.getServiceCode());
            Assert.assertEquals("Pickupp Express Delivery", delivery.getServiceName());
        });

        DelyvaDelivery delyva = delyvaDeliveryRepository.findAll().get(0);
        delyva.setStatus(DelyvaStatusCode.PENDING_FOR_COLLECTION);
        delyvaDeliveryRepository.save(delyva);

        cancelShipment(shipment);

        transactionHelper.withNewTransaction(() -> {
            DelyvaDelivery delivery = delyvaDeliveryRepository.findAll().get(0);
            Assert.assertEquals(DelyvaStatusCode.ORDER_CANCELLED, delivery.getStatus());
            Assert.assertEquals(EXTERNAL_ID, delivery.getExternalId());
        });

        Mockito.verify(delyvaTrackingService, Mockito.atLeastOnce()).track(Mockito.eq("Delyva Update Log"), Mockito.any(DelyvaDelivery.class));

        mockServer.verify();

    }

    @Ignore("In real life condition, Delyva status won't be null since it's already stated as INITIAL in initial creation")
    @Test
    public void whenAdminRequestCancel_AndDelyvaDeliveryStatusIsNull_shouldDeleteDelyvaDelivery() throws Exception {
        shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(1), slot.getStartTime().plusHours(5), 1);
        shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(6), 0);
        setupStockLocation(true, true, "0");

        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = mockServerHelper.mock(mockServer, 2, HttpMethod.POST, getQuotationUrl(), getQuotationResponse);
        mockServer = mockServerHelper.mock(mockServer, 1, HttpMethod.POST, getCreateOrderUrl(), createOrderResponse);
        mockServer = mockServerHelper.mock(mockServer, 0, HttpMethod.POST, getCancelOrderUrl(EXTERNAL_ID), cancelOrderResponse);

        JSONObject shipmentObj = setupShipment(slot);
        checkAvailability(shipmentObj);
        setSlot(shipmentObj, "Order-1", true);

        shipmentHelper.adjustShipment(shipmentObj, user);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);

        Shipment shipment = shipmentRepository.findByNumber("Order-1");
        finalize(shipmentObj, shoppingBatch, shipment);

        transactionHelper.withNewTransaction(() -> {
            DelyvaDelivery delivery = delyvaDeliveryRepository.findAll().get(0);
            Assert.assertEquals(DelyvaStatusCode.DRAFT, delivery.getStatus());
            Assert.assertEquals(EXTERNAL_ID, delivery.getExternalId());
            Assert.assertEquals(BigDecimal.valueOf(8.0), delivery.getServiceFee());
            Assert.assertEquals("HFPK-ODD-KV-PROD", delivery.getServiceCode());
            Assert.assertEquals("Pickupp Express Delivery", delivery.getServiceName());
        });

        DelyvaDelivery delyva = delyvaDeliveryRepository.findAll().get(0);
        delyva.setStatus(null);
        delyvaDeliveryRepository.save(delyva);

        cancelShipment(shipment);

        transactionHelper.withNewTransaction(() -> {
            List<DelyvaDelivery> deliveries = delyvaDeliveryRepository.findAll();
            Assert.assertEquals(0, deliveries.size());
        });

        Mockito.verify(delyvaTrackingService, Mockito.never()).track(Mockito.eq("Delyva Update Log"), Mockito.any(DelyvaDelivery.class));

        mockServer.verify();

    }

    @Ignore("Since the order has not finalized yet, Delyva is still in INITIAL, hence it's not in cancelable status")
    @Test
    public void whenFailShopping_AndOrderIsDelyva_AndIsCancelableStatus_shouldSendCancelToDelyva() throws Exception {
        shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(1), slot.getStartTime().plusHours(5), 1);
        shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(6), 0);
        setupStockLocation(true, true, "0");

        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = mockServerHelper.mock(mockServer, 1, HttpMethod.POST, getQuotationUrl(), getQuotationResponse);
        mockServer = mockServerHelper.mock(mockServer, 0, HttpMethod.POST, getCreateOrderUrl(), createOrderResponse);
        mockServer = mockServerHelper.mock(mockServer, 0, HttpMethod.POST, getCancelOrderUrl(EXTERNAL_ID), cancelOrderResponse);

        JSONObject shipmentObj = setupShipment(slot);
        checkAvailability(shipmentObj);
        setSlot(shipmentObj, "Order-1", true);

        shipmentHelper.adjustShipment(shipmentObj, user);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);

        Shipment shipment = shipmentRepository.findByNumber("Order-1");

        transactionHelper.withNewTransaction(() -> {
            DelyvaDelivery delivery = delyvaDeliveryRepository.findAll().get(0);
            Assert.assertEquals(DelyvaStatusCode.INITIAL, delivery.getStatus());
            Assert.assertNull(delivery.getExternalId());
        });

        failShopping(shoppingBatch, shipment);

        transactionHelper.withNewTransaction(() -> {
            DelyvaDelivery delivery = delyvaDeliveryRepository.findAll().get(0);
            Assert.assertEquals(DelyvaStatusCode.ORDER_CANCELLED, delivery.getStatus());
        });

        Mockito.verify(delyvaTrackingService, Mockito.atLeastOnce()).track(Mockito.eq("Delyva Update Log"), Mockito.any(DelyvaDelivery.class));

        mockServer.verify();

    }

    @Test
    public void whenFailShopping_AndOrderIsDelyva_AndIsNotCancelableStatus_shouldNotSendCancelToDelyva() throws Exception {
        shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(1), slot.getStartTime().plusHours(5), 1);
        shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(6), 0);
        setupStockLocation(true, true, "0");

        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = mockServerHelper.mock(mockServer, 1, HttpMethod.POST, getQuotationUrl(), getQuotationResponse);
        mockServer = mockServerHelper.mock(mockServer, 0, HttpMethod.POST, getCreateOrderUrl(), createOrderResponse);
        mockServer = mockServerHelper.mock(mockServer, 0, HttpMethod.POST, getCancelOrderUrl(EXTERNAL_ID), cancelOrderResponse);

        JSONObject shipmentObj = setupShipment(slot);
        checkAvailability(shipmentObj);
        setSlot(shipmentObj, "Order-1", true);

        shipmentHelper.adjustShipment(shipmentObj, user);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);

        Shipment shipment = shipmentRepository.findByNumber("Order-1");

        transactionHelper.withNewTransaction(() -> {
            DelyvaDelivery delivery = delyvaDeliveryRepository.findAll().get(0);
            Assert.assertEquals(DelyvaStatusCode.INITIAL, delivery.getStatus());
            Assert.assertNull(delivery.getExternalId());
        });

        DelyvaDelivery delyva = delyvaDeliveryRepository.findAll().get(0);
        delyva.setStatus(DelyvaStatusCode.PENDING_FOR_COLLECTION);
        delyvaDeliveryRepository.save(delyva);

        failShopping(shoppingBatch, shipment);

        transactionHelper.withNewTransaction(() -> {
            DelyvaDelivery delivery = delyvaDeliveryRepository.findAll().get(0);
            Assert.assertEquals(DelyvaStatusCode.ORDER_CANCELLED, delivery.getStatus());
        });

        Mockito.verify(delyvaTrackingService, Mockito.atLeastOnce()).track(Mockito.eq("Delyva Update Log"), Mockito.any(DelyvaDelivery.class));

        mockServer.verify();

    }

    @Ignore("In real life condition, Delyva status won't be null since it's already stated as INITIAL in initial creation")
    @Test
    public void whenFailShopping_AndDelyvaDeliveryStatusIsNull_shouldDeleteDelyvaDelivery() throws Exception {
        shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(1), slot.getStartTime().plusHours(5), 1);
        shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(6), 0);
        setupStockLocation(true, true, "0");

        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = mockServerHelper.mock(mockServer, 2, HttpMethod.POST, getQuotationUrl(), getQuotationResponse);
        mockServer = mockServerHelper.mock(mockServer, 1, HttpMethod.POST, getCreateOrderUrl(), createOrderResponse);
        mockServer = mockServerHelper.mock(mockServer, 0, HttpMethod.POST, getCancelOrderUrl(EXTERNAL_ID), cancelOrderResponse);

        JSONObject shipmentObj = setupShipment(slot);
        checkAvailability(shipmentObj);
        setSlot(shipmentObj, "Order-1", true);

        shipmentHelper.adjustShipment(shipmentObj, user);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);

        Shipment shipment = shipmentRepository.findByNumber("Order-1");

        transactionHelper.withNewTransaction(() -> {
            DelyvaDelivery delivery = delyvaDeliveryRepository.findAll().get(0);
            Assert.assertEquals(DelyvaStatusCode.DRAFT, delivery.getStatus());
            Assert.assertEquals(EXTERNAL_ID, delivery.getExternalId());
            Assert.assertEquals(BigDecimal.valueOf(8.0), delivery.getServiceFee());
            Assert.assertEquals("HFPK-ODD-KV-PROD", delivery.getServiceCode());
            Assert.assertEquals("Pickupp Express Delivery", delivery.getServiceName());
        });

        DelyvaDelivery delyva = delyvaDeliveryRepository.findAll().get(0);
        delyva.setStatus(null);
        delyvaDeliveryRepository.save(delyva);

        failShopping(shoppingBatch, shipment);

        transactionHelper.withNewTransaction(() -> {
            List<DelyvaDelivery> deliveries = delyvaDeliveryRepository.findAll();
            Assert.assertEquals(0, deliveries.size());
        });

        Mockito.verify(delyvaTrackingService, Mockito.never()).track(Mockito.eq("Delyva Update Log"), Mockito.any(DelyvaDelivery.class));

        mockServer.verify();

    }
}
