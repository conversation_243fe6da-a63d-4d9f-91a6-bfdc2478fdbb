package com.happyfresh.fulfillment.integrationTest.test.tpl;

import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.GrabExpressDeliveryRepository;
import com.happyfresh.fulfillment.tpl.delyva.model.DelyvaStatusCode;
import org.hamcrest.Matchers;
import org.javers.common.collections.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.LocalDateTime;
import java.util.List;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class PendingDeliveriesTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private LalamoveDeliveryFactory lalamoveDeliveryFactory;

    @Autowired
    private DelyvaDeliveryFactory delyvaDeliveryFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private GrabExpressDeliveryRepository grabExpressDeliveryRepository;

    @Override
    protected void setupElasticSearchBeforeEachTests() {
        elasticSearchSetupService.setPreFillSimpleRouteEdges(false); // Prevent prefill ES
    }

    @Test
    public void shouldReturnGeAndLalamovePendingDeliveries() throws Exception {
        User admin = userFactory.createUserData(Role.Name.ADMIN);
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        Slot slot = slotFactory.createSlot(stockLocations.get(0), admin);
        // Grab Express
        Shipment s1 = shipmentFactory.createShipmentWithEmptyGrabExpress(slot, admin, "Order-1", "Shipment-1");
        Batch batch1 = batchFactory.createBatch(admin, shopper, s1, slot, Batch.Type.SHOPPING, Job.State.FINISHED);
        batchFactory.createTplDeliveryBatch(admin, s1, slot, Batch.TplType.GRAB_EXPRESS, null);
        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveryRepository.findAllByShipmentOrderNumber("Order-1").get(0);
        grabExpressDelivery.setStatus(GrabExpressDelivery.Status.ALLOCATING);
        grabExpressDeliveryRepository.save(grabExpressDelivery);
        // Lalamove
        Shipment s2 = shipmentFactory.createShipment(slot, admin, "Order-2", "Shipment-2");
        Batch batch2 = batchFactory.createBatch(admin, shopper, s2, slot, Batch.Type.SHOPPING, Job.State.FINISHED);
        batchFactory.createTplDeliveryBatch(admin, s2, slot, Batch.TplType.LALAMOVE, Job.State.DELIVERING);
        LalamoveDelivery delivery2 = new LalamoveDelivery();
        delivery2.setTenant(admin.getTenant());
        delivery2.setCreatedBy(admin.getId());
        delivery2.setShipment(s2);
        delivery2.setStatus(LalamoveDelivery.Status.PICKED_UP);
        delivery2.setExternalOrderId("LALAMOVE-111");
        delivery2.setDriverName("John");
        delivery2.setDriverPhone("08192823928");
        delivery2.setDriverPhoto("https://google.com");
        delivery2.setDriverPlateNumber("B 1234 CD");
        delivery2.setServiceType("MOTORCYCLE");
        delivery2.setServiceDescription("Motor");
        delivery2.setTryCount(new Integer(0));
        delivery2.setScheduleAt(LocalDateTime.of(2020, 4, 30, 10, 0));
        lalamoveDeliveryFactory.save(delivery2);
        // should not display FINISHED delivery job
        Shipment s3 = shipmentFactory.createShipment(slot, admin, "Order-3", "Shipment-3");
        batchFactory.createBatch(admin, shopper, s3, slot, Batch.Type.SHOPPING, Job.State.FINISHED);
        batchFactory.createTplDeliveryBatch(admin, s3, slot, Batch.TplType.LALAMOVE, Job.State.FINISHED);
        lalamoveDeliveryFactory.create(s3, admin, LalamoveDelivery.Status.COMPLETED);

        mvc.perform(MockMvcRequestBuilders.get("/api/tpl/pending_deliveries")
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()))
                .andExpect(status().is2xxSuccessful())
                .andExpect(jsonPath("$.tpl_pending_deliveries.grab_express_deliveries", Matchers.hasSize(1)))
                .andExpect(jsonPath("$.tpl_pending_deliveries.lalamove_deliveries", Matchers.hasSize(1)))
                .andExpect(jsonPath("$.tpl_pending_deliveries.grab_express_deliveries[0].order_number", Matchers.equalTo("Order-1")))
                .andExpect(jsonPath("$.tpl_pending_deliveries.grab_express_deliveries[0].status", Matchers.equalTo("ALLOCATING")))
                .andExpect(jsonPath("$.tpl_pending_deliveries.grab_express_deliveries[0].batch_id", Matchers.equalTo(batch1.getId().intValue())))
                .andExpect(jsonPath("$.tpl_pending_deliveries.lalamove_deliveries[0].order_number", Matchers.equalTo("Order-2")))
                .andExpect(jsonPath("$.tpl_pending_deliveries.lalamove_deliveries[0].shipment_number", Matchers.equalTo("Shipment-2")))
                .andExpect(jsonPath("$.tpl_pending_deliveries.lalamove_deliveries[0].batch_id", Matchers.equalTo(batch2.getId().intValue()))) // shopping batch
                .andExpect(jsonPath("$.tpl_pending_deliveries.lalamove_deliveries[0].status", Matchers.equalTo("PICKED_UP")))
                .andExpect(jsonPath("$.tpl_pending_deliveries.lalamove_deliveries[0].external_order_id", Matchers.equalTo("LALAMOVE-111")))
                .andExpect(jsonPath("$.tpl_pending_deliveries.lalamove_deliveries[0].driver_name", Matchers.equalTo("John"))) // 08192823928
                .andExpect(jsonPath("$.tpl_pending_deliveries.lalamove_deliveries[0].driver_phone", Matchers.equalTo("08192823928")))
                .andExpect(jsonPath("$.tpl_pending_deliveries.lalamove_deliveries[0].driver_photo_url", Matchers.equalTo("https://google.com")))
                .andExpect(jsonPath("$.tpl_pending_deliveries.lalamove_deliveries[0].driver_plate_number", Matchers.equalTo("B 1234 CD")))
                .andExpect(jsonPath("$.tpl_pending_deliveries.lalamove_deliveries[0].service_type", Matchers.equalTo("MOTORCYCLE")))
                .andExpect(jsonPath("$.tpl_pending_deliveries.lalamove_deliveries[0].service_description", Matchers.equalTo("Motor")))
                .andExpect(jsonPath("$.tpl_pending_deliveries.lalamove_deliveries[0].slot_start_time", Matchers.equalTo(slot.getStartTime().toString())))
                .andExpect(jsonPath("$.tpl_pending_deliveries.lalamove_deliveries[0].slot_end_time", Matchers.equalTo(slot.getEndTime().toString())));
    }

    /*Should return delyva pending deliveries*/
    @Test
    public void shouldReturnDelyvaPendingDeliveries() throws Exception {
        User admin = userFactory.createUserData(Role.Name.ADMIN);
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        Slot slot = slotFactory.createSlot(stockLocations.get(0), admin);

        // Delyva
        Shipment s1 = shipmentFactory.createShipment(slot, admin, "Order-1", "Shipment-1");
        Batch batch1 = batchFactory.createBatch(admin, shopper, s1, slot, Batch.Type.SHOPPING, Job.State.FINISHED);
        batchFactory.createTplDeliveryBatch(admin, s1, slot, Batch.TplType.DELYVA, Job.State.DELIVERING);
        DelyvaDelivery delyvaDelivery = new DelyvaDelivery();
        delyvaDelivery.setTenant(admin.getTenant());
        delyvaDelivery.setCreatedBy(admin.getId());
        delyvaDelivery.setShipment(s1);
        delyvaDelivery.setStatus(DelyvaStatusCode.ORDER_READY);
        delyvaDelivery.setExternalId("DELYVA-111");
        delyvaDelivery.setDriverName("Christian");
        delyvaDelivery.setDriverPhone("081298765435");
        delyvaDelivery.setDriverPlateNumber("N 6667 SF");
        delyvaDelivery.setVehicleType("MOTORCYCLE");
        delyvaDelivery.setServiceName("DHL");
        delyvaDelivery.setPickUpScheduledAt(LocalDateTime.of(2020, 4, 30, 10, 0));
        delyvaDeliveryFactory.save(delyvaDelivery);

        // Test enableSwitchHF true
        Shipment s2 = shipmentFactory.createShipment(slot, admin, "Order-2", "Shipment-2");
        Batch batch2 = batchFactory.createBatch(admin, shopper, s2, slot, Batch.Type.SHOPPING, Job.State.FINISHED);
        batchFactory.createTplDeliveryBatch(admin, s2, slot, Batch.TplType.DELYVA, Job.State.DELIVERING);
        delyvaDeliveryFactory.create(s2, admin, DelyvaStatusCode.ORDER_CREATED,
                LocalDateTime.of(2020, 4, 25, 10, 0), "DELYVA-112");

        // should not display ORDER_COMPLETED delivery job
        Shipment s3 = shipmentFactory.createShipment(slot, admin, "Order-3", "Shipment-3");
        Batch batch3 = batchFactory.createBatch(admin, shopper, s3, slot, Batch.Type.SHOPPING, Job.State.FINISHED);
        batchFactory.createTplDeliveryBatch(admin, s3, slot, Batch.TplType.DELYVA, Job.State.FINISHED);
        delyvaDeliveryFactory.create(s3, admin, DelyvaStatusCode.ORDER_COMPLETED);

        mvc.perform(MockMvcRequestBuilders.get("/api/tpl/pending_deliveries")
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()))
                .andExpect(status().is2xxSuccessful())
                .andExpect(jsonPath("$.tpl_pending_deliveries.delyva_deliveries", Matchers.hasSize(2)))
                .andExpect(jsonPath("$.tpl_pending_deliveries.delyva_deliveries[0].status", Matchers.equalTo(DelyvaStatusCode.ORDER_READY.name())))
                .andExpect(jsonPath("$.tpl_pending_deliveries.delyva_deliveries[0].order_number", Matchers.equalTo("Order-1")))
                .andExpect(jsonPath("$.tpl_pending_deliveries.delyva_deliveries[0].enable_switch_hf", Matchers.equalTo(false)))
                .andExpect(jsonPath("$.tpl_pending_deliveries.delyva_deliveries[0].batch_id", Matchers.equalTo(batch1.getId().intValue())))
                .andExpect(jsonPath("$.tpl_pending_deliveries.delyva_deliveries[0].service_name", Matchers.equalTo("DHL")))

                .andExpect(jsonPath("$.tpl_pending_deliveries.delyva_deliveries[1].status", Matchers.equalTo(DelyvaStatusCode.ORDER_CREATED.name())))
                .andExpect(jsonPath("$.tpl_pending_deliveries.delyva_deliveries[1].order_number", Matchers.equalTo("Order-2")))
                .andExpect(jsonPath("$.tpl_pending_deliveries.delyva_deliveries[1].enable_switch_hf", Matchers.equalTo(true)))
                .andExpect(jsonPath("$.tpl_pending_deliveries.delyva_deliveries[1].batch_id", Matchers.equalTo(batch2.getId().intValue())));

    }

    @Test
    public void shouldReturnLalamovePendingDeliveriesMaxOneWeekAgo() throws Exception {
        User admin = userFactory.createUserData(Role.Name.ADMIN);
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        Slot slot = slotFactory.createSlot(stockLocations.get(0), admin);

        // Lalamove
        Shipment s2 = shipmentFactory.createShipment(slot, admin, "Order-2", "Shipment-2");
        Batch batch2 = batchFactory.createBatch(admin, shopper, s2, slot, Batch.Type.SHOPPING, Job.State.FINISHED);
        batchFactory.createTplDeliveryBatch(admin, s2, slot, Batch.TplType.LALAMOVE, Job.State.DELIVERING);
        LalamoveDelivery delivery2 = new LalamoveDelivery();
        delivery2.setTenant(admin.getTenant());
        delivery2.setCreatedBy(admin.getId());
        delivery2.setShipment(s2);
        delivery2.setStatus(LalamoveDelivery.Status.PICKED_UP);
        delivery2.setExternalOrderId("LALAMOVE-111");
        delivery2.setDriverName("John");
        delivery2.setDriverPhone("08192823928");
        delivery2.setDriverPhoto("https://google.com");
        delivery2.setDriverPlateNumber("B 1234 CD");
        delivery2.setServiceType("MOTORCYCLE");
        delivery2.setServiceDescription("Motor");
        delivery2.setTryCount(new Integer(0));
        delivery2.setScheduleAt(LocalDateTime.of(2020, 4, 30, 10, 0));
        lalamoveDeliveryFactory.save(delivery2);
        // should not display FINISHED delivery job
        Shipment s3 = shipmentFactory.createShipment(slot, admin, "Order-3", "Shipment-3");
        batchFactory.createBatch(admin, shopper, s3, slot, Batch.Type.SHOPPING, Job.State.FINISHED);
        batchFactory.createTplDeliveryBatch(admin, s3, slot, Batch.TplType.LALAMOVE, Job.State.FINISHED);
        lalamoveDeliveryFactory.create(s3, admin, LalamoveDelivery.Status.COMPLETED);

        // should not display more than one week ago
        Shipment s4 = shipmentFactory.createShipment(slot, admin, "Order-4", "Shipment-4");
        Batch batch = batchFactory.createBatch(admin, shopper, s4, slot, Batch.Type.SHOPPING, Job.State.FINISHED);
        Batch tplDeliveryBatch = batchFactory.createTplDeliveryBatch(admin, s4, slot, Batch.TplType.LALAMOVE, Job.State.ACCEPTED);

        batch.setStartTime(batch.getStartTime().minusDays(8));
        batch.setEndTime(batch.getEndTime().minusDays(8));

        tplDeliveryBatch.setStartTime(tplDeliveryBatch.getStartTime().minusDays(8));
        tplDeliveryBatch.setEndTime(tplDeliveryBatch.getEndTime().minusDays(8));

        batchFactory.saveAll(Lists.asList(batch, tplDeliveryBatch));

        lalamoveDeliveryFactory.create(s4, admin, LalamoveDelivery.Status.PICKED_UP);

        mvc.perform(MockMvcRequestBuilders.get("/api/tpl/pending_deliveries")
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()))
                .andDo(print())
                .andExpect(status().is2xxSuccessful())
                .andExpect(jsonPath("$.tpl_pending_deliveries.grab_express_deliveries", Matchers.hasSize(0)))
                .andExpect(jsonPath("$.tpl_pending_deliveries.lalamove_deliveries", Matchers.hasSize(1)))
                .andExpect(jsonPath("$.tpl_pending_deliveries.delyva_deliveries", Matchers.hasSize(0)))
        ;
    }
}
