package com.happyfresh.fulfillment.integrationTest.test.grabExpress;

import com.happyfresh.fulfillment.common.messaging.kafka.KafkaMessage;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaTopicConfig;
import com.happyfresh.fulfillment.entity.Batch;
import com.happyfresh.fulfillment.entity.GrabExpressDelivery;
import com.happyfresh.fulfillment.entity.Job;
import com.happyfresh.fulfillment.entity.StockLocation;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

public class WebhookPickingUpTest extends WebhookTest {

    @MockBean
    KafkaMessage kafkaMessage;

    @Test
    public void updateGrabExpressDeliveryIfExist() throws Exception {
        long timestamp = LocalDateTime.now().atZone(ZoneId.of("UTC")).toEpochSecond();
        JSONObject payload = grabExpressWebhookFactory.createGEPickingUpPayload(deliveryId, timestamp);

        mvc.perform(MockMvcRequestBuilders.post("/api/grab_express/webhook")
                .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON).content(payload.toString()))
                .andExpect(MockMvcResultMatchers.status().isOk());

        List<GrabExpressDelivery> grabExpressDeliveries = grabExpressDeliveryRepository.findAll();
        Assert.assertEquals(1, grabExpressDeliveries.size());

        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertEquals(GrabExpressDelivery.Status.PICKING_UP, grabExpressDelivery.getStatus());
        Assert.assertEquals(payload.get("pickupPin"), grabExpressDelivery.getPin());
        JSONObject driverPayload = payload.getJSONObject("driver");
        Assert.assertEquals(driverPayload.get("name"), grabExpressDelivery.getDriverName());
        Assert.assertEquals(driverPayload.get("phone"), grabExpressDelivery.getDriverPhone());
        Assert.assertEquals(driverPayload.get("photoURL"), grabExpressDelivery.getDriverPhotoUrl());

        LocalDateTime latestStatusChangedAt = LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp),
                ZoneId.of("UTC"));
        Assert.assertEquals(latestStatusChangedAt, grabExpressDelivery.getLatestStatusChangedAt());
        Assert.assertEquals(shipment.getNumber(), grabExpressDelivery.getShipment().getNumber());

        List<Batch> batches = batchRepository.findByType(Batch.Type.DELIVERY);
        Assert.assertNotNull(batches.get(0).getUser());

        List<Job> jobs = jobRepository.findAllByType(Job.Type.DRIVER);
        Assert.assertEquals(Job.State.INITIAL, jobs.get(0).getState());

        Mockito.verify(kafkaMessage, Mockito.never())
                .publish(eq(KafkaTopicConfig.STRATO_INTEGRATION_TOPIC), anyString(), anyString());
    }

    @Test
    public void updateGrabExpressDeliveryIfExist_andSendWebhookIfFulfilledByStrato() throws Exception {
        stockLocation.setEnabler(StockLocation.Enabler.HFC);
        stockLocation.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);
        stockLocationFactory.save(stockLocation);
        Thread.sleep(100);

        long timestamp = LocalDateTime.now().atZone(ZoneId.of("UTC")).toEpochSecond();
        JSONObject payload = grabExpressWebhookFactory.createGEPickingUpPayload(deliveryId, timestamp);

        mvc.perform(MockMvcRequestBuilders.post("/api/grab_express/webhook")
                .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON).content(payload.toString()))
                .andExpect(MockMvcResultMatchers.status().isOk());

        Thread.sleep(100);
        List<GrabExpressDelivery> grabExpressDeliveries = grabExpressDeliveryRepository.findAll();
        Assert.assertEquals(1, grabExpressDeliveries.size());

        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertEquals(GrabExpressDelivery.Status.PICKING_UP, grabExpressDelivery.getStatus());
        Assert.assertEquals(payload.get("pickupPin"), grabExpressDelivery.getPin());
        JSONObject driverPayload = payload.getJSONObject("driver");
        Assert.assertEquals(driverPayload.get("name"), grabExpressDelivery.getDriverName());
        Assert.assertEquals(driverPayload.get("phone"), grabExpressDelivery.getDriverPhone());
        Assert.assertEquals(driverPayload.get("photoURL"), grabExpressDelivery.getDriverPhotoUrl());

        LocalDateTime latestStatusChangedAt = LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp),
                ZoneId.of("UTC"));
        Assert.assertEquals(latestStatusChangedAt, grabExpressDelivery.getLatestStatusChangedAt());
        Assert.assertEquals(shipment.getNumber(), grabExpressDelivery.getShipment().getNumber());

        List<Batch> batches = batchRepository.findByType(Batch.Type.DELIVERY);
        Assert.assertNotNull(batches.get(0).getUser());

        List<Job> jobs = jobRepository.findAllByType(Job.Type.DRIVER);
        Assert.assertEquals(Job.State.INITIAL, jobs.get(0).getState());

        Mockito.verify(kafkaMessage, Mockito.atLeastOnce())
                .publish(eq(KafkaTopicConfig.STRATO_INTEGRATION_TOPIC), anyString(), anyString());
    }
}
