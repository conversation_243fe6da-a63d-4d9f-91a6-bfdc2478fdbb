package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.ItemRepository;
import org.apache.commons.lang3.RandomStringUtils;
import org.hamcrest.Matchers;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class ItemReplacementWithOtherOrderedItemTest extends BaseTest {
    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private ItemRepository itemRepository;

    @Autowired
    private CountryFactory countryFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private  ItemFactory itemFactory;

    @Autowired
    private TransactionHelper transactionHelper;

    private User shopper;
    private User systemAdmin;
    private List<StockLocation> stockLocations;
    private List<Slot> slots;
    private Batch batch;
    private Shipment shipment;
    private Item replacedItem;
    private Item otherOrderedItem;
    private Item replacementItem;
    private JSONObject foundPayload;
    private JSONObject replacementPayload;

    @Before
    public void setup() throws Exception {
        shopper = userFactory.createUserData(Role.Name.SHOPPER);
        systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, shopper.getTenant());
        stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper);
        slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, shopper);
        batch = batchFactory.createBatch(shopper, slots.get(0), Batch.Type.SHOPPING);
        shipment = batch.getJobs().get(0).getShipment();
        replacedItem = shipment.getItems().get(0);
        otherOrderedItem = shipment.getItems().get(1);
        replacementItem = createReplacementItem(otherOrderedItem);
        foundPayload = createFoundPayload(otherOrderedItem);
        replacementPayload = createReplacementPayload(otherOrderedItem.getSku(), replacedItem.getRequestedQty(), otherOrderedItem.getActualWeight());
    }

    @Test
    public void withFoundFirstThenReplace() throws Exception {
        startShopping(shopper, batch);
        foundOtherItem(shopper, batch, shipment, otherOrderedItem, foundPayload);

        replacementItem.setReplacedItem(replacedItem);
        itemRepository.save(replacementItem);
        replaceItem(shopper, batch, shipment, replacedItem, replacementPayload);

        transactionHelper.withNewTransaction(() -> {
            List<Item> itemsWithSameSku = itemRepository.findAllByShipmentIdAndSku(shipment.getId(), otherOrderedItem.getSku());
            Assert.assertEquals(2, itemsWithSameSku.size());
        });
    }

    @Test
    public void withReplaceFirstThenFound() throws Exception {
        startShopping(shopper, batch);

        replacementItem.setReplacedItem(replacedItem);
        itemRepository.save(replacementItem);
        replaceItem(shopper, batch, shipment, replacedItem, replacementPayload);

        foundOtherItem(shopper, batch, shipment, otherOrderedItem, foundPayload);

        transactionHelper.withNewTransaction(() -> {
            List<Item> itemsWithSameSku = itemRepository.findAllByShipmentIdAndSku(shipment.getId(), otherOrderedItem.getSku());
            Assert.assertEquals(2, itemsWithSameSku.size());
        });
    }

    private ResultActions startShopping(User shopper, Batch batch) throws Exception {
        return mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + batch.getId() + "/start")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()));
    }

    private ResultActions replaceItem(User shopper, Batch batch, Shipment shipment, Item replacedItem, JSONObject replacementPayload) throws Exception {
        return mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + batch.getId() + "/shipments/" + shipment.getNumber() + "/items/" + replacedItem.getSku() + "/replacements")
            .header("X-Fulfillment-User-Token", shopper.getToken())
            .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(replacementPayload.toString()))
        .andDo(print())
        .andExpect(jsonPath("$.item.replacements[0].sku", Matchers.equalTo(replacementPayload.get("sku"))))
        .andExpect(jsonPath("$.item.replacements[0].total_shopped", Matchers.equalTo(replacementPayload.get("quantity"))))
        .andExpect(status().isOk());
    }

    private ResultActions foundOtherItem(User shopper, Batch batch, Shipment shipment, Item otherOrderedItem, JSONObject foundPayload) throws Exception {
        return mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + batch.getId() + "/shipments/" + shipment.getNumber() + "/items/" + otherOrderedItem.getSku() + "/shop")
            .header("X-Fulfillment-User-Token", shopper.getToken())
            .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(foundPayload.toString()))
        .andDo(print())
        .andExpect(jsonPath("$.item.total_shopped", Matchers.equalTo(2)))
        .andExpect(status().isOk());
    }

    private JSONObject createFoundPayload(Item otherOrderedItem) throws JSONException {
        JSONObject foundPayload = new JSONObject();
        foundPayload.put("quantity", otherOrderedItem.getRequestedQty());
        foundPayload.put("shopper_notes_fulfilled", true);
        foundPayload.put("weight", 1.3);
        return foundPayload;
    }

    private JSONObject createReplacementPayload(String sku, int quantity, Double actualWeight) throws Exception {
        JSONObject itemObj = new JSONObject();
        itemObj.put("sku", sku);
        itemObj.put("quantity", quantity);
        itemObj.put("actual_weight", actualWeight);

        return itemObj;
    }

    private Item createReplacementItem(Item replacementItem) {
        Item item = new Item();
        item.setCurrency(replacementItem.getCurrency());
        item.setShipment(replacementItem.getShipment());
        item.setSku(replacementItem.getSku());
        item.setTranslationNames(replacementItem.getTranslationNames());
        item.setImageUrl(replacementItem.getImageUrl());
        item.setReplacementType("ReplacementType-" + RandomStringUtils.random(5, true, false));
        item.setSupermarketUnit(replacementItem.getSupermarketUnit());
        item.setSupermarketUnitCostPrice(replacementItem.getSupermarketUnitCostPrice());
        item.setPrice(replacementItem.getPrice());
        item.setCostPrice(replacementItem.getCostPrice());
        item.setHeight(replacementItem.getHeight());
        item.setWidth(replacementItem.getWidth());
        item.setDepth(replacementItem.getDepth());
        item.setActualWeight(replacementItem.getWeight());
        item.setAverageWeight(replacementItem.getWeight());
        item.setRequestedQty(replacementItem.getRequestedQty());
        item.setBundleQty(replacementItem.getBundleQty());
        item.setFreeQty(replacementItem.getFreeQty());
        item.setFoundQty(replacementItem.getFoundQty());
        item.setOosQty(replacementItem.getOosQty());
        item.setRejectedQty(replacementItem.getRejectedQty());
        item.setCategory(replacementItem.getCategory());
        item.setCreatedBy(replacementItem.getCreatedBy());
        item.setTenant(replacementItem.getTenant());
        item.setConsideredAsAlcohol(replacementItem.isConsideredAsAlcohol());
        item.setConsideredAsGeRestrictedProduct(replacementItem.isConsideredAsGeRestrictedProduct());
        item.setShopperNotes("");
        item.setNormalCostPrice(replacementItem.getNormalCostPrice());
        item.setNormalPrice(replacementItem.getNormalPrice());
        item.setTranslationDescriptions(replacementItem.getTranslationDescriptions());
        item.setPrice(replacementItem.getPrice());
        item.setCostPrice(replacementItem.getCostPrice());
        item.setSupermarketUnitCostPrice(replacementItem.getSupermarketUnitCostPrice());
        item.setHeight(replacementItem.getHeight());
        item.setWidth(replacementItem.getWidth());
        item.setDepth(replacementItem.getDepth());
        item.setActualWeight(replacementItem.getWeight());
        item.setAverageWeight(replacementItem.getWeight());
        item.setRequestedQty(replacementItem.getRequestedQty());
        item.setFoundQty(replacementItem.getFoundQty());
        item.setOosQty(replacementItem.getOosQty());
        item.setRejectedQty(replacementItem.getRejectedQty());

        return item;
    }
}

