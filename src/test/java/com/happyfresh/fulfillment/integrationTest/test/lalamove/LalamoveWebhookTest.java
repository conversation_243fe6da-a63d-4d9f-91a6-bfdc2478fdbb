package com.happyfresh.fulfillment.integrationTest.test.lalamove;

import com.amazonaws.util.Base16Lower;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaMessage;
import com.happyfresh.fulfillment.common.property.LalamoveProperty;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.MockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.lalamove.form.v3.LalamoveV3WebhookForm;
import com.happyfresh.fulfillment.lalamove.service.LalamoveDeliveryMessagingService;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.LalamoveDeliveryRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.repository.TenantRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.rule.OutputCapture;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.nio.file.Files.readAllBytes;
import static java.nio.file.Paths.get;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.not;
import static org.junit.Assert.assertThat;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

public class LalamoveWebhookTest extends BaseTest {

    @Rule
    public OutputCapture output = new OutputCapture();
    private static final String BASE_URL = "https://sandbox-rest.lalamove.com/";
    private static final String API_VERSION_3 = "v3";

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private LalamoveDeliveryFactory lalamoveDeliveryFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private LalamoveProperty lalamoveProperty;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private LalamoveDeliveryRepository deliveryRepository;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private JobFactory jobFactory;

    @MockBean
    private LalamoveDeliveryMessagingService lalamoveDeliveryMessagingService;

    @Autowired
    private ObjectMapper mapper;

    @MockBean
    private KafkaMessage kafkaMessage;

    @Autowired
    private MockServerHelper mockServerHelper;

    @Autowired
    private BatchRepository batchRepository;

    private User user;
    protected User externalSystemAdmin;
    private StockLocation stockLocation;
    private Shipment shipment;
    private LalamoveDelivery delivery;
    private Slot slot;
    private Batch shoppingBatch;
    private Batch deliveryBatch;

    @Before
    public void setUp() throws Exception {
        user = userFactory.createUserData(Role.Name.SYSTEM_ADMIN);
        externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, user.getTenant());
        userFactory.createUserData(Role.Name.LALAMOVE_DRIVER, user.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user);
        slot = slotFactory.createSlot(stockLocations.get(0), user);

        stockLocation = stockLocations.get(0);
        shipment = shipmentFactory.createShipment(slot, externalSystemAdmin, "R234567", "H234567");

        shoppingBatch = batchFactory.createBatch(externalSystemAdmin, shipment, slot, Batch.Type.SHOPPING);
        deliveryBatch = batchFactory.createBatch(externalSystemAdmin, shipment, slot, Batch.Type.DELIVERY);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.LALAMOVE);
        batchFactory.save(deliveryBatch);

        delivery = lalamoveDeliveryFactory.create(shipment, user);
    }

    private void configureLalamoveV3() {
        Tenant tenant = stockLocation.getTenant();
        Map<String, String> preferences = tenant.getPreferences();
        preferences.put("enable_lalamove_api_v3", String.valueOf(true));
        tenantRepository.save(tenant);
    }

    private void placeOrder(String orderId) {
        delivery.setStatus(LalamoveDelivery.Status.ORDER_PLACED);
        delivery.setExternalOrderId(orderId);
        delivery.setCurrency("IDR");
        delivery.setPrice(new BigDecimal(10000));
        lalamoveDeliveryFactory.save(delivery);
    }

    private String getResponseFromResourceFileAsString(String fileName) throws IOException {
        return new String(readAllBytes(get("src", "test", "resources", "fixtures", fileName)));
    }

    private String generateSignature(LalamoveV3WebhookForm form) {
        final String apiSecret = lalamoveProperty.getApiSecrets().get("id");
        final String httpMethod = "POST";
        final String path = LalamoveV3WebhookForm.WEBHOOK_PATH;
        try {
            String body = mapper.writer().writeValueAsString(form.getData());
            String rawSignature = form.getTimestamp() + "\r\n" + httpMethod + "\r\n" +
                    path + "\r\n\r\n" + body;

            Mac sha256HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(apiSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256HMAC.init(secretKey);

            return Base16Lower.encodeAsString(sha256HMAC.doFinal(rawSignature.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "";
    }

    private LalamoveV3WebhookForm getLalamoveV3WebhookForm(String fileName, String eventVersion, String orderId,
                                                           String apiKey) throws IOException {
        String json = getResponseFromResourceFileAsString(fileName);
        LalamoveV3WebhookForm form = mapper.readValue(json, LalamoveV3WebhookForm.class);
        form.setApiKey(apiKey);
        form.setTimestamp(LocalDateTime.now().plusSeconds(60).toEpochSecond(ZoneOffset.UTC));
        form.setEventVersion(eventVersion);
        form.getData().getOrder().setOrderId(orderId);
        return form;
    }

    private void assertSwitchToHf(LalamoveDelivery.Status status){
        transactionHelper.withNewTransaction(() -> {
            Optional<LalamoveDelivery> maybeUpdated = deliveryRepository.findById(delivery.getId());
            Assert.assertTrue(maybeUpdated.isPresent());
            LalamoveDelivery updated = maybeUpdated.get();
            Assert.assertEquals(status, updated.getStatus());
            Assert.assertNotEquals(delivery.getLatestStatusChangedAt(), updated.getLatestStatusChangedAt());
            Optional<Batch> oUpdatedBatch = batchRepository.findById(deliveryBatch.getId());
            Batch updatedBatch = oUpdatedBatch.get();
            Assert.assertEquals(Batch.DeliveryType.NORMAL, updatedBatch.getDeliveryType());
            Assert.assertNull(updatedBatch.getTplType());
            Assert.assertNull(updatedBatch.getUser());
            Assert.assertTrue(updatedBatch.isSwitchedToHf());

        });
    }

    private void setupDriverDetailMockServer(String version, String orderId, String driverId) throws URISyntaxException {
        String driverResponseBody = String.format("{\"data\":{\"driverId\":\"%s\",\"name\":\"TestDriver 88884\",\"phone\":\"+6288888884\",\"plateNumber\":\"**751947*\",\"photo\":\"https://photo-url.web/driver123.jpg\"}}", driverId);
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        String uri = BASE_URL + version + "/orders/" + orderId + "/drivers/" + driverId;
        mockServer.expect(ExpectedCount.once(), requestTo(new URI(uri)))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(driverResponseBody));
    }

    private void sendWebhook(LalamoveV3WebhookForm form) throws Exception {
        mvc.perform(MockMvcRequestBuilders.post("/api/lalamove/v3/webhook?country=ID")
                        .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON).content(mapper.writeValueAsString(form)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print());
    }

    @Test
    public void webhook_invalidSignature() throws Exception {
        String orderId = "ABC1234";
        placeOrder(orderId);

        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_assigning_driver.json", API_VERSION_3,
                orderId, lalamoveProperty.getApiKeys().get(stockLocation.getState().getCountry().getIsoName().toLowerCase()));

        mvc.perform(MockMvcRequestBuilders.post("/api/lalamove/v3/webhook?country=ID")
                        .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON).content(mapper.writeValueAsString(form)))
                .andExpect(MockMvcResultMatchers.status().is4xxClientError());
    }

    @Test
    public void webhook_whenSendEmptyBodyRequest_shouldReturn200() throws Exception {
        mvc.perform(MockMvcRequestBuilders.post("/api/lalamove/v3/webhook?country=ID")
                        .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void webhook_whenStatusChangedToAssigningDriver_shouldUpdateOrderDetail() throws Exception {
        configureLalamoveV3();
        String orderId = "ABC1234";
        placeOrder(orderId);

        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_assigning_driver.json", API_VERSION_3,
                orderId, lalamoveProperty.getApiKeys().get(stockLocation.getState().getCountry().getIsoName().toLowerCase()));
        form.setSignature(generateSignature(form));

        sendWebhook(form);

        transactionHelper.withNewTransaction(() -> {
            Optional<LalamoveDelivery> maybeUpdated = deliveryRepository.findById(delivery.getId());
            Assert.assertTrue(maybeUpdated.isPresent());
            LalamoveDelivery updated = maybeUpdated.get();
            Assert.assertEquals(LalamoveDelivery.Status.ASSIGNING_DRIVER, updated.getStatus());
            Assert.assertNotEquals(delivery.getLatestStatusChangedAt(), updated.getLatestStatusChangedAt());
        });
    }

    @Test
    public void webhook_whenStatusChangedToOngoing_shouldUpdateOrderDetail() throws Exception {
        configureLalamoveV3();
        String orderId = "ABC1234";
        placeOrder(orderId);
        delivery.setStatus(LalamoveDelivery.Status.ASSIGNING_DRIVER);
        deliveryRepository.save(delivery);

        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_on_going.json", API_VERSION_3,
                orderId, lalamoveProperty.getApiKeys().get(stockLocation.getState().getCountry().getIsoName().toLowerCase()));
        form.setSignature(generateSignature(form));

        setupDriverDetailMockServer(API_VERSION_3, form.getData().getOrder().getOrderId(), form.getData().getOrder().getDriverId());

        sendWebhook(form);

        transactionHelper.withNewTransaction(() -> {
            Optional<LalamoveDelivery> maybeUpdated = deliveryRepository.findById(delivery.getId());
            Assert.assertTrue(maybeUpdated.isPresent());
            LalamoveDelivery updated = maybeUpdated.get();
            Assert.assertEquals(LalamoveDelivery.Status.ON_GOING, updated.getStatus());
            Assert.assertNotEquals(delivery.getLatestStatusChangedAt(), updated.getLatestStatusChangedAt());
            Assert.assertEquals(form.getData().getOrder().getDriverId(), updated.getDriverId().toString());
        });
    }

    @Test
    public void webhook_whenStatusChangedToPickup_shouldUpdateOrderDetail() throws Exception {
        configureLalamoveV3();
        String orderId = "ABC1234";
        placeOrder(orderId);

        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_picked_up.json", API_VERSION_3,
                orderId, lalamoveProperty.getApiKeys().get(stockLocation.getState().getCountry().getIsoName().toLowerCase()));
        form.setSignature(generateSignature(form));

        delivery.setStatus(LalamoveDelivery.Status.ON_GOING);
        delivery.setDriverId(Long.valueOf(form.getData().getOrder().getDriverId()));
        deliveryRepository.save(delivery);

        setupDriverDetailMockServer(API_VERSION_3, form.getData().getOrder().getOrderId(), form.getData().getOrder().getDriverId());

        sendWebhook(form);

        transactionHelper.withNewTransaction(() -> {
            Optional<LalamoveDelivery> maybeUpdated = deliveryRepository.findById(delivery.getId());
            Assert.assertTrue(maybeUpdated.isPresent());
            LalamoveDelivery updated = maybeUpdated.get();
            Assert.assertEquals(LalamoveDelivery.Status.PICKED_UP, updated.getStatus());
            Assert.assertNotEquals(delivery.getLatestStatusChangedAt(), updated.getLatestStatusChangedAt());
            Assert.assertEquals(form.getData().getOrder().getDriverId(), updated.getDriverId().toString());
        });
    }

    @Test
    public void webhook_whenStatusChangedToCompleted_shouldUpdateOrderDetail() throws Exception {
        configureLalamoveV3();
        String orderId = "ABC1234";
        placeOrder(orderId);

        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_completed.json", API_VERSION_3,
                orderId, lalamoveProperty.getApiKeys().get(stockLocation.getState().getCountry().getIsoName().toLowerCase()));
        form.setSignature(generateSignature(form));

        delivery.setStatus(LalamoveDelivery.Status.ON_GOING);
        delivery.setDriverId(Long.valueOf(form.getData().getOrder().getDriverId()));
        deliveryRepository.save(delivery);

        transactionHelper.withNewTransaction(() -> {
            shipment = shipmentRepository.findByOrderNumber(shipment.getOrderNumber());
            Job shoppingJob = shipment.getShoppingJob().orElse(null);
            Job deliveryJob = shipment.getDeliveryJob().orElse(null);
            assert shoppingJob != null;
            assert deliveryJob != null;
            shoppingJob.setState(Job.State.STARTED);
            shoppingJob.setState(Job.State.FINALIZING);
            shoppingJob.setState(Job.State.FINISHED);
            jobFactory.save(shoppingJob);
            deliveryJob.setState(Job.State.DELIVERING);
            jobFactory.save(deliveryJob);
        });

        setupDriverDetailMockServer(API_VERSION_3, form.getData().getOrder().getOrderId(), form.getData().getOrder().getDriverId());

        sendWebhook(form);

        transactionHelper.withNewTransaction(() -> {
            Optional<LalamoveDelivery> maybeUpdated = deliveryRepository.findById(delivery.getId());
            Assert.assertTrue(maybeUpdated.isPresent());
            LalamoveDelivery updated = maybeUpdated.get();
            Assert.assertEquals(LalamoveDelivery.Status.COMPLETED, updated.getStatus());
            Assert.assertNotEquals(delivery.getLatestStatusChangedAt(), updated.getLatestStatusChangedAt());
            Assert.assertEquals(form.getData().getOrder().getDriverId(), updated.getDriverId().toString());
        });
    }

    @Test
    public void webhook_whenStatusChangedToExpired_shouldSwitchToHf() throws Exception {
        configureLalamoveV3();
        String orderId = "ABC1234";
        placeOrder(orderId);

        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_expired.json", API_VERSION_3,
                orderId, lalamoveProperty.getApiKeys().get(stockLocation.getState().getCountry().getIsoName().toLowerCase()));
        form.setSignature(generateSignature(form));

        delivery.setStatus(LalamoveDelivery.Status.ASSIGNING_DRIVER);
        deliveryRepository.save(delivery);

        sendWebhook(form);
        assertSwitchToHf(LalamoveDelivery.Status.EXPIRED);
    }

    @Test
    public void webhook_whenStatusChangedToCanceled_shouldSwitchToHf() throws Exception {
        configureLalamoveV3();
        String orderId = "ABC1234";
        placeOrder(orderId);

        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_canceled.json", API_VERSION_3,
                orderId, lalamoveProperty.getApiKeys().get(stockLocation.getState().getCountry().getIsoName().toLowerCase()));
        form.setSignature(generateSignature(form));

        delivery.setStatus(LalamoveDelivery.Status.ASSIGNING_DRIVER);
        deliveryRepository.save(delivery);

        sendWebhook(form);
        assertSwitchToHf(LalamoveDelivery.Status.CANCELED);
    }

    @Test
    public void webhook_whenStatusChangedToRejected_shouldSwitchToHf() throws Exception {
        configureLalamoveV3();
        String orderId = "ABC1234";
        placeOrder(orderId);

        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_rejected.json", API_VERSION_3,
                orderId, lalamoveProperty.getApiKeys().get(stockLocation.getState().getCountry().getIsoName().toLowerCase()));
        form.setSignature(generateSignature(form));

        delivery.setStatus(LalamoveDelivery.Status.ON_GOING);
        delivery.setDriverId(80031L);
        deliveryRepository.save(delivery);

        setupDriverDetailMockServer(API_VERSION_3, form.getData().getOrder().getOrderId(), "80031");

        sendWebhook(form);
        assertSwitchToHf(LalamoveDelivery.Status.REJECTED);
    }

    @Test
    public void webhook_whenDriverAssigned_shouldUpdateDriverDetail() throws Exception {
        configureLalamoveV3();
        String orderId = "ABC1234";
        placeOrder(orderId);

        LalamoveV3WebhookForm form = getLalamoveV3WebhookForm("lalamove_v3_webhook_driver_assigned.json", API_VERSION_3,
                orderId, lalamoveProperty.getApiKeys().get(stockLocation.getState().getCountry().getIsoName().toLowerCase()));
        form.setSignature(generateSignature(form));

        delivery.setStatus(LalamoveDelivery.Status.ON_GOING);
        delivery.setDriverId(Long.valueOf(form.getData().getDriver().getDriverId()));
        deliveryRepository.save(delivery);

        sendWebhook(form);

        transactionHelper.withNewTransaction(() -> {
            Optional<LalamoveDelivery> maybeUpdated = deliveryRepository.findById(delivery.getId());
            Assert.assertTrue(maybeUpdated.isPresent());
            LalamoveDelivery updated = maybeUpdated.get();
            Assert.assertEquals(LalamoveDelivery.Status.ON_GOING, updated.getStatus());
            Assert.assertNotEquals(delivery.getLatestStatusChangedAt(), updated.getLatestStatusChangedAt());
            Assert.assertEquals(form.getData().getDriver().getDriverId(), updated.getDriverId().toString());
            Assert.assertEquals(form.getData().getDriver().getName(), updated.getDriverName());
        });
    }

    @Test
    public void webhook_whenWalletBalanceChanged_shouldNotGetProcessed() throws Exception {
        configureLalamoveV3();
        String orderId = "ABC1234";
        placeOrder(orderId);

        String webhook = "{\"apiKey\":\"pk_test_93cb722cb4b0b47cc9debec02bda9dcc\",\"timestamp\":1657083936,\"signature\":\"011564a3fdcc8ed0e4f6adf42c671ca76779a694b1dfa249aa883e6dc454e1ea\",\"eventId\":\"F0E5EA79-6EB9-063E-EFE0-286D78CCACA2\",\"eventType\":\"WALLET_BALANCE_CHANGED\",\"eventVersion\":\"v3\",\"data\":{\"balance\":{\"currency\":\"THB\",\"amount\":\"21825\"},\"updatedAt\":\"2022-07-06T12:05.00Z\"}}";
        LalamoveV3WebhookForm form = mapper.readValue(webhook, LalamoveV3WebhookForm.class);
        form.setSignature(generateSignature(form));

        delivery.setStatus(LalamoveDelivery.Status.ORDER_PLACED);
        deliveryRepository.save(delivery);

        sendWebhook(form);

        transactionHelper.withNewTransaction(() -> {
            Optional<LalamoveDelivery> maybeUpdated = deliveryRepository.findById(delivery.getId());
            Assert.assertTrue(maybeUpdated.isPresent());
            LalamoveDelivery updated = maybeUpdated.get();
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, updated.getStatus());
            Assert.assertEquals(delivery.getLatestStatusChangedAt(), updated.getLatestStatusChangedAt());

        });

        assertThat(output.toString(), not(containsString("Error when processing lalamove webhook")));
    }

    @Test
    public void webhook_whenDelayedWebhook_shouldUpdateBaseOnLastWebhook() throws Exception {
        configureLalamoveV3();
        String orderId = "ABC1234";
        placeOrder(orderId);

        LalamoveV3WebhookForm ongoingForm = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_on_going.json", API_VERSION_3,
                orderId, lalamoveProperty.getApiKeys().get(stockLocation.getState().getCountry().getIsoName().toLowerCase()));
        ongoingForm.setSignature(generateSignature(ongoingForm));

        Thread.sleep(2000);

        LalamoveV3WebhookForm pickUpForm = getLalamoveV3WebhookForm("lalamove_v3_webhook_order_status_changed_picked_up.json", API_VERSION_3,
                orderId, lalamoveProperty.getApiKeys().get(stockLocation.getState().getCountry().getIsoName().toLowerCase()));
        pickUpForm.setSignature(generateSignature(pickUpForm));

        delivery.setStatus(LalamoveDelivery.Status.ASSIGNING_DRIVER);
        deliveryRepository.save(delivery);

        setupDriverDetailMockServer(API_VERSION_3, pickUpForm.getData().getOrder().getOrderId(), pickUpForm.getData().getOrder().getDriverId());

        sendWebhook(pickUpForm);
        sendWebhook(ongoingForm);

        transactionHelper.withNewTransaction(() -> {
            Optional<LalamoveDelivery> maybeUpdated = deliveryRepository.findById(delivery.getId());
            Assert.assertTrue(maybeUpdated.isPresent());
            LalamoveDelivery updated = maybeUpdated.get();
            Assert.assertEquals(LalamoveDelivery.Status.PICKED_UP, updated.getStatus());
            Assert.assertNotEquals(delivery.getLatestStatusChangedAt(), updated.getLatestStatusChangedAt());
            Assert.assertEquals(pickUpForm.getData().getOrder().getDriverId(), updated.getDriverId().toString());
            Assert.assertEquals("TestDriver 88884", updated.getDriverName());
            Assert.assertEquals("**751947*", updated.getDriverPlateNumber());
        });
    }
}
