package com.happyfresh.fulfillment.integrationTest.test.lezCash;

import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressDeliveryContent;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.lezcash.model.LezCashPayload;
import com.happyfresh.fulfillment.lezcash.service.LezCashContent;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.CountryRepository;
import com.happyfresh.fulfillment.repository.JobRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class LezCashContentTest extends BaseTest {
    @Autowired
    private GrabExpressDeliveryContent grabExpressDeliveryContent;

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private CountryRepository countryRepository;

    private Country country;

    private Shipment shipment;

    private User shopper;

    private StockLocation stockLocation;

    @Before
    public void setup() {}

    @Test
    public void constructContentTest() {
        shopper = userFactory.createUserData(Role.Name.SHOPPER);
        stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, shopper, 0, 1);
        Batch shoppingBatch = batchFactory.createBatch(shopper, slot, Batch.Type.SHOPPING);
        shoppingBatch.setUser(shopper);
        shoppingBatch.getJobs().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });
        batchRepository.save(shoppingBatch);

        shipment = shoppingBatch.getJobs().get(0).getShipment();
        shipment.setJobs(shoppingBatch.getJobs());
        shipment.setOrderPaymentMethod(Shipment.COD_PAYMENT_TYPE);
        shipmentRepository.save(shipment);
        country = shipment.getSlot().getStockLocation().getState().getCountry();

        LezCashContent content = new LezCashContent();
        LezCashPayload payload = content.constructContent(shoppingBatch);
        Assert.assertEquals(1, payload.getItems().size());
    }
}
