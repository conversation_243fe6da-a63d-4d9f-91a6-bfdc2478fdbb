package com.happyfresh.fulfillment.integrationTest.test.shipment;

import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.CountryRepository;
import com.happyfresh.fulfillment.repository.StockLocationRepository;
import com.happyfresh.fulfillment.shipment.presenter.VirtualAccountEligibilityPresenter;
import org.hamcrest.Matchers;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

public class GetPaymentEligibilityTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private CountryFactory countryFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private ItemFactory itemFactory;

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private StockLocationRepository stockLocationRepository;

    private User systemAdmin;

    private Shipment shipment;

    private Country country;

    private Batch deliveryBatch;

    private List<Slot> slots;

    @Before
    public void setup() throws Exception {
        systemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, systemAdmin);
        country = countryRepository.findByIsoName("ID");
        country.setPreferences(new HashMap<String, String>()
        {{
            put("grab_express_max_delivery_volume", "30"); // 30 Litre = 30.000 ML
            put("grab_express_max_cod_amount", "35000.0"); // IDR 35.000
            put("grab_express_max_product_dimension", "50"); // 50 cm
        }});
        countryFactory.save(country);

        slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, systemAdmin);

        // Order total = IDR 10.000
        shipment = shipmentFactory.createShipment(slots.get(0), systemAdmin, "H234567", "H234567", Shipment.State.PENDING);
        batchFactory.createBatch(systemAdmin, shipment, slots.get(0), Batch.Type.SHOPPING);
        deliveryBatch = batchFactory.createBatch(systemAdmin, shipment, slots.get(0), Batch.Type.DELIVERY);

        // Item height = width = depth = 10 cm
        // Total volume = 22.000 ML
        List<Item> items = itemFactory.createItems(shipment, systemAdmin, 10);
    }

    @Test
    public void shouldReturnEligibleForCODAndCC() throws Exception {
        // HF Batch
        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.HF.toString())));

        // GE Batch
        changeToGEBatch();

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.GE.toString())));
    }

    @Test
    public void shouldReturnNotEligibleForCOD_DueToGECODOrderTotalThreshold() throws Exception {
        changeToGEBatch();

        country.setPreferences(new HashMap<String, String>() {{
            put("grab_express_max_cod_amount", "5000.0"); // IDR 5000
        }});
        countryFactory.save(country);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.GE.toString())));
    }

    @Test
    public void shouldReturnNotEligibleForCOD_DueToStockLocationDisableGECOD() throws Exception {
        changeToGEBatch();

        StockLocation stockLocation = stockLocationRepository.findByCode("SL-0");
        stockLocation.setEnableGrabExpressCod(false);
        stockLocationFactory.save(stockLocation);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.GE.toString())));
    }

    @Test
    public void shouldReturnNotEligibleForCOD_DueToBrandStore() throws Exception {
        StockLocation stockLocation = stockLocationRepository.findByCode("SL-0");
        stockLocation.setEnabler(StockLocation.Enabler.RALALI);
        stockLocation.setEnablerPlatform(StockLocation.EnablerPlatform.JUBELIO);
        stockLocationFactory.save(stockLocation);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.HF.toString())));
    }

    @Test
    public void shouldReturnEligibleEwalletForRanger() throws Exception {
        enableCountryTPL(false);
        shipment = shipmentFactory.createShipment(slots.get(0), systemAdmin, "H1", "H1", Shipment.State.PENDING);
        Batch rangerBatch = batchFactory.createBatch(systemAdmin, shipment, slots.get(0), Batch.Type.RANGER);
        rangerBatch.setDeliveryType(Batch.DeliveryType.NORMAL);
        batchFactory.save(rangerBatch);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.HF.toString())));
    }


    @Test
    public void shouldReturnNotEligibleEwalletForLalamove() throws Exception {
        enableCountryTPL(false);

        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.LALAMOVE);
        batchFactory.save(deliveryBatch);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
            .header("X-Fulfillment-User-Token", systemAdmin.getToken())
            .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(false)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(false)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.LALAMOVE.toString())));
    }

    @Test
    public void shouldReturnEligibleEwalletForLalamove() throws Exception {
        enableCountryTPL(true);

        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.LALAMOVE);
        batchFactory.save(deliveryBatch);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.LALAMOVE.toString())));
    }

    @Test
    public void shouldReturnNotEligibleEwalletForGE() throws Exception {
        enableCountryTPL(false);

        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.GRAB_EXPRESS);
        batchFactory.save(deliveryBatch);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.GE.toString())));
    }

    @Test
    public void shouldReturnEligibleEwalletForGE() throws Exception {
        enableCountryTPL(true);

        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.GRAB_EXPRESS);
        batchFactory.save(deliveryBatch);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.GE.toString())));
    }

    @Test
    public void shouldReturnEligibleForCOD_IfEnablerNotJubelio() throws Exception {
        StockLocation stockLocation = stockLocationRepository.findByCode("SL-0");
        stockLocation.setEnabler(StockLocation.Enabler.HFC);
        stockLocation.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);
        stockLocationFactory.save(stockLocation);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.HF.toString())));
    }

    private void changeToGEBatch() {
        Batch deliveryBatch = batchRepository.findByType(Batch.Type.DELIVERY).get(0);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.GRAB_EXPRESS);
        batchRepository.save(deliveryBatch);
    }

    private void enableCountryTPL(boolean enable) {
        country.setPreferences(new HashMap<String, String>()
        {{
            put("allow_ewallet_for_tpl", Boolean.toString(enable));
        }});
        countryFactory.save(country);
    }

    @Test
    public void shouldReturnVAField() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
            .header("X-Fulfillment-User-Token", systemAdmin.getToken())
            .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va").hasJsonPath());
    }

    @Test
    public void shouldReturnVATrue_WhenNotTPLAndNotNexHourOrOngoing() throws Exception {
        Slot slot = shipment.getSlot();
        slot.setStartTime(LocalDateTime.now().plusDays(1));
        slot.setEndTime(LocalDateTime.now().plusHours(1).plusDays(1));
        slotFactory.save(slot);
        StockLocation stockLocation = slot.getStockLocation();
        stockLocation.setEnableVirtualAccountPayment(true);
        stockLocationRepository.save(stockLocation);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
            .header("X-Fulfillment-User-Token", systemAdmin.getToken())
            .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.is_next_hour", Matchers.equalTo(false)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.eligible", Matchers.equalTo(true)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.HF.toString())));
    }

    @Test
    public void shouldReturnVAFalse_WhenTPLAndVAForTPLNotAllowed() throws Exception {
        Slot slot = shipment.getSlot();
        slot.setStartTime(LocalDateTime.now().plusDays(1));
        slot.setEndTime(LocalDateTime.now().plusHours(1).plusDays(1));
        slotFactory.save(slot);
        StockLocation stockLocation = slot.getStockLocation();
        stockLocation.setEnableVirtualAccountPayment(true);
        stockLocationRepository.save(stockLocation);

        Country country = stockLocation.getState().getCountry();
        country.setPreferences(new HashMap<String, String>() {{
            put("allow_va_for_express", "false");
            put("allow_va_for_tpl", "false");
        }});
        countryFactory.save(country);

        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.DEFAULT);
        batchFactory.save(deliveryBatch);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
            .header("X-Fulfillment-User-Token", systemAdmin.getToken())
            .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.is_next_hour", Matchers.equalTo(false)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.eligible", Matchers.equalTo(false)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.not_eligible_reason", Matchers.equalToIgnoringCase(VirtualAccountEligibilityPresenter.NotEligibleReason.TPL_NOT_ELIGIBLE.toString())));
    }

    @Test
    public void shouldReturnVATrue_WhenTPLAndVAForTPLAllowed() throws Exception {
        Slot slot = shipment.getSlot();
        slot.setStartTime(LocalDateTime.now().plusDays(1));
        slot.setEndTime(LocalDateTime.now().plusHours(1).plusDays(1));
        slotFactory.save(slot);
        StockLocation stockLocation = slot.getStockLocation();
        stockLocation.setEnableVirtualAccountPayment(true);
        stockLocationRepository.save(stockLocation);

        Country country = stockLocation.getState().getCountry();
        country.setPreferences(new HashMap<String, String>() {{
            put("allow_va_for_express", "false");
            put("allow_va_for_tpl", "true");
        }});
        countryFactory.save(country);

        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.DEFAULT);
        batchFactory.save(deliveryBatch);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.is_next_hour", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.eligible", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.not_eligible_reason", Matchers.nullValue()));
    }

    @Test
    public void shouldReturnVAFalse_WhenDisableVA() throws Exception {
        Slot slot = shipment.getSlot();
        slot.setStartTime(LocalDateTime.now().plusDays(1));
        slot.setEndTime(LocalDateTime.now().plusHours(1).plusDays(1));
        slotFactory.save(slot);
        StockLocation stockLocation = slot.getStockLocation();
        stockLocation.setEnableVirtualAccountPayment(false);
        stockLocationRepository.save(stockLocation);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
            .header("X-Fulfillment-User-Token", systemAdmin.getToken())
            .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.is_next_hour", Matchers.equalTo(false)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.eligible", Matchers.equalTo(false)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.not_eligible_reason", Matchers.equalToIgnoringCase(VirtualAccountEligibilityPresenter.NotEligibleReason.STORE_NOT_ELIGIBLE.toString())));
    }

    @Test
    public void shouldReturnVAFalse_WhenOnGoingSlot() throws Exception {
        Slot slot = shipment.getSlot();
        slot.setStartTime(LocalDateTime.now().minusHours(1));
        slot.setEndTime(LocalDateTime.now().plusHours(1));
        slotFactory.save(slot);
        StockLocation stockLocation = slot.getStockLocation();
        stockLocation.setEnableVirtualAccountPayment(true);
        stockLocationRepository.save(stockLocation);

        Country country = stockLocation.getState().getCountry();
        country.setPreferences(new HashMap<String, String>() {{
            put("allow_va_for_express", "false");
            put("allow_va_for_tpl", "false");
        }});
        countryFactory.save(country);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
            .header("X-Fulfillment-User-Token", systemAdmin.getToken())
            .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.is_next_hour", Matchers.equalTo(true)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.eligible", Matchers.equalTo(false)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.not_eligible_reason", Matchers.equalToIgnoringCase(VirtualAccountEligibilityPresenter.NotEligibleReason.EXPRESS_NOT_ELIGIBLE.toString())));
    }

    @Test
    public void shouldReturnVAFalse_WhenNextHourSlotAndVAForExpressNotAllowed() throws Exception {
        Slot slot = shipment.getSlot();
        slot.setStartTime(LocalDateTime.now().plusMinutes(30));
        slot.setEndTime(LocalDateTime.now().plusMinutes(90));
        slotFactory.save(slot);
        StockLocation stockLocation = slot.getStockLocation();
        stockLocation.setEnableVirtualAccountPayment(true);
        stockLocationRepository.save(stockLocation);

        Country country = stockLocation.getState().getCountry();
        country.setPreferences(new HashMap<String, String>() {{
            put("allow_va_for_express", "false");
            put("allow_va_for_tpl", "false");
        }});
        countryFactory.save(country);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
            .header("X-Fulfillment-User-Token", systemAdmin.getToken())
            .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.is_next_hour", Matchers.equalTo(true)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.eligible", Matchers.equalTo(false)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.not_eligible_reason", Matchers.equalToIgnoringCase(VirtualAccountEligibilityPresenter.NotEligibleReason.EXPRESS_NOT_ELIGIBLE.toString())));
    }

    @Test
    public void shouldReturnVATrue_WhenNextHourSlotAndVAForExpressIsAllowed() throws Exception {
        Slot slot = shipment.getSlot();
        slot.setStartTime(LocalDateTime.now().plusMinutes(30));
        slot.setEndTime(LocalDateTime.now().plusMinutes(90));
        slotFactory.save(slot);
        StockLocation stockLocation = slot.getStockLocation();
        stockLocation.setEnableVirtualAccountPayment(true);
        stockLocationRepository.save(stockLocation);

        Country country = stockLocation.getState().getCountry();
        country.setPreferences(new HashMap<String, String>() {{
            put("allow_va_for_express", "true");
            put("allow_va_for_tpl", "false");
        }});
        countryFactory.save(country);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.is_next_hour", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.eligible", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_va.not_eligible_reason", Matchers.nullValue()));
    }

    @Test
    public void shouldReturnNotEligibleEwalletForDelyva() throws Exception {
        enableCountryTPL(false);

        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.DELYVA);
        batchFactory.save(deliveryBatch);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(false)));
    }

    @Test
    public void shouldReturnEligibleEwalletForDelyva() throws Exception {
        enableCountryTPL(true);

        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.DELYVA);
        batchFactory.save(deliveryBatch);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(true)));
    }

    @Test
    public void shouldReturnEligibleCODForLalamove_whenCountryIsAllowedLMCod() throws Exception {
        country.setPreferences(new HashMap<String, String>()
        {{
            put("enable_lalamove_cod", Boolean.toString(true));
        }});
        countryFactory.save(country);

        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.LALAMOVE);
        batchFactory.save(deliveryBatch);

        StockLocation stockLocation = deliveryBatch.getStockLocation();
        stockLocation.setEnableLalamove(true);
        stockLocationFactory.save(stockLocation);

        shipment.setOrderCompanyId(null);
        shipment.setOrderCompanyName(null);
        shipmentFactory.save(shipment);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.LALAMOVE.toString())));
    }

    @Test
    public void shouldReturnNotEligibleCODForLalamove_TH_whenCountryIsAllowedLMCod_andShipmentFromHappyCorporate() throws Exception {
        country.setPreferences(new HashMap<String, String>()
        {{
            put("enable_lalamove_cod", Boolean.toString(true));
        }});
        country.setIsoName("TH");
        countryFactory.save(country);

        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.LALAMOVE);
        batchFactory.save(deliveryBatch);

        StockLocation stockLocation = deliveryBatch.getStockLocation();
        stockLocation.setEnableLalamove(true);
        stockLocationFactory.save(stockLocation);

        shipment.setOrderCompanyId(1L);
        shipment.setOrderCompanyName("PT Corporate");
        shipmentFactory.save(shipment);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.LALAMOVE.toString())));
    }

    @Test
    public void shouldReturnNotEligibleCODForLalamove_whenCountryIsNotAllowedLMCod() throws Exception {
        country.setPreferences(new HashMap<String, String>()
        {{
            put("enable_lalamove_cod", Boolean.toString(false));
        }});
        countryFactory.save(country);

        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.LALAMOVE);
        batchFactory.save(deliveryBatch);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.LALAMOVE.toString())));
    }

    @Test
    public void shouldReturnNotEligibleCODForLalamove_whenConfigForLalamoveCodeIsNotSet() throws Exception {

        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.LALAMOVE);
        batchFactory.save(deliveryBatch);

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/payment_eligibility")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_cod", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_credit_card", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.eligible_for_ewallet", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.fleet_type", Matchers.equalTo(Slot.FleetType.LALAMOVE.toString())));
    }
}
