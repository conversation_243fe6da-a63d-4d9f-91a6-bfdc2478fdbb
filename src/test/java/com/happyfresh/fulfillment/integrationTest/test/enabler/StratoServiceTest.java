package com.happyfresh.fulfillment.integrationTest.test.enabler;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaMessage;
import com.happyfresh.fulfillment.common.presenter.StratoEvent;
import com.happyfresh.fulfillment.common.property.DelyvaProperty;
import com.happyfresh.fulfillment.common.property.LalamoveProperty;
import com.happyfresh.fulfillment.common.property.RadarProperty;
import com.happyfresh.fulfillment.common.service.NotificationService;
import com.happyfresh.fulfillment.enabler.form.StratoWebhookForm;
import com.happyfresh.fulfillment.enabler.messaging.StratoEventListener;
import com.happyfresh.fulfillment.enabler.scheduler.StratoEnablerScheduler;
import com.happyfresh.fulfillment.enabler.service.DriverReassignmentService;
import com.happyfresh.fulfillment.enabler.service.StratoService;
import com.happyfresh.fulfillment.enabler.service.StratoWebhookService;
import com.happyfresh.fulfillment.enabler.service.api.StratoApiService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.MockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.shipment.form.ItemForm;
import com.happyfresh.fulfillment.shipment.service.CatalogService;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import com.happyfresh.fulfillment.tpl.delyva.model.DelyvaStatusCode;
import com.happyfresh.fulfillment.user.service.AgentClockInActivityService;
import org.junit.*;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.HttpClientErrorException;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.*;

import static java.nio.file.Files.readAllBytes;
import static java.nio.file.Paths.get;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

public class StratoServiceTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private AgentFactory agentFactory;

    @Autowired
    private StratoEventListener stratoEventListener;

    @Autowired
    private StratoWebhookService webhookService;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private ItemFactory itemFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private StratoService stratoService;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private PackagingFactory packagingFactory;

    @Autowired
    private LalamoveServiceTypeFactory lalamoveServiceTypeFactory;

    @Autowired
    private LalamoveProperty lalamoveProperty;

    @Autowired
    private MockServerHelper mockServerHelper;

    @Autowired
    private LalamoveDeliveryRepository lalamoveDeliveryRepository;

    @MockBean
    private OrderService orderService;

    @MockBean
    private CatalogService catalogService;

    @Autowired
    private StratoEnablerScheduler stratoEnablerScheduler;

    @MockBean
    private StratoApiService stratoApiService;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @SpyBean
    private ShipmentPackaging spyShipmentPackaging;

    @MockBean
    private KafkaMessage kafkaMessage;

    @Autowired
    private DelyvaProperty delyvaProperty;

    @Autowired
    private DelyvaDeliveryFactory delyvaDeliveryFactory;

    @Autowired
    private DelyvaDeliveryRepository delyvaDeliveryRepository;

    @Autowired
    private CategoryOrderFactory categoryOrderFactory;

    @Autowired
    private ItemRepository itemRepository;

    @Autowired
    private RadarProperty radarProperty;

    @SpyBean
    private StratoWebhookService stratoWebhookService;

    @Autowired
    private AgentClockInActivityService agentClockInActivityService;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private AgentInGeofenceRepository agentInGeofenceRepository;

    @MockBean
    private NotificationService notificationService;

    private User creator;

    private User shopper;

    private User systemAdmin;

    private Shipment shipment;

    private byte[] webhookPayload;

    private List<String> itemSkus = Lists.newArrayList("SKU1", "SKU2");

    private List<StockLocation> stockLocations;

    private Batch shoppingBatch;

    private CategoryOrder categoryOrderA;

    private List<Slot> slots1;

    private StockLocation stockLocation;

    @BeforeClass
    public static void initProperty() {
        System.setProperty("scheduler.enabled", "true");
    }

    @AfterClass
    public static void resetProperty() {
        System.setProperty("scheduler.enabled", "false");
    }

    @Before
    public void init() throws IOException {
        if (this.creator == null) {
            this.creator = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
            this.systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, creator.getTenant());
            this.shopper = userFactory.createUserData(Role.Name.STRATO_SHOPPER, creator.getTenant());
        }
        if (this.shipment == null) {
            stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, creator, Slot.Type.LONGER_DELIVERY);
            stockLocation = stockLocations.get(0);
            stockLocation.setEnabler(StockLocation.Enabler.HFC);
            stockLocation.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);
            stockLocationFactory.save(stockLocation);
            slots1 = slotFactory.createLongerDeliverySlots(stockLocation, creator, 2, 4);

            List<Packaging> packagingList = packagingFactory.createPackagingList(3, stockLocation, this.creator);
            Packaging selectedPackaging = packagingList.stream()
                    .filter(p -> p.getId().equals(1L))
                    .findFirst()
                    .orElse(null);

            this.shipment = shipmentFactory.createShipment(slots1.get(0), this.creator);

            ShipmentPackaging shipmentPackaging = new ShipmentPackaging();
            shipmentPackaging.setFulfillmentPackagingId(selectedPackaging.getId());
            shipmentPackaging.setShipment(this.shipment);
            shipmentPackaging.setPackagingInternalName(selectedPackaging.getInternalName());
            shipmentPackaging.setPackagingClientNames(selectedPackaging.getPackagingType().getClientNames());
            shipmentPackaging.setPackagingSndNames(selectedPackaging.getPackagingType().getSndNames());
            shipmentPackaging.setPackagingImageUrl(selectedPackaging.getImageUrl());
            shipmentPackaging.setPackagingMaxQuantity(selectedPackaging.getMaxQuantity());
            shipmentPackaging.setPackagingPrice(BigDecimal.valueOf(4000));
            shipmentPackaging.setCreatedBy(this.creator.getId());
            shipmentPackaging.setUpdatedBy(this.creator.getId());
            shipmentPackaging.setTenant(this.creator.getTenant());
            this.shipment.setShipmentPackagings(Lists.newArrayList(shipmentPackaging));
            shipmentFactory.save(this.shipment);

            int itemCounts = 2;
            List<Item> items = itemFactory.createItems(shipment, shopper, itemCounts);
            Category category = items.get(0).getCategory();
            categoryOrderA = categoryOrderFactory.createCategoryOrder(creator, category, stockLocation, 69);
            for (int i = 0; i < itemCounts; i++) {
                Item item = items.get(i);
                item.setSku(itemSkus.get(i));
                if (item.getCategoryOrder() == null) {
                    item.setCategoryOrder(categoryOrderA);
                }
                itemFactory.save(item);
            }
            List<Shipment> shipments = Lists.newArrayList(shipment);
            shoppingBatch = batchFactory.createBatch(creator, shipments, slots1.get(0), Batch.Type.SHOPPING);

            Mockito.when(spyShipmentPackaging.getClientName()).thenReturn("Packaging 1");
            Mockito.when(spyShipmentPackaging.getSndName()).thenReturn("Packaging 1");
        }
        if (this.webhookPayload == null) {
            this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_picked_payload.json"));
        }
    }

    private List<AgentInGeofence> setupAgentInGeofence(List<Agent> agents, StockLocation stockLocation) {
        List<AgentInGeofence> agentInGeofences = new ArrayList<>();
        for (Agent agent : agents) {
            AgentInGeofence agentInGeofence = new AgentInGeofence();
            agentInGeofence.setAgentId(agent.getId());
            agentInGeofence.setStockLocationId(stockLocation.getId());
            agentInGeofence.setTenant(stockLocation.getTenant());
            agentInGeofence.setCreatedBy(agent.getUser().getId());
            agentInGeofenceRepository.save(agentInGeofence);
            agentInGeofences.add(agentInGeofence);
        }
        return agentInGeofences;
    }

    @Test
    public void eventItemPickedShouldStartShopping() throws Exception {
        String body = mapper.readTree(this.webhookPayload).toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PICKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);

        transactionHelper.withNewTransaction(() -> {
            Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
            Assert.assertEquals(shopper.getId(), shoppingBatch.getUser().getId());
            Job shoppingJob = shoppingBatch.getJobs().get(0);
            Assert.assertEquals(Job.State.STARTED, shoppingJob.getState());
        });
    }

    @Test
    public void eventItemPickedWithInitialItemStateShouldStartShopping() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_picked_initial_payload.json"));
        String body = mapper.readTree(this.webhookPayload).toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PICKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);

        transactionHelper.withNewTransaction(() -> {
            Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
            Assert.assertEquals(shopper.getId(), shoppingBatch.getUser().getId());
            Job shoppingJob = shoppingBatch.getJobs().get(0);
            Assert.assertEquals(Job.State.STARTED, shoppingJob.getState());

            Shipment shipment = shipmentRepository.findByOrderNumber("*********");
            Assert.assertEquals(0, (int) shipment.getItems().get(0).getFoundQty());
            Assert.assertEquals(0, (int) shipment.getItems().get(0).getOosQty());
        });
    }

    @Test
    public void eventItemPackedShouldFinishShopping() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        ItemForm mockItem = new ItemForm();
        mockItem.setSku("SKU233");
        mockItem.setCategoryPermalink("Bread");
        Map<String, String> categoryTranslationNames = new HashMap<>();
        categoryTranslationNames.put("en", "Bread");
        mockItem.setCategoryTranslationNames(new HashMap<>());
        mockItem.setCategoryPosition(-50835);
        Map<String, String> translationNames = new HashMap<>();
        categoryTranslationNames.put("en", "Bread");
        mockItem.setTranslationNames(translationNames);
        Map<String, String> imageUrl = new HashMap<>();
        imageUrl.put("1", "https://d12man5gwydfvl.cloudfront.net/wp-content/uploads/2016/03/art-hf-logo.png");
        mockItem.setImageUrl(imageUrl);
        mockItem.setPrice(15000.0);
        mockItem.setCostPrice(15000.0);
        mockItem.setSupermarketUnitCostPrice(15000.0);
        mockItem.setAverageWeight(0.5);

        List<ItemForm> mockItems = Lists.newArrayList(mockItem);
        List<String> skus = Lists.newArrayList(mockItem.getSku());
        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        Mockito.when(catalogService.getItemsFromCatalogService(eq(skus), eq(stockLocation.getExternalId()), any(Shipment.class))).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);

        transactionHelper.withNewTransaction(() -> {
            Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
            Assert.assertEquals(shopper.getId(), shoppingBatch.getUser().getId());
            Job shoppingJob = shoppingBatch.getJobs().get(0);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());
            Shipment shipment = shoppingJob.getShipment();
            ShipmentPackaging shipmentPackaging = shipment.getShipmentPackagings().stream().filter(p -> p.isReplacement()).findFirst().get();
            Assert.assertEquals(2l, (long) shipmentPackaging.getFulfillmentPackagingId());
            Assert.assertEquals(2000, shipmentPackaging.getPackagingPrice().longValue());
        });

    }

    @Test
    public void eventItemPackedShouldFinishShoppingAndBookLalamove() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        lalamoveServiceTypeFactory.createServiceTypeMotorAndMPV(creator, stockLocations.get(0));

        Batch deliveryBatch = batchFactory.createBatch(creator, Lists.newArrayList(this.shipment), this.shipment.getSlot(), Batch.Type.DELIVERY);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.LALAMOVE);
        batchRepository.save(deliveryBatch);

        ItemForm mockItem = new ItemForm();
        mockItem.setSku("SKU233");
        mockItem.setCategoryPermalink("Bread");
        Map<String, String> categoryTranslationNames = new HashMap<>();
        categoryTranslationNames.put("en", "Bread");
        mockItem.setCategoryTranslationNames(new HashMap<>());
        mockItem.setCategoryPosition(-50835);
        Map<String, String> translationNames = new HashMap<>();
        categoryTranslationNames.put("en", "Bread");
        mockItem.setTranslationNames(translationNames);
        Map<String, String> imageUrl = new HashMap<>();
        imageUrl.put("1", "https://d12man5gwydfvl.cloudfront.net/wp-content/uploads/2016/03/art-hf-logo.png");
        mockItem.setImageUrl(imageUrl);
        mockItem.setPrice(15000.0);
        mockItem.setCostPrice(15000.0);
        mockItem.setSupermarketUnitCostPrice(15000.0);
        mockItem.setAverageWeight(0.5);

        List<ItemForm> mockItems = Lists.newArrayList(mockItem);
        List<String> skus = Lists.newArrayList(mockItem.getSku());
        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        Mockito.when(catalogService.getItemsFromCatalogService(eq(skus), eq(stockLocation.getExternalId()), any(Shipment.class))).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        String url = lalamoveProperty.getBaseUrl() + "/v2/quotations";
        String responseBody = "{ \"totalFee\": \"40000\", \"totalFeeCurrency\": \"IDR\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = setupMockServer(mockServer, 1, HttpMethod.POST, url, responseBody);

        String urlBook = lalamoveProperty.getBaseUrl() + "/v2/orders";
        String responseBook = "{ \"customerOrderId\": \"DEPRECATED_LALAMOVE_ID\", \"orderRef\": \"LALAMOVE-EXTERNAL-ID\" }";
        mockServer = setupMockServer(mockServer, 1, HttpMethod.POST, urlBook, responseBook);

        stratoService.handleEvent(event);
        Thread.sleep(500);

        transactionHelper.withNewTransaction(() -> {
            Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
            Assert.assertEquals(shopper.getId(), shoppingBatch.getUser().getId());
            Job shoppingJob = shoppingBatch.getJobs().get(0);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());
            Shipment shipment = shoppingJob.getShipment();
            ShipmentPackaging shipmentPackaging = shipment.getShipmentPackagings().stream().filter(p -> p.isReplacement()).findFirst().get();
            Assert.assertEquals(2l, (long) shipmentPackaging.getFulfillmentPackagingId());
            Assert.assertEquals(2000, shipmentPackaging.getPackagingPrice().longValue());


            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("LALAMOVE-EXTERNAL-ID", lalamoveDelivery.getExternalOrderId());
        });

        mockServer.verify();
    }

    @Test
    public void eventItemPackedShouldFinishShoppingAndBookDelyva() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        batchFactory.createTplDeliveryBatch(creator, shipment, shipment.getSlot(), Batch.TplType.DELYVA, Job.State.INITIAL);
        delyvaDeliveryFactory.createInitial(shipment, creator);

        ItemForm mockItem = new ItemForm();
        mockItem.setSku("SKU233");
        mockItem.setCategoryPermalink("Bread");
        mockItem.setCategoryTranslationNames(new HashMap<>());
        mockItem.setCategoryPosition(-50835);
        Map<String, String> translationNames = new HashMap<>();
        mockItem.setTranslationNames(translationNames);
        Map<String, String> imageUrl = new HashMap<>();
        imageUrl.put("1", "https://d12man5gwydfvl.cloudfront.net/wp-content/uploads/2016/03/art-hf-logo.png");
        mockItem.setImageUrl(imageUrl);
        mockItem.setPrice(15000.0);
        mockItem.setCostPrice(15000.0);
        mockItem.setSupermarketUnitCostPrice(15000.0);
        mockItem.setAverageWeight(0.5);

        List<ItemForm> mockItems = Lists.newArrayList(mockItem);
        List<String> skus = Lists.newArrayList(mockItem.getSku());
        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        Mockito.when(catalogService.getItemsFromCatalogService(eq(skus), eq(stockLocation.getExternalId()), any(Shipment.class))).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);
        MockRestServiceServer mockServer = mockDelyvaApiCall();

        StratoEvent event = new StratoEvent();
        event.setPayload(body);
        stratoEventListener.listen(event);

        Thread.sleep(500);

        transactionHelper.withNewTransaction(() -> {
            DelyvaDelivery delivery = delyvaDeliveryRepository.findAll().get(0);
            Assert.assertEquals(DelyvaStatusCode.DRAFT, delivery.getStatus());
            Assert.assertEquals("0aa4189a-39f5-488c-9aaf-0f011073de76", delivery.getExternalId());
            Assert.assertEquals(BigDecimal.valueOf(8.0), delivery.getServiceFee());
            Assert.assertEquals("HFPK-ODD-KV-PROD", delivery.getServiceCode());
            Assert.assertEquals("Pickupp Express Delivery", delivery.getServiceName());
        });

        mockServer.verify();
    }

    @Test
    public void eventItemPackedShouldFinishShoppingCase1() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_case_1_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);

        transactionHelper.withNewTransaction(() -> {
            Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
            Assert.assertEquals(shopper.getId(), shoppingBatch.getUser().getId());
            Job shoppingJob = shoppingBatch.getJobs().get(0);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());
            Shipment shipment = shoppingJob.getShipment();
            ShipmentPackaging shipmentPackaging = shipment.getShipmentPackagings().stream().filter(p -> p.isReplacement()).findFirst().get();
            Assert.assertEquals(2l, (long) shipmentPackaging.getFulfillmentPackagingId());
            Assert.assertEquals(2000, shipmentPackaging.getPackagingPrice().longValue());
        });

    }

    @Test
    public void eventItemPackedShouldFinishShoppingCase2() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_case_2_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);
        ItemForm mockItem = new ItemForm();
        mockItem.setSku("SKU233");
        mockItem.setCategoryPermalink("Bread");
        Map<String, String> categoryTranslationNames = new HashMap<>();
        categoryTranslationNames.put("en", "Bread");
        mockItem.setCategoryTranslationNames(new HashMap<>());
        mockItem.setCategoryPosition(-50835);
        Map<String, String> translationNames = new HashMap<>();
        categoryTranslationNames.put("en", "Bread");
        mockItem.setTranslationNames(translationNames);
        Map<String, String> imageUrl = new HashMap<>();
        imageUrl.put("1", "https://d12man5gwydfvl.cloudfront.net/wp-content/uploads/2016/03/art-hf-logo.png");
        mockItem.setImageUrl(imageUrl);
        mockItem.setPrice(15000.0);
        mockItem.setCostPrice(15000.0);
        mockItem.setSupermarketUnitCostPrice(15000.0);
        mockItem.setAverageWeight(0.5);

        List<ItemForm> mockItems = Lists.newArrayList(mockItem);
        List<String> skus = Lists.newArrayList(mockItem.getSku());
        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        Mockito.when(catalogService.getItemsFromCatalogService(eq(skus), eq(stockLocation.getExternalId()), any(Shipment.class))).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);

        transactionHelper.withNewTransaction(() -> {
            Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
            Assert.assertEquals(shopper.getId(), shoppingBatch.getUser().getId());
            Job shoppingJob = shoppingBatch.getJobs().get(0);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());
            Shipment shipment = shoppingJob.getShipment();
            ShipmentPackaging shipmentPackaging = shipment.getShipmentPackagings().stream().filter(p -> p.isReplacement()).findFirst().orElse(null);
            Assert.assertNull(shipmentPackaging);
        });

    }

    @Test
    public void eventItemPackedShouldNotFinishShoppingCase1() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_case_3_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);

        verify(stratoWebhookService, never()).sendDeliveryBookedWebhook(anyString());

        transactionHelper.withNewTransaction(() -> {
            Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
            Assert.assertNull(shoppingBatch.getUser());
            Job shoppingJob = shoppingBatch.getJobs().get(0);
            Assert.assertEquals(Job.State.INITIAL, shoppingJob.getState());
        });

    }

    private MockRestServiceServer setupMockServer(MockRestServiceServer mockServer, int expectedCount, HttpMethod method, String url, String responseBody) throws Exception {
        ExpectedCount count = expectedCount > 0 ? ExpectedCount.min(expectedCount) : ExpectedCount.never();
        mockServer.expect(count, requestTo(new URI(url)))
                .andExpect(method(method))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));
        return mockServer;
    }

    @Test
    public void schedulerItemPickedShouldStartShopping() throws Exception {
        String body = mapper.readTree(this.webhookPayload).toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PICKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        shoppingBatch.setStartTime(LocalDateTime.now().minusMinutes(120));
        shoppingBatch.setEndTime(LocalDateTime.now().minusMinutes(20));
        batchRepository.save(shoppingBatch);

        Mockito.when(stratoApiService.getOrderDetail(any(String.class))).thenReturn(Optional.of(form));
        stratoEnablerScheduler.checkUpdatedOrderStatus();

        Thread.sleep(500);
        transactionHelper.withNewTransaction(() -> {
            Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
            Assert.assertEquals(shopper.getId(), shoppingBatch.getUser().getId());
            Job shoppingJob = shoppingBatch.getJobs().get(0);
            Assert.assertEquals(Job.State.STARTED, shoppingJob.getState());
        });
    }

    @Test
    public void schedulerItemPackedShouldFinishShopping() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        ItemForm mockItem = new ItemForm();
        mockItem.setSku("SKU233");
        mockItem.setCategoryPermalink("Bread");
        Map<String, String> categoryTranslationNames = new HashMap<>();
        categoryTranslationNames.put("en", "Bread");
        mockItem.setCategoryTranslationNames(new HashMap<>());
        mockItem.setCategoryPosition(-50835);
        Map<String, String> translationNames = new HashMap<>();
        categoryTranslationNames.put("en", "Bread");
        mockItem.setTranslationNames(translationNames);
        Map<String, String> imageUrl = new HashMap<>();
        imageUrl.put("1", "https://d12man5gwydfvl.cloudfront.net/wp-content/uploads/2016/03/art-hf-logo.png");
        mockItem.setImageUrl(imageUrl);
        mockItem.setPrice(15000.0);
        mockItem.setCostPrice(15000.0);
        mockItem.setSupermarketUnitCostPrice(15000.0);
        mockItem.setAverageWeight(0.5);

        List<ItemForm> mockItems = Lists.newArrayList(mockItem);
        List<String> skus = Lists.newArrayList(mockItem.getSku());
        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        Mockito.when(catalogService.getItemsFromCatalogService(eq(skus), eq(stockLocation.getExternalId()), any(Shipment.class))).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        shoppingBatch.setStartTime(LocalDateTime.now().minusMinutes(120));
        shoppingBatch.setEndTime(LocalDateTime.now().minusMinutes(20));
        batchRepository.save(shoppingBatch);

        Mockito.when(stratoApiService.getOrderDetail(any(String.class))).thenReturn(Optional.of(form));
        stratoEnablerScheduler.checkUpdatedOrderStatus();
        Thread.sleep(500);

        transactionHelper.withNewTransaction(() -> {
            Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
            Assert.assertEquals(shopper.getId(), shoppingBatch.getUser().getId());
            Job shoppingJob = shoppingBatch.getJobs().get(0);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());
        });
    }

    @Test
    public void schedulerItemPackedShouldFinishShopping_andTriggerBookDelyva() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();
        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        ItemForm mockItem = new ItemForm();
        mockItem.setSku("SKU233");
        mockItem.setCategoryPermalink("Bread");
        mockItem.setCategoryTranslationNames(new HashMap<>());
        mockItem.setCategoryPosition(-50835);
        Map<String, String> translationNames = new HashMap<>();
        mockItem.setTranslationNames(translationNames);
        Map<String, String> imageUrl = new HashMap<>();
        imageUrl.put("1", "https://d12man5gwydfvl.cloudfront.net/wp-content/uploads/2016/03/art-hf-logo.png");
        mockItem.setImageUrl(imageUrl);
        mockItem.setPrice(15000.0);
        mockItem.setCostPrice(15000.0);
        mockItem.setSupermarketUnitCostPrice(15000.0);
        mockItem.setAverageWeight(0.5);

        List<ItemForm> mockItems = Lists.newArrayList(mockItem);
        List<String> skus = Lists.newArrayList(mockItem.getSku());
        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        Mockito.when(catalogService.getItemsFromCatalogService(eq(skus), eq(stockLocation.getExternalId()), any(Shipment.class))).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);
        Mockito.when(stratoApiService.getOrderDetail(any(String.class))).thenReturn(Optional.of(form));
        MockRestServiceServer mockServer = mockDelyvaApiCall();

        shoppingBatch.setStartTime(LocalDateTime.now().minusMinutes(120));
        shoppingBatch.setEndTime(LocalDateTime.now().minusMinutes(20));
        batchRepository.save(shoppingBatch);

        batchFactory.createTplDeliveryBatch(creator, shipment, shipment.getSlot(), Batch.TplType.DELYVA, Job.State.INITIAL);
        delyvaDeliveryFactory.createInitial(shipment, creator);

        stratoEnablerScheduler.checkUpdatedOrderStatus();

        Thread.sleep(1000);
        transactionHelper.withNewTransaction(() -> {
            Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
            Assert.assertEquals(shopper.getId(), shoppingBatch.getUser().getId());
            Job shoppingJob = shoppingBatch.getJobs().get(0);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());

            DelyvaDelivery delivery = delyvaDeliveryRepository.findAll().get(0);
            Assert.assertEquals(DelyvaStatusCode.DRAFT, delivery.getStatus());
            Assert.assertEquals("0aa4189a-39f5-488c-9aaf-0f011073de76", delivery.getExternalId());
            Assert.assertEquals(BigDecimal.valueOf(8.0), delivery.getServiceFee());
            Assert.assertEquals("HFPK-ODD-KV-PROD", delivery.getServiceCode());
            Assert.assertEquals("Pickupp Express Delivery", delivery.getServiceName());
        });

        mockServer.verify();
    }

    @Test
    public void schedulerItemPickedShouldDoThing_whenShipmentStatePending() throws Exception {
        String body = mapper.readTree(this.webhookPayload).toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PICKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        this.shipment.setState(Shipment.State.PENDING);
        shipmentFactory.save(this.shipment);

        shoppingBatch.setStartTime(LocalDateTime.now().minusMinutes(120));
        shoppingBatch.setEndTime(LocalDateTime.now().minusMinutes(20));
        batchRepository.save(shoppingBatch);

        Mockito.when(stratoApiService.getOrderDetail(eq(this.shipment.getOrderNumber())))
                .thenThrow(HttpClientErrorException.create(HttpStatus.NOT_FOUND, "", null, null, null));
        stratoEnablerScheduler.checkUpdatedOrderStatus();

        Thread.sleep(500);

        Mockito.verify(stratoApiService, Mockito.never()).getOrderDetail(eq(this.shipment.getOrderNumber()));
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(form.getOrderNumber());
            Assert.assertEquals(Shipment.State.PENDING, shipment.getState());
            Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
            Assert.assertNull(shoppingBatch.getUser());
        });
    }

    private MockRestServiceServer mockDelyvaApiCall() throws Exception {
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        String uri = delyvaProperty.getBaseUrl();
        String getQuotationResponse = getResponseFromResourceFileAsString("delyva_get_quotation_response.json");
        mockServer = setupMockServer(mockServer, 1, HttpMethod.POST, uri + "/v1.0/service/instantQuote", getQuotationResponse);
        String createOrderResponse = getResponseFromResourceFileAsString("delyva_create_order_response.json");
        mockServer = setupMockServer(mockServer, 1, HttpMethod.POST, uri + "/v1.0/order", createOrderResponse);
        return mockServer;
    }

    @Test
    public void eventItemPackedShouldNotUpdateCategoryOrder() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_case_1_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);
        transactionHelper.withNewTransaction(() -> {
            Item finalItem1CategoryOrderA = itemRepository.findByShipmentIdAndSku(this.shipment.getId(), itemSkus.get(0));
            Assert.assertEquals(categoryOrderA.getPosition(), finalItem1CategoryOrderA.getCategoryOrder().getPosition());

            Item finalItem2CategoryOrderA = itemRepository.findByShipmentIdAndSku(this.shipment.getId(), itemSkus.get(1));
            Assert.assertEquals(categoryOrderA.getPosition(), finalItem2CategoryOrderA.getCategoryOrder().getPosition());
        });

    }

    @Test
    public void eventItemPackedShouldFinishShoppingAndCreateRadarGeofence() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        ItemForm mockItem = new ItemForm();
        mockItem.setSku("SKU233");
        mockItem.setCategoryPermalink("Bread");
        Map<String, String> categoryTranslationNames = new HashMap<>();
        categoryTranslationNames.put("en", "Bread");
        mockItem.setCategoryTranslationNames(new HashMap<>());
        mockItem.setCategoryPosition(-50835);
        Map<String, String> translationNames = new HashMap<>();
        categoryTranslationNames.put("en", "Bread");
        mockItem.setTranslationNames(translationNames);
        Map<String, String> imageUrl = new HashMap<>();
        imageUrl.put("1", "https://d12man5gwydfvl.cloudfront.net/wp-content/uploads/2016/03/art-hf-logo.png");
        mockItem.setImageUrl(imageUrl);
        mockItem.setPrice(15000.0);
        mockItem.setCostPrice(15000.0);
        mockItem.setSupermarketUnitCostPrice(15000.0);
        mockItem.setAverageWeight(0.5);

        List<ItemForm> mockItems = Lists.newArrayList(mockItem);
        List<String> skus = Lists.newArrayList(mockItem.getSku());
        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        Batch deliveryBatch = batchFactory.createBatch(creator, Lists.newArrayList(this.shipment), this.shipment.getSlot(), Batch.Type.DELIVERY);

        Mockito.when(catalogService.getItemsFromCatalogService(eq(skus), eq(stockLocation.getExternalId()), any(Shipment.class))).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        String url = radarProperty.getBaseUrl() + "/geofences/shipment_number/" + this.shipment.getNumber();
        String responseBody = "{ \"response\": \"success\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = setupMockServer(mockServer, 1, HttpMethod.PUT, url, responseBody);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);

        transactionHelper.withNewTransaction(() -> {
            Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
            Assert.assertEquals(shopper.getId(), shoppingBatch.getUser().getId());
            Job shoppingJob = shoppingBatch.getJobs().get(0);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());
            Shipment shipment = shoppingJob.getShipment();
            ShipmentPackaging shipmentPackaging = shipment.getShipmentPackagings().stream().filter(p -> p.isReplacement()).findFirst().get();
            Assert.assertEquals(2l, (long) shipmentPackaging.getFulfillmentPackagingId());
            Assert.assertEquals(2000, shipmentPackaging.getPackagingPrice().longValue());
        });
        Thread.sleep(200);
        mockServer.verify();

    }

    @Test
    public void eventItemPackedShouldSendWebhookFinalizeAndPay() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        ItemForm mockItem = new ItemForm();
        mockItem.setSku("SKU233");
        mockItem.setCategoryPermalink("Bread");
        Map<String, String> categoryTranslationNames = new HashMap<>();
        categoryTranslationNames.put("en", "Bread");
        mockItem.setCategoryTranslationNames(new HashMap<>());
        mockItem.setCategoryPosition(-50835);
        Map<String, String> translationNames = new HashMap<>();
        categoryTranslationNames.put("en", "Bread");
        mockItem.setTranslationNames(translationNames);
        Map<String, String> imageUrl = new HashMap<>();
        imageUrl.put("1", "https://d12man5gwydfvl.cloudfront.net/wp-content/uploads/2016/03/art-hf-logo.png");
        mockItem.setImageUrl(imageUrl);
        mockItem.setPrice(15000.0);
        mockItem.setCostPrice(15000.0);
        mockItem.setSupermarketUnitCostPrice(15000.0);
        mockItem.setAverageWeight(0.5);

        List<ItemForm> mockItems = Lists.newArrayList(mockItem);
        List<String> skus = Lists.newArrayList(mockItem.getSku());
        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        Batch deliveryBatch = batchFactory.createBatch(creator, Lists.newArrayList(this.shipment), this.shipment.getSlot(), Batch.Type.DELIVERY);

        Mockito.when(catalogService.getItemsFromCatalogService(eq(skus), eq(stockLocation.getExternalId()), any(Shipment.class))).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        String url = radarProperty.getBaseUrl() + "/geofences/shipment_number/" + this.shipment.getNumber();
        String responseBody = "{ \"response\": \"success\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = setupMockServer(mockServer, 1, HttpMethod.PUT, url, responseBody);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);

        transactionHelper.withNewTransaction(() -> {
            Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
            Assert.assertEquals(shopper.getId(), shoppingBatch.getUser().getId());
            Job shoppingJob = shoppingBatch.getJobs().get(0);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());
            Shipment shipment = shoppingJob.getShipment();
            ShipmentPackaging shipmentPackaging = shipment.getShipmentPackagings().stream().filter(p -> p.isReplacement()).findFirst().get();
            Assert.assertEquals(2l, (long) shipmentPackaging.getFulfillmentPackagingId());
            Assert.assertEquals(2000, shipmentPackaging.getPackagingPrice().longValue());
        });
        Thread.sleep(200);
        mockServer.verify();
        Mockito.verify(kafkaMessage, Mockito.atMost(2))
                .publish(eq("fulfillment"), anyString(), anyString());

    }

    @Test
    public void eventItemPacked_whenShipmentStatePending_shouldDoNothing() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_case_1_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        this.shipment.setState(Shipment.State.PENDING);
        shipmentFactory.save(this.shipment);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);
        transactionHelper.withNewTransaction(() -> {
            Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
            Assert.assertNull(shoppingBatch.getUser());
            Job shoppingJob = shoppingBatch.getJobs().get(0);
            Assert.assertEquals(Job.State.INITIAL, shoppingJob.getState());
        });
        verify(stratoWebhookService, never()).sendDeliveryBookedWebhook(anyString());
    }

    @Test
    public void eventItemPicked_whenShipmentStatePending_shouldDoNothing() throws Exception {
        String body = mapper.readTree(this.webhookPayload).toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);

        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PICKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        this.shipment.setState(Shipment.State.PENDING);
        shipmentFactory.save(this.shipment);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);

        transactionHelper.withNewTransaction(() -> {
            Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
            Assert.assertNull(shoppingBatch.getUser());
            Job shoppingJob = shoppingBatch.getJobs().get(0);
            Assert.assertEquals(Job.State.INITIAL, shoppingJob.getState());
        });
    }

    private String getResponseFromResourceFileAsString(String fileName) throws IOException {
        return new String(readAllBytes(get("src", "test", "resources", "fixtures", fileName)));
    }

    private ItemForm getMockItem(){
        ItemForm mockItem = new ItemForm();
        mockItem.setSku("SKU233");
        mockItem.setCategoryPermalink("Bread");
        Map<String, String> categoryTranslationNames = new HashMap<>();
        categoryTranslationNames.put("en", "Bread");
        mockItem.setCategoryTranslationNames(new HashMap<>());
        mockItem.setCategoryPosition(-50835);
        Map<String, String> translationNames = new HashMap<>();
        categoryTranslationNames.put("en", "Bread");
        mockItem.setTranslationNames(translationNames);
        Map<String, String> imageUrl = new HashMap<>();
        imageUrl.put("1", "https://d12man5gwydfvl.cloudfront.net/wp-content/uploads/2016/03/art-hf-logo.png");
        mockItem.setImageUrl(imageUrl);
        mockItem.setPrice(15000.0);
        mockItem.setCostPrice(15000.0);
        mockItem.setSupermarketUnitCostPrice(15000.0);
        mockItem.setAverageWeight(0.5);

        return mockItem;
    }

    @Test
    public void eventItemPacked_andEnableDriverReassignment_shouldChangeEarliestDriver_andSendPushNotification() throws Exception {
        Tenant tenant = stockLocation.getTenant();
        Map<String, String> tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("enable_hfs_express_driver_reassignment", "true");
        tenant.setPreferences(tenantPreferences);
        tenantRepository.save(tenant);

        // Prepare On Demand Ranger 1
        User onDemandRanger1 = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, shopper.getTenant());
        Agent agent1 = agentFactory.createAgent(onDemandRanger1, stockLocation, Agent.State.WORKING);
        agent1.setLat(-6.294820319199123);
        agent1.setLon(106.79650800042742);
        agentClockInActivityService.createAgentClockInActivity(agent1, 8);

        // Prepare On Demand Ranger 2
        User onDemandRanger2 = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, shopper.getTenant());
        Agent agent2 = agentFactory.createAgent(onDemandRanger2, stockLocation, Agent.State.WORKING);
        agent2.setLat(-6.291311745113414);
        agent2.setLon(106.7992401541577);
        agentClockInActivityService.createAgentClockInActivity(agent2, 8);

        Batch deliveryBatch = batchFactory.createBatch(creator, shipment, slots1.get(0), Batch.Type.ON_DEMAND_DELIVERY);
        deliveryBatch.setUser(agent1.getUser());
        batchRepository.save(deliveryBatch);

        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        ItemForm mockItem = getMockItem();
        List<ItemForm> mockItems = Lists.newArrayList(mockItem);
        List<String> skus = Lists.newArrayList(mockItem.getSku());
        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        Mockito.when(catalogService.getItemsFromCatalogService(eq(skus), eq(stockLocation.getExternalId()), any(Shipment.class))).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);

        transactionHelper.withNewTransaction(() -> {
            Batch deliveryBatchResult = batchRepository.findByType(Batch.Type.ON_DEMAND_DELIVERY).get(0);
            Assert.assertEquals(agent2.getUser().getId(), deliveryBatchResult.getUser().getId());
            Assert.assertEquals("true", deliveryBatchResult.getFlags().get(Batch.FLAG_REASSIGNED));
            Assert.assertEquals(DriverReassignmentService.FLAG_REASSIGNED_NEAREST_REASON, deliveryBatchResult.getFlags().get(Batch.FLAG_REASSIGNED_REASON));
        });

        Mockito.verify(notificationService).sendPushNotificationForNewOnDemandJob(eq(onDemandRanger2.getId()), eq(shipment.getOrderNumber()), anyString(), anyString(), eq(tenant.getId()));
        Mockito.verify(notificationService).sendPushNotificationForExpressCancellation(onDemandRanger1.getId(), shipment.getOrderNumber(), tenant.getId());
    }

    @Test
    public void eventItemPacked_andEnableDriverReassignment_andOutsideGeofence_shouldKeepUseExistingDriver() throws Exception {
        Tenant tenant = stockLocation.getTenant();
        Map<String, String> tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("enable_hfs_express_driver_reassignment", "true");
        tenant.setPreferences(tenantPreferences);
        tenantRepository.save(tenant);

        // Prepare On Demand Ranger 1
        User onDemandRanger1 = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, shopper.getTenant());
        Agent agent1 = agentFactory.createAgent(onDemandRanger1, stockLocation, Agent.State.WORKING);
        agent1.setLat(-6.294820319199123);
        agent1.setLon(106.79650800042742);
        agentClockInActivityService.createAgentClockInActivity(agent1, 8);

        // Prepare On Demand Ranger 2 with location too far with store
        User onDemandRanger2 = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, shopper.getTenant());
        Agent agent2 = agentFactory.createAgent(onDemandRanger2, stockLocation, Agent.State.WORKING);
        agent2.setLat(-6.201792112607343);
        agent2.setLon(106.780887517408);
        agentClockInActivityService.createAgentClockInActivity(agent2, 8);

        // Prepare On Demand Ranger 3 with active batch > 0
        User onDemandRanger3 = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, shopper.getTenant());
        Agent agent3 = agentFactory.createAgent(onDemandRanger3, stockLocation, Agent.State.WORKING);
        agent3.setLat(-6.294820319199123);
        agent3.setLon(106.79650800042742);
        agentClockInActivityService.createAgentClockInActivity(agent3, 8);
        Shipment shipment2 = shipmentFactory.createShipment(slots1.get(0), this.creator);
        Batch deliveryBatch2 = batchFactory.createBatch(creator, shipment2, slots1.get(0), Batch.Type.ON_DEMAND_DELIVERY);
        deliveryBatch2.setUser(agent3.getUser());
        batchRepository.save(deliveryBatch2);

        Batch deliveryBatch = batchFactory.createBatch(creator, shipment, slots1.get(0), Batch.Type.ON_DEMAND_DELIVERY);
        deliveryBatch.setUser(agent1.getUser());
        batchRepository.save(deliveryBatch);

        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        ItemForm mockItem = getMockItem();
        List<ItemForm> mockItems = Lists.newArrayList(mockItem);
        List<String> skus = Lists.newArrayList(mockItem.getSku());
        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        Mockito.when(catalogService.getItemsFromCatalogService(eq(skus), eq(stockLocation.getExternalId()), any(Shipment.class))).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);

        transactionHelper.withNewTransaction(() -> {
            Shipment shipmentResult = shipmentRepository.findByNumber(shipment.getNumber());
            Batch deliveryBatchResult = shipmentResult.getOnDemandDeliveryOrRangerJob().get().getBatch();
            Assert.assertEquals(agent1.getUser().getId(), deliveryBatchResult.getUser().getId());
            Assert.assertNull(deliveryBatchResult.getFlags().get(Batch.FLAG_REASSIGNED));
        });
    }

    @Test
    public void eventItemPacked_andEnableDriverReassignment_andNoIdleDrivers_shouldNotUpdateUser() throws Exception {
        Tenant tenant = stockLocation.getTenant();
        Map<String, String> tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("enable_hfs_express_driver_reassignment", "true");
        tenant.setPreferences(tenantPreferences);
        tenantRepository.save(tenant);

        // Prepare On Demand Ranger 1
        User onDemandRanger1 = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, shopper.getTenant());
        Agent agent1 = agentFactory.createAgent(onDemandRanger1, stockLocation, Agent.State.WORKING);
        agent1.setLat(-6.294820319199123);
        agent1.setLon(106.79650800042742);
        agentClockInActivityService.createAgentClockInActivity(agent1, 8);

        Batch deliveryBatch = batchFactory.createBatch(creator, shipment, slots1.get(0), Batch.Type.ON_DEMAND_DELIVERY);
        deliveryBatch.setUser(agent1.getUser());
        batchRepository.save(deliveryBatch);

        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        ItemForm mockItem = getMockItem();
        List<ItemForm> mockItems = Lists.newArrayList(mockItem);
        List<String> skus = Lists.newArrayList(mockItem.getSku());
        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        Mockito.when(catalogService.getItemsFromCatalogService(eq(skus), eq(stockLocation.getExternalId()), any(Shipment.class))).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);

        transactionHelper.withNewTransaction(() -> {
            Batch deliveryBatchResult = batchRepository.findByType(Batch.Type.ON_DEMAND_DELIVERY).get(0);
            Assert.assertEquals(agent1.getUser().getId(), deliveryBatchResult.getUser().getId());
            Assert.assertNull(deliveryBatchResult.getFlags().get(Batch.FLAG_REASSIGNED));
        });

        Mockito.verify(notificationService, never()).sendPushNotificationForNewOnDemandJob(anyLong(), anyString(), anyString(), anyString(), anyLong());
        Mockito.verify(notificationService, never()).sendPushNotificationForExpressCancellation(anyLong(), anyString(), anyLong());
    }

    @Test
    public void eventItemPacked_enableDriverReassignment_nonExpressDelivery_shouldKeepProcessStratoEvent() throws Exception {
        Tenant tenant = stockLocation.getTenant();
        Map<String, String> tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("enable_hfs_express_driver_reassignment", "true");
        tenant.setPreferences(tenantPreferences);
        tenantRepository.save(tenant);

        User driver1 = userFactory.createUserData(Role.Name.DRIVER, shopper.getTenant());
        Agent agent1 = agentFactory.createAgent(driver1, stockLocation, Agent.State.WORKING);
        agent1.setLat(-6.294820319199123);
        agent1.setLon(106.79650800042742);
        agentClockInActivityService.createAgentClockInActivity(agent1, 8);

        User driver2 = userFactory.createUserData(Role.Name.DRIVER, shopper.getTenant());
        Agent agent2 = agentFactory.createAgent(driver2, stockLocation, Agent.State.WORKING);
        agent2.setLat(-6.291311745113414);
        agent2.setLon(106.7992401541577);
        agentClockInActivityService.createAgentClockInActivity(agent2, 8);

        Batch deliveryBatch = batchFactory.createBatch(creator, shipment, slots1.get(0), Batch.Type.DELIVERY);
        deliveryBatch.setUser(agent1.getUser());
        batchRepository.save(deliveryBatch);

        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        ItemForm mockItem = getMockItem();
        List<ItemForm> mockItems = Lists.newArrayList(mockItem);
        List<String> skus = Lists.newArrayList(mockItem.getSku());
        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        Mockito.when(catalogService.getItemsFromCatalogService(eq(skus), eq(stockLocation.getExternalId()), any(Shipment.class))).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);

        transactionHelper.withNewTransaction(() -> {
            Batch deliveryBatchResult = batchRepository.findByType(Batch.Type.DELIVERY).get(0);
            Assert.assertEquals(agent1.getUser().getId(), deliveryBatchResult.getUser().getId());
            Assert.assertNull(deliveryBatchResult.getFlags().get(Batch.FLAG_REASSIGNED));
        });
    }

    @Test
    public void eventItemPacked_whenOriginalFleetStillInStoreGeofence_shouldNotReassign() throws Exception {
        Tenant tenant = stockLocation.getTenant();
        Map<String, String> tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("enable_hfs_express_driver_reassignment", "true");
        tenant.setPreferences(tenantPreferences);
        tenantRepository.save(tenant);

        // Prepare On Demand Ranger 1
        User onDemandRanger1 = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, shopper.getTenant());
        Agent agent1 = agentFactory.createAgent(onDemandRanger1, stockLocation, Agent.State.WORKING);
        agent1.setLat(-6.294820319199123);
        agent1.setLon(106.79650800042742);
        agentClockInActivityService.createAgentClockInActivity(agent1, 8);

        // Prepare On Demand Ranger 2
        User onDemandRanger2 = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, shopper.getTenant());
        Agent agent2 = agentFactory.createAgent(onDemandRanger2, stockLocation, Agent.State.WORKING);
        agent2.setLat(-6.291311745113414);
        agent2.setLon(106.7992401541577);
        agentClockInActivityService.createAgentClockInActivity(agent2, 8);

        setupAgentInGeofence(Lists.newArrayList(agent1, agent2), stockLocation);

        Batch deliveryBatch = batchFactory.createBatch(creator, shipment, slots1.get(0), Batch.Type.ON_DEMAND_DELIVERY);
        deliveryBatch.setUser(agent1.getUser());
        batchRepository.save(deliveryBatch);

        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_payload.json"));
        JsonNode jsonNode = mapper.readTree(this.webhookPayload);
        String body = jsonNode.toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        ItemForm mockItem = getMockItem();
        List<ItemForm> mockItems = Lists.newArrayList(mockItem);
        List<String> skus = Lists.newArrayList(mockItem.getSku());
        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        Mockito.when(catalogService.getItemsFromCatalogService(eq(skus), eq(stockLocation.getExternalId()), any(Shipment.class))).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        StratoEvent event = new StratoEvent();
        event.setPayload(body);

        stratoService.handleEvent(event);

        transactionHelper.withNewTransaction(() -> {
            Batch deliveryBatchResult = batchRepository.findByType(Batch.Type.ON_DEMAND_DELIVERY).get(0);
            Assert.assertEquals(agent1.getUser().getId(), deliveryBatchResult.getUser().getId());
            Assert.assertNull(deliveryBatchResult.getFlags().get(Batch.FLAG_REASSIGNED));
            Assert.assertNull(deliveryBatchResult.getFlags().get(Batch.FLAG_REASSIGNED_REASON));
        });
    }
}
