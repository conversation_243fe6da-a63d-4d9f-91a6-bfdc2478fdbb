package com.happyfresh.fulfillment.integrationTest.test.enabler;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.presenter.EnablerWebhookEvent;
import com.happyfresh.fulfillment.common.presenter.StratoEvent;
import com.happyfresh.fulfillment.common.property.DelyvaProperty;
import com.happyfresh.fulfillment.common.property.LalamoveProperty;
import com.happyfresh.fulfillment.common.property.RadarProperty;
import com.happyfresh.fulfillment.common.service.EnablerEventPublisherService;
import com.happyfresh.fulfillment.common.service.WebhookCustomPublisherService;
import com.happyfresh.fulfillment.enabler.form.EnablerItemForm;
import com.happyfresh.fulfillment.enabler.form.EnablerWebhookForm;
import com.happyfresh.fulfillment.enabler.form.StratoWebhookForm;
import com.happyfresh.fulfillment.enabler.messaging.EnablerEventListener;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.MockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import com.happyfresh.fulfillment.stockLocation.service.StockLocationService;
import com.happyfresh.fulfillment.tpl.delyva.model.DelyvaStatusCode;
import org.hamcrest.Matchers;
import org.junit.*;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.math.BigDecimal;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.nio.file.Files.readAllBytes;
import static java.nio.file.Paths.get;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

public class EnablerUpdateItemTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private ItemFactory itemFactory;

    @Autowired
    private BatchFactory batchFactory;

    @MockBean
    private OrderService orderService;

    @SpyBean
    private EnablerEventPublisherService enablerEventPublisherService;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private MockServerHelper mockServerHelper;

    @Autowired
    private LalamoveProperty lalamoveProperty;

    @Autowired
    private LalamoveDeliveryRepository lalamoveDeliveryRepository;

    @Autowired
    private Environment environment;

    @Autowired
    private RadarProperty radarProperty;

    @Autowired
    private LalamoveServiceTypeFactory lalamoveServiceTypeFactory;

    @Autowired
    private GrabExpressDeliveryRepository grabExpressDeliveryRepository;

    @MockBean
    private WebhookCustomPublisherService webhookCustomPublisherService;

    @MockBean
    private StockLocationService stockLocationService;

    @Autowired
    private DelyvaProperty delyvaProperty;

    @Autowired
    private DelyvaDeliveryFactory delyvaDeliveryFactory;

    @Autowired
    private DelyvaDeliveryRepository delyvaDeliveryRepository;

    @Autowired
    private EnablerEventListener enablerEventListener;

    @Autowired
    private LalamoveDeliveryFactory lalamoveDeliveryFactory;

    @Autowired
    private StateRepository stateRepository;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private PackagingFactory packagingFactory;

    private User creator;

    private User shopper;

    private User systemAdmin;

    private User hypermartAdmin;

    private Shipment shipment;

    private byte[] webhookPayload;

    private List<String> itemSkus = Lists.newArrayList("SKU1", "SKU2");

    private String orderNumber = "R34212343";

    private List<StockLocation> stockLocations;
    private StockLocation stockLocation;

    @BeforeClass
    public static void initProperty() {
        System.setProperty("kafka.listener.enabled","true");
    }

    @AfterClass
    public static void resetProperty() {
        System.setProperty("kafka.listener.enabled","false");
    }

    @Before
    public void init() throws Exception {
        Thread.sleep(500);
        if (this.creator == null) {
            this.creator = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
            this.systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, creator.getTenant());
            this.hypermartAdmin = userFactory.createEnablerShopper(StockLocation.Enabler.HYPERMART, systemAdmin.getTenant());
            userFactory.createEnablerShopper(StockLocation.Enabler.HYPERMART, systemAdmin.getTenant());
            this.shopper = userFactory.createUserData(Role.Name.STRATO_SHOPPER, creator.getTenant());
        }
        if (this.shipment == null) {
            stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, creator, Slot.Type.LONGER_DELIVERY);
            stockLocation = stockLocations.get(0);
            stockLocation.setEnabler(StockLocation.Enabler.HYPERMART);
            stockLocationFactory.save(stockLocation);
            List<Slot> slots1 = slotFactory.createLongerDeliverySlots(stockLocation, creator, 2, 4, LocalDateTime.now());

            this.shipment = shipmentFactory.createShipment(slots1.get(0), this.creator, orderNumber, orderNumber);
            int itemCounts = 2;
            List<Item> items = itemFactory.createItems(shipment, shopper, itemCounts);
            for (int i=0; i < itemCounts; i++) {
                Item item = items.get(i);
                item.setSku(itemSkus.get(i));
                item.setRequestedQty(2);
                itemFactory.save(item);
            }
            this.shipment.setItems(items);

            List<Shipment> shipments = Lists.newArrayList(shipment);
            Batch shoppingBatch = batchFactory.createBatch(shopper, shipments, slots1.get(0), Batch.Type.SHOPPING);
            Batch deliveryBatch = batchFactory.createBatch(shopper, shipments, slots1.get(0), Batch.Type.DELIVERY);
        }
        if (this.webhookPayload == null) {
            this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "enabler_update_item_payload.json"));
        }
        lalamoveServiceTypeFactory.createServiceTypeMotorAndMPV(creator, stockLocations.get(0));
        Thread.sleep(400);
    }

    private MockRestServiceServer setupMockServer(MockRestServiceServer mockServer, int expectedCount, HttpMethod method, String url, String responseBody) throws Exception {
        ExpectedCount count = expectedCount > 0 ? ExpectedCount.min(expectedCount) : ExpectedCount.never();
        mockServer.expect(count, requestTo(new URI(url)))
                .andExpect(method(method))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));
        return mockServer;
    }

    @Test
    public void testUpdateItemStatusWithFailCase() throws Exception {
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(0).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PICKED.toString(), form.getStatus());

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isUnauthorized());

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/giant/orders")
                .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is4xxClientError());

        // Invalid Status
        form.setStatus("invalid_status");
        body = mapper.writeValueAsString(form);
        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                        .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is4xxClientError());

        // null Status
        form.setStatus(null);
        body = mapper.writeValueAsString(form);
        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                        .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is4xxClientError());
    }

    @Test
    public void testUpdateItemStatus_orderItemPicked() throws Exception {
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(1).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PICKED.toString(), form.getStatus());
        EnablerItemForm itemForm1 = form.getItems().get(0);
        EnablerItemForm itemForm2 = form.getItems().get(1);
        Item item1 = this.shipment.getItems().get(0);
        Item item2 = this.shipment.getItems().get(1);

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.order.order_number").value(shipment.getOrderNumber()))
                .andExpect(jsonPath("$.order.order_items", Matchers.hasSize(2)))
                .andExpect(jsonPath("$.order.order_items[0].item_sku").value(itemForm1.getItemSku()))
                .andExpect(jsonPath("$.order.order_items[0].quantity").value(item1.getRequestedQty()))
                .andExpect(jsonPath("$.order.order_items[0].quantity_on_hand").value(itemForm1.getQuantityOnHand()))
                .andExpect(jsonPath("$.order.order_items[0].quantity_out_of_stock").value(itemForm1.getQuantityOutOfStock()))
                .andExpect(jsonPath("$.order.order_items[1].item_sku").value(itemForm2.getItemSku()))
                .andExpect(jsonPath("$.order.order_items[1].quantity").value(item2.getRequestedQty()))
                .andExpect(jsonPath("$.order.order_items[1].quantity_on_hand").value(itemForm2.getQuantityOnHand()))
                .andExpect(jsonPath("$.order.order_items[1].quantity_out_of_stock").value(itemForm2.getQuantityOutOfStock()));

        verify(enablerEventPublisherService, atLeastOnce()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());
        Thread.sleep(1000);
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            for (Item item : shipment.getItems()) {
                Assert.assertEquals(item.getRequestedQty(), item.getFoundQty());
            }
        });
    }

    @Test
    public void testUpdateItemStatus_orderItemPicked_shouldAllowPartialItem() throws Exception {
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(0).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PICKED.toString(), form.getStatus());

        form.getItems().get(0).setQuantityOnHand(2); // requested_qty: 2
        form.getItems().get(0).setQuantityOutOfStock(0);
        form.getItems().remove(1);
        body = mapper.writeValueAsString(form);

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                        .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        verify(enablerEventPublisherService, atLeastOnce())
                .publish(any(EnablerWebhookEvent.class), anyString(), anyString());

        Thread.sleep(500);
        transactionHelper.withNewTransactionReadOnly(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            Assert.assertEquals((Integer) 2, shipment.getItems().get(0).getFoundQty());
            Assert.assertEquals((Integer) 0, shipment.getItems().get(1).getFoundQty());
        });
    }

    @Test
    public void testUpdateItemStatus_orderItemPicked_withInvalidQuantity() throws Exception {
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(0).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PICKED.toString(), form.getStatus());

        form.getItems().get(0).setQuantityOnHand(2); // requested_qty: 2
        form.getItems().get(0).setQuantityOutOfStock(0);
        form.getItems().get(1).setQuantityOnHand(1); // requested_qty: 2
        form.getItems().get(1).setQuantityOutOfStock(0);
        body = mapper.writeValueAsString(form);

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is4xxClientError())
                .andExpect(jsonPath("$.errors[0].type").value("InvalidItemQtyException"))
                .andExpect(jsonPath("$.errors[0].field").value("SKU2"));

        verify(enablerEventPublisherService, never())
                .publish(any(EnablerWebhookEvent.class), anyString(), anyString());

        Thread.sleep(1000);
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            for (Item item : shipment.getItems()) {
                Assert.assertEquals((Integer) 0, item.getFoundQty());
            }
        });
    }

    @Test
    public void testUpdateItemStatus_orderItemPicked_withOosItem() throws Exception {
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(0).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PICKED.toString(), form.getStatus());

        form.getItems().forEach(enablerItemForm -> {
            enablerItemForm.setQuantityOutOfStock(2); // requested_qty: 2
            enablerItemForm.setQuantityOnHand(0);
        });
        body = mapper.writeValueAsString(form);

        EnablerItemForm itemForm1 = form.getItems().get(0);
        EnablerItemForm itemForm2 = form.getItems().get(1);
        Item item1 = this.shipment.getItems().get(0);
        Item item2 = this.shipment.getItems().get(1);

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                        .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.order.order_number").value(shipment.getOrderNumber()))
                .andExpect(jsonPath("$.order.order_items", Matchers.hasSize(2)))
                .andExpect(jsonPath("$.order.order_items[0].item_sku").value(itemForm1.getItemSku()))
                .andExpect(jsonPath("$.order.order_items[0].quantity").value(item1.getRequestedQty()))
                .andExpect(jsonPath("$.order.order_items[0].quantity_on_hand").value(itemForm1.getQuantityOnHand()))
                .andExpect(jsonPath("$.order.order_items[0].quantity_out_of_stock").value(itemForm1.getQuantityOutOfStock()))
                .andExpect(jsonPath("$.order.order_items[1].item_sku").value(itemForm2.getItemSku()))
                .andExpect(jsonPath("$.order.order_items[1].quantity").value(item2.getRequestedQty()))
                .andExpect(jsonPath("$.order.order_items[1].quantity_on_hand").value(itemForm2.getQuantityOnHand()))
                .andExpect(jsonPath("$.order.order_items[1].quantity_out_of_stock").value(itemForm2.getQuantityOutOfStock()));

        verify(enablerEventPublisherService, atLeastOnce()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());
        Thread.sleep(200);
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            for (Item item : shipment.getItems()) {
                Assert.assertEquals("Other reason...", item.getOosDetail());
                Assert.assertEquals((Integer) 0, item.getOosType());
                Assert.assertEquals((Integer) 2, item.getOosQty());
                Assert.assertEquals((Integer) 0, item.getFoundQty());
            }
        });

        // Revert back OOS
        form.getItems().forEach(enablerItemForm -> {
            enablerItemForm.setQuantityOutOfStock(0);
            enablerItemForm.setQuantityOnHand(2); // requested_qty: 2
        });
        body = mapper.writeValueAsString(form);
        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                        .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
        verify(enablerEventPublisherService, atLeastOnce())
                .publish(any(EnablerWebhookEvent.class), anyString(), anyString());
        Thread.sleep(200);
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            for (Item item : shipment.getItems()) {
                Assert.assertEquals((Integer) 0, item.getOosQty());
                Assert.assertEquals((Integer) 2, item.getFoundQty());
                Assert.assertNull(item.getOosDetail());
                Assert.assertNull(item.getOosType());
            }
        });
    }

    @Test
    public void testUpdateItemStatus_orderItemPacked() throws Exception {
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(2).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        verify(enablerEventPublisherService, atLeastOnce()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());
        Thread.sleep(1000);
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            for (Item item : shipment.getItems()) {
                Assert.assertEquals(item.getRequestedQty(), item.getFoundQty());
            }
            Job shoppingJob = shipment.getShoppingJob().get();
            Assert.assertNotNull(shoppingJob);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());
        });
    }

    @Test
    public void testUpdateItemStatus_orderItemPacked_withOosItem() throws Exception {
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(2).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());
        form.getItems().forEach(enablerItemForm -> {
            enablerItemForm.setQuantityOutOfStock(enablerItemForm.getQuantityOnHand());
            enablerItemForm.setQuantityOnHand(0);
        });
        body = mapper.writeValueAsString(form);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                        .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        verify(enablerEventPublisherService, atLeastOnce()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());
        Thread.sleep(1000);
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            for (Item item : shipment.getItems()) {
                if (item.getOosQty() > 0) {
                    Assert.assertEquals("Other reason...", item.getOosDetail());
                    Assert.assertEquals((Integer) 0, item.getOosType());
                }
            }
            Job shoppingJob = shipment.getShoppingJob().get();
            Assert.assertNotNull(shoppingJob);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());
        });
    }

    @Test
    public void testUpdateItemStatus_orderItemPicked_withOosItem_thenAllFoundWhenPacking() throws Exception {
        // PICK --> OOS
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(0).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PICKED.toString(), form.getStatus());
        form.getItems().forEach(enablerItemForm -> {
            enablerItemForm.setQuantityOutOfStock(2); // requested_qty: 2
            enablerItemForm.setQuantityOnHand(0);
        });
        body = mapper.writeValueAsString(form);
        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                        .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
        verify(enablerEventPublisherService, atLeastOnce()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());
        Thread.sleep(200);
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            for (Item item : shipment.getItems()) {
                Assert.assertEquals("Other reason...", item.getOosDetail());
                Assert.assertEquals((Integer) 0, item.getOosType());
                Assert.assertEquals((Integer) 2, item.getOosQty());
                Assert.assertEquals((Integer) 0, item.getFoundQty());
            }
        });

        // PACK --> Revert OOS
        node = mapper.readTree(this.webhookPayload);
        body = node.get(2).toString();
        form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());
        form.getItems().forEach(enablerItemForm -> {
            enablerItemForm.setQuantityOutOfStock(0);
            enablerItemForm.setQuantityOnHand(2); // requested_qty: 2
        });
        body = mapper.writeValueAsString(form);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);
        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                        .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
        verify(enablerEventPublisherService, atLeastOnce()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());
        Thread.sleep(1000);
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            for (Item item : shipment.getItems()) {
                Assert.assertNull(item.getOosDetail());
                Assert.assertNull(item.getOosType());
            }
            Job shoppingJob = shipment.getShoppingJob().get();
            Assert.assertNotNull(shoppingJob);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());
        });
    }

    @Test
    public void testUpdateItemStatus_orderItemPacked_withInvalidQuantity() throws Exception {
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(3).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is4xxClientError())
                .andExpect(jsonPath("$.errors[0].type").value("InvalidItemQtyException"))
                .andExpect(jsonPath("$.errors[0].field").value("SKU2"));

        String body1 = node.get(4).toString();

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(body1))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is4xxClientError())
                .andExpect(jsonPath("$.errors[0].type").value("InvalidItemQtyException"))
                .andExpect(jsonPath("$.errors[1].type").value("InvalidItemQtyException"));
    }

    @Test
    public void testUpdateItemStatus_orderItemPacked_withInvalidRequest() throws Exception {
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(5).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is4xxClientError());
    }

    @Test
    public void testUpdateItemStatus_orderItemPacked_withLalamoveDelivery() throws Exception {
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(2).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        Batch deliveryBatch = batchFactory.createBatch(creator, Lists.newArrayList(this.shipment), this.shipment.getSlot(), Batch.Type.DELIVERY);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.LALAMOVE);
        batchFactory.save(deliveryBatch);
        Thread.sleep(500);
        String url = lalamoveProperty.getBaseUrl() + "/v2/quotations";
        String responseBody = "{ \"totalFee\": \"40000\", \"totalFeeCurrency\": \"IDR\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, url, responseBody);

        String urlBook = lalamoveProperty.getBaseUrl() + "/v2/orders";
        String responseBook = "{ \"customerOrderId\": \"DEPRECATED_LALAMOVE_ID\", \"orderRef\": \"LALAMOVE-EXTERNAL-ID\" }";
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, urlBook, responseBook);

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        verify(enablerEventPublisherService, atLeastOnce()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());
        Thread.sleep(1000);
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            for (Item item : shipment.getItems()) {
                Assert.assertEquals(item.getRequestedQty(), item.getFoundQty());
            }
            Job shoppingJob = shipment.getShoppingJob().get();
            Assert.assertNotNull(shoppingJob);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());

            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("LALAMOVE-EXTERNAL-ID", lalamoveDelivery.getExternalOrderId());
        });
        mockServer.verify();
    }

    @Test
    public void testUpdateItemStatus_orderItemPacked_withGEDelivery() throws Exception {
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(2).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        Batch deliveryBatch = batchFactory.createBatch(creator, Lists.newArrayList(this.shipment), this.shipment.getSlot(), Batch.Type.DELIVERY);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.GRAB_EXPRESS);
        batchFactory.save(deliveryBatch);
        Thread.sleep(500);

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        verify(enablerEventPublisherService, atLeastOnce()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());
        Thread.sleep(1500);
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            for (Item item : shipment.getItems()) {
                Assert.assertEquals(item.getRequestedQty(), item.getFoundQty());
            }
            Job shoppingJob = shipment.getShoppingJob().get();
            Assert.assertNotNull(shoppingJob);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());
        });
    }

    @Test
    public void testUpdateItemStatus_orderItemPacked_withHFDelivery_shouldCreateGeofence() throws Exception {
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(2).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        Batch deliveryBatch = batchFactory.createBatch(creator, Lists.newArrayList(this.shipment), this.shipment.getSlot(), Batch.Type.DELIVERY);

        String url = radarProperty.getBaseUrl() + "/geofences/shipment_number/" + this.shipment.getNumber();
        String responseBody = "{ \"response\": \"success\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = setupMockServer(mockServer,1, HttpMethod.PUT, url, responseBody);

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                        .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        verify(enablerEventPublisherService, atLeastOnce()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());
        Thread.sleep(1500);
        verify(webhookCustomPublisherService, times(1)).publishWebhookForBatch(eq(WebhookType.FINALIZE_BATCH), any(Batch.class));
        verify(webhookCustomPublisherService, times(1)).publishWebhookForBatchShipment(eq(WebhookType.PAY_SHIPMENT), any(Batch.class), any(Shipment.class));
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            for (Item item : shipment.getItems()) {
                Assert.assertEquals(item.getRequestedQty(), item.getFoundQty());
            }
            Job shoppingJob = shipment.getShoppingJob().get();
            Assert.assertNotNull(shoppingJob);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());
        });
        Thread.sleep(200);
        mockServer.verify();
    }

    @Test
    public void testUpdateItemStatusDirectCall_orderItemPacked_withDelyvaDelivery() throws Exception {
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(2).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());

        batchFactory.createTplDeliveryBatch(creator, shipment, shipment.getSlot(), Batch.TplType.DELYVA, Job.State.INITIAL);
        delyvaDeliveryFactory.createInitial(shipment, creator);

        Thread.sleep(500);

        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        String uri = delyvaProperty.getBaseUrl();
        String getQuotationResponse = getResponseFromResourceFile("delyva_get_quotation_response.json");
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, uri + "/v1.0/service/instantQuote", getQuotationResponse);
        String createOrderResponse = getResponseFromResourceFile("delyva_create_order_response.json");
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, uri + "/v1.0/order", createOrderResponse);

        EnablerWebhookEvent event = new EnablerWebhookEvent();
        event.setStatus(form.getStatus());
        event.setPayload(body);
        enablerEventListener.listen(event);

        Thread.sleep(1000);
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            for (Item item : shipment.getItems()) {
                Assert.assertEquals(item.getRequestedQty(), item.getFoundQty());
            }
            Job shoppingJob = shipment.getShoppingJob().get();
            Assert.assertNotNull(shoppingJob);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());

            DelyvaDelivery delyvaDelivery = delyvaDeliveryRepository.findAll().get(0);
            Assert.assertEquals(DelyvaStatusCode.DRAFT, delyvaDelivery.getStatus());
            Assert.assertEquals("0aa4189a-39f5-488c-9aaf-0f011073de76", delyvaDelivery.getExternalId());
            Assert.assertEquals(BigDecimal.valueOf(8.0), delyvaDelivery.getServiceFee());
            Assert.assertEquals("HFPK-ODD-KV-PROD", delyvaDelivery.getServiceCode());
            Assert.assertEquals("Pickupp Express Delivery", delyvaDelivery.getServiceName());
        });
        mockServer.verify();
    }

    @Test
    public void testUpdateItemStatus_orderItemPacked_withDelyvaDelivery() throws Exception {
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(2).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());

        batchFactory.createTplDeliveryBatch(creator, shipment, shipment.getSlot(), Batch.TplType.DELYVA, Job.State.INITIAL);
        delyvaDeliveryFactory.createInitial(shipment, creator);

        Thread.sleep(500);

        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        String uri = delyvaProperty.getBaseUrl();
        String getQuotationResponse = getResponseFromResourceFile("delyva_get_quotation_response.json");
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, uri + "/v1.0/service/instantQuote", getQuotationResponse);
        String createOrderResponse = getResponseFromResourceFile("delyva_create_order_response.json");
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, uri + "/v1.0/order", createOrderResponse);

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                        .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        verify(enablerEventPublisherService, atLeastOnce()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());

        Thread.sleep(2000);
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            for (Item item : shipment.getItems()) {
                Assert.assertEquals(item.getRequestedQty(), item.getFoundQty());
            }
            Job shoppingJob = shipment.getShoppingJob().get();
            Assert.assertNotNull(shoppingJob);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());

            DelyvaDelivery delyvaDelivery = delyvaDeliveryRepository.findAll().get(0);
            Assert.assertEquals(DelyvaStatusCode.DRAFT, delyvaDelivery.getStatus());
            Assert.assertEquals("0aa4189a-39f5-488c-9aaf-0f011073de76", delyvaDelivery.getExternalId());
            Assert.assertEquals(BigDecimal.valueOf(8.0), delyvaDelivery.getServiceFee());
            Assert.assertEquals("HFPK-ODD-KV-PROD", delyvaDelivery.getServiceCode());
            Assert.assertEquals("Pickupp Express Delivery", delyvaDelivery.getServiceName());
        });
        mockServer.verify();
    }

    @Test
    public void testUpdateItemStatus_orderItemPacked_withAllItemsOos_thenCancelOrder() throws Exception {
        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(2).toString();
        EnablerWebhookForm form = mapper.readValue(body, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());
        form.getItems().forEach(enablerItemForm -> {
            enablerItemForm.setQuantityOutOfStock(2); // requested_qty: 2
            enablerItemForm.setQuantityOnHand(0);
        });
        body = mapper.writeValueAsString(form);
        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                        .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andExpect(MockMvcResultMatchers.jsonPath("$.order.status", Matchers.equalTo("order_cancelled")))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        verify(enablerEventPublisherService, atLeastOnce()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());
        Thread.sleep(1000);

        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            Assert.assertEquals(Shipment.State.CANCELLED, shipment.getState());
            Optional<Job> optionalJob = shipment.getShoppingJob();
            Assert.assertFalse(optionalJob.isPresent());
        });
    }

    private void setupLalamoveV3(StockLocation stockLocation, boolean isSetupServiceType) {
        String lalamoveCityCode = "JKT";
        if(isSetupServiceType)
            lalamoveServiceTypeFactory.createServiceTypeMotorAndMPVV3(creator, stockLocation, lalamoveCityCode);

        State state = stockLocation.getState();
        state.setPreferences(new HashMap<String, String>() {{
            put("lalamove_city_code", lalamoveCityCode);
        }});
        stateRepository.save(state);

        Tenant tenant = stockLocation.getTenant();
        Map<String, String> tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("enable_lalamove_api_v3", "true");
        tenantRepository.save(tenant);
    }

    @Test
    public void testUpdateItemStatus_hypermart_orderItemPacked_withLalamove() throws Exception {
        setupLalamoveV3(stockLocation, false);
        lalamoveServiceTypeFactory.createMotorcycleServiceTypeV3(stockLocation.getState().getCountry(),creator, "JKT");
        lalamoveServiceTypeFactory.createServiceTypes(creator, stockLocation);

        JsonNode node = mapper.readTree(this.webhookPayload);
        String body = node.get(2).toString();
        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());

        batchFactory.createTplDeliveryBatch(creator, shipment, shipment.getSlot(), Batch.TplType.LALAMOVE, Job.State.INITIAL);
        lalamoveDeliveryFactory.create(shipment, creator);

        Thread.sleep(500);

        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        String uri = lalamoveProperty.getBaseUrl();
        String getQuotationResponse = getResponseFromResourceFile("lalamove_v3_get_quotation_response_success.json");
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, uri + "/v3/quotations", getQuotationResponse);
        String createOrderResponse = getResponseFromResourceFile("lalamove_v3_place_order_response_success.json");
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, uri + "/v3/orders", createOrderResponse);

        mvc.perform(MockMvcRequestBuilders.put("/api/v1/enablers/hypermart/orders")
                        .header("X-Fulfillment-User-Token", hypermartAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", hypermartAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        verify(enablerEventPublisherService, atLeastOnce()).publish(any(EnablerWebhookEvent.class), anyString(), anyString());

        Thread.sleep(2000);
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            for (Item item : shipment.getItems()) {
                Assert.assertEquals(item.getRequestedQty(), item.getFoundQty());
            }
            Job shoppingJob = shipment.getShoppingJob().get();
            Assert.assertNotNull(shoppingJob);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());

            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("124620405314", lalamoveDelivery.getExternalOrderId());
            Assert.assertEquals(BigDecimal.valueOf(20), lalamoveDelivery.getPrice());
            Assert.assertEquals("MOTORCYCLE", lalamoveDelivery.getServiceType());
        });
        mockServer.verify();
    }

    @Test
    public void testUpdateItemStatus_hfs_orderItemPacked_withLalamove() throws Exception {
        setupLalamoveV3(stockLocation, false);
        lalamoveServiceTypeFactory.createMotorcycleServiceTypeV3(stockLocation.getState().getCountry(),creator, "JKT");
        lalamoveServiceTypeFactory.createServiceTypes(creator, stockLocation);

        String payload = getResponseFromResourceFile("strato_webhook_order_item_packed_case_1_payload.json");
        EnablerWebhookForm form = mapper.readValue(payload, EnablerWebhookForm.class);
        Assert.assertEquals(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString(), form.getStatus());

        batchFactory.createTplDeliveryBatch(creator, shipment, shipment.getSlot(), Batch.TplType.LALAMOVE, Job.State.INITIAL);
        lalamoveDeliveryFactory.create(shipment, creator);

        stockLocation.setEnabler(StockLocation.Enabler.HFC);
        stockLocation.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);
        stockLocationFactory.save(stockLocation);

        packagingFactory.createPackagingList(1, stockLocation, creator);
        packagingFactory.createPackagingList(1, stockLocation, creator);

        Thread.sleep(500);

        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        String uri = lalamoveProperty.getBaseUrl();
        String getQuotationResponse = getResponseFromResourceFile("lalamove_v3_get_quotation_response_success.json");
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, uri + "/v3/quotations", getQuotationResponse);
        String createOrderResponse = getResponseFromResourceFile("lalamove_v3_place_order_response_success.json");
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, uri + "/v3/orders", createOrderResponse);

        mvc.perform(MockMvcRequestBuilders.post("/api/strato/webhook")
                        .header("X-Fulfillment-User-Token", creator.getToken())
                        .header("X-Fulfillment-Tenant-Token", creator.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        verify(enablerEventPublisherService, atLeastOnce()).publish(any(StratoEvent.class), anyString(), anyString());

        Thread.sleep(2000);
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
            for (Item item : shipment.getItems()) {
                Assert.assertEquals(item.getRequestedQty(), item.getFoundQty());
            }
            Job shoppingJob = shipment.getShoppingJob().get();
            Assert.assertNotNull(shoppingJob);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());

            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("124620405314", lalamoveDelivery.getExternalOrderId());
            Assert.assertEquals(BigDecimal.valueOf(20), lalamoveDelivery.getPrice());
            Assert.assertEquals("MOTORCYCLE", lalamoveDelivery.getServiceType());
        });
        mockServer.verify();
    }
}
