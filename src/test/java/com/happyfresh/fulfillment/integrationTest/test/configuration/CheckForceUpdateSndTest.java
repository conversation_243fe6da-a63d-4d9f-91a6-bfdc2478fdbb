package com.happyfresh.fulfillment.integrationTest.test.configuration;

import com.happyfresh.fulfillment.entity.Role;
import com.happyfresh.fulfillment.entity.Tenant;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.repository.TenantRepository;
import org.hamcrest.Matchers;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

public class CheckForceUpdateSndTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private TenantRepository tenantRepository;

    @Test
    public void returnForceUpdateTrue() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);

        Tenant tenant = user.getTenant();
        tenant.setSndLatestVersionCode(190);
        tenant.setSndLatestApkUrl("http://apkurlnew");
        tenantRepository.save(tenant);

        mvc.perform(MockMvcRequestBuilders.get("/api/check_update")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .param("current_version", "180")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.require_update", Matchers.equalTo(true)))
                .andExpect(jsonPath("$.response.version", Matchers.equalTo("190")))
                .andExpect(jsonPath("$.response.apk", Matchers.equalTo("http://apkurlnew")));
    }

    @Test
    public void returnForceUpdateFalse() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);

        Tenant tenant = user.getTenant();
        tenant.setSndLatestVersionCode(180);
        tenant.setSndLatestApkUrl("http://apkurlnew");
        tenantRepository.save(tenant);

        mvc.perform(MockMvcRequestBuilders.get("/api/check_update")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .param("current_version", "180")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.require_update", Matchers.equalTo(false)))
                .andExpect(jsonPath("$.response.version", Matchers.equalTo("180")))
                .andExpect(jsonPath("$.response.apk", Matchers.equalTo("http://apkurlnew")));
    }

    @Test
    public void returnForceUpdateFalseWhenInfoVersionNull() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);

        Tenant tenant = user.getTenant();
        tenant.setSndLatestApkUrl("http://apkurlnew");
        tenantRepository.save(tenant);

        mvc.perform(MockMvcRequestBuilders.get("/api/check_update")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .param("current_version", "180")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.require_update", Matchers.equalTo(false)))
                .andExpect(jsonPath("$.response.version", Matchers.nullValue()))
                .andExpect(jsonPath("$.response.apk", Matchers.nullValue()));
    }
}
