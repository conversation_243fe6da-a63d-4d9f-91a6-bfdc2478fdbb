package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.happyfresh.fulfillment.batch.service.BatchSndService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.JobRepository;
import org.hamcrest.Matchers;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.List;

import static org.mockito.ArgumentMatchers.*;

public class BatchDeliverTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private AgentFactory agentFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private BatchSndService batchSndService;

    @Test
    public void returnUnauthorizedIfNotOwner() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER);
        User driver = userFactory.createUserData(Role.Name.DRIVER, shopper.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        Batch batch = batchFactory.createBatch(driver, driver, slots.get(0), Batch.Type.DELIVERY);
        Shipment shipment = batch.getJobs().get(0).getShipment();

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/"+ batch.getId() +"/shipments/"+ shipment.getNumber() +"/deliver")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isUnauthorized());
    }

    @Test
    public void returnExceptionIfCurrentJobStateIsNotAccepted() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        List<Batch> batches = batchFactory.createBatches(1, driver, driver, slots.get(0));
        Batch shoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        shoppingBatches.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();
        Shipment shipment = deliveryBatch.getJobs().get(0).getShipment();

        batchSndService.pickup(deliveryBatch.getId());

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/"+ deliveryBatch.getId() +"/shipments/"+ shipment.getNumber() +"/deliver")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isUnprocessableEntity());
    }

    @Test
    public void returnExceptionIfCurrentJobIsCancelled() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        User externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, driver.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        List<Batch> batches = batchFactory.createBatches(1, driver, driver, slots.get(0));
        Thread.sleep(500); // Prevent race condition with other tests.

        Batch shoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        shoppingBatches.getJobs().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();
        Shipment shipment = deliveryBatch.getJobs().get(0).getShipment();

        batchSndService.pickup(deliveryBatch.getId());
        batchSndService.accept(shipment.getNumber());

        // Cancel Shipment/Job
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/"+ shipment.getNumber() +"/cancel")
                .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken()));

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/"+ deliveryBatch.getId() +"/shipments/"+ shipment.getNumber() +"/deliver")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isUnprocessableEntity())
                .andExpect(MockMvcResultMatchers.jsonPath("$.errors[0].message", Matchers.equalTo("One of orders in batch has been canceled. Please reload the shopping list to continue shopping.")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.errors[0].type", Matchers.equalTo("ShipmentCancelledException")));
    }

    @Test
    public void return200IfSuccess() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        List<Batch> batches = batchFactory.createBatches(1, driver, driver, slots.get(0));
        Batch shoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        shoppingBatches.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();
        Shipment shipment = deliveryBatch.getJobs().get(0).getShipment();

        batchSndService.pickup(deliveryBatch.getId());
        batchSndService.accept(shipment.getNumber());

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/"+ deliveryBatch.getId() +"/shipments/"+ shipment.getNumber() +"/deliver")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.state", Matchers.equalTo(Job.State.DELIVERING.toString())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.states[*].state", Matchers.containsInAnyOrder(Job.State.INITIAL.toString(), Job.State.STARTED.toString(), Job.State.ACCEPTED.toString(), Job.State.DELIVERING.toString())))
                .andExpect(MockMvcResultMatchers.status().isOk());

    }

    @Test
    public void shouldPublishFleetSyncMessage() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        agentFactory.createAgent(driver, stockLocations.get(0), Agent.State.WORKING);

        List<Batch> batches = batchFactory.createBatches(1, driver, driver, slots.get(0));
        Batch shoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        shoppingBatches.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();
        Shipment shipment = deliveryBatch.getJobs().get(0).getShipment();

        batchSndService.pickup(deliveryBatch.getId());
        batchSndService.accept(shipment.getNumber());

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/"+ deliveryBatch.getId() +"/shipments/"+ shipment.getNumber() +"/deliver")
            .header("X-Fulfillment-User-Token", driver.getToken())
            .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()));

    }

    @Test
    public void shouldPublishFleetSyncMessageForOnDemand() throws Exception {
        User driver = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        Slot slot = slotFactory.createOnDemandSlot(stockLocations.get(0), driver);

        agentFactory.createAgent(driver, stockLocations.get(0), Agent.State.WORKING);

        List<Batch> batches = batchFactory.createOnDemandRangerBatches(1, driver, driver, slot);

        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.ON_DEMAND).findAny().get();
        Shipment shipment = deliveryBatch.getJobs().get(0).getShipment();
        deliveryBatch.getJobs().stream().forEach(job -> {
            job.setState(Job.State.STARTED);
            job.setState(Job.State.FINALIZING);
            job.setState(Job.State.ACCEPTED);
            jobRepository.save(job);
        });

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/"+ deliveryBatch.getId() +"/shipments/"+ shipment.getNumber() +"/deliver")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()));

    }
}
