package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.AgentHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.BatchHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.SetSlotHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.ShipmentHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.JobRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import org.elasticsearch.common.geo.GeoPoint;
import org.json.JSONObject;
import org.junit.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

public class SlotOptimizationLazyExceptionTest extends BaseTest {

    @Autowired
    private AgentHelper agentHelper;

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private CountryFactory countryFactory;

    @Autowired
    private SetSlotHelper slotHelper;

    @Autowired
    private ShipmentHelper shipmentHelper;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private BatchHelper batchHelper;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private ClusterFactory clusterFactory;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private JobFactory jobFactory;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private JobRepository jobRepository;

    private User driver;
    private StockLocation stockLocation;
    private Shift dShift;
    private User extSysAdmin;
    private JSONObject shipmentObj1;
    private List<Slot> slots;

    @BeforeClass
    public static void initProperty() {
        System.setProperty("kafka.listener.enabled", "true");
    }

    @AfterClass
    public static void resetProperty() {
        System.setProperty("kafka.listener.enabled", "false");
    }

    @Before
    public void setup() throws Exception {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));

        TimeUnit.SECONDS.sleep(1);
        extSysAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        userFactory.createUserData(Role.Name.SYSTEM_ADMIN, extSysAdmin.getTenant());
        driver = userFactory.createUserData(Role.Name.DRIVER, extSysAdmin.getTenant());
        userFactory.createUserData(Role.Name.SHOPPER, extSysAdmin.getTenant());
        Country countryID = countryFactory.createCountry(extSysAdmin);
        setupStockLocations(countryID);

        Cluster cluster = stockLocation.getCluster();
        Map<String, String> clsPreferences = cluster.getPreferences();
        clsPreferences.put("enable_driver_auto_assignment", "true");
        clusterFactory.save(cluster);

        Map<String, String> slPreferences = stockLocation.getPreferences();
        slPreferences.put("enable_shopper_auto_assignment", "true");
        slPreferences.put("instant_delivery_cutoff_time", "10");
        stockLocationFactory.save(stockLocation);

        shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 1, 16, 5, extSysAdmin);
        dShift = shiftFactory.createDriverShifts(stockLocation, LocalDateTime.now(), 1, 16, 5, extSysAdmin).get(0);

        slots = slotFactory.createLongerDeliverySlots(stockLocation, extSysAdmin, 1, 2, LocalDateTime.now());

    }

    private void setupStockLocations(Country countryID) {
        List<StockLocation> stockLocations1 = stockLocationFactory.createListOnSameCountry(1, extSysAdmin, countryID, StockLocation.Type.ORIGINAL, "001");
        setStockLocationConfiguration(stockLocations1);
        stockLocation = stockLocations1.get(0);

    }

    private void setStockLocationConfiguration(List<StockLocation> stockLocations1) {
        stockLocations1.forEach(sl -> {
            sl.setExternalId(sl.getId() + 10);
            sl.setShoppingBatchNotifiedOffset(120);
            stockLocationFactory.save(sl);
        });
    }

    private void addJobState(Job job, Job.State state) {
        job.setState(state);
        jobFactory.save(job);
    }

    @Test
    public void test_slotOptimization_whenCallAdjustAsynchronously_shouldNotFailWhenProcessingSlotOptimization() throws Exception {
        Slot slot = slots.get(1);

        agentHelper.clockInToShift(driver, stockLocation, stockLocation.getLat(), stockLocation.getLon(), dShift);

        dShift.setCount(5);
        shiftFactory.save(dShift);

        shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), 1, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        JSONObject shipmentObj2 = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), 1, "Order-2", new GeoPoint(-6.291202, 106.7855), "Lintasarta");

        slotHelper.assertSetRegularSlotV2(true, shipmentObj1, extSysAdmin);
        slotHelper.assertSetRegularSlotV2(true, shipmentObj2, extSysAdmin);

        shipmentHelper.adjustShipment(shipmentObj1, extSysAdmin);

        CompletableFuture<Void> custAdjust1 = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        shipmentHelper.adjustShipment(shipmentObj1, extSysAdmin);
                    } catch (Exception e) {
                        System.out.println(e);
                    }
                    return null;
                });

        custAdjust1.get();

        TimeUnit.SECONDS.sleep(3L);

        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            deliveryBatches.sort(Comparator.comparing(Batch::getId));
            Assert.assertEquals(2, deliveryBatches.size());

            Batch deliveryBatch = deliveryBatches.get(0);
            Assert.assertTrue(deliveryBatch.isAutoAssigned());
            Assert.assertEquals(driver.getId(), deliveryBatch.getUser().getId());
        });

        Shipment shipment = shipmentRepository.findByOrderNumber("Order-1");
        Job dJob = jobRepository.findAllByShipmentIdInAndTypeIn(List.of(shipment.getId()), List.of(Job.Type.DRIVER)).get(0);
        Job sJob = jobRepository.findAllByShipmentIdInAndTypeIn(List.of(shipment.getId()), List.of(Job.Type.SHOPPER)).get(0);

        addJobState(sJob, Job.State.FINISHED);

        CompletableFuture<Void> fleetAccept = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        batchHelper.assertStartDeliveryBatch(true, dJob.getBatch(), driver);
                    } catch (Exception e) {
                        System.out.println(e);
                    }
                    return null;
                });

        CompletableFuture<Void> custAdjust = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        shipmentHelper.adjustShipment(shipmentObj2, extSysAdmin);
                    } catch (Exception e) {
                        System.out.println(e);
                    }
                    return null;
                });
        CompletableFuture<Void> combinedFuture1 = CompletableFuture.allOf(fleetAccept, custAdjust);
        combinedFuture1.get();

        Assert.assertTrue(fleetAccept.isDone());
        Assert.assertTrue(custAdjust.isDone());

        TimeUnit.SECONDS.sleep(3L);

        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            deliveryBatches.sort(Comparator.comparing(Batch::getId));
            Assert.assertEquals(2, deliveryBatches.size());

            Batch deliveryBatch = deliveryBatches.get(0);
            Assert.assertTrue(deliveryBatch.isAutoAssigned());
            Assert.assertEquals(driver.getId(), deliveryBatch.getUser().getId());

            Batch deliveryBatch2 = deliveryBatches.get(1);
            Assert.assertFalse(deliveryBatch2.isAutoAssigned());
        });
    }

    @Test
    public void test_slotOptimization_twoOrdersGetAdjustedConcurrently_shouldNotFailWhenProcessingSlotOptimization() throws Exception {
        TimeUnit.SECONDS.sleep(1);
        Slot slot = slots.get(1);

        agentHelper.clockInToShift(driver, stockLocation, stockLocation.getLat(), stockLocation.getLon(), dShift);

        dShift.setCount(5);
        shiftFactory.save(dShift);

        shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), 1, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        JSONObject shipmentObj2 = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), 1, "Order-2", new GeoPoint(-6.291202, 106.7855), "Lintasarta");

        slotHelper.assertSetRegularSlotV2(true, shipmentObj1, extSysAdmin);
        slotHelper.assertSetRegularSlotV2(true, shipmentObj2, extSysAdmin);

        CompletableFuture<Void> custAdjust1 = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        shipmentHelper.adjustShipment(shipmentObj1, extSysAdmin);
                    } catch (Exception e) {
                        System.out.println(e);
                    }
                    return null;
                });

        CompletableFuture<Void> custAdjust2 = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        shipmentHelper.adjustShipment(shipmentObj2, extSysAdmin);
                    } catch (Exception e) {
                        System.out.println(e);
                    }
                    return null;
                });
        CompletableFuture<Void> combinedFuture1 = CompletableFuture.allOf(custAdjust1, custAdjust2);
        combinedFuture1.get();

        Assert.assertTrue(custAdjust1.isDone());
        Assert.assertTrue(custAdjust2.isDone());

        TimeUnit.SECONDS.sleep(3L);

        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            Assert.assertEquals(1, deliveryBatches.size());

            Batch deliveryBatch = deliveryBatches.get(0);
            Assert.assertTrue(deliveryBatch.isAutoAssigned());
            Assert.assertEquals(driver.getId(), deliveryBatch.getUser().getId());
        });
    }
}
