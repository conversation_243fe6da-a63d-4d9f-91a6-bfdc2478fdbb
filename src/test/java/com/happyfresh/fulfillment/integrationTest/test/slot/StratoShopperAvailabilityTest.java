package com.happyfresh.fulfillment.integrationTest.test.slot;

import com.happyfresh.fulfillment.common.property.StratoProperty;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.MockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.NoResetRequestExpectationManager;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import org.apache.kafka.common.utils.Time;
import org.elasticsearch.common.geo.GeoPoint;
import org.hamcrest.Matchers;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.web.client.RestTemplate;

import java.time.*;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

public class StratoShopperAvailabilityTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private ClusterFactory clusterFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private MockServerHelper mockServerHelper;

    @Autowired
    private StratoProperty stratoProperty;

    @Autowired
    @Qualifier("AllowGetBodyRestTemplate")
    private RestTemplate restTemplate;

    private User admin;
    private StockLocation stockLocation1;
    private List<Slot> slots;
    private MockRestServiceServer mockServer;

    @Before
    public void setUp() throws InterruptedException, JSONException {
        admin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);

        // StockLocation should be fulfilled by Strato
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        stockLocation1 = stockLocations.get(0);
        stockLocation1.setEnabler(StockLocation.Enabler.HFC);
        stockLocation1.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);
        stockLocationFactory.save(stockLocation1);

        // Cluster should be DDS
        Cluster cluster = stockLocation1.getCluster();
        cluster.setSlotType(Slot.Type.LONGER_DELIVERY);
        clusterFactory.save(cluster);

        // No need for Shopper Shift
        LocalDateTime slotStart = LocalDateTime.now().withSecond(0).withNano(0);
        slots = slotFactory.createLongerDeliverySlots(stockLocation1, admin, 2, 2, slotStart);
        shiftFactory.createShift(stockLocation1, admin, Shift.Type.DRIVER, slots.get(0).getStartTime(), slots.get(slots.size()-1).getEndTime(), 2);

        Time.SYSTEM.sleep(100);
    }

    @Test
    public void shouldAvailable_withStratoResponseAvailable_withoutShopperShift() throws Exception {
        mockSuccessStratoGetCapacity(slots.get(0).getId(), slots.get(1).getId());
        verifySlot(true, true);
    }

    @Test
    public void shouldUnavailable_withNonStratoStockLocation() throws Exception {
        stockLocation1.setEnabler(null);
        stockLocation1.setEnablerPlatform(null);
        stockLocationFactory.save(stockLocation1);
        shiftFactory.createShift(stockLocation1, admin, Shift.Type.SHOPPER, slots.get(0).getStartTime().minusHours(2), slots.get(1).getEndTime().minusHours(2), 1);
        verifySlot(true, true);
    }

    @Test
    public void shouldUnavailable_withStratoErrorResponse() throws Exception {
        mockErrorStratoGetCapacity(HttpStatus.INTERNAL_SERVER_ERROR);
        verifySlot(false, false);

        mockErrorStratoGetCapacity(HttpStatus.UNPROCESSABLE_ENTITY);
        verifySlot(false, false);
    }

    @Test
    public void shouldUnavailable_withStratoUnexpectedNullResponse() throws Exception {
        mockServer = MockRestServiceServer
                .bindTo(restTemplate)
                .bufferContent()
                .build(new NoResetRequestExpectationManager());
        String uri = stratoProperty.getBaseUrl() + "/store/" + stockLocation1.getExternalId() + "/capacity";
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body("[]"));
        verifySlot(false, false);
    }

    @Test
    public void shouldUnavailable_withStratoResponseUnavailable() throws Exception {
        mockSuccessStratoGetCapacity(slots.get(0).getId());
        verifySlot(true, false);
    }

    private void verifySlot(Boolean... isAvailable) throws Exception {
        List<Boolean> availableList = Arrays.stream(isAvailable).collect(Collectors.toList());

        int numOfItems = 3;
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation1, numOfItems, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");

        ResultActions resultActions = mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        for (int i = 0; i < availableList.size(); i++) {
            resultActions.andExpect(MockMvcResultMatchers.jsonPath("$.slots["+i+"].available", Matchers.equalTo(availableList.get(i))));
        }
    }

    private void mockSuccessStratoGetCapacity(Long... availableSlotIds) throws JSONException {
        List<Long> availableSlotIdList = Arrays.stream(availableSlotIds).collect(Collectors.toList());

        mockServer = MockRestServiceServer
                .bindTo(restTemplate)
                .bufferContent()
                .build(new NoResetRequestExpectationManager());
        String uri = stratoProperty.getBaseUrl() + "/store/" + stockLocation1.getExternalId() + "/capacity";
        JSONArray respArray = new JSONArray();
        for (Slot slot : slots) {
            JSONObject respObj = new JSONObject();
            respObj.put("delivery_slot", DateTimeUtil.localDateTimeToStratoDateTimeString(slot.getStartTime()));
            respObj.put("book_available", availableSlotIdList.contains(slot.getId()));
            respArray.put(respObj);
        }
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(respArray.toString()));
    }

    private void mockErrorStratoGetCapacity(HttpStatus httpStatus) {
        mockServer = MockRestServiceServer
                .bindTo(restTemplate)
                .bufferContent()
                .build(new NoResetRequestExpectationManager());
        String uri = stratoProperty.getBaseUrl() + "/store/" + stockLocation1.getExternalId() + "/capacity";
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withStatus(httpStatus)
                        .contentType(MediaType.APPLICATION_JSON).body("Strato default error message"));
    }
}
