package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.messaging.activemq.type.MessageDestination;
import com.happyfresh.fulfillment.common.property.LalamoveProperty;
import com.happyfresh.fulfillment.common.service.SegmentIOService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.BatchHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.MockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.SetSlotHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.ShipmentHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.lalamove.form.LalamoveGetQuotationForm;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import org.elasticsearch.common.geo.GeoPoint;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.*;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.*;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

public class BatchLalamoveTriggerTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private ItemFactory itemFactory;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private StateRepository stateRepository;

    @Autowired
    private SetSlotHelper slotHelper;

    @Autowired
    private ShipmentHelper shipmentHelper;

    @Autowired
    private BatchHelper batchHelper;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private LalamoveServiceTypeFactory lalamoveServiceTypeFactory;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private LalamoveDeliveryRepository lalamoveDeliveryRepository;

    @Autowired
    private MockServerHelper mockServerHelper;

    @Autowired
    private LalamoveProperty lalamoveProperty;

    @Autowired
    private LalamoveDeliveryFactory lalamoveDeliveryFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private LalamoveServiceTypeRepository lalamoveServiceTypeRepository;

    @MockBean
    private OrderService orderService;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private TenantRepository tenantRepository;

    @Captor
    private ArgumentCaptor<ImmutableMap<String, Object>> immutableMapArgumentCaptor;

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private CountryFactory countryFactory;

    @Autowired
    private LalamoveServiceTypeRepository serviceTypeRepository;


    private User systemAdmin;
    private User admin;
    private User userAdmin;
    private User shopper;

    @Before
    public void setup() throws InterruptedException {
        systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN);
        admin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, systemAdmin.getTenant());
        userAdmin = userFactory.createUserData(Role.Name.ADMIN, systemAdmin.getTenant());
        shopper = userFactory.createUserData(Role.Name.SHOPPER, systemAdmin.getTenant());
        Thread.sleep(400);
    }


    @Test
    public void finalizeLalamoveShoppingBatch_shouldTriggerPlaceOrder() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableLalamove(true);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<String, String>(){{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000");
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);
        Slot slot = slotFactory.createSlot(stockLocation, admin, 1, 0);
        /*
            Create Order-1 that eligible for Lalamove VAN:
            - Length 210.0
            - Width 150.0
            - Height 120.0
            - Weight 600.0
         */
        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);
        JSONObject item1 = shipmentJsonObjectFactory.createItemObject(1L, "SKU-000-", 1, 300.0, 60.0, 75.0, 105.0);
        item1.put("average_weight", "300.0");
        JSONObject item2 = shipmentJsonObjectFactory.createItemObject(2L, "SKU-001-", 1, 300.0, 60.0, 75.0, 105.0);
        item2.put("average_weight", "300.0");
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(item1);
        itemsArray.put(item2);
        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta", itemsArray);

        // Mock - Get Quotation Request
        String uri = lalamoveProperty.getBaseUrl() + "/v2/quotations";
        String responseBody = "{ \"totalFee\": \"35000\", \"totalFeeCurrency\": \"IDR\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.max(2), requestTo(uri))
            .andExpect(method(HttpMethod.POST))
            .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));
        // Mock - Place Order Request
        uri = lalamoveProperty.getBaseUrl() + "/v2/orders";
        responseBody = "{ \"customerOrderId\": \"DEPRECATED_LALAMOVE_ID\", \"orderRef\": \"LALAMOVE-EXTERNAL-ID\" }";
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
            .andExpect(method(HttpMethod.POST))
            .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));

        slotHelper.assertSetRegularSlot(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        Thread.sleep(200);

        JSONArray itemFinalizeArray = new JSONArray();
        item1.put("id", 1L);
        item1.put("found_qty", 1);
        item2.put("id", 2L);
        item2.put("found_qty", 1);
        itemFinalizeArray.put(item1);
        itemFinalizeArray.put(item2);
        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipmentObj1.get("number"));
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemFinalizeArray);
        requestBody.put("shipment_numbers", shipmentsArray);

        String urlFinalize = String.format("/api/v3/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(urlFinalize)
            .header("X-Fulfillment-User-Token", shopper.getToken())
            .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
            .content(requestBody.toString())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Thread.sleep(200);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("LALAMOVE-EXTERNAL-ID", lalamoveDelivery.getExternalOrderId());
            Assert.assertEquals(BigDecimal.valueOf(40000.0), lalamoveDelivery.getPrice()); // 35000 + 5000 (Addition)
            Assert.assertEquals("VAN", lalamoveDelivery.getServiceType());
            Assert.assertEquals(BigDecimal.valueOf(40000.0), lalamoveDelivery.getInitialPrice()); // 35000 + 5000 (Addition)
            Assert.assertEquals("VAN", lalamoveDelivery.getInitialServiceType());
        });
    }

    @Test
    public void finalizeLalamoveShoppingBatch_shouldTriggerPlaceOrder_withLlmMarketHeader() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableLalamove(true);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<String, String>(){{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000");
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);

        final String lalamoveCityCode = "ID_JKT";
        State state = stockLocation.getState();
        state.setPreferences(new HashMap<String, String>() {{
            put("lalamove_city_code", lalamoveCityCode);
        }});
        stateRepository.save(state);

        Slot slot = slotFactory.createSlot(stockLocation, admin, 1, 0);
        /*
            Create Order-1 that eligible for Lalamove VAN:
            - Length 210.0
            - Width 150.0
            - Height 120.0
            - Weight 600.0
         */
        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);
        JSONObject item1 = shipmentJsonObjectFactory.createItemObject(1L, "SKU-000-", 1, 300.0, 60.0, 75.0, 105.0);
        item1.put("average_weight", "300.0");
        JSONObject item2 = shipmentJsonObjectFactory.createItemObject(2L, "SKU-001-", 1, 300.0, 60.0, 75.0, 105.0);
        item2.put("average_weight", "300.0");
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(item1);
        itemsArray.put(item2);
        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta", itemsArray);

        // Mock - Get Quotation Request
        String uri = lalamoveProperty.getBaseUrl() + "/v2/quotations";
        String responseBody = "{ \"totalFee\": \"35000\", \"totalFeeCurrency\": \"IDR\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.max(2), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andExpect(header("X-LLM-Market", lalamoveCityCode))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));
        // Mock - Place Order Request
        uri = lalamoveProperty.getBaseUrl() + "/v2/orders";
        responseBody = "{ \"customerOrderId\": \"DEPRECATED_LALAMOVE_ID\", \"orderRef\": \"LALAMOVE-EXTERNAL-ID\" }";
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andExpect(header("X-LLM-Market", lalamoveCityCode))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));

        slotHelper.assertSetRegularSlot(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        Thread.sleep(200);

        JSONArray itemFinalizeArray = new JSONArray();
        item1.put("id", 1L);
        item1.put("found_qty", 1);
        item2.put("id", 2L);
        item2.put("found_qty", 1);
        itemFinalizeArray.put(item1);
        itemFinalizeArray.put(item2);
        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipmentObj1.get("number"));
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemFinalizeArray);
        requestBody.put("shipment_numbers", shipmentsArray);

        String urlFinalize = String.format("/api/v3/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(urlFinalize)
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                        .content(requestBody.toString())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Thread.sleep(200);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("LALAMOVE-EXTERNAL-ID", lalamoveDelivery.getExternalOrderId());
            Assert.assertEquals(BigDecimal.valueOf(40000.0), lalamoveDelivery.getPrice()); // 35000 + 5000 (Addition)
            Assert.assertEquals("VAN", lalamoveDelivery.getServiceType());
            Assert.assertEquals(BigDecimal.valueOf(40000.0), lalamoveDelivery.getInitialPrice()); // 35000 + 5000 (Addition)
            Assert.assertEquals("VAN", lalamoveDelivery.getInitialServiceType());
        });
    }

    @Ignore
    @Test
    public void finalizeLalamoveShoppingBatchBeforeOffset_shouldQueueJob() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableLalamove(true);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<String, String>(){{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000");
            put("grab_express_offset_time", "30");
        }});
        stockLocationFactory.save(stockLocation);
        Slot slot = slotFactory.createSlot(stockLocation, admin, 0, 2);
        /*
            Create Order-1 that eligible for Lalamove VAN:
            - Length 210.0
            - Width 150.0
            - Height 120.0
            - Weight 600.0
         */
        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);
        JSONObject item1 = shipmentJsonObjectFactory.createItemObject(1L, "SKU-000-", 1, 300.0, 60.0, 75.0, 105.0);
        item1.put("average_weight", "300.0");
        JSONObject item2 = shipmentJsonObjectFactory.createItemObject(2L, "SKU-001-", 1, 300.0, 60.0, 75.0, 105.0);
        item2.put("average_weight", "300.0");
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(item1);
        itemsArray.put(item2);
        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta", itemsArray);

        // Mock - Get Quotation Request
        String uri = lalamoveProperty.getBaseUrl() + "/v2/quotations";
        String responseBody = "{ \"totalFee\": \"35000\", \"totalFeeCurrency\": \"IDR\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
            .andExpect(method(HttpMethod.POST))
            .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));

        slotHelper.assertSetRegularSlot(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        Thread.sleep(200);

        JSONArray itemFinalizeArray = new JSONArray();
        item1.put("id", 1L);
        item1.put("found_qty", 1);
        item2.put("id", 2L);
        item2.put("found_qty", 1);
        itemFinalizeArray.put(item1);
        itemFinalizeArray.put(item2);
        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipmentObj1.get("number"));
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemFinalizeArray);
        requestBody.put("shipment_numbers", shipmentsArray);

        String urlFinalize = String.format("/api/v3/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(urlFinalize)
            .header("X-Fulfillment-User-Token", shopper.getToken())
            .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
            .content(requestBody.toString())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Thread.sleep(200);

        mockServer.verify();
        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.INITIAL, lalamoveDelivery.getStatus());
            Assert.assertEquals(0, (int) lalamoveDelivery.getTryCount());
            Assert.assertEquals(slot.getStartTime().minusMinutes(30), lalamoveDelivery.getScheduleAt());
        });
    }

    @Test
    public void finalizeLalamoveShoppingBatch_withPlaceOrderFailed() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableLalamove(true);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<String, String>(){{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000"); // Addition fee
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);
        Slot slot = slotFactory.createSlot(stockLocation, admin, 1, 0);
        /*
            Create Order-1 that eligible for Lalamove VAN:
            - Length 210.0
            - Width 150.0
            - Height 120.0
            - Weight 600.0
         */
        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);
        JSONObject item1 = shipmentJsonObjectFactory.createItemObject(1L, "SKU-000-", 1, 300.0, 60.0, 75.0, 105.0);
        item1.put("average_weight", "300.0");
        JSONObject item2 = shipmentJsonObjectFactory.createItemObject(2L, "SKU-001-", 1, 300.0, 60.0, 75.0, 105.0);
        item2.put("average_weight", "300.0");
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(item1);
        itemsArray.put(item2);
        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta", itemsArray);

        // Mock - Get Quotation Request
        String uri = lalamoveProperty.getBaseUrl() + "/v2/quotations";
        String responseBody = "{ \"totalFee\": \"35000\", \"totalFeeCurrency\": \"IDR\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.min(1), requestTo(uri))
            .andExpect(method(HttpMethod.POST))
            .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));
        // Mock - Place Order Request - 409
        uri = lalamoveProperty.getBaseUrl() + "/v2/orders";
        responseBody = "{ \"message\": \"ERR_PRICE_MISMATCH\" }"; // 409 - Conflict
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
            .andExpect(method(HttpMethod.POST))
            .andRespond(withStatus(HttpStatus.CONFLICT).contentType(MediaType.APPLICATION_JSON).body(responseBody));

        slotHelper.assertSetRegularSlot(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);

        JSONArray itemFinalizeArray = new JSONArray();
        item1.put("id", 1L);
        item1.put("found_qty", 1);
        item2.put("id", 2L);
        item2.put("found_qty", 1);
        itemFinalizeArray.put(item1);
        itemFinalizeArray.put(item2);
        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipmentObj1.get("number"));
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemFinalizeArray);
        requestBody.put("shipment_numbers", shipmentsArray);

        String urlFinalize = String.format("/api/v3/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(urlFinalize)
            .header("X-Fulfillment-User-Token", shopper.getToken())
            .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
            .content(requestBody.toString())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Thread.sleep(200);


        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.INITIAL, lalamoveDelivery.getStatus());
            Assert.assertNull(lalamoveDelivery.getExternalOrderId());
            Assert.assertNull(lalamoveDelivery.getPrice());
        });
    }

    @Test
    public void finalizeLalamoveShoppingBatch_withLalamoveOrderInitial_withNoServiceTypeFound_shouldSwitchToHf() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableLalamove(true);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<String, String>(){{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000"); // Addition fee
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);
        Slot slot = slotFactory.createSlot(stockLocation, admin, 1, 0);

        Country country = stockLocation.getState().getCountry();
        LalamoveServiceType motorServiceType = lalamoveServiceTypeFactory.createMotorcycleServiceType(country,admin);
        LalamoveServiceType mpvServiceType = lalamoveServiceTypeFactory.createMPVServiceType(country,admin);
        lalamoveServiceTypeFactory.saveAll(Lists.newArrayList(motorServiceType, mpvServiceType));

        JSONObject item1 = shipmentJsonObjectFactory.createItemObject(1L, "SKU-000-", 1, 60.0, 60.0, 75.0, 105.0);
        JSONObject item2 = shipmentJsonObjectFactory.createItemObject(2L, "SKU-000-", 1, 60.0, 60.0, 75.0, 105.0);
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(item1);
        itemsArray.put(item2);
        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta", itemsArray);

        // Mock - Get Quotation Request
        String uri = lalamoveProperty.getBaseUrl() + "/v2/quotations";
        String responseBody = "{ \"totalFee\": \"35000\", \"totalFeeCurrency\": \"IDR\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.min(1), requestTo(uri))
            .andExpect(method(HttpMethod.POST))
            .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));

        slotHelper.assertSetRegularSlot(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        JSONObject item3 = shipmentJsonObjectFactory.createItemObject(2L, "SKU-000-9", 3, 60.0, 60.0, 75.0, 105.0);
        JSONObject orderTotalObj = new JSONObject();
        orderTotalObj.put("order_total", 50000);
        orderTotalObj.put("item", item3);

        mvc.perform(MockMvcRequestBuilders.put("/api/v2/shipments/Order-1/items")
            .header("X-Fulfillment-User-Token", admin.getToken())
            .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(orderTotalObj.toString()))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);

        JSONArray itemFinalizeArray = new JSONArray();
        item1.put("id", 1L);
        item1.put("found_qty", 1);
        item1.put("weight", 2.0);
        item2.put("id", 2L);
        item2.put("found_qty", 1);
        item2.put("weight", 2.0);
        item3.put("id", 3L);
        item3.put("found_qty", 3);
        item3.put("weight", 6.0);
        itemFinalizeArray.put(item1);
        itemFinalizeArray.put(item2);
        itemFinalizeArray.put(item3);
        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipmentObj1.get("number"));
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemFinalizeArray);
        requestBody.put("shipment_numbers", shipmentsArray);

        String urlFinalize = String.format("/api/v3/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(urlFinalize)
            .header("X-Fulfillment-User-Token", shopper.getToken())
            .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
            .content(requestBody.toString())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Thread.sleep(200);

        transactionHelper.withNewTransaction(() -> {
            Batch deliveryBatch = batchRepository.findByType(Batch.Type.DELIVERY).get(0);
            Assert.assertEquals(Batch.DeliveryType.NORMAL, deliveryBatch.getDeliveryType());
            Assert.assertEquals(null, deliveryBatch.getTplType());
            Assert.assertEquals(true, deliveryBatch.isSwitchedToHf());
        });
    }

    @Test
    public void finalizeLalamoveShoppingBatch_withoutLalamoveOrderInitial_withNoServiceTypeFound_shouldSwitchToHf() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableLalamove(true);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<String, String>(){{
            put("enable_lalamove_delivery_fee", "false");
            put("lalamove_flat_service_fee", "5000"); // Addition fee
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);
        Slot slot = slotFactory.createSlot(stockLocation, admin, 1, 0);

        Country country = stockLocation.getState().getCountry();
        LalamoveServiceType motorServiceType = lalamoveServiceTypeFactory.createMotorcycleServiceType(country,admin);
        LalamoveServiceType mpvServiceType = lalamoveServiceTypeFactory.createMPVServiceType(country,admin);
        lalamoveServiceTypeFactory.saveAll(Lists.newArrayList(motorServiceType, mpvServiceType));

        JSONObject item1 = shipmentJsonObjectFactory.createItemObject(1L, "SKU-000-", 1, 60.0, 60.0, 75.0, 105.0);
        JSONObject item2 = shipmentJsonObjectFactory.createItemObject(2L, "SKU-000-", 1, 60.0, 60.0, 75.0, 105.0);
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(item1);
        itemsArray.put(item2);
        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta", itemsArray);

        slotHelper.assertSetRegularSlot(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        JSONObject item3 = shipmentJsonObjectFactory.createItemObject(2L, "SKU-000-9", 3, 60.0, 60.0, 75.0, 105.0);
        JSONObject orderTotalObj = new JSONObject();
        orderTotalObj.put("order_total", 50000);
        orderTotalObj.put("item", item3);

        mvc.perform(MockMvcRequestBuilders.put("/api/v2/shipments/Order-1/items")
            .header("X-Fulfillment-User-Token", admin.getToken())
            .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(orderTotalObj.toString()))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);

        JSONArray itemFinalizeArray = new JSONArray();
        item1.put("id", 1L);
        item1.put("found_qty", 1);
        item1.put("weight", 2.0);
        item2.put("id", 2L);
        item2.put("found_qty", 1);
        item2.put("weight", 2.0);
        item3.put("id", 3L);
        item3.put("found_qty", 3);
        item3.put("weight", 6.0);
        itemFinalizeArray.put(item1);
        itemFinalizeArray.put(item2);
        itemFinalizeArray.put(item3);
        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipmentObj1.get("number"));
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemFinalizeArray);
        requestBody.put("shipment_numbers", shipmentsArray);

        String urlFinalize = String.format("/api/v3/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(urlFinalize)
            .header("X-Fulfillment-User-Token", shopper.getToken())
            .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
            .content(requestBody.toString())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Thread.sleep(200);

        transactionHelper.withNewTransaction(() -> {
            Batch deliveryBatch = batchRepository.findByType(Batch.Type.DELIVERY).get(0);
            Assert.assertEquals(Batch.DeliveryType.NORMAL, deliveryBatch.getDeliveryType());
            Assert.assertEquals(null, deliveryBatch.getTplType());
            Assert.assertEquals(true, deliveryBatch.isSwitchedToHf());
        });
    }

    private JSONObject setupShipment(Shipment shipment, StockLocation stockLocation, Slot slot) throws Exception {
        Item i1 = shipment.getItems().get(0);
        Item i2 = shipment.getItems().get(1);
        JSONObject item1 = shipmentJsonObjectFactory.createItemObject(1L, i1.getSku(), i1.getRequestedQty(), 1.0, 10.0, 10.0, 10.0);
        item1.put("average_weight", "1.0");
        JSONObject item2 = shipmentJsonObjectFactory.createItemObject(2L, i2.getSku(), i2.getRequestedQty(), 1.0, 10.0, 10.0, 10.0);
        item2.put("average_weight", "1.0");
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(item1);
        itemsArray.put(item2);
        return shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), shipment.getOrderNumber(), new GeoPoint(-6.291202, 106.7855), "Lintasarta", itemsArray);
    }

    @Test
    public void finalizeLalamoveShoppingBatch_withEnableLalamoveV3_shouldTriggerPlaceOrderWithV3API() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableLalamove(true);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<>() {{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000");
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);

        setupLalamoveV3(stockLocation, true);
        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);

        Tenant tenant = stockLocation.getTenant();
        Map<String, String> tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("enable_lalamove_api_v3", "true");
        tenantRepository.save(tenant);

        shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 2, 8, 1, admin);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, admin, 1, 4, LocalDateTime.now());

        // Mock - Get Quotation Request
        String response = getResponseFromResourceFile("lalamove_v3_get_quotation_response_success.json");
        String uri = lalamoveProperty.getBaseUrl() + "/v3/quotations";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.max(2), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));
        // Mock - Place Order Request
        uri = lalamoveProperty.getBaseUrl() + "/v3/orders";
        response = getResponseFromResourceFile("lalamove_v3_place_order_response_success.json");
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));


        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 1, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");

        slotHelper.assertSetRegularSlotV2(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        TimeUnit.MILLISECONDS.sleep(200);

        Shipment shipment = shipmentRepository.findByOrderNumberFetchItems("Order-1");

        batchHelper.shipmentFinalizeV3(shoppingBatch, shipment, shopper);

        TimeUnit.MILLISECONDS.sleep(1000);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("124620405314", lalamoveDelivery.getExternalOrderId());
            Assert.assertEquals(BigDecimal.valueOf(36900.0), lalamoveDelivery.getPrice()); // 31900 + 5000 (Addition)
            Assert.assertEquals("MOTORCYCLE", lalamoveDelivery.getServiceType());

        });

        mockServer.verify();

    }

    @Test
    public void finalizeLalamoveShoppingBatch_withPaymentMethodCOD_andCountryEnableLalamoveCOD_shouldPutCODInSpecialRequest() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        String orderNumber = "order001";

        Country countryMY = countryFactory.createCountryMY(admin);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin, false, countryMY);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocation.setEnableLalamove(true);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<>() {{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000");
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);

        Country country = stockLocation.getState().getCountry();
        Map<String, String> countryPreferences = country.getPreferences();
        countryPreferences.put("enable_lalamove_cod", "true");
        countryRepository.save(country);

        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);

        shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 2, 8, 1, admin);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, admin, 1, 4, LocalDateTime.now());

        // Mock - Get Quotation Request
        String uri = lalamoveProperty.getBaseUrl() + "/v2/quotations";
        String responseBody = "{ \"totalFee\": \"35000\", \"totalFeeCurrency\": \"IDR\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.max(2), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));
        // Mock - Place Order Request
        uri = lalamoveProperty.getBaseUrl() + "/v2/orders";
        responseBody = "{ \"customerOrderId\": \"DEPRECATED_LALAMOVE_ID\", \"orderRef\": \"124620405314\" }";
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));


        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 1, orderNumber, new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        shipmentObj1.put("order_payment_method", Shipment.COD_PAYMENT_TYPE);

        slotHelper.assertSetRegularSlotV2(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        TimeUnit.MILLISECONDS.sleep(200);

        Shipment shipment = shipmentRepository.findByOrderNumberFetchItems(orderNumber);

        batchHelper.shipmentFinalizeV3(shoppingBatch, shipment, shopper);

        TimeUnit.MILLISECONDS.sleep(1000);

        Mockito.verify(coralogixAPIService, Mockito.times(2)).sendLog(any(), isNull(),
                Mockito.eq("Lalamove Get Quotation Log"), any(), any(),
                immutableMapArgumentCaptor.capture()
        );

        ImmutableMap<String, Object> immutableMap = immutableMapArgumentCaptor.getAllValues().get(0);
        String requestPayload = (String) immutableMap.get("request_body");

        LalamoveGetQuotationForm lalamoveGetQuotationForm = mapper.readValue(requestPayload, LalamoveGetQuotationForm.class);
        Assert.assertEquals(2, lalamoveGetQuotationForm.getSpecialRequests().size());
        Assert.assertEquals("LALABAG", lalamoveGetQuotationForm.getSpecialRequests().get(0));
        Assert.assertEquals("COD", lalamoveGetQuotationForm.getSpecialRequests().get(1));

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
        });

        mockServer.verify();
    }

    private void setupLalamoveV3(StockLocation stockLocation, boolean isSetupServiceType) {
        String lalamoveCityCode = "JKT";
        if(isSetupServiceType)
            lalamoveServiceTypeFactory.createServiceTypeMotorAndMPVV3(admin, stockLocation, lalamoveCityCode);

        State state = stockLocation.getState();
        state.setPreferences(new HashMap<String, String>() {{
            put("lalamove_city_code", lalamoveCityCode);
        }});
        stateRepository.save(state);
    }

    @Test
    public void finalizeLalamoveShoppingBatch_v3_withLalamoveOrderInitial_withNoServiceTypeFound_shouldSwitchToHf() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableLalamove(true);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<>() {{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000");
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);

        setupLalamoveV3(stockLocation, false);
        lalamoveServiceTypeFactory.createMotorcycleServiceTypeV3(stockLocation.getState().getCountry(),admin, "JKT");
        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);

        Tenant tenant = stockLocation.getTenant();
        Map<String, String> tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("enable_lalamove_api_v3", "true");
        tenantRepository.save(tenant);

        shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 2, 8, 1, admin);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, admin, 1, 4, LocalDateTime.now());

        // Mock - Get Quotation Request
        String response = getResponseFromResourceFile("lalamove_v3_get_quotation_response_success.json");
        String uri = lalamoveProperty.getBaseUrl() + "/v3/quotations";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.max(2), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));
        // Mock - Place Order Request
        uri = lalamoveProperty.getBaseUrl() + "/v3/orders";
        response = getResponseFromResourceFile("lalamove_v3_place_order_response_success.json");
        mockServer.expect(ExpectedCount.never(), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));


        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 1, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");

        slotHelper.assertSetRegularSlotV2(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);

        TimeUnit.MILLISECONDS.sleep(200);

        JSONObject item3 = shipmentJsonObjectFactory.createItemObject(2L, "SKU-000-9", 1, 60.0, 60.0, 75.0, 105.0);
        JSONObject orderTotalObj = new JSONObject();
        orderTotalObj.put("order_total", 50000);
        orderTotalObj.put("item", item3);

        mvc.perform(MockMvcRequestBuilders.put("/api/v2/shipments/Order-1/items")
                        .header("X-Fulfillment-User-Token", admin.getToken())
                        .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(orderTotalObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Shipment shipment = shipmentRepository.findByOrderNumberFetchItems("Order-1");

        batchHelper.shipmentFinalizeV3(shoppingBatch, shipment, shopper);

        TimeUnit.MILLISECONDS.sleep(1000);

        transactionHelper.withNewTransaction(() -> {
            Batch deliveryBatch = batchRepository.findByType(Batch.Type.DELIVERY).get(0);
            Assert.assertEquals(Batch.DeliveryType.NORMAL, deliveryBatch.getDeliveryType());
            Assert.assertNull(deliveryBatch.getTplType());
            Assert.assertTrue(deliveryBatch.isSwitchedToHf());
        });

        mockServer.verify();
    }

    @Test
    public void finalizeLalamoveShoppingBatch_v3_withLalamoveOrderInitial_withWrongServiceTypeEnum_shouldSwitchToHf() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableLalamove(true);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<>() {{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000");
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);

        setupLalamoveV3(stockLocation, true);
        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);
        transactionHelper.withNewTransaction(() -> {
            List<LalamoveServiceType> lalamoveServiceTypes = serviceTypeRepository.findAll();
            lalamoveServiceTypes.stream().forEach(lalamoveServiceType -> {
                lalamoveServiceType.setKey("4X4");
            });
            serviceTypeRepository.saveAll(lalamoveServiceTypes);
        });

        Tenant tenant = stockLocation.getTenant();
        Map<String, String> tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("enable_lalamove_api_v3", "true");
        tenantRepository.save(tenant);

        shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 2, 8, 1, admin);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, admin, 1, 4, LocalDateTime.now());

        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 1, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");

        slotHelper.assertSetRegularSlotV2(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        TimeUnit.MILLISECONDS.sleep(200);

        Shipment shipment = shipmentRepository.findByOrderNumberFetchItems("Order-1");

        batchHelper.shipmentFinalizeV3(shoppingBatch, shipment, shopper);

        TimeUnit.MILLISECONDS.sleep(2000);

        transactionHelper.withNewTransaction(() -> {
            Batch deliveryBatch = batchRepository.findByType(Batch.Type.DELIVERY).get(0);
            Assert.assertEquals(Batch.DeliveryType.NORMAL, deliveryBatch.getDeliveryType());
            Assert.assertNull(deliveryBatch.getTplType());
            Assert.assertTrue(deliveryBatch.isSwitchedToHf());
        });
    }

    @Test
    public void finalizeLalamoveShoppingBatch_withPaymentMethodCOD_andCountryDisableLalamoveCOD_shouldNotPutCODInSpecialRequest() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        String orderNumber = "order001";

        Country countryMY = countryFactory.createCountryMY(admin);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin, false, countryMY);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocation.setEnableLalamove(true);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<>() {{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000");
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);

        Country country = stockLocation.getState().getCountry();
        Map<String, String> countryPreferences = country.getPreferences();
        countryPreferences.put("enable_lalamove_cod", "false");
        countryRepository.save(country);

        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);

        shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 2, 8, 1, admin);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, admin, 1, 4, LocalDateTime.now());

        // Mock - Get Quotation Request
        String uri = lalamoveProperty.getBaseUrl() + "/v2/quotations";
        String responseBody = "{ \"totalFee\": \"35000\", \"totalFeeCurrency\": \"IDR\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.max(2), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));
        // Mock - Place Order Request
        uri = lalamoveProperty.getBaseUrl() + "/v2/orders";
        responseBody = "{ \"customerOrderId\": \"DEPRECATED_LALAMOVE_ID\", \"orderRef\": \"124620405314\" }";
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));


        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 1, orderNumber, new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        shipmentObj1.put("order_payment_method", Shipment.COD_PAYMENT_TYPE);

        slotHelper.assertSetRegularSlotV2(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        TimeUnit.MILLISECONDS.sleep(200);

        Shipment shipment = shipmentRepository.findByOrderNumberFetchItems(orderNumber);

        batchHelper.shipmentFinalizeV3(shoppingBatch, shipment, shopper);

        TimeUnit.MILLISECONDS.sleep(1000);

        Mockito.verify(coralogixAPIService, Mockito.times(2)).sendLog(any(), isNull(),
                Mockito.eq("Lalamove Get Quotation Log"), any(), any(),
                immutableMapArgumentCaptor.capture()
        );

        ImmutableMap<String, Object> immutableMap = immutableMapArgumentCaptor.getAllValues().get(0);
        String requestPayload = (String) immutableMap.get("request_body");

        LalamoveGetQuotationForm lalamoveGetQuotationForm = mapper.readValue(requestPayload, LalamoveGetQuotationForm.class);
        Assert.assertEquals(1, lalamoveGetQuotationForm.getSpecialRequests().size());
        Assert.assertNotEquals("COD", lalamoveGetQuotationForm.getSpecialRequests().get(0));

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
        });

        mockServer.verify();
    }

    @Test
    public void finalizeLalamoveShoppingBatch_withEnableLalamoveV3_disableLalamoveDeliveryFee_shouldUseDDF() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocation.setEnableLalamove(true);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<>() {{
            put("enable_lalamove_delivery_fee", "false");
            put("lalamove_flat_service_fee", "5000");
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);

        setupLalamoveV3(stockLocation, true);
        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);

        Tenant tenant = stockLocation.getTenant();
        Map<String, String> tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("enable_lalamove_api_v3", "true");
        tenantRepository.save(tenant);

        shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 2, 8, 1, admin);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, admin, 1, 4, LocalDateTime.now());

        // Mock - Get Quotation Request
        String response = getResponseFromResourceFile("lalamove_v3_get_quotation_response_success.json");
        String uri = lalamoveProperty.getBaseUrl() + "/v3/quotations";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));
        // Mock - Place Order Request
        uri = lalamoveProperty.getBaseUrl() + "/v3/orders";
        response = getResponseFromResourceFile("lalamove_v3_place_order_response_success.json");
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));


        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 1, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");

        slotHelper.assertSetRegularSlotV2(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        TimeUnit.MILLISECONDS.sleep(200);

        Shipment shipment = shipmentRepository.findByOrderNumberFetchItems("Order-1");

        batchHelper.shipmentFinalizeV3(shoppingBatch, shipment, shopper);

        TimeUnit.MILLISECONDS.sleep(1000);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("124620405314", lalamoveDelivery.getExternalOrderId());
            Assert.assertEquals(BigDecimal.valueOf(0), lalamoveDelivery.getPrice()); // use shipment.getCost
            Assert.assertEquals("MOTORCYCLE", lalamoveDelivery.getServiceType());
        });

        mockServer.verify();
    }

    @Test
    public void finalizeLalamoveShoppingBatch_withPaymentMethodCOD_andOrderHappyCorporate_andCountryEnableLalamoveCOD_shouldNotPutCODInSpecialRequest() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        String orderNumber = "order001";

        Country countryMY = countryFactory.createCountryMY(admin);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin, false, countryMY);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocation.setEnableLalamove(true);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<>() {{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000");
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);

        Country country = stockLocation.getState().getCountry();
        Map<String, String> countryPreferences = country.getPreferences();
        countryPreferences.put("enable_lalamove_cod", "true");
        countryRepository.save(country);

        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);

        shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 2, 8, 1, admin);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, admin, 1, 4, LocalDateTime.now());

        // Mock - Get Quotation Request
        String uri = lalamoveProperty.getBaseUrl() + "/v2/quotations";
        String responseBody = "{ \"totalFee\": \"35000\", \"totalFeeCurrency\": \"IDR\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.max(2), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));
        // Mock - Place Order Request
        uri = lalamoveProperty.getBaseUrl() + "/v2/orders";
        responseBody = "{ \"customerOrderId\": \"DEPRECATED_LALAMOVE_ID\", \"orderRef\": \"124620405314\" }";
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));


        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 1, orderNumber, new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        shipmentObj1.put("order_company_id", "101");
        shipmentObj1.put("order_payment_method", Shipment.COD_PAYMENT_TYPE);

        slotHelper.assertSetRegularSlotV2(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        TimeUnit.MILLISECONDS.sleep(200);

        Shipment shipment = shipmentRepository.findByOrderNumberFetchItems(orderNumber);

        batchHelper.shipmentFinalizeV3(shoppingBatch, shipment, shopper);

        TimeUnit.MILLISECONDS.sleep(1000);

        Mockito.verify(coralogixAPIService, Mockito.times(2)).sendLog(any(), isNull(),
                Mockito.eq("Lalamove Get Quotation Log"), any(), any(),
                immutableMapArgumentCaptor.capture()
        );

        ImmutableMap<String, Object> immutableMap = immutableMapArgumentCaptor.getAllValues().get(0);
        String requestPayload = (String) immutableMap.get("request_body");

        LalamoveGetQuotationForm lalamoveGetQuotationForm = mapper.readValue(requestPayload, LalamoveGetQuotationForm.class);
        Assert.assertEquals(1, lalamoveGetQuotationForm.getSpecialRequests().size());
        Assert.assertNotEquals("COD", lalamoveGetQuotationForm.getSpecialRequests().get(0));

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
        });

        mockServer.verify();
    }

    @Test
    public void finalizeLalamoveShoppingBatch_withEnableLalamoveV3_andServiceTypeHasEmptySpecialRequest_shouldTriggerPlaceOrderWithV3API() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableLalamove(true);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<>() {{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000");
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);

        setupLalamoveV3(stockLocation, false);
        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);

        LalamoveServiceType motorcycleServiceTypeV3 = lalamoveServiceTypeFactory.createMotorcycleServiceTypeV3(stockLocation.getState().getCountry(), admin, "JKT");
        motorcycleServiceTypeV3.setActive(false);
        lalamoveServiceTypeRepository.save(motorcycleServiceTypeV3);

        LalamoveServiceType vanServiceTypeV3 = lalamoveServiceTypeFactory.createVanServiceTypeV3(stockLocation.getState().getCountry(), admin, "JKT");
        vanServiceTypeV3.setActiveSpecialRequests(null);
        lalamoveServiceTypeRepository.save(vanServiceTypeV3);

        // Set v3 service type special request to null
        /*List<LalamoveServiceType> serviceTypesV3 = lalamoveServiceTypeRepository.findAllActiveV3ByCityCode("JKT");
        serviceTypesV3.forEach(data -> {
            data.setActiveSpecialRequests(null);
            data.setAvailableSpecialRequests(null);
            lalamoveServiceTypeRepository.save(data);
        });*/

        Tenant tenant = stockLocation.getTenant();
        Map<String, String> tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("enable_lalamove_api_v3", "true");
        tenantRepository.save(tenant);

        shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 2, 8, 1, admin);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, admin, 1, 4, LocalDateTime.now());

        // Mock - Get Quotation Request
        String response = getResponseFromResourceFile("lalamove_v3_get_quotation_response_success.json");
        String uri = lalamoveProperty.getBaseUrl() + "/v3/quotations";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.max(2), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));
        // Mock - Place Order Request
        uri = lalamoveProperty.getBaseUrl() + "/v3/orders";
        response = getResponseFromResourceFile("lalamove_v3_place_order_response_success.json");
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));


        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 1, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");

        slotHelper.assertSetRegularSlotV2(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        TimeUnit.MILLISECONDS.sleep(200);

        Shipment shipment = shipmentRepository.findByOrderNumberFetchItems("Order-1");

        batchHelper.shipmentFinalizeV3(shoppingBatch, shipment, shopper);

        TimeUnit.MILLISECONDS.sleep(1000);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("124620405314", lalamoveDelivery.getExternalOrderId());
            Assert.assertEquals(BigDecimal.valueOf(36900.0), lalamoveDelivery.getPrice()); // 31900 + 5000 (Addition)
            Assert.assertEquals("VAN", lalamoveDelivery.getServiceType());

        });

        mockServer.verify();

    }

    @Test
    public void finalizeLalamoveShoppingBatch_withEnableLalamoveV3_andServiceTypeHasCODSpecialRequest_andNotEligibleForCOD_shouldTriggerPlaceOrderWithoutCOD() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableLalamove(true);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<>() {{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000");
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);

        Country country = stockLocation.getState().getCountry();
        Map<String, String> countryPreferences = country.getPreferences();
        countryPreferences.put("enable_lalamove_cod", "false");
        countryFactory.save(country);

        setupLalamoveV3(stockLocation, false);
        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);

        LalamoveServiceType motorcycleServiceTypeV3 = lalamoveServiceTypeFactory.createMotorcycleServiceTypeV3(stockLocation.getState().getCountry(), admin, "JKT");
        motorcycleServiceTypeV3.setActiveSpecialRequests(new String[]{"CASH_ON_DELIVERY", "DOOR_TO_DOOR"});
        lalamoveServiceTypeRepository.save(motorcycleServiceTypeV3);

        Tenant tenant = stockLocation.getTenant();
        Map<String, String> tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("enable_lalamove_api_v3", "true");
        tenantRepository.save(tenant);

        shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 2, 8, 1, admin);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, admin, 1, 4, LocalDateTime.now());

        // Mock - Get Quotation Request
        String response = getResponseFromResourceFile("lalamove_v3_get_quotation_response_success.json");
        String uri = lalamoveProperty.getBaseUrl() + "/v3/quotations";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.max(2), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));
        // Mock - Place Order Request
        uri = lalamoveProperty.getBaseUrl() + "/v3/orders";
        response = getResponseFromResourceFile("lalamove_v3_place_order_response_success.json");
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));


        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 1, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");

        slotHelper.assertSetRegularSlotV2(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        TimeUnit.MILLISECONDS.sleep(200);

        Shipment shipment = shipmentRepository.findByOrderNumberFetchItems("Order-1");

        batchHelper.shipmentFinalizeV3(shoppingBatch, shipment, shopper);

        TimeUnit.MILLISECONDS.sleep(1000);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("124620405314", lalamoveDelivery.getExternalOrderId());
            Assert.assertEquals(BigDecimal.valueOf(36900.0), lalamoveDelivery.getPrice()); // 31900 + 5000 (Addition)
            Assert.assertEquals("MOTORCYCLE", lalamoveDelivery.getServiceType());

        });

        mockServer.verify();

    }

    @Test
    public void finalizeLalamoveShoppingBatch_withEnableLalamoveV3_andServiceTypeHasCODSpecialRequest_andEligibleForCOD_shouldTriggerPlaceOrderWithCOD() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableLalamove(true);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<>() {{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000");
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);

        Country country = stockLocation.getState().getCountry();
        Map<String, String> countryPreferences = country.getPreferences();
        countryPreferences.put("enable_lalamove_cod", "true");
        countryFactory.save(country);

        setupLalamoveV3(stockLocation, false);
        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);

        LalamoveServiceType motorcycleServiceTypeV3 = lalamoveServiceTypeFactory.createMotorcycleServiceTypeV3(stockLocation.getState().getCountry(), admin, "JKT");
        motorcycleServiceTypeV3.setActiveSpecialRequests(new String[]{"CASH_ON_DELIVERY", "DOOR_TO_DOOR"});
        lalamoveServiceTypeRepository.save(motorcycleServiceTypeV3);

        Tenant tenant = stockLocation.getTenant();
        Map<String, String> tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("enable_lalamove_api_v3", "true");
        tenantRepository.save(tenant);

        shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 2, 8, 1, admin);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, admin, 1, 4, LocalDateTime.now());

        // Mock - Get Quotation Request
        String response = getResponseFromResourceFile("lalamove_v3_get_quotation_response_success.json");
        String uri = lalamoveProperty.getBaseUrl() + "/v3/quotations";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.max(2), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));
        // Mock - Place Order Request
        uri = lalamoveProperty.getBaseUrl() + "/v3/orders";
        response = getResponseFromResourceFile("lalamove_v3_place_order_response_success.json");
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));


        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 1, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        shipmentObj1.put("order_payment_method", Shipment.COD_PAYMENT_TYPE);
        slotHelper.assertSetRegularSlotV2(true, shipmentObj1, admin);
        shipmentHelper.adjustShipment(shipmentObj1, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        TimeUnit.MILLISECONDS.sleep(200);

        Shipment shipment = shipmentRepository.findByOrderNumberFetchItems("Order-1");
        shipment.setOrderPaymentMethod(Shipment.COD_PAYMENT_TYPE);
        shipmentFactory.save(shipment);

        batchHelper.shipmentFinalizeV3(shoppingBatch, shipment, shopper);

        TimeUnit.MILLISECONDS.sleep(1000);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("124620405314", lalamoveDelivery.getExternalOrderId());
            Assert.assertEquals(BigDecimal.valueOf(36900.0), lalamoveDelivery.getPrice()); // 31900 + 5000 (Addition)
            Assert.assertEquals("MOTORCYCLE", lalamoveDelivery.getServiceType());

        });

        mockServer.verify();

    }

}
