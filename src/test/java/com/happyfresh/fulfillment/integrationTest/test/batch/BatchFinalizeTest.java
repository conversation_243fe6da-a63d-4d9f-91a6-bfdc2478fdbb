package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.batch.service.ShopperAutoAssignmentService;
import com.happyfresh.fulfillment.common.exception.type.InvalidItemQtyException;
import com.happyfresh.fulfillment.common.exception.type.InvalidReplacementItemException;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.property.RadarProperty;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.BatchHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.MockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.ItemRepository;
import com.happyfresh.fulfillment.shipment.form.ItemForm;
import com.happyfresh.fulfillment.shipment.service.CatalogService;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import com.happyfresh.fulfillment.slot.presenter.SlotOptimizationEvent;
import org.hamcrest.Matchers;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.net.URI;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

public class BatchFinalizeTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private AgentFactory agentFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private ItemFactory itemFactory;

    @Autowired
    private ItemRepository itemRepository;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private TransactionHelper transactionHelper;

    @MockBean
    private CatalogService catalogService;

    @MockBean
    private OrderService orderService;

    @Autowired
    private MockServerHelper mockServerHelper;

    @Autowired
    private RadarProperty radarProperty;

    @Autowired
    private CategoryFactory categoryFactory;

    @Autowired
    private CategoryOrderFactory categoryOrderFactory;

    @SpyBean
    private ShopperAutoAssignmentService shopperAutoAssignmentService;

    @Autowired
    private BatchHelper batchHelper;

    User systemAdmin;

    User externalSystemAdmin;

    User shopper;

    User admin;

    @Before
    public void setUp() throws InterruptedException {
        Thread.sleep(750);
        systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN);
        externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, systemAdmin.getTenant());
        shopper = userFactory.createUserData(Role.Name.SHOPPER, systemAdmin.getTenant());
        admin = userFactory.createUserData(Role.Name.ADMIN, systemAdmin.getTenant());
    }

    @Test
    public void returnExceptionIfJobIsCancelled() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, shopper);

        Shipment shipment = shipmentFactory.createShipment(slot, shopper);
        Item item = itemFactory.createItems(shipment, shopper, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment);

        Batch batch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.DELIVERY);

        String url = String.format("/api/batches/%d/start", batch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        // Cancel Shipment/Job
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + shipment.getNumber() + "/cancel")
                .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken()));

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        url = String.format("/api/batches/%d/shipments/%s/finalize", batch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is4xxClientError());
    }

    @Test
    public void shouldFinalizeBatch() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, shopper);

        Shipment shipment = shipmentFactory.createShipment(slot, shopper);
        Item item = itemFactory.createItems(shipment, shopper, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment);

        Batch batch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.SHOPPING);

        String url = String.format("/api/batches/%d/start", batch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        url = String.format("/api/batches/%d/shipments/%s/finalize", batch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.shipment.order_total").value(15000.0))
                .andExpect(jsonPath("$.response.shipment.shopping_job.state").value("finalizing"));

        User user2 = userFactory.createUserData(Role.Name.SHOPPER, shopper.getTenant());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user2.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isUnauthorized());

        Mockito.verify(shopperAutoAssignmentService, Mockito.never()).publishAutoAssignmentEvent(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyLong(), Mockito.any(SlotOptimizationEvent.AutoAssignmentTriggerEvent.class));
    }

    @Test
    public void shouldFinalizeBatchWithReplacement() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, shopper);

        Shipment shipment = shipmentFactory.createShipment(slot, shopper);
        Item item = itemFactory.createItems(shipment, shopper, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment);

        Batch batch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.SHOPPING);

        String url = String.format("/api/batches/%d/start", batch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty() - 1);
        object1.put("oos_qty", 1);

        String replacementItemSku = "8999999390419-ID";
        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(111, replacementItemSku, 1);
        object2.put("found_qty", 1);
        object2.put("replaced_item_id", item.getId());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        ObjectMapper mapper = new ObjectMapper();
        TypeFactory typeFactory = mapper.getTypeFactory();
        List<ItemForm> mockItems = mapper.readValue(requestBody.get("items").toString(), typeFactory.constructCollectionType(List.class, ItemForm.class));
        List<String> skus = Lists.newArrayList(object2.get("sku").toString());
        Mockito.when(catalogService.getItemsFromCatalogService(skus, stockLocation.getExternalId())).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        url = String.format("/api/batches/%d/shipments/%s/finalize", batch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.shipment.order_total").value(15000.0))
                .andExpect(jsonPath("$.response.shipment.shopping_job.state").value("finalizing"))
                .andExpect(jsonPath("$.response.shipment.items").isNotEmpty())
                .andExpect(jsonPath("$.response.shipment.items[0].replacements[0]").isNotEmpty());

        final long itemId = item.getId();
        transactionHelper.withNewTransaction(() -> {
            Item _item = itemRepository.findById(itemId).get();
            Assert.assertEquals(1, _item.getReplacements().size());
            Assert.assertEquals(replacementItemSku, _item.getReplacements().iterator().next().getSku());
        });
    }


    @Test
    public void shouldFinalizeBatchWithReplacementWhenCallTwiceConcurrently() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, shopper);

        Shipment shipment = shipmentFactory.createShipment(slot, shopper);
        Item item = itemFactory.createItems(shipment, shopper, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment);

        Batch batch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.SHOPPING);

        String url = String.format("/api/batches/%d/start", batch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty() - 1);
        object1.put("oos_qty", 1);

        String replacementItemSku = "8999999390419-ID";
        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(111, replacementItemSku, 1);
        object2.put("found_qty", 1);
        object2.put("replaced_item_id", item.getId());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        ObjectMapper mapper = new ObjectMapper();
        TypeFactory typeFactory = mapper.getTypeFactory();
        List<ItemForm> mockItems = mapper.readValue(requestBody.get("items").toString(), typeFactory.constructCollectionType(List.class, ItemForm.class));
        List<String> skus = Lists.newArrayList(object2.get("sku").toString());
        Mockito.when(catalogService.getItemsFromCatalogService(skus, stockLocation.getExternalId())).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        List<CompletableFuture<Void>> futures = Lists.newArrayList();

        final String finalizeUrl = String.format("/api/v2/batches/%d/finalize", batch.getId());
        for (int i = 0; i < 2; i++) {
            Thread.sleep(100);
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    mvc.perform(MockMvcRequestBuilders.post(finalizeUrl)
                            .header("X-Fulfillment-User-Token", shopper.getToken())
                            .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                            .content(requestBody.toString())
                            .contentType(MediaType.APPLICATION_JSON))
                            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            futures.add(future);
        }

        // wait until all futures completed
        CompletableFuture<Void> allFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[]{}));
        allFuture.get();

        final long itemId = item.getId();
        transactionHelper.withNewTransaction(() -> {
            Item _item = itemRepository.findById(itemId).get();
            Assert.assertEquals(1, _item.getReplacements().size());
            Assert.assertEquals(replacementItemSku, _item.getReplacements().iterator().next().getSku());
        });
    }

    @Test
    public void shouldFinalizeBatch_EvenReplacementItemFromBackendAndClientIsDifferent() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, shopper);

        Shipment shipment = shipmentFactory.createShipment(slot, shopper);
        List<Item> items = itemFactory.createItems(shipment, shopper, 2);
        Item item = items.get(0);
        Item replacementItem = items.get(1);
        replacementItem.setReplacedItem(item);
        itemRepository.save(replacementItem);

        List<Shipment> shipments = Lists.newArrayList(shipment);
        Batch batch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.SHOPPING);

        String url = String.format("/api/batches/%d/start", batch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty() - 1);
        object1.put("oos_qty", 1);

        String replacementItemSku = "8999999390419-ID";
        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(111, replacementItemSku, 1);
        object2.put("found_qty", 1);
        object2.put("replaced_item_id", item.getId());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        ObjectMapper mapper = new ObjectMapper();
        TypeFactory typeFactory = mapper.getTypeFactory();
        List<ItemForm> mockItems = mapper.readValue(requestBody.get("items").toString(), typeFactory.constructCollectionType(List.class, ItemForm.class));
        List<String> skus = Lists.newArrayList(object2.get("sku").toString());
        Mockito.when(catalogService.getItemsFromCatalogService(skus, stockLocation.getExternalId())).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        url = String.format("/api/batches/%d/shipments/%s/finalize", batch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.shipment.order_total").value(15000.0))
                .andExpect(jsonPath("$.response.shipment.shopping_job.state").value("finalizing"));

        final long itemId = item.getId();
        transactionHelper.withNewTransaction(() -> {
            Item _item = itemRepository.findById(itemId).get();
            Assert.assertEquals(1, _item.getReplacements().size());
            Assert.assertEquals(replacementItemSku, _item.getReplacements().iterator().next().getSku());
        });
    }

    @Test
    public void shouldNotFinalizeBatchIfItemQuantityIsNotValid() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, shopper);

        Shipment shipment = shipmentFactory.createShipment(slot, shopper);
        Item item = itemFactory.createItems(shipment, shopper, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment);

        Batch batch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.SHOPPING);

        String url = String.format("/api/batches/%d/start", batch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty() - 1);

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        url = String.format("/api/batches/%d/shipments/%s/finalize", batch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isMultiStatus())
                .andExpect(jsonPath("$.response.shipment.order_total").value(10000.0))
                .andExpect(jsonPath("$.response.shipment.shopping_job.state").value("started"))
                .andExpect(jsonPath("$.response.exceptions[0].type").value(InvalidItemQtyException.class.getSimpleName()))
                .andExpect(jsonPath("$.response.exceptions[0].field").value(item.getSku()));
    }

    @Test
    public void shouldFinalizeBatchPooling() throws Exception {
        Thread.sleep(50);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, shopper, 0, 1);
        agentFactory.createAgent(shopper, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(slot, shopper);
        Item item1 = itemFactory.createItems(shipment1, shopper, 1).get(0);
        Shipment shipment2 = shipmentFactory.createShipment(slot, shopper);
        Item item2 = itemFactory.createItems(shipment2, shopper, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch shoppingBatch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.SHOPPING);

        String url = String.format("/api/batches/%d/start", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1.getId(), item1.getSku(), item1.getRequestedQty());
        object1.put("id", item1.getId());
        object1.put("found_qty", item1.getRequestedQty());
        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(15000.0);
        Mockito.when(orderService.getOrderTotal(shipment2.getNumber())).thenReturn(15000.0);

        url = String.format("/api/v2/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.batch.shipments[0].order_total").value(15000.0))
                .andExpect(jsonPath("$.response.batch.shipments[1].order_total").value(15000.0))
                .andExpect(jsonPath("$.response.batch.shipments[0].shopping_job.state").value("finalizing"))
                .andExpect(jsonPath("$.response.batch.shipments[1].shopping_job.state").value("finalizing"));
    }

    @Test
    public void shouldNotFinalizeBatchPoolingIfNotAllItemSent() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, shopper, 0, 1);
        agentFactory.createAgent(shopper, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(slot, shopper);
        Item item1 = itemFactory.createItems(shipment1, shopper, 1).get(0);
        Shipment shipment2 = shipmentFactory.createShipment(slot, shopper);
        List<Item> items2 = itemFactory.createItems(shipment2, shopper, 2);
        Item item2 = items2.get(0);
        Item item3 = items2.get(1);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch shoppingBatch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.SHOPPING);

        String url = String.format("/api/batches/%d/start", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1.getId(), item1.getSku(), item1.getRequestedQty());
        object1.put("id", item1.getId());
        object1.put("found_qty", item1.getRequestedQty());
        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(15000.0);
        Mockito.when(orderService.getOrderTotal(shipment2.getNumber())).thenReturn(15000.0);

        url = String.format("/api/v2/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.batch.shipments[0].order_total").value(10000.0))
                .andExpect(jsonPath("$.response.batch.shipments[1].order_total").value(10000.0))
                .andExpect(jsonPath("$.response.batch.shipments[0].shopping_job.state").value("started"))
                .andExpect(jsonPath("$.response.batch.shipments[1].shopping_job.state").value("started"))
                .andExpect(jsonPath("$.response.exceptions[0].type").value(InvalidItemQtyException.class.getSimpleName()))
                .andExpect(jsonPath("$.response.exceptions[0].field").value(item3.getSku()));
        ;
    }

    @Test
    public void shouldNotFinalizeBatchPoolingIfItemQuantityIsNotValid() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, shopper, 0, 1);
        agentFactory.createAgent(shopper, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(slot, shopper);
        Item item1 = itemFactory.createItems(shipment1, shopper, 1).get(0);
        Shipment shipment2 = shipmentFactory.createShipment(slot, shopper);
        Item item2 = itemFactory.createItems(shipment2, shopper, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch shoppingBatch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.SHOPPING);

        String url = String.format("/api/batches/%d/start", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1.getId(), item1.getSku(), item1.getRequestedQty());
        object1.put("id", item1.getId());
        object1.put("found_qty", item1.getRequestedQty() + 1);
        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(15000.0);
        Mockito.when(orderService.getOrderTotal(shipment2.getNumber())).thenReturn(15000.0);

        url = String.format("/api/v2/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isMultiStatus())
                .andExpect(jsonPath("$.response.batch.shipments[0].order_total").value(10000.0))
                .andExpect(jsonPath("$.response.batch.shipments[1].order_total").value(10000.0))
                .andExpect(jsonPath("$.response.batch.shipments[0].shopping_job.state").value("started"))
                .andExpect(jsonPath("$.response.batch.shipments[1].shopping_job.state").value("started"))
                .andExpect(jsonPath("$.response.exceptions[0].type").value(InvalidItemQtyException.class.getSimpleName()))
                .andExpect(jsonPath("$.response.exceptions[0].field").value(item1.getSku()));

        Item persistedItem1 = itemRepository.findById(item1.getId()).get();
        Assert.assertEquals(0, persistedItem1.getFoundQty().intValue());

        Item persistedItem2 = itemRepository.findById(item2.getId()).get();
        Assert.assertEquals(item2.getRequestedQty().intValue(), persistedItem2.getFoundQty().intValue());
    }

    @Test
    public void shouldNotFinalizeBatchPoolingIfReplacementItemIsMoreThanOne() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, shopper, 0, 1);
        agentFactory.createAgent(shopper, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(slot, shopper);
        Item item1 = itemFactory.createItems(shipment1, shopper, 1).get(0);
        Shipment shipment2 = shipmentFactory.createShipment(slot, shopper);
        Item item2 = itemFactory.createItems(shipment2, shopper, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch shoppingBatch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.SHOPPING);

        String url = String.format("/api/batches/%d/start", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1.getId(), item1.getSku(), item1.getRequestedQty());
        object1.put("id", item1.getId());
        object1.put("found_qty", item1.getRequestedQty() - 1);
        object1.put("oos_qty", 1);
        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(111, "sku_replacement_1", 1);
        object2.put("found_qty", 1);
        object2.put("replaced_item_id", 1);
        JSONObject object3 = shipmentJsonObjectFactory.createItemObject(222, "sku_replacement_2", 1);
        object3.put("found_qty", 1);
        object3.put("replaced_item_id", 1);
        JSONObject object4 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object4.put("id", item2.getId());
        object4.put("found_qty", item2.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);
        itemsArray.put(object3);
        itemsArray.put(object4);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(15000.0);
        Mockito.when(orderService.getOrderTotal(shipment2.getNumber())).thenReturn(15000.0);

        url = String.format("/api/v2/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isMultiStatus())
                .andExpect(jsonPath("$.response.batch.shipments[0].order_total").value(10000.0))
                .andExpect(jsonPath("$.response.batch.shipments[1].order_total").value(10000.0))
                .andExpect(jsonPath("$.response.batch.shipments[0].shopping_job.state").value("started"))
                .andExpect(jsonPath("$.response.batch.shipments[1].shopping_job.state").value("started"))
                .andExpect(jsonPath("$.response.exceptions[0].type").value(InvalidReplacementItemException.class.getSimpleName()))
                .andExpect(jsonPath("$.response.exceptions[0].field").value(item1.getSku()));

        Item persistedItem1 = itemRepository.findById(item1.getId()).get();
        Assert.assertEquals(1, persistedItem1.getFoundQty().intValue());
        Assert.assertEquals(1, persistedItem1.getOosQty().intValue());

        Item persistedItem2 = itemRepository.findById(item2.getId()).get();
        Assert.assertEquals(item2.getRequestedQty().intValue(), persistedItem2.getFoundQty().intValue());

        long totalPersisted = itemRepository.count();
        Assert.assertEquals(2, totalPersisted);
    }

    @Test
    public void shouldNotFinalizeBatchPoolingIfOneOfTheOrderIsCancelled() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, shopper, 0, 1);
        agentFactory.createAgent(shopper, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(slot, shopper);
        Item item1 = itemFactory.createItems(shipment1, shopper, 1).get(0);
        Shipment shipment2 = shipmentFactory.createShipment(slot, shopper);
        Item item2 = itemFactory.createItems(shipment2, shopper, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch shoppingBatch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.DELIVERY);

        String url = String.format("/api/batches/%d/start", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        // CANCEL SHIPMENT
        url = String.format("/api/shipments/%s/cancel", shipment2.getNumber());
        mvc.perform(MockMvcRequestBuilders.put(url)
                .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1.getId(), item1.getSku(), item1.getRequestedQty());
        object1.put("id", item1.getId());
        object1.put("found_qty", item1.getRequestedQty() - 1);
        object1.put("oos_qty", 1);
        object1.put("weight", 1);

        String replacementItemSku = "8999999390419-ID";
        JSONObject replacementObject1 = shipmentJsonObjectFactory.createItemObject(111, replacementItemSku, 1);
        replacementObject1.put("found_qty", item1.getRequestedQty());
        replacementObject1.put("weight", 2);
        replacementObject1.put("replaced_item_id", item1.getId());

        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());
        object2.put("weight", 1);

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(replacementObject1);
        itemsArray.put(object2);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        ObjectMapper mapper = new ObjectMapper();
        TypeFactory typeFactory = mapper.getTypeFactory();
        List<ItemForm> mockItems = mapper.readValue(requestBody.get("items").toString(), typeFactory.constructCollectionType(List.class, ItemForm.class));
        List<String> skus = Lists.newArrayList(replacementObject1.get("sku").toString());
        Mockito.when(catalogService.getItemsFromCatalogService(skus, stockLocation.getExternalId())).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(15000.0);
        Mockito.when(orderService.getOrderTotal(shipment2.getNumber())).thenReturn(15000.0);

        url = String.format("/api/v2/batches/%d/finalize", shoppingBatch.getId());
        // Test calling the same api 3 times will gives consistent result
        for (int i = 0; i < 3; i++) {
            mvc.perform(MockMvcRequestBuilders.post(url)
                    .header("X-Fulfillment-User-Token", shopper.getToken())
                    .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                    .content(requestBody.toString())
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
        }

    }

    @Test
    public void shouldReturn4xxOnEmptyItems() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, shopper, 0, 1);
        agentFactory.createAgent(shopper, stockLocation, Agent.State.WORKING);

        Shipment shipment = shipmentFactory.createShipment(slot, shopper, "R123", "H123");
        Item item1 = itemFactory.createItems(shipment, shopper, 1).get(0);
        Batch shoppingBatch = batchFactory.createBatch(shopper, shipment, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(shopper, shipment, slot, Batch.Type.DELIVERY);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/"+shoppingBatch.getId()+"/start")
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1.getId(), item1.getSku(), item1.getRequestedQty());
        object1.put("id", item1.getId());
        object1.put("found_qty", item1.getRequestedQty());
        // request body with no items
        JSONObject requestBody = new JSONObject();
        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipment.getNumber());
        requestBody.put("shipment_numbers", shipmentsArray);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        mvc.perform(MockMvcRequestBuilders.post("/api/v3/batches/"+shoppingBatch.getId()+"/finalize")
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                        .content(requestBody.toString())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is4xxClientError());
    }

    @Test
    public void shouldPartiallyFinalizeBatchPooling() throws Exception {
        Thread.sleep(50);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, shopper, 0, 1);
        agentFactory.createAgent(shopper, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(slot, shopper, "R123", "H123");
        Item item1 = itemFactory.createItems(shipment1, shopper, 1).get(0);
        Shipment shipment2 = shipmentFactory.createShipment(slot, shopper, "R456", "H456");
        Item item2 = itemFactory.createItems(shipment2, shopper, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch shoppingBatch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.DELIVERY);

        String url = String.format("/api/batches/%d/start", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1.getId(), item1.getSku(), item1.getRequestedQty());
        object1.put("id", item1.getId());
        object1.put("found_qty", item1.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);

        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipment1.getNumber());

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);
        requestBody.put("shipment_numbers", shipmentsArray);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(15000.0);

        // Create geofence in radar
        String radarUrl = radarProperty.getBaseUrl() + "/geofences/shipment_number/" + shipment1.getNumber();
        String geofenceResponse = "{\"meta\":{\"code\":200},\"geofence\":{\"_id\":\"60c898502cb0090064a7b83b\",\"createdAt\":\"2021-06-15T12:08:48.668Z\",\"updatedAt\":\"2021-06-15T12:08:48.641Z\",\"live\":false,\"description\":\"H12345\",\"tag\":\"shipment_number\",\"externalId\":\"H12345\",\"type\":\"circle\",\"deleteAfter\":\"2021-06-17T00:00:00.000Z\",\"geometryCenter\":{\"coordinates\":[106.80363212609046,-6.291185885319718],\"type\":\"Point\"},\"geometryRadius\":100,\"geometry\":{\"type\":\"Polygon\",\"coordinates\":[[[106.80453689638168,-6.291185885319718],[106.80451951147423,-6.291010436378023]]},\"enabled\":true}}";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.min(1), requestTo(new URI(radarUrl)))
                .andExpect(method(HttpMethod.PUT))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(geofenceResponse));

        url = String.format("/api/v3/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.batch.shipments[0].order_total").value(15000.0))
                .andExpect(jsonPath("$.response.batch.shipments[1].order_total").value(10000.0))
                .andExpect(jsonPath("$.response.batch.shipments[0].shopping_job.state").value("finalizing"))
                .andExpect(jsonPath("$.response.batch.shipments[1].shopping_job.state").value("started"));

        mockServer.verify();

        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());
        JSONArray itemsArray2 = new JSONArray();
        itemsArray2.put(object2);

        JSONArray shipmentsArray2 = new JSONArray();
        shipmentsArray2.put(shipment2.getNumber());

        JSONObject requestBody2 = new JSONObject();
        requestBody2.put("items", itemsArray2);
        requestBody2.put("shipment_numbers", shipmentsArray2);

        Mockito.when(orderService.getOrderTotal(shipment2.getNumber())).thenReturn(15000.0);

        // Create geofence in radar
        radarUrl = radarProperty.getBaseUrl() + "/geofences/shipment_number/" + shipment2.getNumber();
        MockRestServiceServer mockServer1 = mockServerHelper.buildMockServer();
        mockServer1.expect(ExpectedCount.min(1), requestTo(new URI(radarUrl)))
                .andExpect(method(HttpMethod.PUT))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(geofenceResponse));

        url = String.format("/api/v3/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .content(requestBody2.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.batch.shipments[0].order_total").value(15000.0))
                .andExpect(jsonPath("$.response.batch.shipments[1].order_total").value(15000.0))
                .andExpect(jsonPath("$.response.batch.shipments[0].shopping_job.state").value("finalizing"))
                .andExpect(jsonPath("$.response.batch.shipments[1].shopping_job.state").value("finalizing"));

        mockServer1.verify();

        Mockito.verify(shopperAutoAssignmentService, Mockito.never()).publishAutoAssignmentEvent(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyLong(), Mockito.any(SlotOptimizationEvent.AutoAssignmentTriggerEvent.class));
    }

    @Test
    public void shouldPartiallyFinalizeRangerBatchPooling() throws Exception {
        Thread.sleep(50);
        User ranger = userFactory.createUserData(Role.Name.DRIVER, systemAdmin.getTenant());
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, ranger).get(0);
        stockLocation.setType(StockLocation.Type.SPECIAL);
        stockLocationFactory.save(stockLocation);
        Slot slot = slotFactory.createSlot(stockLocation, ranger, 0, 1);
        agentFactory.createAgent(ranger, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(slot, ranger, "R123", "H123");
        Item item1 = itemFactory.createItems(shipment1, ranger, 1).get(0);
        Shipment shipment2 = shipmentFactory.createShipment(slot, ranger, "R456", "H456");
        Item item2 = itemFactory.createItems(shipment2, ranger, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch rangerBatch = batchFactory.createBatch(ranger, shipments, slot, Batch.Type.RANGER);

        String url = String.format("/api/batches/%d/start", rangerBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", ranger.getToken())
                .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1.getId(), item1.getSku(), item1.getRequestedQty());
        object1.put("id", item1.getId());
        object1.put("found_qty", item1.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);

        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipment1.getNumber());

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);
        requestBody.put("shipment_numbers", shipmentsArray);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(15000.0);
        url = String.format("/api/v3/batches/%d/finalize", rangerBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", ranger.getToken())
                .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.batch.shipments[0].order_total").value(15000.0))
                .andExpect(jsonPath("$.response.batch.shipments[1].order_total").value(10000.0))
                .andExpect(jsonPath("$.response.batch.shipments[0].ranger_job.state").value("finalizing"))
                .andExpect(jsonPath("$.response.batch.shipments[1].ranger_job.state").value("started"));


        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());
        JSONArray itemsArray2 = new JSONArray();
        itemsArray2.put(object2);

        JSONArray shipmentsArray2 = new JSONArray();
        shipmentsArray2.put(shipment2.getNumber());

        JSONObject requestBody2 = new JSONObject();
        requestBody2.put("items", itemsArray2);
        requestBody2.put("shipment_numbers", shipmentsArray2);

        Mockito.when(orderService.getOrderTotal(shipment2.getNumber())).thenReturn(15000.0);
        url = String.format("/api/v3/batches/%d/finalize", rangerBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", ranger.getToken())
                .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
                .content(requestBody2.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.batch.shipments[0].order_total").value(15000.0))
                .andExpect(jsonPath("$.response.batch.shipments[1].order_total").value(15000.0))
                .andExpect(jsonPath("$.response.batch.shipments[0].ranger_job.state").value("finalizing"))
                .andExpect(jsonPath("$.response.batch.shipments[1].ranger_job.state").value("finalizing"));

    }

    @Test
    public void concurrentFinalizeRangerBatchPoolingAndCancel() throws Exception {
        Thread.sleep(50);
        User ranger = userFactory.createUserData(Role.Name.DRIVER, systemAdmin.getTenant());
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, ranger).get(0);
        stockLocation.setType(StockLocation.Type.SPECIAL);
        stockLocationFactory.save(stockLocation);
        Slot slot = slotFactory.createSlot(stockLocation, ranger, 0, 1);
        agentFactory.createAgent(ranger, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(slot, ranger, "R123", "H123");
        Item item1 = itemFactory.createItems(shipment1, ranger, 1).get(0);
        Shipment shipment2 = shipmentFactory.createShipment(slot, ranger, "R456", "H456");
        Item item2 = itemFactory.createItems(shipment2, ranger, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch rangerBatch = batchFactory.createBatch(ranger, shipments, slot, Batch.Type.RANGER);

        String url = String.format("/api/batches/%d/start", rangerBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", ranger.getToken())
                .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1.getId(), item1.getSku(), item1.getRequestedQty());
        object1.put("id", item1.getId());
        object1.put("found_qty", item1.getRequestedQty());

        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);

        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipment1.getNumber());
        shipmentsArray.put(shipment2.getNumber());

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);
        requestBody.put("shipment_numbers", shipmentsArray);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(15000.0);
        Mockito.when(orderService.getOrderTotal(shipment2.getNumber())).thenReturn(15000.0);

        CompletableFuture<Void> setSlotFuture = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + shipment1.getNumber() + "/cancel")
                                .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                                .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken())
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
                    } catch (Exception e) {
                        System.out.println(e);
                    }
                    return null;
                });

        CompletableFuture<Void> batchStartFuture = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        Thread.sleep(200);
                        String urlFinalize = String.format("/api/v3/batches/%d/finalize", rangerBatch.getId());
                        mvc.perform(MockMvcRequestBuilders.post(urlFinalize)
                                .header("X-Fulfillment-User-Token", ranger.getToken())
                                .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
                                .content(requestBody.toString())
                                .contentType(MediaType.APPLICATION_JSON))
                                .andExpect(MockMvcResultMatchers.status().is(207));
                    } catch (Exception e) {
                        System.out.println(e);
                    }
                    return null;
                });

        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(setSlotFuture, batchStartFuture);
        combinedFuture.get();
    }


    @Test
    public void concurrentFinalizeRangerBatchPoolingAndCancelJobCase1() throws Exception {
        Thread.sleep(50);
        User ranger = userFactory.createUserData(Role.Name.DRIVER, systemAdmin.getTenant());
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, ranger).get(0);
        stockLocation.setType(StockLocation.Type.SPECIAL);
        stockLocationFactory.save(stockLocation);
        Slot slot = slotFactory.createSlot(stockLocation, ranger, 0, 1);
        agentFactory.createAgent(ranger, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(slot, ranger, "R123", "H123");
        Item item1 = itemFactory.createItems(shipment1, ranger, 1).get(0);
        Shipment shipment2 = shipmentFactory.createShipment(slot, ranger, "R456", "H456");
        Item item2 = itemFactory.createItems(shipment2, ranger, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch rangerBatch = batchFactory.createBatch(ranger, shipments, slot, Batch.Type.RANGER);

        String url = String.format("/api/batches/%d/start", rangerBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", ranger.getToken())
                .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1.getId(), item1.getSku(), item1.getRequestedQty());
        object1.put("id", item1.getId());
        object1.put("found_qty", item1.getRequestedQty());

        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);

        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipment1.getNumber());
        shipmentsArray.put(shipment2.getNumber());

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);
        requestBody.put("shipment_numbers", shipmentsArray);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(15000.0);
        Mockito.when(orderService.getOrderTotal(shipment2.getNumber())).thenReturn(15000.0);

        CompletableFuture<Void> setSlotFuture = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        Thread.sleep(100);
                        mvc.perform(MockMvcRequestBuilders.put("/api/admin/shipments/" + shipment1.getNumber() + "/cancel_job")
                                .header("X-Fulfillment-User-Token", admin.getToken())
                                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(MockMvcResultMatchers.status().is4xxClientError());
                    } catch (Exception e) {
                        System.out.println(e);
                    }
                    return null;
                });

        CompletableFuture<Void> batchStartFuture = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        String urlFinalize = String.format("/api/v3/batches/%d/finalize", rangerBatch.getId());
                        mvc.perform(MockMvcRequestBuilders.post(urlFinalize)
                                .header("X-Fulfillment-User-Token", ranger.getToken())
                                .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
                                .content(requestBody.toString())
                                .contentType(MediaType.APPLICATION_JSON))
                                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
                    } catch (Exception e) {
                        System.out.println(e);
                    }
                    return null;
                });

        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(setSlotFuture, batchStartFuture);
        combinedFuture.get();
    }

    @Test
    public void concurrentFinalizeRangerBatchPoolingAndCancelJobCase2() throws Exception {
        Thread.sleep(50);
        User ranger = userFactory.createUserData(Role.Name.DRIVER, systemAdmin.getTenant());
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, ranger).get(0);
        stockLocation.setType(StockLocation.Type.SPECIAL);
        stockLocationFactory.save(stockLocation);
        Slot slot = slotFactory.createSlot(stockLocation, ranger, 0, 1);
        agentFactory.createAgent(ranger, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(slot, ranger, "R123", "H123");
        Item item1 = itemFactory.createItems(shipment1, ranger, 1).get(0);
        Shipment shipment2 = shipmentFactory.createShipment(slot, ranger, "R456", "H456");
        Item item2 = itemFactory.createItems(shipment2, ranger, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch rangerBatch = batchFactory.createBatch(ranger, shipments, slot, Batch.Type.RANGER);

        String url = String.format("/api/batches/%d/start", rangerBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", ranger.getToken())
                .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1.getId(), item1.getSku(), item1.getRequestedQty());
        object1.put("id", item1.getId());
        object1.put("found_qty", item1.getRequestedQty());

        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);

        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipment1.getNumber());
        shipmentsArray.put(shipment2.getNumber());

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);
        requestBody.put("shipment_numbers", shipmentsArray);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(15000.0);
        Mockito.when(orderService.getOrderTotal(shipment2.getNumber())).thenReturn(15000.0);

        CompletableFuture<Void> setSlotFuture = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        mvc.perform(MockMvcRequestBuilders.put("/api/admin/shipments/" + shipment1.getNumber() + "/cancel_job")
                                .header("X-Fulfillment-User-Token", admin.getToken())
                                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
                    } catch (Exception e) {
                        System.out.println(e);
                    }
                    return null;
                });

        CompletableFuture<Void> batchStartFuture = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        Thread.sleep(100);
                        String urlFinalize = String.format("/api/v3/batches/%d/finalize", rangerBatch.getId());
                        mvc.perform(MockMvcRequestBuilders.post(urlFinalize)
                                .header("X-Fulfillment-User-Token", ranger.getToken())
                                .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
                                .content(requestBody.toString())
                                .contentType(MediaType.APPLICATION_JSON))
                                .andExpect(MockMvcResultMatchers.status().is4xxClientError());
                    } catch (Exception e) {
                        System.out.println(e);
                    }
                    return null;
                });

        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(setSlotFuture, batchStartFuture);
        combinedFuture.get();
    }

    @Test
    public void finalizeShouldNotUpdateCategoryOrder() throws Exception {
        Thread.sleep(50);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, shopper, 0, 1);
        agentFactory.createAgent(shopper, stockLocation, Agent.State.WORKING);

        Category categoryA = categoryFactory.createCategory(admin);
        Category categoryB = categoryFactory.createCategory(admin);
        CategoryOrder categoryOrderA = categoryOrderFactory.createCategoryOrder(admin, categoryA, stockLocation, 69);
        CategoryOrder categoryOrderB = categoryOrderFactory.createCategoryOrder(admin, categoryB, stockLocation, 77);

        Shipment shipment1 = shipmentFactory.createShipment(slot, shopper, "R123", "H123");
        Item item1CategoryACategoryOrderA = itemFactory.createItem(categoryA, shipment1, admin, categoryOrderA);
        Item item2CategoryBCategoryOrderB = itemFactory.createItem(categoryB, shipment1, admin, categoryOrderB);
        Shipment shipment2 = shipmentFactory.createShipment(slot, shopper, "R456", "H456");
        Item item3CategoryACategoryOrderA = itemFactory.createItem(categoryA, shipment2, admin, categoryOrderA);
        List<Shipment> shipments = Lists.newArrayList(shipment1);

        Batch shoppingBatch1 = batchFactory.createBatch(shopper, shipment1, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch1 = batchFactory.createBatch(shopper, shipment1, slot, Batch.Type.DELIVERY);
        Batch shoppingBatch2 = batchFactory.createBatch(shopper, shipment2, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch2 = batchFactory.createBatch(shopper, shipment2, slot, Batch.Type.DELIVERY);

        String url = String.format("/api/batches/%d/start", shoppingBatch1.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1CategoryACategoryOrderA.getId(), item1CategoryACategoryOrderA.getSku(), item1CategoryACategoryOrderA.getRequestedQty());
        object1.put("id", item1CategoryACategoryOrderA.getId());
        object1.put("found_qty", item1CategoryACategoryOrderA.getRequestedQty());

        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2CategoryBCategoryOrderB.getId(), item2CategoryBCategoryOrderB.getSku(), item2CategoryBCategoryOrderB.getRequestedQty());
        object2.put("id", item2CategoryBCategoryOrderB.getId());
        object2.put("found_qty", item2CategoryBCategoryOrderB.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);

        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipment1.getNumber());

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);
        requestBody.put("shipment_numbers", shipmentsArray);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(15000.0);

        // Create geofence in radar
        String radarUrl = radarProperty.getBaseUrl() + "/geofences/shipment_number/" + shipment1.getNumber();
        String geofenceResponse = "{\"meta\":{\"code\":200},\"geofence\":{\"_id\":\"60c898502cb0090064a7b83b\",\"createdAt\":\"2021-06-15T12:08:48.668Z\",\"updatedAt\":\"2021-06-15T12:08:48.641Z\",\"live\":false,\"description\":\"H12345\",\"tag\":\"shipment_number\",\"externalId\":\"H12345\",\"type\":\"circle\",\"deleteAfter\":\"2021-06-17T00:00:00.000Z\",\"geometryCenter\":{\"coordinates\":[106.80363212609046,-6.291185885319718],\"type\":\"Point\"},\"geometryRadius\":100,\"geometry\":{\"type\":\"Polygon\",\"coordinates\":[[[106.80453689638168,-6.291185885319718],[106.80451951147423,-6.291010436378023]]},\"enabled\":true}}";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.min(1), requestTo(new URI(radarUrl)))
                .andExpect(method(HttpMethod.PUT))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(geofenceResponse));

        url = String.format("/api/v3/batches/%d/finalize", shoppingBatch1.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mockServer.verify();

        transactionHelper.withNewTransaction(() -> {
            Item finalItem1CategoryACategoryOrderA = itemRepository.findByShipmentIdAndSku(shipment1.getId(), item1CategoryACategoryOrderA.getSku());
            Assert.assertEquals(categoryOrderA.getPosition(), finalItem1CategoryACategoryOrderA.getCategoryOrder().getPosition());

            Item finalItem2CategoryBCategoryOrderB = itemRepository.findByShipmentIdAndSku(shipment1.getId(), item2CategoryBCategoryOrderB.getSku());
            Assert.assertEquals(categoryOrderB.getPosition(), finalItem2CategoryBCategoryOrderB.getCategoryOrder().getPosition());

            Item finalItem3CategoryACategoryOrderA = itemRepository.findByShipmentIdAndSku(shipment2.getId(), item3CategoryACategoryOrderA.getSku());
            Assert.assertEquals(categoryOrderA.getPosition(), finalItem3CategoryACategoryOrderA.getCategoryOrder().getPosition());
        });
    }

    @Test
    public void testFinalizeShoppping_multipleShipmentInOneBatch_withSameSKUs_whenShipmentMissingItem_shouldReturn207() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, admin, 1, 5, LocalDateTime.now().minusMinutes(30L));
        Slot slot = slots.get(0);

        Category category = categoryFactory.createCategory(admin);

        // Create 4 shipments with same SKU
        Shipment shipment1 = shipmentFactory.createShipment(slot, admin, "order001", "order001");
        Item item1 = itemFactory.createItem(category, shipment1, admin);
        item1.setSku("item001");
        itemFactory.save(item1);

        Shipment shipment2 = shipmentFactory.createShipment(slot, admin, "order002", "order002");
        Item item2 = itemFactory.createItem(category, shipment2, admin);
        item2.setSku("item001");
        itemFactory.save(item2);

        Shipment shipment3 = shipmentFactory.createShipment(slot, admin, "order003", "order003");
        Item item3 = itemFactory.createItem(category, shipment3, admin);
        item3.setSku("item001");
        itemFactory.save(item3);

        Shipment shipment4 = shipmentFactory.createShipment(slot, admin, "order004", "order004");
        Item item4 = itemFactory.createItem(category, shipment4, admin);
        item4.setSku("item001");
        itemFactory.save(item4);

        // Pooled all shipments to one shopping batch
        Batch sBatch = batchFactory.createBatch(admin, Lists.newArrayList(shipment1, shipment2, shipment3, shipment4), slot, Batch.Type.SHOPPING);

        // Start shopping the batch
        batchHelper.startBatch(sBatch, shopper);

        Mockito.when(orderService.getOrderTotal(Mockito.anyString())).thenReturn(15000.0);

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1.getId(), item1.getSku(), item1.getRequestedQty());
        object1.put("id", item1.getId());
        object1.put("found_qty", item1.getRequestedQty());

        JSONObject object4 = shipmentJsonObjectFactory.createItemObject(item4.getId(), item4.getSku(), item4.getRequestedQty());
        object4.put("id", item4.getId());
        object4.put("found_qty", item4.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object4);

        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipment1.getNumber());
        shipmentsArray.put(shipment2.getNumber());
        shipmentsArray.put(shipment3.getNumber());
        shipmentsArray.put(shipment4.getNumber());

        JSONObject finalizeRequestBody = new JSONObject();
        finalizeRequestBody.put("items", itemsArray);
        finalizeRequestBody.put("shipment_numbers", shipmentsArray);

        // finalize shopping all shipments but only 1 item in the payload
        String url = String.format("/api/v3/batches/%d/finalize", sBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                        .content(finalizeRequestBody.toString())
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isMultiStatus())
                .andExpect(jsonPath("$.response.exceptions", Matchers.hasSize(2)))
                .andExpect(jsonPath("$.response.exceptions[0].message", Matchers.equalTo("Missing finalize item(s)")))
        ;

    }

    @Test
    public void testFinalizeShoppping_multipleShipmentInOneBatch_withSameSKUs_whenItemInPayloadIsComplete_shouldReturn200() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper).get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, admin, 1, 5, LocalDateTime.now().minusMinutes(30L));
        Slot slot = slots.get(0);

        Category category = categoryFactory.createCategory(admin);

        // Create 4 shipments with same SKU
        Shipment shipment1 = shipmentFactory.createShipment(slot, admin, "order001", "order001");
        Item item1 = itemFactory.createItem(category, shipment1, admin);
        item1.setSku("item001");
        itemFactory.save(item1);

        Shipment shipment2 = shipmentFactory.createShipment(slot, admin, "order002", "order002");
        Item item2 = itemFactory.createItem(category, shipment2, admin);
        item2.setSku("item001");
        itemFactory.save(item2);

        Shipment shipment3 = shipmentFactory.createShipment(slot, admin, "order003", "order003");
        Item item3 = itemFactory.createItem(category, shipment3, admin);
        item3.setSku("item001");
        itemFactory.save(item3);

        Shipment shipment4 = shipmentFactory.createShipment(slot, admin, "order004", "order004");
        Item item4 = itemFactory.createItem(category, shipment4, admin);
        item4.setSku("item001");
        itemFactory.save(item4);
        Item item5 = itemFactory.createItem(category, shipment4, admin);
        item5.setSku("item002");
        item5.setReplacedItem(item4);
        itemFactory.save(item5);

        // Pooled all shipments to one shopping batch
        Batch sBatch = batchFactory.createBatch(admin, Lists.newArrayList(shipment1, shipment2, shipment3, shipment4), slot, Batch.Type.SHOPPING);

        // Start shopping the batch
        batchHelper.startBatch(sBatch, shopper);

        Mockito.when(orderService.getOrderTotal(Mockito.anyString())).thenReturn(15000.0);

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1.getId(), item1.getSku(), item1.getRequestedQty());
        object1.put("id", item1.getId());
        object1.put("found_qty", item1.getRequestedQty());

        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());

        JSONObject object3 = shipmentJsonObjectFactory.createItemObject(item3.getId(), item3.getSku(), item3.getRequestedQty());
        object3.put("id", item3.getId());
        object3.put("found_qty", item3.getRequestedQty());

        JSONObject object4 = shipmentJsonObjectFactory.createItemObject(item4.getId(), item4.getSku(), item4.getRequestedQty());
        object4.put("id", item4.getId());
        object4.put("found_qty", item4.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);
        itemsArray.put(object3);
        itemsArray.put(object4);

        JSONArray shipmentsArray = new JSONArray();
        shipmentsArray.put(shipment1.getNumber());
        shipmentsArray.put(shipment2.getNumber());
        shipmentsArray.put(shipment3.getNumber());
        shipmentsArray.put(shipment4.getNumber());

        JSONObject finalizeRequestBody = new JSONObject();
        finalizeRequestBody.put("items", itemsArray);
        finalizeRequestBody.put("shipment_numbers", shipmentsArray);

        // finalize shopping all shipments but complete items in the payload
        String url = String.format("/api/v3/batches/%d/finalize", sBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                        .content(finalizeRequestBody.toString())
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk());

    }

}
