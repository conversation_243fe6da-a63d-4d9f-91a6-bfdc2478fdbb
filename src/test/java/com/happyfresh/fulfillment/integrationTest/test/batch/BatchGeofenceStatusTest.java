package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.happyfresh.fulfillment.batch.service.BatchSndService;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.BatchFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.SlotFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.repository.CountryRepository;
import com.happyfresh.fulfillment.repository.JobRepository;
import org.hamcrest.Matchers;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.List;
import java.util.Map;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;

public class BatchGeofenceStatusTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private BatchSndService batchSndService;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private TransactionHelper transactionHelper;

    @Test
    public void returnUnauthorizedIfNotOwner() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER);
        User driver = userFactory.createUserData(Role.Name.DRIVER, shopper.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        Batch batch = batchFactory.createBatch(driver, driver, slots.get(0), Batch.Type.DELIVERY);
        Shipment shipment = batch.getJobs().get(0).getShipment();

        String geofenceStatusPath = String.format("/geofence_status?lat=%f&lon=%f", shipment.getAddressLat(), shipment.getAddressLon());

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/"+ batch.getId() +"/shipments/"+ shipment.getNumber() + geofenceStatusPath)
            .header("X-Fulfillment-User-Token", shopper.getToken())
            .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isUnauthorized());
    }

    @Test
    public void withWithinRadiusReturns200Status() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        List<Batch> batches = batchFactory.createBatches(1, driver, driver, slots.get(0));
        Batch shoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        transactionHelper.withNewTransaction(() -> {
            shoppingBatches.getJobs().forEach(job -> {
                job.setState(Job.State.FINISHED);
                jobRepository.save(job);
            });
        });

        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();
        Shipment shipment = deliveryBatch.getJobs().get(0).getShipment();

        batchSndService.pickup(deliveryBatch.getId());
        batchSndService.accept(shipment.getNumber());
        batchSndService.deliver(shipment.getNumber());

        String geofenceStatusPath = String.format(
            "/geofence_status?lat=%f&lon=%f",
            shipment.getAddressLat(),
            shipment.getAddressLon());

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/"+ deliveryBatch.getId() +"/shipments/"+ shipment.getNumber() + geofenceStatusPath)
            .header("X-Fulfillment-User-Token", driver.getToken())
            .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
            .andDo(print())
            .andExpect(MockMvcResultMatchers.jsonPath("$.geofence_status.within_geofence", Matchers.equalTo(true)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.geofence_status.geofence_radius", Matchers.equalTo(100.0)))
            .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void withOutsideRadiusReturns200Status() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        List<Batch> batches = batchFactory.createBatches(1, driver, driver, slots.get(0));
        Batch shoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        transactionHelper.withNewTransaction(() -> {
            shoppingBatches.getJobs().forEach(job -> {
                job.setState(Job.State.FINISHED);
                jobRepository.save(job);
            });
        });

        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();
        Shipment shipment = deliveryBatch.getJobs().get(0).getShipment();

        batchSndService.pickup(deliveryBatch.getId());
        batchSndService.accept(shipment.getNumber());
        batchSndService.deliver(shipment.getNumber());

        Country country = deliveryBatch.getStockLocation().getState().getCountry();
        Map<String, String> preferences = country.getPreferences();
        preferences.put("delivery_geofence_radius", "5.0");
        country.setPreferences(preferences);
        transactionHelper.withNewTransaction(() -> {
            countryRepository.save(country);
        });

        // Address lat lon is shipment.setAddressLat(-6.276499); shipment.setAddressLon(106.797361);
        // Use driver lat lon which distance greater than 5.0 meters : -6.280517, 106.792953
        Double driverLat = -6.280517;
        Double driverLon = 106.792953;

        String geofenceStatusPath = String.format(
            "/geofence_status?lat=%f&lon=%f",
            driverLat,
            driverLon);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/"+ deliveryBatch.getId() +"/shipments/"+ shipment.getNumber() + geofenceStatusPath)
            .header("X-Fulfillment-User-Token", driver.getToken())
            .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
            .andDo(print())
            .andExpect(MockMvcResultMatchers.jsonPath("$.geofence_status.within_geofence", Matchers.equalTo(false)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.geofence_status.geofence_radius", Matchers.equalTo(5.0)))
            .andExpect(MockMvcResultMatchers.status().isOk());
    }
}
