package com.happyfresh.fulfillment.integrationTest.test.grabExpress;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.batch.service.DriverAutoAssignmentService;
import com.happyfresh.fulfillment.common.util.ApplicationUtil;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.grabExpress.model.GrabExpressOAuth2_0Response;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressAPI;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressService;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.MockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.slot.presenter.SlotOptimizationEvent;
import org.elasticsearch.common.geo.GeoPoint;
import org.hamcrest.Matchers;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.test.web.client.ResponseCreator;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.web.client.ResourceAccessException;

import java.net.URI;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withBadRequest;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

public class GrabExpressBookingTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private ItemFactory itemFactory;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private GrabExpressDeliveryRepository grabExpressDeliveryRepository;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private MockServerHelper mockServerHelper;

    @Autowired
    private Environment environment;

    @Autowired
    private GrabExpressService grabExpressService;

    @Autowired
    private JobRepository jobRepository;

    @SpyBean
    private GrabExpressAPI grabExpressAPI;

    @Autowired
    private ShiftFactory shiftFactory;

    @SpyBean
    private DriverAutoAssignmentService driverAutoAssignmentService;

    private User shopper;

    private User driver;

    private User admin;

    private Shipment shipment;

    private Batch shoppingBatch;

    private Batch deliveryBatch;

    private JSONObject shipmentObj;

    private JSONObject cancelReasonObj;

    private List<StockLocation> stockLocations;

    private List<Slot> slots;

    @Before
    public void setup() throws Exception {
        admin = userFactory.createUserData(Role.Name.ADMIN);
        shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());
        driver = userFactory.createUserData(Role.Name.DRIVER, admin.getTenant());
        User grabDriver = userFactory.createUserData(Role.Name.GRAB_EXPRESS_DRIVER, admin.getTenant());

        stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper);
        slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, shopper);

        shipment = shipmentFactory.createShipmentWithEmptyGrabExpress(slots.get(0), shopper, "H234567");

        shoppingBatch = batchFactory.createBatch(shopper, shipment, slots.get(0), Batch.Type.SHOPPING);
        shoppingBatch.setUser(shopper);
        batchRepository.save(shoppingBatch);

        deliveryBatch = batchFactory.createBatch(driver, shipment, slots.get(0), Batch.Type.DELIVERY);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.GRAB_EXPRESS);
        batchRepository.save(deliveryBatch);

        List<Item> items = itemFactory.createItems(shipment, shopper, 20);

        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocations.get(0), slots.get(0).getId(), 1, "H234567", new GeoPoint(-6.291202, 106.7855), "Lintasarta");

        cancelReasonObj = new JSONObject();
        cancelReasonObj.put("cancel_reason", "DRIVER_NOT_SHOWING");
    }

    private MockRestServiceServer setupMockServer(MockRestServiceServer mockServer, int expectedCount, HttpMethod method, String url, String responseBody, boolean isSuccess) throws Exception {
        ExpectedCount count = expectedCount > 0 ? ExpectedCount.min(expectedCount) : ExpectedCount.never();
        ResponseCreator responseCreator = isSuccess ? withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody) : withBadRequest().contentType(MediaType.APPLICATION_JSON).body(responseBody);
        mockServer.expect(count, requestTo(new URI(url)))
                .andExpect(method(method))
                .andRespond(responseCreator);
        return mockServer;
    }

    @Test
    public void return2xxOauth() throws Exception {
        String authorization = grabExpressAPI.oAuth2_0();
        Assert.assertNotEquals(authorization, "");
    }

    private GrabExpressOAuth2_0Response parseResponse(ResponseEntity responseEntity) {
        String jsonString = Objects.requireNonNull(responseEntity.getBody()).toString();
        GrabExpressOAuth2_0Response grabExpressOAuth2_0Response = new GrabExpressOAuth2_0Response();
        ObjectMapper mapper = new ObjectMapper();
        try {
            grabExpressOAuth2_0Response = mapper.readValue(jsonString, GrabExpressOAuth2_0Response.class);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return grabExpressOAuth2_0Response;
    }

    @Test
    public void return2xxBookGetAndCancelGrabExpressByAdmin() throws Exception {
        String url = environment.getProperty("GRAB_API_URL") + ApplicationUtil.GRAB_EXPRESS_MAIN_PATH;
        String bookResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"ALLOCATING\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, url, bookResponseBody, true);

        String checkUrl = url + "/" + "GE_DELIVERY_ID";
        String checkResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"ALLOCATING\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        mockServer = setupMockServer(mockServer,1, HttpMethod.GET, checkUrl, checkResponseBody, true);

        String cancelResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"CANCELLED\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        mockServer = setupMockServer(mockServer,1, HttpMethod.DELETE, checkUrl, cancelResponseBody, true);

        mvc.perform(MockMvcRequestBuilders.post("/api/shipments/" + shipment.getNumber() + "/grab")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                .header("locale", "id")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.deliveryID", Matchers.any(String.class)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.status", Matchers.equalTo("ALLOCATING")))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/grab")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + shipment.getNumber() + "/grab/cancel")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(cancelReasonObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mockServer.verify();
    }

    @Test
    public void return4xxBookGrabExpressByAdmin() throws Exception {
        shopper.setPhone("");
        userRepository.save(shopper);

        String url = environment.getProperty("GRAB_API_URL") + ApplicationUtil.GRAB_EXPRESS_MAIN_PATH;
        String bookResponseBody = "{ \"message\": \"Missing value for phone\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, url, bookResponseBody, false);

        mvc.perform(MockMvcRequestBuilders.post("/api/shipments/" + shipment.getNumber() + "/grab")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.errors[0].type", Matchers.equalTo("GrabExpressException")))
                .andExpect(MockMvcResultMatchers.status().is4xxClientError());

        mockServer.verify();
    }

    @Test
    public void return401BookGrabExpressShouldIncreaseRetryCount() throws Exception {
        User systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, admin.getTenant());

        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveryRepository.findAllByShipmentNumber(shipment.getNumber()).get(0);
        grabExpressDelivery.setBookingScheduledAt(grabExpressDelivery.getBookingScheduledAt().minusMinutes(20));
        grabExpressDeliveryRepository.save(grabExpressDelivery);

        Mockito.doThrow(ResourceAccessException.class).when(grabExpressAPI).bookingGrabExpress(any(Shipment.class), any(Country.class), "");
        grabExpressService.createGrabExpressBooking(grabExpressDelivery.getShipment().getNumber(), true);

        GrabExpressDelivery grabExpressDelivery1 = grabExpressDeliveryRepository.findAllByShipmentNumber(shipment.getNumber()).get(0);
        Assert.assertEquals(1, (int) grabExpressDelivery1.getRetryCount());
    }

    @Test
    public void return2xxBookGetAndCancelGrabExpressByShopperAndChangeToHF() throws Exception {
        String url = environment.getProperty("GRAB_API_URL") + ApplicationUtil.GRAB_EXPRESS_MAIN_PATH;
        String bookResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"ALLOCATING\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, url, bookResponseBody, true);

        String checkUrl = url + "/" + "GE_DELIVERY_ID";
        String checkResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"ALLOCATING\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        mockServer = setupMockServer(mockServer,1, HttpMethod.GET, checkUrl, checkResponseBody, true);

        String cancelResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"CANCELLED\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        mockServer = setupMockServer(mockServer,1, HttpMethod.DELETE, checkUrl, cancelResponseBody, true);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/grab")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.deliveryID", Matchers.any(String.class)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.status", Matchers.equalTo("ALLOCATING")))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/grab")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/grab/cancel")
                .param("cancel_reason", "NO_DRIVER_ACCEPTED_BOOKING")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(cancelReasonObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/grab/to_hf")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Optional<Batch> newDeliveryBatch = batchRepository.findById(deliveryBatch.getId());
        Assert.assertNull(newDeliveryBatch.get().getTplType());
        Assert.assertEquals(Batch.DeliveryType.NORMAL, newDeliveryBatch.get().getDeliveryType());
        Assert.assertTrue(newDeliveryBatch.get().isSwitchedToHf());
        mockServer.verify();
    }

    @Test
    public void return2xxBookGetAndCancelGrabExpressByShopperAndChangeToHFWithTplEndpoint() throws Exception {
        String url = environment.getProperty("GRAB_API_URL") + ApplicationUtil.GRAB_EXPRESS_MAIN_PATH;
        String bookResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"ALLOCATING\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, url, bookResponseBody, true);

        String checkUrl = url + "/" + "GE_DELIVERY_ID";
        String checkResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"ALLOCATING\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        mockServer = setupMockServer(mockServer,1, HttpMethod.GET, checkUrl, checkResponseBody, true);

        String cancelResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"CANCELLED\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        mockServer = setupMockServer(mockServer,1, HttpMethod.DELETE, checkUrl, cancelResponseBody, true);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/grab")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.deliveryID", Matchers.any(String.class)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.status", Matchers.equalTo("ALLOCATING")))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/grab")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/grab/cancel")
                .param("cancel_reason", "NO_DRIVER_ACCEPTED_BOOKING")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(cancelReasonObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/to_hf")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Optional<Batch> newDeliveryBatch = batchRepository.findById(deliveryBatch.getId());
        Assert.assertNull(newDeliveryBatch.get().getTplType());
        Assert.assertEquals(Batch.DeliveryType.NORMAL, newDeliveryBatch.get().getDeliveryType());
        Assert.assertTrue(newDeliveryBatch.get().isSwitchedToHf());
        mockServer.verify();
    }

    @Test
    public void return2xxCancelGrabExpressByShopperWhenOrderAlreadyCancelledOnGESide() throws Exception {
        String url = environment.getProperty("GRAB_API_URL") + ApplicationUtil.GRAB_EXPRESS_MAIN_PATH;
        String bookResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"ALLOCATING\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, url, bookResponseBody, true);

        String checkUrl = url + "/" + "GE_DELIVERY_ID";
        String checkResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"ALLOCATING\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        mockServer = setupMockServer(mockServer,1, HttpMethod.GET, checkUrl, checkResponseBody, true);

        String cancelResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"CANCELLED\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        mockServer = setupMockServer(mockServer,1, HttpMethod.DELETE, checkUrl, cancelResponseBody, true);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/grab")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.deliveryID", Matchers.any(String.class)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.status", Matchers.equalTo("ALLOCATING")))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/grab")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/grab/cancel")
                .param("cancel_reason", "NO_DRIVER_ACCEPTED_BOOKING")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(cancelReasonObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveryRepository.findAllByShipmentNumber(shipment.getNumber()).get(0);
        grabExpressDelivery.setStatus(GrabExpressDelivery.Status.ALLOCATING);
        grabExpressDeliveryRepository.save(grabExpressDelivery);

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/grab/cancel")
                .param("cancel_reason", "NO_DRIVER_ACCEPTED_BOOKING")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(cancelReasonObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Optional<Batch> newDeliveryBatch = batchRepository.findById(deliveryBatch.getId());
        Assert.assertEquals(Batch.TplType.GRAB_EXPRESS, newDeliveryBatch.get().getTplType());
        Assert.assertEquals(Batch.DeliveryType.TPL, newDeliveryBatch.get().getDeliveryType());
        grabExpressDelivery = grabExpressDeliveryRepository.findAllByShipmentNumber(shipment.getNumber()).get(0);
        Assert.assertEquals(GrabExpressDelivery.Status.CANCELED, grabExpressDelivery.getStatus());
        mockServer.verify();
    }

    @Test
    public void return2xxBookGetAndCancelGrabExpressByShopperButDeliveryStarted() throws Exception {
        String url = environment.getProperty("GRAB_API_URL") + ApplicationUtil.GRAB_EXPRESS_MAIN_PATH;
        String bookResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"ALLOCATING\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, url, bookResponseBody, true);

        String checkUrl = url + "/" + "GE_DELIVERY_ID";
        String checkResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"IN_DELIVERY\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        mockServer = setupMockServer(mockServer,2, HttpMethod.GET, checkUrl, checkResponseBody, true);

        String cancelResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"IN_DELIVERY\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        mockServer = setupMockServer(mockServer,1, HttpMethod.DELETE, checkUrl, cancelResponseBody, false);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/grab")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.deliveryID", Matchers.any(String.class)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.status", Matchers.equalTo("ALLOCATING")))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/grab")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/grab/cancel")
                .param("cancel_reason", "NO_DRIVER_ACCEPTED_BOOKING")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(cancelReasonObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mockServer.verify();

        Optional<Batch> newDeliveryBatch = batchRepository.findById(deliveryBatch.getId());
        Job deliveryJob = jobRepository.findAllByBatchId(newDeliveryBatch.get().getId()).get(0);
        Assert.assertEquals(Job.State.DELIVERING, deliveryJob.getState());
        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveryRepository.findAllByShipmentNumber(shipment.getNumber()).get(0);
        Assert.assertEquals(GrabExpressDelivery.Status.IN_DELIVERY, grabExpressDelivery.getStatus());
    }

    @Test
    public void return2xxBookGetAndCancelGrabExpressByShopperButDeliveryAlreadyCompleted() throws Exception {
        String url = environment.getProperty("GRAB_API_URL") + ApplicationUtil.GRAB_EXPRESS_MAIN_PATH;
        String bookResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"ALLOCATING\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, url, bookResponseBody, true);

        String checkUrl = url + "/" + "GE_DELIVERY_ID";
        String checkResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"COMPLETED\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        mockServer = setupMockServer(mockServer,2, HttpMethod.GET, checkUrl, checkResponseBody, true);

        String cancelResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"COMPLETED\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        mockServer = setupMockServer(mockServer,1, HttpMethod.DELETE, checkUrl, cancelResponseBody, false);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/grab")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.deliveryID", Matchers.any(String.class)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.status", Matchers.equalTo("ALLOCATING")))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/grab")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/grab/cancel")
                .param("cancel_reason", "NO_DRIVER_ACCEPTED_BOOKING")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(cancelReasonObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mockServer.verify();

        Optional<Batch> newDeliveryBatch = batchRepository.findById(deliveryBatch.getId());
        Job deliveryJob = jobRepository.findAllByBatchId(newDeliveryBatch.get().getId()).get(0);
        Assert.assertEquals(Job.State.FINISHED, deliveryJob.getState());
        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveryRepository.findAllByShipmentNumber(shipment.getNumber()).get(0);
        Assert.assertEquals(GrabExpressDelivery.Status.COMPLETED, grabExpressDelivery.getStatus());
    }

    @Test
    public void return2xxSwitchToHFToShift() throws Exception {
        Shipment earliestShipment = shipmentFactory.createShipmentWithEmptyGrabExpress(slots.get(0), shopper, "H234568");
        Shift shift = shiftFactory.createShift(stockLocations.get(0), admin, Shift.Type.DRIVER, slots.get(0).getStartTime(), slots.get(0).getStartTime().plusHours(10), 1);

        Batch earliestBatch = batchFactory.createBatch(driver, earliestShipment, slots.get(0), Batch.Type.DELIVERY);
        earliestBatch.setDeliveryType(Batch.DeliveryType.LONG_HOUR);
        earliestBatch.setVehicle(1);
        earliestBatch.setShift(shift);
        batchRepository.save(earliestBatch);

        String url = environment.getProperty("GRAB_API_URL") + ApplicationUtil.GRAB_EXPRESS_MAIN_PATH;
        String bookResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"ALLOCATING\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer = setupMockServer(mockServer,1, HttpMethod.POST, url, bookResponseBody, true);

        String checkUrl = url + "/" + "GE_DELIVERY_ID";
        String checkResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"ALLOCATING\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        mockServer = setupMockServer(mockServer,1, HttpMethod.GET, checkUrl, checkResponseBody, true);

        String cancelResponseBody = "{ \"deliveryID\": \"GE_DELIVERY_ID\", \"status\": \"CANCELLED\", \"trackURL\": \"https://track.it/i4J6\", \"pickupPin\": \"GE_PICKUP_PIN\", \"courier\": { \"name\": \"Jason\", \"phone\": \"08459238243\", \"pictureUrl\": \"http://google.com?q=jason\"} }";
        mockServer = setupMockServer(mockServer,1, HttpMethod.DELETE, checkUrl, cancelResponseBody, true);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/grab")
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.jsonPath("$.deliveryID", Matchers.any(String.class)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.status", Matchers.equalTo("ALLOCATING")))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.get("/api/shipments/" + shipment.getNumber() + "/grab")
                        .header("X-Fulfillment-User-Token", admin.getToken())
                        .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/grab/cancel")
                        .param("cancel_reason", "NO_DRIVER_ACCEPTED_BOOKING")
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(cancelReasonObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + shoppingBatch.getId() + "/shipments/" + shipment.getNumber() + "/to_hf")
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Optional<Batch> newDeliveryBatch = batchRepository.findById(deliveryBatch.getId());
        Assert.assertNull(newDeliveryBatch.get().getTplType());
        Assert.assertEquals(Batch.DeliveryType.NORMAL, newDeliveryBatch.get().getDeliveryType());
        Assert.assertTrue(newDeliveryBatch.get().isSwitchedToHf());
        Assert.assertEquals(earliestBatch.getShift().getId(), newDeliveryBatch.get().getShift().getId());
        Assert.assertEquals(earliestBatch.getVehicle(), newDeliveryBatch.get().getVehicle());
        verify(driverAutoAssignmentService, times(1)).publishAutoAssignmentEvent(any(String.class), any(String.class), eq(SlotOptimizationEvent.AutoAssignmentTriggerEvent.SWITCH_TO_HF));
        mockServer.verify();
    }
}
