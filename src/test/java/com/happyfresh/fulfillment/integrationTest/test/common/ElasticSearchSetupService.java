package com.happyfresh.fulfillment.integrationTest.test.common;

import com.happyfresh.fulfillment.common.service.ESClientService;
import com.happyfresh.fulfillment.common.service.TemplateEngine;
import com.happyfresh.fulfillment.entity.Agent;
import com.happyfresh.fulfillment.entity.es.RouteEdge;
import com.happyfresh.fulfillment.entity.es.SimpleRouteEdge;
import com.happyfresh.fulfillment.entity.es.StockLocationEs;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Profile("it")
public class ElasticSearchSetupService {

    private final Logger LOGGER = LoggerFactory.getLogger(BaseTest.class);

    @Autowired
    private ESClientService esClientService;

    @Autowired
    private SimpleRouteEdgeHelper simpleRouteEdgeHelper;

    @Autowired
    private TemplateEngine templateEngine;

    private boolean preFillSimpleRouteEdges = true;

    public void setupDefault() throws  Exception {
        setup(SimpleRouteEdgeHelper.DEFAULT_ROUTE_EDGE_FIXTURE_FILE_NAME);
    }

    public void setup(String fileName) throws Exception{
        esClientService.deleteIndex();
        esClientService.createIndex();
        createMapping();

        if (preFillSimpleRouteEdges)
            simpleRouteEdgeHelper.preFillSimpleRouteEdge(esClientService, fileName);
    }

    public void setPreFillSimpleRouteEdges(boolean enabled) {
        this.preFillSimpleRouteEdges = enabled;
    }

    private void createMapping() {
        Map<Class, String> maps = new HashMap<>();
        maps.put(StockLocationEs.class, "stock_location_es_mapping.ftl");
        maps.put(Agent.class, "agent_es_mapping.ftl");
        maps.put(RouteEdge.class, "route_edge_mapping.ftl");
        maps.put(SimpleRouteEdge.class, "simple_route_edge_mapping.ftl");

        maps.forEach((k, v) -> {
            try {
                String mappingPayload = templateEngine.getTemplateString(v, null);
                esClientService.putMapping(mappingPayload, k);
            } catch (Exception ex) {
                LOGGER.info("Exception: " + ex);
            }
        });
    }
}
