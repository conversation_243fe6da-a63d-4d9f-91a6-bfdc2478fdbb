package com.happyfresh.fulfillment.integrationTest.test.slot;

import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import org.assertj.core.util.Lists;
import org.elasticsearch.common.geo.GeoPoint;
import org.hamcrest.Matchers;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.*;
import java.util.List;

public class ShopperLongerDeliveryAvailabilityTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private ClusterFactory clusterFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    private double shopperAveragePickingTimePerUniqItem = 3;
    private User user;
    private User shopper;
    private StockLocation stockLocation1;
    private Slot slot1;
    private Slot slot2;
    private Shift shiftDriver;
    private Shift shiftShopper1;
    private final LocalDate today = LocalDate.now();

    @Before
    public void setUp() throws InterruptedException {
        Thread.sleep(750);

        user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        shopper = userFactory.createUserData(Role.Name.SHOPPER, user.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user);
        stockLocation1 = stockLocations.get(0);
        stockLocation1.setShopperAveragePickingTimePerUniqItem(shopperAveragePickingTimePerUniqItem);
        stockLocationFactory.save(stockLocation1);
        Cluster cluster = stockLocation1.getCluster();
        cluster.setSlotType(Slot.Type.LONGER_DELIVERY);
        clusterFactory.save(cluster);

        slot1 = slotFactory.createLongerDeliverySlot(stockLocation1, user, 0, 1, 4);
        slot2 = slotFactory.createLongerDeliverySlot(stockLocation1, user, 0, 5, 4);
        shiftShopper1 = shiftFactory.createShift(stockLocation1, user, Shift.Type.SHOPPER, slot1.getStartTime().minusHours(2), slot2.getEndTime().minusHours(2), 1);
        shiftDriver = shiftFactory.createShift(stockLocation1, user, Shift.Type.DRIVER, slot1.getStartTime(), slot2.getEndTime(), 2);
    }

    @Test
    public void testNoBatch() throws Exception {
        verifySlot(true, true);
    }

    @Test
    public void testNoShift() throws Exception {
        shiftShopper1.setCount(0);
        shiftFactory.save(shiftShopper1);

        verifySlot(false, false);

        // There's shiftShopper1 for first Slot, but shiftShopper1 is about to end. There's shiftShopper1 for 2nd slot
        LocalDateTime startShift = slot2.getStartTime().minusMinutes(5);
        LocalDateTime endShift = slot2.getEndTime();
        shiftShopper1.setStartTime(startShift);
        shiftShopper1.setEndTime(endShift);
        shiftShopper1.setCount(1);
        shiftFactory.save(shiftShopper1);

        verifySlot(false, true);

        // No shiftShopper1 for first Slot. There's shiftShopper1 for 2nd slot
        startShift = slot2.getStartTime().plusHours(1);
        endShift = slot2.getEndTime();
        shiftShopper1.setStartTime(startShift);
        shiftShopper1.setEndTime(endShift);
        shiftShopper1.setCount(1);
        shiftFactory.save(shiftShopper1);

        verifySlot(false, true);

        // No shiftShopper1 for All Slot
        startShift = slot1.getStartTime().minusHours(2).plusDays(1);
        endShift = slot2.getEndTime().minusHours(2).plusDays(1);
        shiftShopper1.setStartTime(startShift);
        shiftShopper1.setEndTime(endShift);
        shiftFactory.save(shiftShopper1);

        verifySlot(false, false);
    }

    @Test
    public void testBatchEndTimeLogic() throws Exception {
        Batch batch1 = batchFactory.createBatch(user, slot1, Batch.Type.SHOPPING);
        batch1.setStartTime(shiftShopper1.getStartTime());
        batch1.setEndTime(slot1.getEndTime().minusMinutes(20));
        batch1.setShift(shiftShopper1);
        batch1.setVehicle(1);
        batch1.setUser(shopper);
        batchFactory.save(batch1);

        Batch batch2 = batchFactory.createBatch(user, slot2, Batch.Type.SHOPPING);
        batch2.setStartTime(batch1.getEndTime());
        batch2.setEndTime(shiftShopper1.getEndTime().minusMinutes(5));
        batch2.setShift(shiftShopper1);
        batch2.setVehicle(1);
        batchFactory.save(batch2);

        verifySlot(false, false);

        batch1.setEndTime(slot1.getEndTime().minusMinutes(55));
        batch1.setUser(null);
        batchFactory.save(batch1);

        batch2.setEndTime(shiftShopper1.getEndTime().minusMinutes(55));
        batchFactory.save(batch2);

        verifySlot(true, true);
    }

    @Test
    public void testShiftEndTimeLogic() throws Exception {
        Batch batch1 = batchFactory.createBatch(user, slot1, Batch.Type.SHOPPING);
        batch1.setEndTime(slot1.getEndTime().minusMinutes(55));
        batch1.setShift(shiftShopper1);
        batch1.setVehicle(1);
        batch1.setUser(shopper);
        batchFactory.save(batch1);

        // Test shopper Shift End, Return false due to shopper shiftShopper1 has already ended
        Batch batch2 = batchFactory.createBatch(user, slot2, Batch.Type.SHOPPING);
        batch2.setEndTime(slot2.getEndTime().minusMinutes(55));
        batch2.setShift(shiftShopper1);
        batch2.setVehicle(1);
        batchFactory.save(batch2);

        // First slot fail because when check next following slots batches in same shiftShopper1 (batch 2) plus shopping duration exceed shiftShopper1 end time
        verifySlot(false, false);
    }

    @Test
    public void testSlotEndTimeLogic() throws Exception {
        LocalDateTime slot1EndTime = LocalDateTime.now().plusMinutes(25);

        slot1.setStartTime(slot1EndTime.minusHours(4));
        slot1.setEndTime(slot1EndTime);
        slotFactory.save(slot1);

        slot2.setStartTime(slot1EndTime);
        slot2.setEndTime(slot1EndTime.plusHours(4));
        slotFactory.save(slot2);

        Batch batch1 = batchFactory.createBatch(user, slot1, Batch.Type.SHOPPING);
        batch1.setEndTime(slot1.getEndTime().minusMinutes(55));
        batch1.setShift(shiftShopper1);
        batch1.setVehicle(1);
        batch1.setUser(shopper);
        batchFactory.save(batch1);

        // First slot fail because now + shopping time exceed slot end time, while second slot is available
        verifySlot(false, true);
    }

    @Test
    public void testSlotEndTimeLogicCase2() throws Exception {
        LocalDateTime slot1EndTime = LocalDateTime.now().plusMinutes(40);

        slot1.setStartTime(slot1EndTime.minusHours(2));
        slot1.setEndTime(slot1EndTime);
        slotFactory.save(slot1);

        slot2.setStartTime(slot1EndTime);
        slot2.setEndTime(slot1EndTime.plusHours(4));
        slotFactory.save(slot2);

        shiftShopper1.setStartTime(LocalDateTime.now().minusMinutes(40));
        shiftShopper1.setEndTime(slot2.getEndTime());
        shiftFactory.save(shiftShopper1);

        Batch batch1 = batchFactory.createBatch(user, slot1, Batch.Type.SHOPPING);
        batch1.setStartTime(LocalDateTime.now().minusMinutes(10));
        batch1.setEndTime(LocalDateTime.now().plusMinutes(30));
        batch1.setShift(shiftShopper1);
        batch1.setVehicle(1);
        batchFactory.save(batch1);

        System.out.println("Slot 1: " + slot1.getStartTime() + "-" + slot1.getEndTime());
        System.out.println("Slot 2: " + slot2.getStartTime() + "-" + slot2.getEndTime());
        System.out.println("Shift 1: " + shiftShopper1.getStartTime() + "-" + shiftShopper1.getEndTime());
        System.out.println("Batch 1: " + batch1.getStartTime() + "-" + batch1.getEndTime());
        // First slot fail because now + shopping time exceed slot end time, while second slot is available
        verifySlot(false, true);
    }

    @Test
    public void testSlotEndTimeLogicCase3() throws Exception {
        slotFactory.deleteAll(Lists.newArrayList(slot1, slot2));
        shiftFactory.delete(shiftShopper1);
        LocalDateTime now = LocalDateTime.now();
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation1, user, 1, 3, now);
        shiftShopper1 = shiftFactory.createShift(stockLocation1, user, Shift.Type.SHOPPER, slots.get(0).getStartTime().minusHours(2), slots.get(2).getEndTime().minusHours(1), 1);

        Batch batch1Slot1 = batchFactory.createBatch(user, slots.get(0), Batch.Type.SHOPPING);
        batch1Slot1.setStartTime(LocalDateTime.now().minusMinutes(25));
        batch1Slot1.setEndTime(LocalDateTime.now().plusMinutes(15));
        batch1Slot1.setShift(shiftShopper1);
        batch1Slot1.setVehicle(1);
        batchFactory.save(batch1Slot1);

        Batch batch1 = batchFactory.createBatch(user, slots.get(1), Batch.Type.SHOPPING);
        batch1.setStartTime(batch1Slot1.getEndTime());
        batch1.setEndTime(batch1.getStartTime().plusMinutes(40));
        batch1.setShift(shiftShopper1);
        batch1.setVehicle(1);
        batchFactory.save(batch1);

        Batch batch2 = batchFactory.createBatch(user, slots.get(1), Batch.Type.SHOPPING);
        batch2.setStartTime(batch1.getEndTime());
        batch2.setEndTime(batch2.getStartTime().plusMinutes(40));
        batch2.setShift(shiftShopper1);
        batch2.setVehicle(1);
        batchFactory.save(batch2);
        // if now = 02:00, slot 1 = 02:00-03:00, slot 2 = 03:00-04:00, slot 3 = 04:00-05:00
        // shift shopper 00:00-04:00
        // Batch 1 Slot 1 01:35-02:15
        // Batch 1 02:15 - 02:55
        // Batch 2 02:55 - 03:35
        verifySlot(false, false);
    }

    @Test
    public void testBatchEndTimeLogicWithMoreThanOneVehicle() throws Exception {
        shiftShopper1.setCount(2);
        shiftFactory.save(shiftShopper1);

        Batch batch1_slot1 = batchFactory.createBatch(user, slot1, Batch.Type.SHOPPING);
        batch1_slot1.setEndTime(slot1.getEndTime().minusMinutes(20));
        batch1_slot1.setShift(shiftShopper1);
        batch1_slot1.setVehicle(1);
        batchFactory.save(batch1_slot1);

        Batch batch1_slot2 = batchFactory.createBatch(user, slot2, Batch.Type.SHOPPING);
        batch1_slot2.setEndTime(slot2.getEndTime().minusMinutes(20));
        batch1_slot2.setShift(shiftShopper1);
        batch1_slot2.setVehicle(1);
        batchFactory.save(batch1_slot2);

        verifySlot(true, true);

        Batch batch2_slot1 = batchFactory.createBatch(user, slot1, Batch.Type.SHOPPING);
        batch2_slot1.setEndTime(slot1.getEndTime().minusMinutes(55));
        batch2_slot1.setShift(shiftShopper1);
        batch2_slot1.setVehicle(2);
        batchFactory.save(batch1_slot1);

        Batch batch2_slot2 = batchFactory.createBatch(user, slot2, Batch.Type.SHOPPING);
        batch2_slot2.setEndTime(slot2.getEndTime().minusHours(2).minusMinutes(55));
        batch2_slot2.setShift(shiftShopper1);
        batch2_slot2.setVehicle(2);
        batchFactory.save(batch1_slot2);

        verifySlot(true, true);
        batch1_slot2.setUser(shopper);
        batchFactory.save(batch1_slot2);

        batch2_slot1.setEndTime(slot1.getEndTime().minusMinutes(20));
        batch2_slot1.setShift(shiftShopper1);
        batchFactory.save(batch2_slot1);

        batch2_slot2.setEndTime(slot2.getEndTime().minusMinutes(20));
        batch2_slot2.setShift(shiftShopper1);
        batch2_slot2.setUser(shopper);
        batchFactory.save(batch2_slot2);

        verifySlot(false, false);
    }

    @Test
    public void testMultiShift() throws Exception {
        shiftShopper1.setEndTime(slot1.getEndTime().minusHours(2));
        shiftFactory.save(shiftShopper1);
        Shift shift2 = shiftFactory.createShift(stockLocation1, user, Shift.Type.SHOPPER, slot2.getStartTime().minusHours(2), slot2.getEndTime().minusHours(2), 1);

        Batch batch1_slot1_shift1 = batchFactory.createBatch(user, slot1, Batch.Type.SHOPPING);
        batch1_slot1_shift1.setEndTime(slot1.getEndTime().minusHours(2).minusMinutes(20));
        batch1_slot1_shift1.setShift(shiftShopper1);
        batch1_slot1_shift1.setVehicle(1);
        batchFactory.save(batch1_slot1_shift1);

        verifySlot(true, true);

        Batch batch1_slot1_shift2 = batchFactory.createBatch(user, slot1, Batch.Type.SHOPPING);
        batch1_slot1_shift2.setEndTime(slot1.getEndTime().minusMinutes(55));
        batch1_slot1_shift2.setShift(shift2);
        batch1_slot1_shift2.setVehicle(1);
        batchFactory.save(batch1_slot1_shift2);

        verifySlot(true, true);
        batch1_slot1_shift1.setEndTime(shiftShopper1.getEndTime().minusMinutes(5));
        batch1_slot1_shift1.setUser(shopper);
        batchFactory.save(batch1_slot1_shift1);
        batch1_slot1_shift2.setUser(shopper);
        batch1_slot1_shift2.setEndTime(slot1.getEndTime().minusMinutes(20));
        batchFactory.save(batch1_slot1_shift2);

        verifySlot(false, true);
    }

    @Test
    public void testShiftInsideSlot() throws Exception {
        shiftShopper1.setStartTime(slot1.getStartTime().minusHours(1));
        shiftShopper1.setEndTime(slot2.getStartTime().minusHours(1));
        shiftFactory.save(shiftShopper1);

        verifySlot(true, true);
    }

    @Test
    public void testShiftIsNotIntersectWithSlot() throws Exception {
        shiftShopper1.setStartTime(slot1.getStartTime().plusHours(1));
        shiftShopper1.setEndTime(slot1.getEndTime().minusHours(1));
        shiftFactory.save(shiftShopper1);

        verifySlot(true, true);
    }

    @Test
    public void testUTCCrossDayShift() throws Exception {
        ZoneId UTCZone = ZoneId.of("UTC");
        ZoneId storeZone = ZoneId.of(stockLocation1.getState().getTimeZone());
        LocalDateTime slotStart = ZonedDateTime.of(today.plusDays(1), LocalTime.of(6, 0, 0, 0), storeZone)
                .withZoneSameInstant(UTCZone)
                .toLocalDateTime();

        slot1.setStartTime(slotStart);
        slot1.setEndTime(slotStart.plusHours(4));
        slotFactory.save(slot1);
        slot2.setStartTime(slotStart.plusHours(4));
        slot2.setEndTime(slotStart.plusHours(8));
        slotFactory.save(slot2);

        shiftShopper1.setStartTime(slot1.getStartTime().minusHours(1));
        shiftShopper1.setEndTime(slot2.getEndTime().minusHours(1));
        shiftFactory.save(shiftShopper1);
        shiftDriver.setStartTime(slot1.getStartTime());
        shiftDriver.setEndTime(slot2.getEndTime());
        shiftFactory.save(shiftDriver);

        int numOfItems = 3;
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation1, numOfItems, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available?date=" + today.toString())
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots", Matchers.hasSize(0)));

        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available?date=" + today.plusDays(1).toString())
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].start_time", Matchers.equalTo(slot1.getStartTime().toString())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[1].start_time", Matchers.equalTo(slot2.getStartTime().toString())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[1].available", Matchers.equalTo(true)));
    }

    private void verifySlot(boolean slot1Availability, boolean slot2Availability) throws Exception {
        int numOfItems = 3;
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation1, numOfItems, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");

        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(slot1Availability)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[1].available", Matchers.equalTo(slot2Availability)));
    }
}
