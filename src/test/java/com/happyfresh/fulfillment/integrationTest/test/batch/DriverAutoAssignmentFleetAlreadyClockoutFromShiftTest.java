package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.AgentHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.BatchHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.SetSlotHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.ShipmentHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import org.elasticsearch.common.geo.GeoPoint;
import org.json.JSONObject;
import org.junit.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class DriverAutoAssignmentFleetAlreadyClockoutFromShiftTest extends BaseTest {

    @Autowired
    private AgentHelper agentHelper;

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private CountryFactory countryFactory;

    @Autowired
    private SetSlotHelper slotHelper;

    @Autowired
    private ShipmentHelper shipmentHelper;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private BatchHelper batchHelper;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private ClusterFactory clusterFactory;

    @Autowired
    private TransactionHelper transactionHelper;

    private User driver;
    private StockLocation stockLocation1;
    private StockLocation stockLocation2;
    private Shift dShift;
    private User extSysAdmin;
    private JSONObject shipmentObj1;
    private User shopper;
    private User driver2;

    @BeforeClass
    public static void initProperty() {
        System.setProperty("kafka.listener.enabled", "true");
    }

    @AfterClass
    public static void resetProperty() {
        System.setProperty("kafka.listener.enabled", "false");
    }

    @Before
    public void setup() throws Exception {
        TimeUnit.SECONDS.sleep(1);
        extSysAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        userFactory.createUserData(Role.Name.SYSTEM_ADMIN, extSysAdmin.getTenant());
        driver = userFactory.createUserData(Role.Name.DRIVER, extSysAdmin.getTenant());
        driver2 = userFactory.createUserData(Role.Name.DRIVER, extSysAdmin.getTenant());
        shopper = userFactory.createUserData(Role.Name.SHOPPER, extSysAdmin.getTenant());
        Country countryID = countryFactory.createCountry(extSysAdmin);
        setupStockLocations(countryID);

        Cluster cluster = stockLocation1.getCluster();
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");
        clusterFactory.save(cluster);

        shiftFactory.createShopperShifts(stockLocation1, LocalDateTime.now(), 2, 8, 1, extSysAdmin).get(0);
        dShift = shiftFactory.createDriverShifts(stockLocation1, LocalDateTime.now(), 2, 8, 1, extSysAdmin).get(0);
        shiftFactory.createShopperShifts(stockLocation2, LocalDateTime.now(), 2, 8, 1, extSysAdmin).get(0);
        shiftFactory.createDriverShifts(stockLocation2, LocalDateTime.now(), 2, 8, 1, extSysAdmin).get(0);

        List<Slot> slots1 = slotFactory.createLongerDeliverySlots(stockLocation1, extSysAdmin, 1, 5, LocalDateTime.now());
        slotFactory.createLongerDeliverySlots(stockLocation2, extSysAdmin, 1, 5, LocalDateTime.now());

        shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation1, slots1.get(2).getId(), 1, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");

    }

    private void setupStockLocations(Country countryID) {
        List<StockLocation> stockLocations1 = stockLocationFactory.createListOnSameCountry(1, extSysAdmin, countryID, StockLocation.Type.ORIGINAL, "001");
        setStockLocationConfiguration(stockLocations1);
        stockLocation1 = stockLocations1.get(0);

        List<StockLocation> stockLocations2 = stockLocationFactory.createListOnSameCountry(1, extSysAdmin, countryID, StockLocation.Type.ORIGINAL, "002");
        setStockLocationConfiguration(stockLocations2);
        stockLocation2 = stockLocations2.get(0);
    }

    private void setStockLocationConfiguration(List<StockLocation> stockLocations1) {
        stockLocations1.forEach(sl -> {
            sl.setExternalId(sl.getId() + 10);
            sl.setShoppingBatchNotifiedOffset(120);
            stockLocationFactory.save(sl);
        });
    }

    @Test
    public void test_fleetAlreadyClockOutFromShift_ClockInToOtherCluster_shouldNotGetAutoAssignment() throws Exception {
        agentHelper.clockInToShift(driver, stockLocation1, stockLocation1.getLat(), stockLocation1.getLon(), dShift);
        agentHelper.clockOut(driver); // user driver already clock-out from shift
        agentHelper.clockInToClockInDuration(driver, stockLocation2, stockLocation2.getLat(), stockLocation2.getLon(), 12); // user driver clock-in to non-shift, shouldn't get auto assignment

        TimeUnit.SECONDS.sleep(1);
        agentHelper.clockInToShift(driver2, stockLocation1, stockLocation1.getLat(), stockLocation1.getLon(), dShift); // user driver2 clock-in to shift, should get auto assignment

        slotHelper.assertSetRegularSlotV2(true, shipmentObj1, extSysAdmin);
        shipmentHelper.adjustShipment(shipmentObj1, extSysAdmin);
        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        TimeUnit.SECONDS.sleep(1);

        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            Assert.assertEquals(1, deliveryBatches.size());

            Batch deliveryBatch = deliveryBatches.get(0);
            Assert.assertTrue(deliveryBatch.isAutoAssigned());
            Assert.assertEquals(driver2.getId(), deliveryBatch.getUser().getId());
        });
    }

}
