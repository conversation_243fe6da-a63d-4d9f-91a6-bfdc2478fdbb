package com.happyfresh.fulfillment.integrationTest.test.slot;

import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.ShipmentJsonObjectFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.SlotFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.JobRepository;
import org.elasticsearch.common.geo.GeoPoint;
import org.hamcrest.Matchers;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

public class OneDayAvailabilityTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private BatchRepository batchRepository;

    private StockLocation stockLocation;

    private User user;

    @Before
    public void setUp() {
        user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user, Slot.Type.ONE_DAY);
        stockLocation = stockLocations.get(0);
    }

    @Test
    public void shouldReturnValidSlots() throws Exception {
        LocalDateTime now = LocalDateTime.now();
        int nowHour = now.getHour();

        stockLocation.setOpenAt(LocalTime.of(nowHour - 2, 00));
        stockLocation.setCloseAt(LocalTime.of(nowHour + 3, 00));
        stockLocationFactory.save(stockLocation);

        List<Slot> slots = slotFactory.createOneDaySlots(Lists.newArrayList(stockLocation), now.toLocalDate(), now.toLocalDate().plusDays(2), user, 2, 2);

        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots", Matchers.hasSize(3)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].start_time", Matchers.equalTo(LocalDateTime.of(LocalDate.now(),LocalTime.of(nowHour - 2, 00)).toString())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].end_time", Matchers.equalTo(LocalDateTime.of(LocalDate.now(),LocalTime.of(nowHour + 3, 00)).toString())))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    @Test
    public void shouldSuccessSetSlot() throws Exception {
        LocalDateTime now = LocalDateTime.now();
        List<Slot> slots = slotFactory.createOneDaySlots(Lists.newArrayList(stockLocation), now.toLocalDate(), now.toLocalDate(), user, 2, 2);

        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Assert.assertEquals(2, jobRepository.count());
        Assert.assertEquals(2, batchRepository.count());

        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-2", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Assert.assertEquals(4, jobRepository.count());
        Assert.assertEquals(4, batchRepository.count());

        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-3", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().isUnprocessableEntity());

        Assert.assertEquals(4, jobRepository.count());
        Assert.assertEquals(4, batchRepository.count());
    }

    @Test
    public void shouldSuccessChangeSlot() throws Exception {
        LocalDateTime now = LocalDateTime.now();
        List<Slot> slots = slotFactory.createOneDaySlots(Lists.newArrayList(stockLocation), now.toLocalDate(), now.toLocalDate().plusDays(2), user, 2, 2);

        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Assert.assertEquals(2, jobRepository.count());
        Assert.assertEquals(2, batchRepository.count());

        Batch beforeChangeSlotBatchShopping = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        Batch beforeChangeSlotBatchDelivery = batchRepository.findByType(Batch.Type.DELIVERY).get(0);

        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(1).getId(), 1, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Assert.assertEquals(2, jobRepository.count());
        Assert.assertEquals(2, batchRepository.count());

        Batch afterChangeSlotBatchShopping = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        Batch afterChangeSlotBatchDelivery = batchRepository.findByType(Batch.Type.DELIVERY).get(0);

        Assert.assertNotEquals(beforeChangeSlotBatchShopping.getStartTime(), afterChangeSlotBatchShopping.getStartTime());
        Assert.assertNotEquals(beforeChangeSlotBatchDelivery.getStartTime(), afterChangeSlotBatchDelivery.getStartTime());


    }
}
