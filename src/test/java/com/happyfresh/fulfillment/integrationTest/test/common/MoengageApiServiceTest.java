package com.happyfresh.fulfillment.integrationTest.test.common;

import com.happyfresh.fulfillment.common.service.MoengageAPIService;
import com.happyfresh.fulfillment.common.service.NotificationService;
import com.happyfresh.fulfillment.common.util.ApplicationUtil;
import com.happyfresh.fulfillment.entity.Role;
import com.happyfresh.fulfillment.entity.Tenant;
import com.happyfresh.fulfillment.entity.Tenant.NotificationProvider;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.repository.TenantRepository;
import org.assertj.core.util.Lists;
import org.hamcrest.Matchers;
import org.junit.Before;
import org.junit.Test;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import static org.springframework.test.web.client.match.MockRestRequestMatchers.*;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

public class MoengageApiServiceTest extends BaseTest {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private MoengageAPIService moengageAPIService;

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private MockServerHelper mockServerHelper;

    private User driver;
    private Tenant tenant;
    private MockRestServiceServer mockServer;

    @Override
    protected void setupElasticSearchBeforeEachTests() {
        elasticSearchSetupService.setPreFillSimpleRouteEdges(false);
    }

    @Before
    public void setup() {
        mockServer = mockServerHelper.buildMockServer();
        moengageAPIService.setRestTemplate(mockServerHelper.getRestTemplate());
        Whitebox.setInternalState(notificationService, "moengageAPIService", moengageAPIService);
    }

    @Test
    public void availableDeliveryBatch_shouldCorrectlyHitMoengageApi() {
        driver = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        tenant = driver.getTenant();

        Map<String, String> preferences = new HashMap<>();
        preferences.put("notification_provider", NotificationProvider.MOENGAGE.toString());
        preferences.put("moengage_app_id", "sampleId");
        preferences.put("moengage_api_secret", "sampleSecret");
        preferences.put("moengage_api_url", "http://localhost:3000");
        tenant.setPreferences(preferences);
        tenantRepository.save(tenant);

        Map<String, Long> summary = new TreeMap<>(); // sorted automatically
        summary.put("Stock Location 1", 1L);
        summary.put("Stock Location 2", 2L);
        summary.put("Stock Location 3", 5L);

        String expectedMessage = "1 pekerjaan tertunda pada Stock Location 1.\n2 pekerjaan tertunda pada Stock Location 2.\n5 pekerjaan tertunda pada Stock Location 3.\nTekan untuk membuka di aplikasi SND.";

        String url = tenant.getMoengageApiUrl() + ApplicationUtil.MOENGAGE_PUSH_API_PATH;
        mockServer.expect(ExpectedCount.once(), requestTo(url))
                .andExpect(jsonPath("$.appId", Matchers.equalTo("sampleId")))
                .andExpect(jsonPath("$.targetUserAttributes.attributeValue", Matchers.hasSize(1)))
                .andExpect(jsonPath("$.payload.ANDROID.message", Matchers.equalTo(expectedMessage)))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(""));

        notificationService.sendPushNotificationForAvailableDeliveryBatch(Lists.newArrayList(driver.getId()), summary, tenant.getId(), "ID");
        verifyWithRetries(mockServer, 400);
    }

    @Test
    public void availableDeliveryBatch_withoutMoengageApiKeyAndCampaignId_shouldNotHitMoengageApi() {
        driver = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        tenant = driver.getTenant();

        Map<String, String> preferences = new HashMap<>();
        preferences.put("notification_provider", NotificationProvider.MOENGAGE.toString());
        preferences.put("moengage_app_id", "");
        preferences.put("moengage_api_secret", "sampleSecret");
        preferences.put("moengage_api_url", "http://localhost:3000");
        tenant.setPreferences(preferences);
        tenantRepository.save(tenant);

        Map<String, Long> summary = new TreeMap<>(); // sorted automatically
        summary.put("Stock Location 1", 1L);
        summary.put("Stock Location 2", 2L);
        summary.put("Stock Location 3", 5L);
        String expectedMessage = "1 pekerjaan tertunda di Stock Location 1.\n2 pekerjaan tertunda di Stock Location 2.\n5 pekerjaan tertunda di Stock Location 3.\nKetuk untuk membuka di aplikasi SND.";

        String url = tenant.getMoengageApiUrl() + ApplicationUtil.MOENGAGE_PUSH_API_PATH;
        mockServer.expect(ExpectedCount.never(), requestTo(url))
                .andExpect(jsonPath("$.appId", Matchers.equalTo("sampleId")))
                .andExpect(jsonPath("$.targetUserAttributes.attributeValue", Matchers.hasSize(1)))
                .andExpect(jsonPath("$.payload.ANDROID.message", Matchers.equalTo(expectedMessage)))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(""));

        notificationService.sendPushNotificationForAvailableDeliveryBatch(Lists.newArrayList(driver.getId()), summary, tenant.getId(), "ID");
        verifyWithRetries(mockServer, 400);
    }

    private void verifyWithRetries(MockRestServiceServer mockServer, int timeoutMillis) {
        final long startTime = System.currentTimeMillis();
        while ((System.currentTimeMillis() - startTime) < timeoutMillis) {
            try {
                mockServer.verify();
                return;
            } catch (AssertionError e) {
                 //System.out.println(e.getMessage());
            }
        }
        throw new AssertionError("Timeout when verify");
    }
}
