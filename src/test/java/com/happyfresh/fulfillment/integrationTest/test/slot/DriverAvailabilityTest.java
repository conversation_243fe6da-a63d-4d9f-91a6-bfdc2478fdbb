package com.happyfresh.fulfillment.integrationTest.test.slot;

import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import org.elasticsearch.common.geo.GeoPoint;
import org.hamcrest.Matchers;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

public class DriverAvailabilityTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private CountryFactory countryFactory;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    private StockLocation stockLocation;

    private List<Slot> slots;

    private User user;

    @Before
    public void setUp() throws InterruptedException {
        Thread.sleep(1000);

        user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user);
        stockLocation = stockLocations.get(0);
        stockLocation.setOpenAt(LocalTime.of(10, 00));
        stockLocation.setCloseAt(LocalTime.of(13, 00));
        stockLocation.setShopperAveragePickingTimePerUniqItem(5);
        stockLocation.setType(StockLocation.Type.ORIGINAL);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocationFactory.save(stockLocation);

        LocalDate date = LocalDate.now().getDayOfWeek() == DayOfWeek.MONDAY ? LocalDate.now().plusDays(1) : LocalDate.now().plusWeeks(1).with(DayOfWeek.MONDAY);
        slots = slotFactory.createSlots(stockLocations, date, date, Slot.Type.ONE_HOUR, user, 1, 1);
    }

    @Test
    public void notPoolingWhenShipmentExceedsAirDistance() throws Exception {
        Country country = stockLocation.getState().getCountry();
        country.setAirDistanceThresholdConfiguration(1000.0);
        countryFactory.save(country);

        Slot slot = slots.get(0);

        String orderNumber1 = "Order-1";
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), 1, orderNumber1, new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        String orderNumber2 = "Order-2";
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), 1, orderNumber2, new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    @Test
    public void poolingWhenShipmentDoesNotExceedAirDistance() throws Exception {
        Country country = stockLocation.getState().getCountry();
        country.setAirDistanceThresholdConfiguration(5000.0);
        countryFactory.save(country);

        Slot slot = slots.get(0);

        String orderNumber1 = "Order-1";
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), 1, orderNumber1, new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        String orderNumber2 = "Order-2";
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), 1, orderNumber2, new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    @Test
    public void poolingWhenAirDistanceThresholdNotSet() throws Exception {
        Slot slot = slots.get(0);

        String orderNumber1 = "Order-1";
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), 1, orderNumber1, new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        String orderNumber2 = "Order-2";
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), 1, orderNumber2, new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    @Test
    public void notPoolingWhenShipmentExceedsMaxDeliveryVolume() throws Exception {
        stockLocation.setMaxDeliveryVolume(5);
        stockLocationFactory.save(stockLocation);

        Slot slot = slots.get(0);

        String orderNumber1 = "Order-1";
        JSONArray itemsArray = new JSONArray();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), 1, orderNumber1, new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        JSONObject itemObject = shipmentJsonObjectFactory.createItemObject(1, "sku-", 1, 1, 20, 20, 10);
        itemsArray.put(itemObject);
        shipmentObj.put("items", itemsArray);
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        String orderNumber2 = "Order-2";
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), 1, orderNumber2, new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        itemObject = shipmentJsonObjectFactory.createItemObject(1, "sku-", 1, 1, 20, 20, 10);
        itemsArray.put(itemObject);
        shipmentObj.put("items", itemsArray);
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    @Test
    public void poolingWhenShipmentDoesNotExceedMaxDeliveryVolume() throws Exception {
        Slot slot = slots.get(0);

        String orderNumber1 = "Order-1";
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), 1, orderNumber1, new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        String orderNumber2 = "Order-2";
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slot.getId(), 1, orderNumber2, new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }
}
