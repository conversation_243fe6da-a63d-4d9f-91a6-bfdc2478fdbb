package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.happyfresh.fulfillment.batch.service.BatchSndService;
import com.happyfresh.fulfillment.batch.service.DriverAutoAssignmentService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.JobRepository;
import com.happyfresh.fulfillment.repository.PaymentRepository;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import com.happyfresh.fulfillment.slot.presenter.SlotOptimizationEvent;
import org.hamcrest.Matchers;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;

public class BatchFailedTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private BatchSndService batchSndService;

    @Autowired
    private AgentFactory agentFactory;

    @Autowired
    private PaymentRepository paymentRepository;

    @MockBean
    private OrderService orderService;

    @Autowired
    private ClusterFactory clusterFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @SpyBean
    private DriverAutoAssignmentService driverAutoAssignmentService;

    @Test
    public void returnUnauthorizedIfNotOwner() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER);
        User driver = userFactory.createUserData(Role.Name.DRIVER, shopper.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        Batch batch = batchFactory.createBatch(driver, driver, slots.get(0), Batch.Type.DELIVERY);
        Shipment shipment = batch.getJobs().get(0).getShipment();

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/"+ batch.getId() +"/shipments/"+ shipment.getNumber() +"/fail_delivery")
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isUnauthorized());
    }

    @Test
    public void returnExceptionIfCurrentJobStateIsNotDelivering() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        List<Batch> batches = batchFactory.createBatches(1, driver, driver, slots.get(0));
        Batch shoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        shoppingBatches.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();
        Shipment shipment = deliveryBatch.getJobs().get(0).getShipment();

        batchSndService.pickup(deliveryBatch.getId());
        batchSndService.accept(shipment.getNumber());

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/"+ deliveryBatch.getId() +"/shipments/"+ shipment.getNumber() +"/fail_delivery")
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isUnprocessableEntity());
    }

    @Test
    public void return200IfSuccess() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        List<Batch> batches = batchFactory.createBatches(1, driver, driver, slots.get(0));
        Batch shoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        shoppingBatches.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();
        Shipment shipment = deliveryBatch.getJobs().get(0).getShipment();

        batchSndService.pickup(deliveryBatch.getId());
        batchSndService.accept(shipment.getNumber());
        batchSndService.deliver(shipment.getNumber());

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/"+ deliveryBatch.getId() +"/shipments/"+ shipment.getNumber() +"/fail_delivery")
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.state", Matchers.equalTo(Job.State.FAILED.toString())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.states[*].state", Matchers.containsInAnyOrder(Job.State.INITIAL.toString(), Job.State.STARTED.toString(), Job.State.ACCEPTED.toString(), Job.State.DELIVERING.toString(), Job.State.FAILED.toString())))
                .andExpect(MockMvcResultMatchers.status().isOk());

    }

    @Test
    public void shouldPublishFleetSyncMessageOnFailDelivery() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        agentFactory.createAgent(driver, stockLocations.get(0), Agent.State.WORKING);

        List<Batch> batches = batchFactory.createBatches(1, driver, driver, slots.get(0));
        Batch shoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        shoppingBatches.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();
        Shipment shipment = deliveryBatch.getJobs().get(0).getShipment();

        batchSndService.pickup(deliveryBatch.getId());
        batchSndService.accept(shipment.getNumber());
        batchSndService.deliver(shipment.getNumber());

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/"+ deliveryBatch.getId() +"/shipments/"+ shipment.getNumber() +"/fail_delivery")
                .content(requestBody.toString())
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()));

    }

    @Test
    public void return200OnCurrentJobStateFinalizingDelivery() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        List<Batch> batches = batchFactory.createBatches(1, driver, driver, slots.get(0));
        Batch shoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        shoppingBatches.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();
        Shipment shipment = deliveryBatch.getJobs().get(0).getShipment();

        batchSndService.pickup(deliveryBatch.getId());
        batchSndService.accept(shipment.getNumber());
        batchSndService.deliver(shipment.getNumber());
        batchSndService.arrive(shipment.getNumber());

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(7500.0);
        finalizingDelivery(deliveryBatch.getId(), shipment.getNumber(), driver);

        Payment payment = paymentRepository.findByShipmentId(shipment.getId());
        Assert.assertEquals(Payment.State.PENDING, payment.getState());
        Assert.assertNull(payment.getCash());
        Assert.assertNull(payment.getCashless());

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_UNCOLLECTED_PAYMENT.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/"+ deliveryBatch.getId() +"/shipments/"+ shipment.getNumber() +"/fail_delivery")
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.state", Matchers.equalTo(Job.State.FAILED.toString())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.states[*].state", Matchers.containsInAnyOrder(Job.State.INITIAL.toString(), Job.State.STARTED.toString(), Job.State.ACCEPTED.toString(), Job.State.DELIVERING.toString(), Job.State.FOUND_ADDRESS.toString(), Job.State.FINALIZING_DELIVERY.toString(), Job.State.FAILED.toString())));
    }

    private void finalizingDelivery(Long batchId, String shipmentNumber, User user) throws Exception {
        JSONArray itemsArray = new JSONArray();
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);
        String url = String.format("/api/v2/batches/%d/shipments/%s/finalize_delivery", batchId, shipmentNumber);
        mvc.perform(MockMvcRequestBuilders.put(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void shouldTriggerDriverAutoAssignment() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        User admin = userFactory.createUserData(Role.Name.ADMIN, driver.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.LONGER_DELIVERY, driver);
        List<Shift> driverShifts = shiftFactory.createDriverShifts(stockLocations.get(0), LocalDateTime.now(), 1, 8, 1, admin);
        Shift driverShift = driverShifts.get(0);

        Cluster cluster = stockLocations.get(0).getCluster();
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");
        clusterFactory.save(cluster);

        Shipment shipment1 = shipmentFactory.createShipment(slots.get(0), admin, "order-001", "order-001");

        Batch sBatch = batchFactory.createBatch(admin, List.of(shipment1), slots.get(0), Batch.Type.SHOPPING);
        sBatch.getJobs().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch dBatch = batchFactory.createBatch(admin, List.of(shipment1), slots.get(0), Batch.Type.DELIVERY);
        dBatch.setShift(driverShift);
        batchFactory.save(dBatch);

        batchSndService.pickup(dBatch.getId());
        batchSndService.accept(shipment1.getNumber());
        batchSndService.deliver(shipment1.getNumber());

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/"+ dBatch.getId() +"/shipments/"+ shipment1.getNumber() +"/fail_delivery")
                        .content(requestBody.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("X-Fulfillment-User-Token", driver.getToken())
                        .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.state", Matchers.equalTo(Job.State.FAILED.toString())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.states[*].state", Matchers.containsInAnyOrder(Job.State.INITIAL.toString(), Job.State.STARTED.toString(), Job.State.ACCEPTED.toString(), Job.State.DELIVERING.toString(), Job.State.FAILED.toString())))
                .andExpect(MockMvcResultMatchers.status().isOk());

        Mockito.verify(driverAutoAssignmentService).publishAutoAssignmentEvent(stockLocations.get(0).getCluster().getId().toString(),
                driverShift.getId().toString(), SlotOptimizationEvent.AutoAssignmentTriggerEvent.FAILED_DELIVERY);

    }

    @Test
    public void whenStillHasPendingShipment_shouldNotTriggerDriverAutoAssignment() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        User admin = userFactory.createUserData(Role.Name.ADMIN, driver.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.LONGER_DELIVERY, driver);
        List<Shift> driverShifts = shiftFactory.createDriverShifts(stockLocations.get(0), LocalDateTime.now(), 1, 8, 1, admin);
        Shift driverShift = driverShifts.get(0);

        Cluster cluster = stockLocations.get(0).getCluster();
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");
        clusterFactory.save(cluster);

        Shipment shipment1 = shipmentFactory.createShipment(slots.get(0), admin, "order-001", "order-001");
        Shipment shipment2 = shipmentFactory.createShipment(slots.get(0), admin, "order-002", "order-002");

        Batch sBatch = batchFactory.createBatch(admin, List.of(shipment1, shipment2), slots.get(0), Batch.Type.SHOPPING);
        sBatch.getJobs().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch dBatch = batchFactory.createBatch(admin, List.of(shipment1, shipment2), slots.get(0), Batch.Type.DELIVERY);
        dBatch.setShift(driverShift);
        batchFactory.save(dBatch);

        batchSndService.pickup(dBatch.getId());
        batchSndService.accept(shipment1.getNumber());
        batchSndService.deliver(shipment1.getNumber());

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/"+ dBatch.getId() +"/shipments/"+ shipment1.getNumber() +"/fail_delivery")
                        .content(requestBody.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("X-Fulfillment-User-Token", driver.getToken())
                        .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.state", Matchers.equalTo(Job.State.FAILED.toString())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.states[*].state", Matchers.containsInAnyOrder(Job.State.INITIAL.toString(), Job.State.STARTED.toString(), Job.State.ACCEPTED.toString(), Job.State.DELIVERING.toString(), Job.State.FAILED.toString())))
                .andExpect(MockMvcResultMatchers.status().isOk());

        Mockito.verify(driverAutoAssignmentService, Mockito.never()).publishAutoAssignmentEvent(stockLocations.get(0).getCluster().getId().toString(),
                driverShift.getId().toString(), SlotOptimizationEvent.AutoAssignmentTriggerEvent.FAILED_DELIVERY);

    }

    @Test
    public void ranger_shouldTriggerDriverAutoAssignment() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        User admin = userFactory.createUserData(Role.Name.ADMIN, driver.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.LONGER_DELIVERY, driver);
        List<Shift> driverShifts = shiftFactory.createDriverShifts(stockLocations.get(0), LocalDateTime.now(), 1, 8, 1, admin);
        Shift driverShift = driverShifts.get(0);

        Cluster cluster = stockLocations.get(0).getCluster();
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");
        clusterFactory.save(cluster);

        Shipment shipment1 = shipmentFactory.createShipment(slots.get(0), admin, "order-001", "order-001");

        Batch sBatch = batchFactory.createBatch(admin, List.of(shipment1), slots.get(0), Batch.Type.RANGER);
        sBatch.getJobs().forEach(job -> {
            job.setState(Job.State.STARTED);
            job.setState(Job.State.FINALIZING);
            job.setState(Job.State.FINISHED);
            job.setState(Job.State.ACCEPTED);
            jobRepository.save(job);
        });

        sBatch.setShift(driverShift);
        batchFactory.save(sBatch);

        batchSndService.deliver(shipment1.getNumber());

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/"+ sBatch.getId() +"/shipments/"+ shipment1.getNumber() +"/fail_delivery")
                        .content(requestBody.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("X-Fulfillment-User-Token", driver.getToken())
                        .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].ranger_job.state", Matchers.equalTo(Job.State.FAILED.toString())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].ranger_job.states[*].state", Matchers.containsInAnyOrder(Job.State.INITIAL.toString(), Job.State.STARTED.toString(), Job.State.FINALIZING.toString(), Job.State.ACCEPTED.toString(), Job.State.DELIVERING.toString(), Job.State.FAILED.toString())))
                .andExpect(MockMvcResultMatchers.status().isOk());

        Mockito.verify(driverAutoAssignmentService).publishAutoAssignmentEvent(stockLocations.get(0).getCluster().getId().toString(),
                driverShift.getId().toString(), SlotOptimizationEvent.AutoAssignmentTriggerEvent.FAILED_DELIVERY);

    }

    @Test
    public void ranger_stillHasOnGoingShipment_shouldTriggerDriverAutoAssignment() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        User admin = userFactory.createUserData(Role.Name.ADMIN, driver.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.LONGER_DELIVERY, driver);
        List<Shift> driverShifts = shiftFactory.createDriverShifts(stockLocations.get(0), LocalDateTime.now(), 1, 8, 1, admin);
        Shift driverShift = driverShifts.get(0);

        Cluster cluster = stockLocations.get(0).getCluster();
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");
        clusterFactory.save(cluster);

        Shipment shipment1 = shipmentFactory.createShipment(slots.get(0), admin, "order-001", "order-001");
        Shipment shipment2 = shipmentFactory.createShipment(slots.get(0), admin, "order-002", "order-002");

        Batch sBatch = batchFactory.createBatch(admin, List.of(shipment1, shipment2), slots.get(0), Batch.Type.RANGER);
        sBatch.getJobs().forEach(job -> {
            job.setState(Job.State.STARTED);
            job.setState(Job.State.FINALIZING);
            job.setState(Job.State.FINISHED);
            job.setState(Job.State.ACCEPTED);
            jobRepository.save(job);
        });

        sBatch.setShift(driverShift);
        batchFactory.save(sBatch);

        batchSndService.deliver(shipment1.getNumber());

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/"+ sBatch.getId() +"/shipments/"+ shipment1.getNumber() +"/fail_delivery")
                        .content(requestBody.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("X-Fulfillment-User-Token", driver.getToken())
                        .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].ranger_job.state", Matchers.equalTo(Job.State.FAILED.toString())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].ranger_job.states[*].state", Matchers.containsInAnyOrder(Job.State.INITIAL.toString(), Job.State.STARTED.toString(), Job.State.FINALIZING.toString(), Job.State.ACCEPTED.toString(), Job.State.DELIVERING.toString(), Job.State.FAILED.toString())))
                .andExpect(MockMvcResultMatchers.status().isOk());

        Mockito.verify(driverAutoAssignmentService, Mockito.never()).publishAutoAssignmentEvent(stockLocations.get(0).getCluster().getId().toString(),
                driverShift.getId().toString(), SlotOptimizationEvent.AutoAssignmentTriggerEvent.FAILED_DELIVERY);

    }

    @Test
    public void withShiftIsNull_shouldSetShiftIdToZeroBeforePublishAutoAssignmentEvent() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        User admin = userFactory.createUserData(Role.Name.ADMIN, driver.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.LONGER_DELIVERY, driver);
        List<Shift> driverShifts = shiftFactory.createDriverShifts(stockLocations.get(0), LocalDateTime.now(), 1, 8, 1, admin);
        Shift driverShift = driverShifts.get(0);

        Cluster cluster = stockLocations.get(0).getCluster();
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");
        clusterFactory.save(cluster);

        Shipment shipment1 = shipmentFactory.createShipment(slots.get(0), admin, "order-001", "order-001");

        Batch sBatch = batchFactory.createBatch(admin, List.of(shipment1), slots.get(0), Batch.Type.SHOPPING);
        sBatch.getJobs().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch dBatch = batchFactory.createBatch(admin, List.of(shipment1), slots.get(0), Batch.Type.DELIVERY);
        dBatch.setShift(null);
        batchFactory.save(dBatch);

        batchSndService.pickup(dBatch.getId());
        batchSndService.accept(shipment1.getNumber());
        batchSndService.deliver(shipment1.getNumber());

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/"+ dBatch.getId() +"/shipments/"+ shipment1.getNumber() +"/fail_delivery")
                        .content(requestBody.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("X-Fulfillment-User-Token", driver.getToken())
                        .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.state", Matchers.equalTo(Job.State.FAILED.toString())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.states[*].state", Matchers.containsInAnyOrder(Job.State.INITIAL.toString(), Job.State.STARTED.toString(), Job.State.ACCEPTED.toString(), Job.State.DELIVERING.toString(), Job.State.FAILED.toString())))
                .andExpect(MockMvcResultMatchers.status().isOk());

        Mockito.verify(driverAutoAssignmentService).publishAutoAssignmentEvent(Mockito.anyString(),
                Mockito.eq("0"), Mockito.eq(SlotOptimizationEvent.AutoAssignmentTriggerEvent.FAILED_DELIVERY));

    }
}
