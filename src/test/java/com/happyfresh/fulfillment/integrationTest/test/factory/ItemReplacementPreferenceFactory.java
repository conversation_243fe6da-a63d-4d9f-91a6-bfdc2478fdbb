package com.happyfresh.fulfillment.integrationTest.test.factory;

import com.happyfresh.fulfillment.entity.Item;
import com.happyfresh.fulfillment.entity.ItemReplacementPreference;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.repository.ItemReplacementPreferenceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Service
@Profile("it")
public class ItemReplacementPreferenceFactory {

    @Autowired
    private ItemReplacementPreferenceRepository repository;

    public ItemReplacementPreference save(ItemReplacementPreference preference) {
        return repository.save(preference);
    }

    public ItemReplacementPreference createReplacementPreference(Item item, User user) {
        ItemReplacementPreference preference = new ItemReplacementPreference();
        preference.setItem(item);
        preference.setVariantId(100L);
        preference.setSku("RPREFSKU-1");
        preference.setTranslationNames(nameUrlSample());
        preference.setTranslationDescriptions(nameUrlSample());
        preference.setImageUrl(nameUrlSample());
        preference.setCostPrice(BigDecimal.valueOf(20000.0));
        preference.setPrice(BigDecimal.valueOf(20000.0));
        preference.setNormalPrice(BigDecimal.valueOf(20000.0));
        preference.setNormalCostPrice(BigDecimal.valueOf(20000.0));
        preference.setUnitPrice(BigDecimal.valueOf(20000.0));
        preference.setSupermarketUnitCostPrice(BigDecimal.valueOf(20000.0));
        preference.setHeight(12.0);
        preference.setWidth(12.0);
        preference.setDepth(12.0);
        preference.setWeight(50.0);
        preference.setAverageWeight(50.0);
        preference.setUnit("600 ml");
        preference.setSupermarketUnit("ml");
        preference.setCurrency(item.getCurrency());
        preference.setMaximumOrderQuantity(50);
        preference.setMaximumPromoQuantity(null);
        preference.setConsideredAsAlcohol(false);
        preference.setConsideredAsGeRestrictedProduct(false);
        preference.setCreatedBy(user.getId());
        preference.setUpdatedBy(user.getId());
        preference.setTenant(user.getTenant());

        if (repository != null) save(preference);
        return preference;
    }

    private Map<String, String> nameUrlSample() {
        return new HashMap<String, String>(){{
            put("default", "default");
        }};
    }
}
