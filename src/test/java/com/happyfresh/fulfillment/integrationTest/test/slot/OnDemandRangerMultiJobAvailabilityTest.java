package com.happyfresh.fulfillment.integrationTest.test.slot;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.batch.service.JobSndService;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.service.NotificationService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.SetSlotHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.ItemRepository;
import com.happyfresh.fulfillment.repository.JobRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.shipment.form.ItemForm;
import com.happyfresh.fulfillment.shipment.service.CatalogService;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import com.happyfresh.fulfillment.shipment.service.PaymentService;
import com.happyfresh.fulfillment.shipment.service.ShipmentService;
import com.happyfresh.fulfillment.user.service.AgentClockInActivityService;
import org.apache.commons.io.IOUtils;
import org.elasticsearch.common.geo.GeoPoint;
import org.hamcrest.Matchers;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.io.File;
import java.io.FileInputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;

public class OnDemandRangerMultiJobAvailabilityTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private AgentFactory agentFactory;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private ItemFactory itemFactory;

    @Autowired
    private ItemRepository itemRepository;

    @Autowired
    private AgentClockInActivityService agentClockInActivityService;

    @Autowired
    private JobSndService jobSndService;

    @Autowired
    private SetSlotHelper helper;

    @MockBean
    private OrderService orderService;

    @MockBean
    private CatalogService catalogService;

    @MockBean
    private PaymentService paymentService;

    @Autowired
    private PaymentMockFactory paymentMockFactory;

    @Autowired
    private ShipmentService shipmentService;

    @MockBean
    private NotificationService notificationService;

    private StockLocation stockLocation;

    private List<Slot> slots;

    private User user;

    @Before
    public void setUp() throws InterruptedException {
        Thread.sleep(750);
        user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        LocalDateTime now = LocalDateTime.now();
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user);
        for (StockLocation stockLocation : stockLocations) {
            stockLocation.setOpenAt(now.toLocalTime().minusHours(1));
            stockLocation.setCloseAt(now.toLocalTime().plusHours(10));
            stockLocation.setShopperAveragePickingTimePerUniqItem(5);
            stockLocation.setType(StockLocation.Type.SPECIAL);
            stockLocation.setEnableOnDemandDelivery(true);
            stockLocation.setEnableOnDemandSnd(true);
            stockLocationFactory.save(stockLocation);
        }
        stockLocation = stockLocations.get(0);

        LocalDate tomorrow = LocalDateTime.now().plusDays(1).toLocalDate();
        slots = slotFactory.createSlots(stockLocations, tomorrow, tomorrow, Slot.Type.ONE_HOUR, user, 1, 1);
    }

    private Agent setupOneOnDemandAgent() {
        User onDemandRanger = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, user.getTenant());
        Agent agent = agentFactory.createAgent(onDemandRanger, stockLocation, Agent.State.WORKING);
        agent.setLat(stockLocation.getLat());
        agent.setLon(stockLocation.getLon());
        agentClockInActivityService.createAgentClockInActivity(agent, 8);
        return agent;
    }

    private void shopPayAndStartDelivery(Shipment shipment, User ranger) throws Exception {
        // Start job
        Job job = jobRepository.findAllByShipmentIdIn(Collections.singletonList(shipment.getId())).get(0);
        jobSndService.start(job);
        Batch batch = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND, ranger, Job.getInactiveJobStates()).get(0);

        shopAllItems(shipment, ranger, batch);
        payBatchShipments(shipment, ranger, batch);
        startDelivery(shipment, ranger, batch);
    }

    private void shopAllItems(Shipment shipment, User ranger, Batch batch) throws Exception {
        List<Item> items = itemRepository.findAllByShipmentId(shipment.getId());
        JSONArray itemsArray = new JSONArray();
        for (Item item : items) {
            item.setFoundQty(item.getRequestedQty());
            itemRepository.save(item);
            JSONObject item1Object = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
            item1Object.put("id", item.getId());
            item1Object.put("found_qty", item.getRequestedQty());
            itemsArray.put(item1Object);
        }
        finalizeShopping(shipment, ranger, batch, itemsArray);
    }

    private void finalizeShopping(Shipment shipment, User ranger, Batch batch, JSONArray itemsArray) throws Exception {
        finalizeShopping(shipment, ranger, batch, itemsArray, Lists.newArrayList());
    }

    private void finalizeShopping(Shipment shipment, User ranger, Batch batch, JSONArray itemsArray, List<String> skus ) throws Exception {
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        ObjectMapper mapper = new ObjectMapper();
        TypeFactory typeFactory = mapper.getTypeFactory();
        List<ItemForm> mockItems = mapper.readValue(requestBody.get("items").toString(), typeFactory.constructCollectionType(List.class, ItemForm.class));
        Mockito.when(catalogService.getItemsFromCatalogService(skus, stockLocation.getExternalId())).thenReturn(mockItems);
        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(7500.0); // Mock Spree call
        String url = String.format("/api/batches/%d/shipments/%s/finalize", batch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
            .header("X-Fulfillment-User-Token", ranger.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
            .content(requestBody.toString())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    private void payBatchShipments(Shipment shipment, User ranger, Batch batch) throws Exception {
        File file1 = new File("src/test/resources/fixtures/receipt1.jpg");
        FileInputStream input1 = new FileInputStream(file1);
        MockMultipartFile multipartFile1 = new MockMultipartFile("receipts[0].attachments[0]", file1.getName(), "image/jpeg", IOUtils.toByteArray(input1));
        input1.close();
        String url = String.format("/api/batches/%d/shipments/%s/pay", batch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.multipart(url)
            .file(multipartFile1)
            .param("receipts[0].number", "SomeNumber")
            .param("receipts[0].total", "10.53")
            .param("receipts[0].tax", "1.66")
            .header("X-Fulfillment-User-Token", ranger.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void startDelivery(Shipment shipment, User ranger, Batch batch) throws Exception {
        String url = String.format("/api/batches/%d/shipments/%s/deliver", batch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
            .header("X-Fulfillment-User-Token", ranger.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void shouldBeAvailable_inTheMiddleOfDelivering1stJob() throws Exception {
        User ranger1 = setupOneOnDemandAgent().getUser();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        final Shipment shipment = shipmentRepository.findByOrderNumber("Order-1");
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + shipment.getNumber() + "/adjust")
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(shipmentObj.toString()));

        shopPayAndStartDelivery(shipment, ranger1);

        // On demand set slot must be successful for Order-2
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        // But unavailable for 3rd order
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 10, "Order-3", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(false, shipmentObj, user);
    }

    @Test
    public void shouldBePushedToRangerWithLowestDeliveryTime() throws Exception {
        User ranger1 = setupOneOnDemandAgent().getUser();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1a", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);
        final Shipment shipment1 = shipmentRepository.findByOrderNumber("Order-1a");
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + shipment1.getNumber() + "/adjust")
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(shipmentObj.toString()));

        shopPayAndStartDelivery(shipment1, ranger1);

        User ranger2 = setupOneOnDemandAgent().getUser();
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1b", new GeoPoint(-6.2915547, 106.7977159), "Citos");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);
        final Shipment shipment2 = shipmentRepository.findByOrderNumber("Order-1b");
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + shipment2.getNumber() + "/adjust")
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(shipmentObj.toString()));

        shopPayAndStartDelivery(shipment2, ranger2);

        // On demand set slot must be successful for Order-2
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        // Verify that Order-2 is pushed only to agent2
        List<Batch> agent1OnDemandBatches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND, ranger1, Job.getInactiveJobStates());
        Assert.assertEquals(1, agent1OnDemandBatches.size());
        Assert.assertTrue(agent1OnDemandBatches.stream()
            .noneMatch(batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-2"))
        );
        List<Batch> agent2OnDemandBatches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND, ranger2, Job.getInactiveJobStates());
        Assert.assertEquals(2, agent2OnDemandBatches.size());
        Assert.assertTrue(agent2OnDemandBatches.stream()
            .anyMatch(batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-2"))
        );
    }

    @Test
    public void shouldConsiderBufferTime() throws Exception {
        setupOneOnDemandAgent();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        // Incoming order to be delivered to Menara FIF (10 minutes from store)
        // On demand slot must be available for Order-2
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);

        // Increase Buffer time
        Map<String, String> preferences = stockLocation.getPreferences();
        preferences.put("on_demand_buffer_time", "10");
        stockLocation.setPreferences(preferences);
        stockLocationFactory.save(stockLocation);
        helper.assertOnDemandSlotAvailability(false, shipmentObj, user);
    }

    @Test
    public void shouldOnlyAssignedToMaximum2Batch() throws Exception {
        // Setup agent1 located at Lintasarta.
        // Agent1 must go to store first on Talavera to do shopping and then deliver order to RS Fatmawati.
        Agent agent1 = setupOneOnDemandAgent();
        agent1.setLat(-6.291202);
        agent1.setLon(106.7855);
        agentFactory.save(agent1);
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        // Incoming order to be delivered to Menara FIF (10 minutes from store)
        // On demand slot must be available for Order-2
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        // Should not be available for 3rd shipment
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-3", new GeoPoint( -6.291202, 106.7855), "Lintasarta");
        helper.assertOnDemandSlotAvailability(false, shipmentObj, user);
        helper.assertSetOnDemandSlot(false, shipmentObj, user);
    }

    @Test
    public void shouldBeAvailable_whenRangerHasNotStartedShopping1stJob() throws Exception {
        // Setup agent1 located at Lintasarta.
        // Agent1 must go to store first on Talavera to do shopping and then deliver order to RS Fatmawati.
        Agent agent1 = setupOneOnDemandAgent();
        agent1.setLat(-6.291202);
        agent1.setLon(106.7855);
        agentFactory.save(agent1);
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1a", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        // Setup agent2 which already located at store Talavera.
        // Agent2 must deliver order to RS Fatmawati.
        Agent agent2 = setupOneOnDemandAgent();
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1b", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        // Incoming order to be delivered to Menara FIF (10 minutes from store)
        // On demand slot must be available for Order-2
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        // Verify that Order-2 is pushed to agent2
        List<Batch> agent1OnDemandBatches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND, agent1.getUser(), Job.getInactiveJobStates());
        Assert.assertEquals(1, agent1OnDemandBatches.size());
        Assert.assertTrue(agent1OnDemandBatches.stream()
            .noneMatch(batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-2"))
        );
        List<Batch> agent2OnDemandBatches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND, agent2.getUser(), Job.getInactiveJobStates());
        Assert.assertEquals(2, agent2OnDemandBatches.size());
        Assert.assertTrue(agent2OnDemandBatches.stream()
            .anyMatch(batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-2"))
        );
    }

    @Test
    public void shouldBeAvailable_inTheMiddleOfShopping1stJob() throws Exception {
        setupOneOnDemandAgent();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        // Hit "/adjust" endpoint to change shipment state to READY
        final Shipment shipment = shipmentRepository.findByOrderNumber("Order-1");
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + shipment.getNumber() + "/adjust")
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(shipmentObj.toString()));

        // Verify shopping has started and item has been picked
        transactionHelper.withNewTransaction(() -> {
            List<Item> items = itemRepository.findAllByShipmentId(shipment.getId());
            Assert.assertTrue(items.stream().allMatch(item -> item.getFoundQty() == 0));
            Assert.assertEquals(5, items.size());
            // Start job
            Job job = jobRepository.findAllByShipmentIdIn(Collections.singletonList(shipment.getId())).get(0);
            jobSndService.start(job);
            // Pick 2 items
            Item item1 = items.get(0);
            item1.setFoundQty(item1.getRequestedQty());
            itemRepository.save(item1);
            Item item2 = items.get(1);
            item2.setFoundQty(item2.getRequestedQty());
            itemRepository.save(item2);
            Assert.assertEquals(2, itemRepository.countFoundOrOosByShipment(shipment));
        });

        // On demand slot must be available for Order-2
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        // But unavailable when shipment has big basket size
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 10, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(false, shipmentObj, user);
    }

    @Test
    public void shouldBePushedToRangerWithLeastRemainingItem() throws Exception {
        User ranger1 = setupOneOnDemandAgent().getUser();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 6, "Order-1a", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);
        // Hit "/adjust" endpoint to change shipment state to READY
        final Shipment shipment1a = shipmentRepository.findByOrderNumber("Order-1a");
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + shipment1a.getNumber() + "/adjust")
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(shipmentObj.toString()));

        User ranger2 = setupOneOnDemandAgent().getUser();
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 6, "Order-1b", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);
        // Hit "/adjust" endpoint to change shipment state to READY
        final Shipment shipment1b = shipmentRepository.findByOrderNumber("Order-1b");
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + shipment1b.getNumber() + "/adjust")
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(shipmentObj.toString()));

        // Verify agent2 has picked 2 items
        transactionHelper.withNewTransaction(() -> {
            List<Item> items = itemRepository.findAllByShipmentId(shipment1b.getId());
            Assert.assertTrue(items.stream().allMatch(item -> item.getFoundQty() == 0));
            Assert.assertEquals(6, items.size());
            // Start job
            Job job = jobRepository.findAllByShipmentIdIn(Collections.singletonList(shipment1b.getId())).get(0);
            jobSndService.start(job);
            // Pick 2 items
            Item item1 = items.get(0);
            item1.setFoundQty(item1.getRequestedQty());
            itemRepository.save(item1);
            Item item2 = items.get(1);
            item2.setFoundQty(item2.getRequestedQty());
            itemRepository.save(item2);
            Assert.assertEquals(2, itemRepository.countFoundOrOosByShipment(shipment1b));
        });

        // On demand slot must be available for Order-2
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        // Verify that Order-2 is pushed only to agent2
        List<Batch> agent1OnDemandBatches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND, ranger1, Job.getInactiveJobStates());
        Assert.assertEquals(1, agent1OnDemandBatches.size());
        Assert.assertTrue(agent1OnDemandBatches.stream()
            .noneMatch(batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-2"))
        );
        List<Batch> agent2OnDemandBatches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND, ranger2, Job.getInactiveJobStates());
        Assert.assertEquals(2, agent2OnDemandBatches.size());
        Assert.assertTrue(agent2OnDemandBatches.stream()
            .anyMatch(batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-2"))
        );
    }

    @Test
    public void shouldStartSecondShipmentWhenFirstShipmentFailDelivery() throws Exception {
        User ranger1 = setupOneOnDemandAgent().getUser();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        final Shipment firstShipment = shipmentRepository.findByOrderNumber("Order-1");
        List<Batch> batches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND, ranger1, Job.getInactiveJobStates());

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + firstShipment.getNumber() + "/adjust")
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(shipmentObj.toString()));

        shopPayAndStartDelivery(firstShipment,ranger1);

        // On demand set slot must be successful for Order-2
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        final Shipment secondShipment = shipmentRepository.findByOrderNumber("Order-2");

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + secondShipment.getNumber() + "/adjust")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()));


        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/"+ batches.get(0).getId() +"/shipments/"+ firstShipment.getNumber() +"/fail_delivery")
            .content(requestBody.toString())
            .contentType(MediaType.APPLICATION_JSON)
            .header("X-Fulfillment-User-Token", ranger1.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger1.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isOk());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandBatches = batchRepository.findByType(Batch.Type.ON_DEMAND);
            Job firstOnDemandJob = onDemandBatches.stream().filter( batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-1")).findFirst().get().getJobs().get(0);
            Job secondOnDemandJob = onDemandBatches.stream().filter( batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-2")).findFirst().get().getJobs().get(0);
            Assert.assertEquals(Job.State.FAILED, firstOnDemandJob.getState());
            Assert.assertEquals(Job.State.STARTED, secondOnDemandJob.getState());

        });
    }

    @Test
    public void shouldStartSecondShipmentWhenFirstShipmentCancelled() throws Exception {
        User ranger1 = setupOneOnDemandAgent().getUser();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        final Shipment firstShipment = shipmentRepository.findByOrderNumber("Order-1");
        List<Batch> batches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND, ranger1, Job.getInactiveJobStates());

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + firstShipment.getNumber() + "/adjust")
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(shipmentObj.toString()));

        shopPayAndStartDelivery(firstShipment,ranger1);

        // On demand set slot must be successful for Order-2
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        final Shipment secondShipment = shipmentRepository.findByOrderNumber("Order-2");
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + secondShipment.getNumber() + "/adjust")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()));

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/"+ firstShipment.getNumber() +"/cancel")
            .content(requestBody.toString())
            .contentType(MediaType.APPLICATION_JSON)
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isOk());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandBatches = batchRepository.findByType(Batch.Type.ON_DEMAND);
            Job firstOnDemandJob = onDemandBatches.stream().filter( batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-1")).findFirst().get().getJobs().get(0);
            Job secondOnDemandJob = onDemandBatches.stream().filter( batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-2")).findFirst().get().getJobs().get(0);
            Assert.assertEquals(Job.State.CANCELLED, firstOnDemandJob.getState());
            Assert.assertEquals(Job.State.STARTED, secondOnDemandJob.getState());

        });
    }

    @Test
    public void shouldNotTouchFirstShipmentWhenSecondShipmentCancelled() throws Exception {
        User ranger1 = setupOneOnDemandAgent().getUser();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        final Shipment firstShipment = shipmentRepository.findByOrderNumber("Order-1");
        List<Batch> batches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND, ranger1, Job.getInactiveJobStates());

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + firstShipment.getNumber() + "/adjust")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()));

        shopPayAndStartDelivery(firstShipment,ranger1);

        // On demand set slot must be successful for Order-2
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        final Shipment secondShipment = shipmentRepository.findByOrderNumber("Order-2");

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/"+ secondShipment.getNumber() +"/cancel")
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isOk());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandBatches = batchRepository.findByType(Batch.Type.ON_DEMAND);
            Job firstOnDemandJob = onDemandBatches.stream().filter( batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-1")).findFirst().get().getJobs().get(0);
            Assert.assertEquals(Job.State.DELIVERING, firstOnDemandJob.getState());

        });
    }

    @Test
    public void shouldStartSecondShipmentWhenFirstShipmentDestroyed() throws Exception {
        User ranger1 = setupOneOnDemandAgent().getUser();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        // On demand set slot must be successful for Order-2
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);
        final Shipment secondShipment = shipmentRepository.findByOrderNumber("Order-2");

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + secondShipment.getNumber() + "/adjust")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()));
        transactionHelper.withNewTransaction(() -> {
            final Shipment firstShipment = shipmentRepository.findByOrderNumber("Order-1");
            shipmentService.destroy(firstShipment);
        });
        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandBatches = batchRepository.findByType(Batch.Type.ON_DEMAND);
            Job secondOnDemandJob = onDemandBatches.stream().filter( batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-2")).findFirst().get().getJobs().get(0);
            Assert.assertEquals(Job.State.STARTED, secondOnDemandJob.getState());
        });
    }

    @Test
    public void shouldStartSecondShipmentWhenCompleteEarlierThanFirstShipment() throws Exception {
        User ranger1 = setupOneOnDemandAgent().getUser();
        JSONObject shipmentObj1 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj1, user);
        final Shipment firstShipment = shipmentRepository.findByOrderNumber("Order-1");

        // On demand set slot must be successful for Order-2
        JSONObject shipmentObj2 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj2, user);
        helper.assertSetOnDemandSlot(true, shipmentObj2, user);
        final Shipment secondShipment = shipmentRepository.findByOrderNumber("Order-2");

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + secondShipment.getNumber() + "/adjust")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj2.toString()));

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandBatches = batchRepository.findByType(Batch.Type.ON_DEMAND);
            Job secondOnDemandJob = onDemandBatches.stream().filter( batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-2")).findFirst().get().getJobs().get(0);
            Assert.assertEquals(Job.State.STARTED, secondOnDemandJob.getState());
        });

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + firstShipment.getNumber() + "/adjust")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj1.toString()));

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandBatches = batchRepository.findByType(Batch.Type.ON_DEMAND);
            Job firstOnDemandJob = onDemandBatches.stream().filter( batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-1")).findFirst().get().getJobs().get(0);
            Assert.assertEquals(Job.State.INITIAL, firstOnDemandJob.getState());
        });
    }

    @Test
    public void withActiveOnDemandDeliveryJob() throws Exception {
        stockLocation.setType(StockLocation.Type.ORIGINAL);
        // Nonshopping time = 36', 9 or 7 items @ 8' will fail, 5 items will succeed
        stockLocation.setShopperAveragePickingTimePerUniqItem(8);
        stockLocationFactory.save(stockLocation);
        User shopper = setupShopper();
        User onDemandRanger = setupOnDemandRanger();
        List<Slot> slots = slotFactory.createSlots(Collections.singletonList(stockLocation), LocalDate.now(), LocalDate.now(), Slot.Type.ONE_HOUR, shopper, 1, 1);
        Batch activePreOnDemandShoppingBatch = batchFactory.createBatch(shopper, shopper, slots.get(0), Batch.Type.SHOPPING);
        Batch activeOnDemandShoppingBatch = batchFactory.createBatch(shopper, shopper, slots.get(0), Batch.Type.ON_DEMAND_SHOPPING);
        Shipment onDemandSnDShipment = activeOnDemandShoppingBatch.getJobs().get(0).getShipment();
        Batch activeOnDemandDeliveryBatch = batchFactory.createBatch(onDemandRanger, onDemandRanger, Collections.singletonList(onDemandSnDShipment), slots.get(0), Batch.Type.ON_DEMAND_DELIVERY);

        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");

        // Requested=5 + 2 * ActiveBatches=2 = 9 items
        helper.assertOnDemandSlotAvailability(false, shipmentObj, user);

        // Requested=5 + 1 * ActiveBatches=2 = 7 items
        markItemsFound(activePreOnDemandShoppingBatch);
        helper.assertOnDemandSlotAvailability(false, shipmentObj, user);

        // Requested=5 + 0 * ActiveBatches=2 = 5 items
        markItemsFound(activeOnDemandShoppingBatch);
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
    }

    private User setupShopper() {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, user.getTenant());
        Agent agent = agentFactory.createAgent(shopper, stockLocation, Agent.State.WORKING);
        agent.setLat(stockLocation.getLat());
        agent.setLon(stockLocation.getLon());
        agentClockInActivityService.createAgentClockInActivity(agent, 8);
        return shopper;
    }

    private User setupOnDemandRanger() {
        User ranger = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, user.getTenant());
        Agent agent = agentFactory.createAgent(ranger, stockLocation, Agent.State.WORKING);
        agent.setLat(stockLocation.getLat());
        agent.setLon(stockLocation.getLon());
        agentClockInActivityService.createAgentClockInActivity(agent, 8);
        return ranger;
    }

    private void markItemsFound(Batch batch) {
        transactionHelper.withNewTransaction(() -> {
            List<Shipment> shipments = shipmentRepository.fetchAllByBatchIdFetchItems(batch.getId());
            for (Shipment s : shipments) {
                for (Item i : s.getItems()) {
                    i.setFoundQty(i.getRequestedQty());
                    itemRepository.save(i); }}});
    }

    @Test
    public void shouldNotCountReplacementItemsInShoppingDuration() throws Exception {
        User ranger1 = setupOneOnDemandAgent().getUser();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        final Shipment shipment = shipmentRepository.findByOrderNumber("Order-1");
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + shipment.getNumber() + "/adjust")
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(shipmentObj.toString()));

        shopWithReplacePayAndStartDelivery(shipment, ranger1);

        // Set stock location average picking time to fail for counting replacement items but succeed otherwise
        stockLocation.setShopperAveragePickingTimePerUniqItem(6);
        stockLocationFactory.save(stockLocation);

        // On demand set slot must be successful for Order-2
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);
    }

    private void shopWithReplacePayAndStartDelivery(Shipment shipment, User ranger) throws Exception {
        // Start job
        Job job = jobRepository.findAllByShipmentIdIn(Collections.singletonList(shipment.getId())).get(0);
        jobSndService.start(job);
        Batch batch = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND, ranger, Job.getInactiveJobStates()).get(0);

        shopAllItemsWithReplacement(shipment, ranger, batch);
        payBatchShipments(shipment, ranger, batch);
        startDelivery(shipment, ranger, batch);
    }

    private void shopAllItemsWithReplacement(Shipment shipment, User ranger, Batch batch) throws Exception {
        List<String> skus = Lists.newArrayList();
        List<Item> items = itemRepository.findAllByShipmentId(shipment.getId());
        JSONArray itemsArray = new JSONArray();
        for (Item item : items) {
            item.setFoundQty(item.getRequestedQty() - 1);
            itemRepository.save(item);
            JSONObject item1Object = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
            item1Object.put("id", item.getId());
            item1Object.put("found_qty", item.getRequestedQty() - 1);
            item1Object.put("oos_qty", 1);

            String replacementItemSku = "8999999390419-ID";
            JSONObject replacementObj = shipmentJsonObjectFactory.createItemObject(111, replacementItemSku, 1);
            replacementObj.put("found_qty", 1);
            replacementObj.put("replaced_item_id", item.getId());

            skus.add(replacementObj.get("sku").toString());

            itemsArray.put(item1Object);
            itemsArray.put(replacementObj);
        }

        finalizeShopping(shipment, ranger, batch, itemsArray, skus);
    }

    @Test
    public void shouldStartSecondShipmentWhenFirstOnDemandSNDFailDelivery() throws Exception {
        User ranger1 = setupOneOnDemandAgent().getUser();
        User shopper = setupShopper();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandShoppingBatches = batchRepository.findByType(Batch.Type.ON_DEMAND_SHOPPING);
            List<Batch> onDemandDeliveryBatches = batchRepository.findByType(Batch.Type.ON_DEMAND_DELIVERY);
            Assert.assertThat(onDemandShoppingBatches, Matchers.hasSize(1));
            Assert.assertThat(onDemandDeliveryBatches, Matchers.hasSize(1));
        });

        final Shipment firstShipment = shipmentRepository.findByOrderNumber("Order-1");
        List<Batch> batches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND_DELIVERY, ranger1, Job.getInactiveJobStates());

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + firstShipment.getNumber() + "/adjust")
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(shipmentObj.toString()));

        shopperDriverShopPayAndStartDelivery(firstShipment, ranger1, shopper);

        // On demand set slot must be successful for Order-2
        clockOutShopper(shopper);
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + "Order-2" + "/adjust")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()));

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandRangerBatches = batchRepository.findByType(Batch.Type.ON_DEMAND);
            Assert.assertThat(onDemandRangerBatches, Matchers.hasSize(1));
        });

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/"+ batches.get(0).getId() +"/shipments/"+ firstShipment.getNumber() +"/fail_delivery")
            .content(requestBody.toString())
            .contentType(MediaType.APPLICATION_JSON)
            .header("X-Fulfillment-User-Token", ranger1.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger1.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isOk());

        transactionHelper.withNewTransaction(() -> {
            Job firstOnDemandDeliveryJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_DRIVER).get(0);
            Job secondOnDemandRangerJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_RANGER).get(0);
            Assert.assertEquals(Job.State.FAILED, firstOnDemandDeliveryJob.getState());
            Assert.assertEquals(Job.State.STARTED, secondOnDemandRangerJob.getState());

        });
    }

    @Test
    public void shouldStartSecondShipmentWhenFirstODShopperAndDriverCancelled() throws Exception {
        User ranger1 = setupOneOnDemandAgent().getUser();
        User shopper = setupShopper();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandShoppingBatches = batchRepository.findByType(Batch.Type.ON_DEMAND_SHOPPING);
            List<Batch> onDemandDeliveryBatches = batchRepository.findByType(Batch.Type.ON_DEMAND_DELIVERY);
            Assert.assertThat(onDemandShoppingBatches, Matchers.hasSize(1));
            Assert.assertThat(onDemandDeliveryBatches, Matchers.hasSize(1));
        });

        final Shipment firstShipment = shipmentRepository.findByOrderNumber("Order-1");
        List<Batch> batches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND_DELIVERY, ranger1, Job.getInactiveJobStates());

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + firstShipment.getNumber() + "/adjust")
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(shipmentObj.toString()));

        shopperDriverShopPayAndStartDelivery(firstShipment, ranger1, shopper);

        // On demand set slot must be successful for Order-2
        clockOutShopper(shopper);
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + "Order-2" + "/adjust")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()));

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandRangerBatches = batchRepository.findByType(Batch.Type.ON_DEMAND);
            Assert.assertThat(onDemandRangerBatches, Matchers.hasSize(1));
        });

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/"+ firstShipment.getNumber() +"/cancel")
            .content(requestBody.toString())
            .contentType(MediaType.APPLICATION_JSON)
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isOk());

        transactionHelper.withNewTransaction(() -> {
            Job firstOnDemandDeliveryJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_DRIVER).get(0);
            Job secondOnDemandRangerJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_RANGER).get(0);
            Assert.assertEquals(Job.State.CANCELLED, firstOnDemandDeliveryJob.getState());
            Assert.assertEquals(Job.State.STARTED, secondOnDemandRangerJob.getState());

        });
    }

    @Test
    public void shouldStartSecondShipmentWhenFinishDelivery() throws Exception {
        User ranger1 = setupOneOnDemandAgent().getUser();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        final Shipment firstShipment = shipmentRepository.findByOrderNumber("Order-1");
        List<Batch> batches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND, ranger1, Job.getInactiveJobStates());

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + firstShipment.getNumber() + "/adjust")
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(shipmentObj.toString()));

        shopPayAndStartDelivery(firstShipment, ranger1);

        // On demand set slot must be successful for Order-2
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + "Order-2" + "/adjust")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()));

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandRangerBatches = batchRepository.findByType(Batch.Type.ON_DEMAND);
            Assert.assertThat(onDemandRangerBatches, Matchers.hasSize(2));
        });

        finishDelivery(firstShipment, ranger1, batches.get(0));

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandBatches = batchRepository.findByType(Batch.Type.ON_DEMAND);
            Job firstOnDemandJob = onDemandBatches.stream().filter( batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-1")).findFirst().get().getJobs().get(0);
            Job secondOnDemandJob = onDemandBatches.stream().filter( batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-2")).findFirst().get().getJobs().get(0);
            Assert.assertEquals(Job.State.FINISHED, firstOnDemandJob.getState());
            Assert.assertEquals(Job.State.STARTED, secondOnDemandJob.getState());
        });

        Mockito.verify(notificationService, Mockito.times(2)).sendPushNotificationForNewOnDemandJob(anyLong(), anyString(), anyString(), anyString(), anyLong());
    }

    @Test
    public void shouldNotStartDeliveryJobForSecondShipmentWhenFailDelivery() throws Exception {
        User ranger1 = setupOneOnDemandAgent().getUser();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandRangerBatches = batchRepository.findByType(Batch.Type.ON_DEMAND);
            Assert.assertThat(onDemandRangerBatches, Matchers.hasSize(1));
        });

        final Shipment firstShipment = shipmentRepository.findByOrderNumber("Order-1");
        List<Batch> batches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND, ranger1, Job.getInactiveJobStates());

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + firstShipment.getNumber() + "/adjust")
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(shipmentObj.toString()));

        shopPayAndStartDelivery(firstShipment, ranger1);
//        shopperDriverShopPayAndStartDelivery(firstShipment, ranger1, shopper);

        // On demand set slot must be successful for Order-2
//        clockOutShopper(shopper);
        setupShopper();
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandShoppingBatches = batchRepository.findByType(Batch.Type.ON_DEMAND_SHOPPING);
            List<Batch> onDemandDeliveryBatches = batchRepository.findByType(Batch.Type.ON_DEMAND_DELIVERY);
            Assert.assertThat(onDemandShoppingBatches, Matchers.hasSize(1));
            Assert.assertThat(onDemandDeliveryBatches, Matchers.hasSize(1));
        });

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/"+ batches.get(0).getId() +"/shipments/"+ firstShipment.getNumber() +"/fail_delivery")
            .content(requestBody.toString())
            .contentType(MediaType.APPLICATION_JSON)
            .header("X-Fulfillment-User-Token", ranger1.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger1.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isOk());

        transactionHelper.withNewTransaction(() -> {
            Job firstOnDemandDeliveryJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_RANGER).get(0);
            Job secondOnDemandRangerJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_DRIVER).get(0);
            Assert.assertEquals(Job.State.FAILED, firstOnDemandDeliveryJob.getState());
            Assert.assertEquals(Job.State.INITIAL, secondOnDemandRangerJob.getState());

        });
    }

    @Test
    public void shouldNotStartDeliveryJobForSecondShipmentWhenCancelled() throws Exception {
        User ranger1 = setupOneOnDemandAgent().getUser();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandRangerBatches = batchRepository.findByType(Batch.Type.ON_DEMAND);
            Assert.assertThat(onDemandRangerBatches, Matchers.hasSize(1));
        });

        final Shipment firstShipment = shipmentRepository.findByOrderNumber("Order-1");
        List<Batch> batches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND, ranger1, Job.getInactiveJobStates());

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + firstShipment.getNumber() + "/adjust")
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(shipmentObj.toString()));

        shopPayAndStartDelivery(firstShipment, ranger1);

        // On demand set slot must be successful for Order-2
        setupShopper();
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandShoppingBatches = batchRepository.findByType(Batch.Type.ON_DEMAND_SHOPPING);
            List<Batch> onDemandDeliveryBatches = batchRepository.findByType(Batch.Type.ON_DEMAND_DELIVERY);
            Assert.assertThat(onDemandShoppingBatches, Matchers.hasSize(1));
            Assert.assertThat(onDemandDeliveryBatches, Matchers.hasSize(1));
        });

        JSONObject requestBody = new JSONObject();
        requestBody.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/"+ firstShipment.getNumber() +"/cancel")
            .content(requestBody.toString())
            .contentType(MediaType.APPLICATION_JSON)
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isOk());

        transactionHelper.withNewTransaction(() -> {
            Job firstOnDemandDeliveryJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_RANGER).get(0);
            Job secondOnDemandRangerJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_DRIVER).get(0);
            Assert.assertEquals(Job.State.CANCELLED, firstOnDemandDeliveryJob.getState());
            Assert.assertEquals(Job.State.INITIAL, secondOnDemandRangerJob.getState());

        });
    }

    @Test
    public void shouldNotStartDeliveryJobForSecondShipmentIfStillPending() throws Exception {
        User ranger1 = setupOneOnDemandAgent().getUser();
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandRangerBatches = batchRepository.findByType(Batch.Type.ON_DEMAND);
            Assert.assertThat(onDemandRangerBatches, Matchers.hasSize(1));
        });

        final Shipment firstShipment = shipmentRepository.findByOrderNumber("Order-1");
        List<Batch> batches = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND, ranger1, Job.getInactiveJobStates());

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + firstShipment.getNumber() + "/adjust")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()));

        shopPayAndStartDelivery(firstShipment, ranger1);

        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 5, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        helper.assertOnDemandSlotAvailability(true, shipmentObj, user);
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        finishDelivery(firstShipment, ranger1, batches.get(0));

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandBatches = batchRepository.findByType(Batch.Type.ON_DEMAND);
            Assert.assertThat(onDemandBatches, Matchers.hasSize(2));
            Job firstOnDemandJob = onDemandBatches.stream().filter( batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-1")).findFirst().get().getJobs().get(0);
            Job secondOnDemandJob = onDemandBatches.stream().filter( batch -> batch.getJobs().get(0).getShipment().getOrderNumber().equals("Order-2")).findFirst().get().getJobs().get(0);
            Assert.assertEquals(Job.State.FINISHED, firstOnDemandJob.getState());
            Assert.assertEquals(Job.State.INITIAL, secondOnDemandJob.getState());
        });
    }

    private void shopperDriverShopPayAndStartDelivery(Shipment shipment, User ranger, User shopper) throws Exception {
        Batch deliveryBatch = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND_DELIVERY, ranger, Job.getInactiveJobStates()).get(0);
        Batch shoppingBatch = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND_SHOPPING, shopper, Job.getInactiveJobStates()).get(0);

        shopAllItemsWithReplacement(shipment, shopper, shoppingBatch);
        payBatchShipments(shipment, shopper, shoppingBatch);
        pickupDelivery(ranger, deliveryBatch);
        acceptDelivery(shipment, ranger, deliveryBatch);
        startDelivery(shipment, ranger, deliveryBatch);
    }

    private void acceptDelivery(Shipment shipment, User ranger, Batch batch) throws Exception {
        String url = String.format("/api/batches/%d/shipments/%s/accept", batch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
            .header("X-Fulfillment-User-Token", ranger.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void pickupDelivery(User ranger, Batch batch) throws Exception {
        String url = String.format("/api/batches/%d/pickup", batch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
            .header("X-Fulfillment-User-Token", ranger.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    private void clockOutShopper(User shopper) throws Exception {
        mvc.perform(MockMvcRequestBuilders.put("/api/users/me/agent/clock_out")
            .header("X-Fulfillment-User-Token", shopper.getToken())
            .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    private void finishDelivery(Shipment shipment, User ranger, Batch batch) throws Exception{
        String url = String.format("/api/batches/%d/shipments/%s/arrive", batch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
            .header("X-Fulfillment-User-Token", ranger.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isOk());

        JSONObject payload = new JSONObject();
        payload.put("items", new JSONArray());
        url = String.format("/api/v2/batches/%d/shipments/%s/finalize_delivery", batch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.put(url)
            .header("X-Fulfillment-User-Token", ranger.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(payload.toString()))
            .andExpect(MockMvcResultMatchers.status().isOk());

            JsonNode response = paymentMockFactory.createPayment(null, shipment.getOrderTotal(), true);
            Mockito.when(paymentService.capturePayment(shipment.getNumber(), "127.0.0.1", false)).thenReturn(ResponseEntity.ok(response.toString()));
            url = String.format("/api/v2/batches/%d/shipments/%s/capture", batch.getId(), shipment.getNumber());

            mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", ranger.getToken())
                .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk());

            File file = new File("src/test/resources/fixtures/signature.jpg");
            FileInputStream input1 = new FileInputStream(file);
            MockMultipartFile multipartFile = new MockMultipartFile("attachment", file.getName(), "image/jpeg", IOUtils.toByteArray(input1));
            input1.close();

            url = String.format("/api/batches/%d/shipments/%s/finish", batch.getId(), shipment.getNumber());
            mvc.perform(MockMvcRequestBuilders.multipart(url)
                .file(multipartFile)
                .param("receiver", "Robert")
                .param("cash_amount", "7500.0")
                .param("lat", "-6.2047739")
                .param("lon", "106.7100399")
                .param("age_consent", "true")
                .header("X-Fulfillment-User-Token", ranger.getToken())
                .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }
}
