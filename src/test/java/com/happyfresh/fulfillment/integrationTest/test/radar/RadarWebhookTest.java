package com.happyfresh.fulfillment.integrationTest.test.radar;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaMessage;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaTopicConfig;
import com.happyfresh.fulfillment.common.service.RadarService;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import org.jobrunr.jobs.lambdas.JobLambda;
import org.jobrunr.scheduling.JobScheduler;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import static java.nio.file.Files.readAllBytes;
import static java.nio.file.Paths.get;
import static org.mockito.ArgumentMatchers.*;

public class RadarWebhookTest extends BaseTest {

    @Autowired
    private RadarService radarService;

    @MockBean
    KafkaMessage kafkaMessage;

    @MockBean
    JobScheduler jobScheduler;

    @Captor
    private ArgumentCaptor<ImmutableMap<String, Object>> segmentTrackingPropertiesCaptor;

    @Autowired
    ObjectMapper mapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this.getClass());
    }

    protected void setupElasticSearchBeforeEachTests() {
        elasticSearchSetupService.setPreFillSimpleRouteEdges(false);
    }

    @Test
    public void webhook_withValidSignHeader() throws Exception {
        String body = new String(readAllBytes(get("src", "test", "resources", "fixtures", "radar_webhook_payload.json")));
        String signature = "b0579737826c5959e246286dc613c355ad8d7b77";
        Assert.assertTrue(radarService.isValidSignature(body, signature));

        mvc.perform(MockMvcRequestBuilders.post("/api/radar/webhook")
            .header("x-radar-signature", signature)
            .contentType(MediaType.APPLICATION_JSON)
            .content(body))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Mockito.verify(kafkaMessage, Mockito.never())
                .publish(eq(KafkaTopicConfig.RADAR_EVENT_PROCESSING_TOPIC), anyString(), anyString());
    }

    @Test
    public void webhook_withInvalidSignHeader() throws Exception {
        String body = new String(readAllBytes(get("src", "test", "resources", "fixtures", "radar_webhook_payload.json")));

        mvc.perform(MockMvcRequestBuilders.post("/api/radar/webhook")
            .header("x-radar-signature", "invalid-signature")
            .contentType(MediaType.APPLICATION_JSON)
            .content(body))
            .andExpect(MockMvcResultMatchers.status().isUnauthorized());

        mvc.perform(MockMvcRequestBuilders.post("/api/radar/webhook")
            .contentType(MediaType.APPLICATION_JSON)
            .content(body))
            .andExpect(MockMvcResultMatchers.status().isUnauthorized());
    }

    @Test
    public void webhook_withGeofenceEvent_shouldTriggerKafka() throws Exception {
        String body = new String(readAllBytes(get("src", "test", "resources", "fixtures", "radar_dummy_webhook_warehouse_enter_geofence_payload.json")));
        String signature = "6ec1cbe986eb65538c06e51237f818351750975b";
        Assert.assertTrue(radarService.isValidSignature(body, signature));

        mvc.perform(MockMvcRequestBuilders.post("/api/radar/webhook")
                        .header("x-radar-signature", signature)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Mockito.verify(kafkaMessage, Mockito.never())
                .publish(eq(KafkaTopicConfig.RADAR_EVENT_PROCESSING_TOPIC), anyString(), anyString());
    }

    @Test
    public void webhook_whenTypeEnterGeofenceAndTagSupermarket_shouldTrackToSegment() throws Exception {
        String body = new String(readAllBytes(get("src", "test", "resources", "fixtures", "radar_dummy_webhook_supermarket_enter_geofence_payload.json")));
        String signature = "97e7cb25656c30cbe4802a3eb580bd2b9406e03f";
        Assert.assertTrue(radarService.isValidSignature(body, signature));

        mvc.perform(MockMvcRequestBuilders.post("/api/radar/webhook")
                        .header("x-radar-signature", signature)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Mockito.verify(kafkaMessage, Mockito.never())
                .publish(eq(KafkaTopicConfig.RADAR_EVENT_PROCESSING_TOPIC), anyString(), anyString());

        Mockito.verify(coralogixAPIService, Mockito.never()).sendLog(Mockito.any(), isNull(), Mockito.eq("Geofence Data Log"), any(), any(), segmentTrackingPropertiesCaptor.capture());
//        List<ImmutableMap<String, Object>> onDemandReqTrackerMap = segmentTrackingPropertiesCaptor.getAllValues();
//        Assert.assertEquals("3297", onDemandReqTrackerMap.get(0).get("user_id"));
//        Assert.assertEquals("106.8157267,-6.26399", onDemandReqTrackerMap.get(0).get("user_coordinates"));
//        Assert.assertEquals("106.8157251,-6.2640421", onDemandReqTrackerMap.get(0).get("geofence_coordinates"));
//        Assert.assertEquals(RadarEventPayload.RADAR_SUPERMARKET_TAG, onDemandReqTrackerMap.get(0).get("tag"));
//        Assert.assertEquals("58", onDemandReqTrackerMap.get(0).get("external_id"));
//        Assert.assertEquals(DateTimeUtil.spreeStringToLocalDateTime("2022-04-04T04:31:19.668Z"), onDemandReqTrackerMap.get(0).get("create_timestamp"));
//        Assert.assertEquals("3297", onDemandReqTrackerMap.get(1).get("user_id"));
//        Assert.assertEquals("106.8157267,-6.26399", onDemandReqTrackerMap.get(1).get("user_coordinates"));
//        Assert.assertEquals("106.8157267,-6.26399", onDemandReqTrackerMap.get(1).get("geofence_coordinates"));
//        Assert.assertEquals(RadarEventPayload.RADAR_SHIPMENT_NUMBER_TAG, onDemandReqTrackerMap.get(1).get("tag"));
//        Assert.assertEquals("H16682767204", onDemandReqTrackerMap.get(1).get("external_id"));
//        Assert.assertEquals(DateTimeUtil.spreeStringToLocalDateTime("2022-04-04T04:31:19.668Z"), onDemandReqTrackerMap.get(1).get("create_timestamp"));
    }

    @Test
    public void webhook_whenTypeEnterGeofenceAndTagWarehouse_shouldTrackToSegment() throws Exception {
        String body = new String(readAllBytes(get("src", "test", "resources", "fixtures", "radar_dummy_webhook_warehouse_enter_geofence_payload.json")));
        String signature = "6ec1cbe986eb65538c06e51237f818351750975b";
        Assert.assertTrue(radarService.isValidSignature(body, signature));

        mvc.perform(MockMvcRequestBuilders.post("/api/radar/webhook")
                        .header("x-radar-signature", signature)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Mockito.verify(kafkaMessage, Mockito.never())
                .publish(eq(KafkaTopicConfig.RADAR_EVENT_PROCESSING_TOPIC), anyString(), anyString());

        Mockito.verify(coralogixAPIService, Mockito.never()).sendLog(Mockito.any(), isNull(), Mockito.eq("Geofence Data Log"), any(), any(),segmentTrackingPropertiesCaptor.capture());
//        List<ImmutableMap<String, Object>> onDemandReqTrackerMap = segmentTrackingPropertiesCaptor.getAllValues();
//        Assert.assertEquals("3532", onDemandReqTrackerMap.get(0).get("user_id"));
//        Assert.assertEquals("106.79400131106377,-6.296822298047915", onDemandReqTrackerMap.get(0).get("user_coordinates"));
//        Assert.assertEquals("106.7941968,-6.296494", onDemandReqTrackerMap.get(0).get("geofence_coordinates"));
//        Assert.assertEquals(RadarEventPayload.RADAR_WAREHOUSE_TAG, onDemandReqTrackerMap.get(0).get("tag"));
//        Assert.assertEquals("118", onDemandReqTrackerMap.get(0).get("external_id"));
//        Assert.assertEquals(DateTimeUtil.spreeStringToLocalDateTime("2022-03-07T07:56:03.715Z"), onDemandReqTrackerMap.get(0).get("create_timestamp"));
//
//        Assert.assertEquals("3532", onDemandReqTrackerMap.get(1).get("user_id"));
//        Assert.assertEquals("106.79400131106377,-6.296822298047915", onDemandReqTrackerMap.get(1).get("user_coordinates"));
//        Assert.assertEquals("101.644449345767,3.08990597081725", onDemandReqTrackerMap.get(1).get("geofence_coordinates"));
//        Assert.assertEquals(RadarEventPayload.RADAR_SHIPMENT_NUMBER_TAG, onDemandReqTrackerMap.get(1).get("tag"));
//        Assert.assertEquals("H03849261295", onDemandReqTrackerMap.get(1).get("external_id"));
//        Assert.assertEquals(DateTimeUtil.spreeStringToLocalDateTime("2022-04-11T06:39:05.405Z"), onDemandReqTrackerMap.get(1).get("create_timestamp"));
    }

    @Test
    public void webhook_whenTypeExitGeofenceAndTagSupermarket_shouldTrackToSegment() throws Exception {
        String body = new String(readAllBytes(get("src", "test", "resources", "fixtures", "radar_dummy_webhook_supermarket_exit_geofence_payload.json")));
        String signature = "58dfed87aad5dbb4721be3d20e986fd0c4862289";
        Assert.assertTrue(radarService.isValidSignature(body, signature));

        mvc.perform(MockMvcRequestBuilders.post("/api/radar/webhook")
                        .header("x-radar-signature", signature)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Mockito.verify(kafkaMessage, Mockito.never())
                .publish(eq(KafkaTopicConfig.RADAR_EVENT_PROCESSING_TOPIC), anyString(), anyString());

        Mockito.verify(coralogixAPIService, Mockito.never()).sendLog(Mockito.any(), isNull(), Mockito.eq("Geofence Data Log"), any(), any(), segmentTrackingPropertiesCaptor.capture());
//        List<ImmutableMap<String, Object>> onDemandReqTrackerMap = segmentTrackingPropertiesCaptor.getAllValues();
//        Assert.assertEquals("2426", onDemandReqTrackerMap.get(0).get("user_id"));
//        Assert.assertEquals("106.6438062,-6.3050078", onDemandReqTrackerMap.get(0).get("user_coordinates"));
//        Assert.assertEquals("106.784251,-6.262529", onDemandReqTrackerMap.get(0).get("geofence_coordinates"));
//        Assert.assertEquals(RadarEventPayload.RADAR_SUPERMARKET_TAG, onDemandReqTrackerMap.get(0).get("tag"));
//        Assert.assertEquals("3", onDemandReqTrackerMap.get(0).get("external_id"));
//        Assert.assertEquals(DateTimeUtil.spreeStringToLocalDateTime("2022-04-01T11:43:09.922Z"), onDemandReqTrackerMap.get(0).get("create_timestamp"));
//
//        Assert.assertEquals("2426", onDemandReqTrackerMap.get(1).get("user_id"));
//        Assert.assertEquals("106.6438062,-6.3050078", onDemandReqTrackerMap.get(1).get("user_coordinates"));
//        Assert.assertEquals("106.784339,-6.2655371", onDemandReqTrackerMap.get(1).get("geofence_coordinates"));
//        Assert.assertEquals(RadarEventPayload.RADAR_SHIPMENT_NUMBER_TAG, onDemandReqTrackerMap.get(1).get("tag"));
//        Assert.assertEquals("H91087129047", onDemandReqTrackerMap.get(1).get("external_id"));
//        Assert.assertEquals(DateTimeUtil.spreeStringToLocalDateTime("2022-04-01T11:43:09.922Z"), onDemandReqTrackerMap.get(1).get("create_timestamp"));
    }

    @Test
    public void webhook_whenTypeExitGeofenceAndTagWarehouse_shouldTrackToSegment() throws Exception {
        String body = new String(readAllBytes(get("src", "test", "resources", "fixtures", "radar_dummy_webhook_warehouse_exit_geofence_payload.json")));
        String signature = "02ab1aed50050beaa5d4fd9f70257ed41f9b98ee";
        Assert.assertTrue(radarService.isValidSignature(body, signature));

        mvc.perform(MockMvcRequestBuilders.post("/api/radar/webhook")
                        .header("x-radar-signature", signature)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Mockito.verify(kafkaMessage, Mockito.never())
                .publish(eq(KafkaTopicConfig.RADAR_EVENT_PROCESSING_TOPIC), anyString(), anyString());

        Mockito.verify(coralogixAPIService, Mockito.never()).sendLog(Mockito.any(), isNull(), Mockito.eq("Geofence Data Log"), any(), any(), segmentTrackingPropertiesCaptor.capture());
//        List<ImmutableMap<String, Object>> onDemandReqTrackerMap = segmentTrackingPropertiesCaptor.getAllValues();
//        Assert.assertEquals("3532", onDemandReqTrackerMap.get(0).get("user_id"));
//        Assert.assertEquals("106.8196946,-6.3316127", onDemandReqTrackerMap.get(0).get("user_coordinates"));
//        Assert.assertEquals("106.7941968,-6.296494", onDemandReqTrackerMap.get(0).get("geofence_coordinates"));
//        Assert.assertEquals(RadarEventPayload.RADAR_WAREHOUSE_TAG, onDemandReqTrackerMap.get(0).get("tag"));
//        Assert.assertEquals("594", onDemandReqTrackerMap.get(0).get("external_id"));
//        Assert.assertEquals(DateTimeUtil.spreeStringToLocalDateTime("2022-03-07T08:59:16.531Z"), onDemandReqTrackerMap.get(0).get("create_timestamp"));
//
//        Assert.assertEquals("3532", onDemandReqTrackerMap.get(1).get("user_id"));
//        Assert.assertEquals("106.8196946,-6.3316127", onDemandReqTrackerMap.get(1).get("user_coordinates"));
//        Assert.assertEquals("101.644449345767,3.08990597081725", onDemandReqTrackerMap.get(1).get("geofence_coordinates"));
//        Assert.assertEquals(RadarEventPayload.RADAR_SHIPMENT_NUMBER_TAG, onDemandReqTrackerMap.get(1).get("tag"));
//        Assert.assertEquals("H03849261295", onDemandReqTrackerMap.get(1).get("external_id"));
//        Assert.assertEquals(DateTimeUtil.spreeStringToLocalDateTime("2022-04-11T06:39:05.405Z"), onDemandReqTrackerMap.get(1).get("create_timestamp"));
    }

    @Test
    public void webhook_withValidSignature_shouldScheduleJobRunrJob() throws Exception {
        String body = new String(readAllBytes(get("src", "test", "resources", "fixtures", "radar_dummy_webhook_warehouse_enter_geofence_payload.json")));
        String signature = "6ec1cbe986eb65538c06e51237f818351750975b";
        Assert.assertTrue(radarService.isValidSignature(body, signature));

        mvc.perform(MockMvcRequestBuilders.post("/api/radar/webhook")
                        .header("x-radar-signature", signature)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        // Verify JobRunr scheduling
        Mockito.verify(jobScheduler).enqueue((JobLambda) any());
    }
}
