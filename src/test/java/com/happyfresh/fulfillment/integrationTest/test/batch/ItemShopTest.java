package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.ItemRepository;
import org.hamcrest.Matchers;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.List;

public class ItemShopTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private ItemRepository itemRepository;

    @Autowired
    private CountryFactory countryFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private  ItemFactory itemFactory;

    User systemAdminUser;

    User shopper;

    User ranger;

    User driver;

    User admin;

    @Before
    public void setUp() throws InterruptedException {
        systemAdminUser = userFactory.createUserData(Role.Name.SYSTEM_ADMIN);
        shopper = userFactory.createUserData(Role.Name.SHOPPER, systemAdminUser.getTenant());
        ranger = userFactory.createUserData(Role.Name.DRIVER, systemAdminUser.getTenant());
        driver = userFactory.createUserData(Role.Name.DRIVER, systemAdminUser.getTenant());
        admin = userFactory.createUserData(Role.Name.ADMIN, systemAdminUser.getTenant());
    }

    @Test
    public void updatesFoundQty() throws Exception {
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, shopper);

        Batch batch = batchFactory.createBatch(shopper, slots.get(0), Batch.Type.SHOPPING);
        Shipment shipment = batch.getJobs().get(0).getShipment();
        Item item = shipment.getItems().get(0);

        JSONObject content = new JSONObject();
        content.put("quantity", 2);
        content.put("shopper_notes_fulfilled", false);
        content.put("weight", 1.3);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + batch.getId() + "/start")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()));

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + batch.getId() + "/shipments/" + shipment.getNumber() + "/items/" + item.getSku() + "/shop")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(content.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.total_shopped", Matchers.equalTo(2)))
                .andExpect(MockMvcResultMatchers.status().isOk());

        Item item2 = itemRepository.findById(item.getId()).get();
        Assert.assertEquals(2L, item2.getFoundQty().longValue());
    }

    @Test
    public void invalidatesQty() throws Exception {
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, shopper);

        Batch batch = batchFactory.createBatch(shopper, slots.get(0), Batch.Type.SHOPPING);
        Shipment shipment = batch.getJobs().get(0).getShipment();
        Item item = shipment.getItems().get(0);

        JSONObject content = new JSONObject();
        content.put("quantity", 3);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + batch.getId() + "/start")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()));

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + batch.getId() + "/shipments/" + shipment.getNumber() + "/items/" + item.getSku() + "/shop")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(content.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.errors[0].type", Matchers.equalTo("InvalidItemQtyException")))
                .andExpect(MockMvcResultMatchers.status().isBadRequest());
    }

    @Test
    public void shouldRequestSuccessIfWeightIsValid() throws Exception {
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, shopper);

        Batch batch = batchFactory.createBatch(shopper, slots.get(0), Batch.Type.SHOPPING);
        Shipment shipment = batch.getJobs().get(0).getShipment();
        Item item = shipment.getItems().get(0);

        JSONObject content = new JSONObject();
        content.put("quantity", 2);
        content.put("shopper_notes_fulfilled", false);
        content.put("weight", 1.3);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + batch.getId() + "/start")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()));

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + batch.getId() + "/shipments/" + shipment.getNumber() + "/items/" + item.getSku() + "/shop")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(content.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.actual_weight", Matchers.equalTo(1.3)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.average_weight", Matchers.equalTo(1.0)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.total_actual_weight_adjustment", Matchers.equalTo(2.6)))
                .andExpect(MockMvcResultMatchers.status().isOk());

        content.put("weight", 2.7);
        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + batch.getId() + "/shipments/" + shipment.getNumber() + "/items/" + item.getSku() + "/shop")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(content.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.actual_weight", Matchers.equalTo(2.7)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.average_weight", Matchers.equalTo(1.0)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.total_actual_weight_adjustment", Matchers.equalTo(5.4)))
                .andExpect(MockMvcResultMatchers.status().isOk());

        content.put("weight", 1.5);
        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + batch.getId() + "/shipments/" + shipment.getNumber() + "/items/" + item.getSku() + "/shop")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(content.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.actual_weight", Matchers.equalTo(1.5)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.average_weight", Matchers.equalTo(1.0)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.total_actual_weight_adjustment", Matchers.equalTo(3.0)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void shouldRequestErrorIfWeightInvalid() throws Exception {
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, shopper);

        Batch batch = batchFactory.createBatch(shopper, slots.get(0), Batch.Type.SHOPPING);
        Shipment shipment = batch.getJobs().get(0).getShipment();
        Item item = shipment.getItems().get(0);

        JSONObject content = new JSONObject();
        content.put("quantity", 2);
        content.put("shopper_notes_fulfilled", false);
        content.put("weight", 3.1);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + batch.getId() + "/start")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()));

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + batch.getId() + "/shipments/" + shipment.getNumber() + "/items/" + item.getSku() + "/shop")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(content.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isBadRequest());

        content.put("weight", 0.2);
        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + batch.getId() + "/shipments/" + shipment.getNumber() + "/items/" + item.getSku() + "/shop")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(content.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isBadRequest());
    }

    @Test
    public void shouldReturnErrorIfActualWeightIsNull() throws Exception {
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, shopper);

        Batch batch = batchFactory.createBatch(shopper, slots.get(0), Batch.Type.SHOPPING);
        Shipment shipment = batch.getJobs().get(0).getShipment();
        Item item = shipment.getItems().get(0);

        JSONObject content = new JSONObject();
        content.put("quantity", 2);
        content.put("shopper_notes_fulfilled", false);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + batch.getId() + "/start")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()));

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + batch.getId() + "/shipments/" + shipment.getNumber() + "/items/" + item.getSku() + "/shop")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(content.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isBadRequest());
    }

    @Test
    public void updatesOosQty() throws Exception {
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, shopper);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, shopper);

        Batch batch = batchFactory.createBatch(shopper, slots.get(0), Batch.Type.SHOPPING);
        Shipment shipment = batch.getJobs().get(0).getShipment();
        Item item = shipment.getItems().get(0);
        item.setRequestedQty(5);
        itemRepository.save(item);

        JSONObject content = new JSONObject();
        content.put("quantity", 2);
        content.put("shopper_notes_fulfilled", false);
        content.put("weight", 1.3);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + batch.getId() + "/start")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()));

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + batch.getId() + "/shipments/" + shipment.getNumber() + "/items/" + item.getSku() + "/shop")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(content.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.total_shopped", Matchers.equalTo(2)))
                .andExpect(MockMvcResultMatchers.status().isOk());

        Item checkItem = itemRepository.findById(item.getId()).get();
        Assert.assertEquals(2l, checkItem.getFoundQty().longValue());
        Assert.assertEquals(0l, checkItem.getOosQty().longValue());

        String url = String.format("/api/batches/%d/shipments/%s/items/%s/mark_oos", batch.getId(), shipment.getNumber(), item.getSku());
        mvc.perform(MockMvcRequestBuilders.put(url)
                .contentType("application/json")
                .content("{\"oos_type\": 4, \"oos_detail\": \"Item is not available in the shelf\"}")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.total_oos", Matchers.equalTo(3)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.flag", Matchers.equalTo("out_of_stock")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.oos_type", Matchers.equalTo(4)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.oos_detail", Matchers.equalTo("Item is not available in the shelf")))
                .andExpect(MockMvcResultMatchers.status().isOk());

        checkItem = itemRepository.findById(item.getId()).get();
        Assert.assertEquals(2l, checkItem.getFoundQty().longValue());
        Assert.assertEquals(3l, checkItem.getOosQty().longValue());

        content = new JSONObject();
        content.put("quantity", 4);
        content.put("shopper_notes_fulfilled", false);
        content.put("weight", 2.6);

        mvc.perform(MockMvcRequestBuilders.put("/api/batches/" + batch.getId() + "/shipments/" + shipment.getNumber() + "/items/" + item.getSku() + "/shop")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(content.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.item.total_shopped", Matchers.equalTo(4)))
                .andExpect(MockMvcResultMatchers.status().isOk());

        checkItem = itemRepository.findById(item.getId()).get();
        Assert.assertEquals(4l, checkItem.getFoundQty().longValue());
        Assert.assertEquals(1l, checkItem.getOosQty().longValue());
    }

}
