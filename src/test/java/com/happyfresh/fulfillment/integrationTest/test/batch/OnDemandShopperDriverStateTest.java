package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.fasterxml.jackson.databind.JsonNode;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.service.NotificationService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.SetSlotHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.ShipmentHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.ItemRepository;
import com.happyfresh.fulfillment.repository.JobRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import com.happyfresh.fulfillment.shipment.service.PaymentService;
import com.happyfresh.fulfillment.user.service.AgentClockInActivityService;
import org.apache.commons.io.IOUtils;
import org.elasticsearch.common.geo.GeoPoint;
import org.hamcrest.Matchers;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.io.File;
import java.io.FileInputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

public class OnDemandShopperDriverStateTest extends BaseTest {

    @MockBean
    private OrderService orderService;

    @MockBean
    private PaymentService paymentService;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private ItemRepository itemRepository;

    @Autowired
    private AgentClockInActivityService agentClockInActivityService;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private AgentFactory agentFactory;

    @Autowired
    private OnDemandClusterFactory onDemandClusterFactory;

    @Autowired
    private ClusterFactory clusterFactory;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private PaymentMockFactory paymentMockFactory;

    @Autowired
    private SetSlotHelper helper;

    @Autowired
    private ShipmentHelper shipmentHelper;

    @Autowired
    private TransactionHelper transactionHelper;

    @MockBean
    private NotificationService notificationService;

    private StockLocation stockLocation;

    private StockLocation stockLocationInSameODCluster;

    private List<Slot> slots;

    private User user;

    private User shopper;

    private User ranger;

    private Shipment shipment;

    private JSONObject shipmentObj;

    private Batch shoppingBatch;

    private Batch deliveryBatch;

    @Before
    public void setUp() throws InterruptedException {
        Thread.sleep(750);
        user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        LocalDateTime now = LocalDateTime.now();
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user);
        for (StockLocation stockLocation : stockLocations) {
            stockLocation.setOpenAt(now.toLocalTime().minusHours(1).withMinute(0).withSecond(0).withNano(0));
            stockLocation.setCloseAt(now.toLocalTime().plusHours(10).withMinute(0).withSecond(0).withNano(0));
            stockLocation.setShopperAveragePickingTimePerUniqItem(5);
            stockLocation.setType(StockLocation.Type.SPECIAL);
            stockLocation.setEnableOnDemandDelivery(true);
            stockLocation.setEnableOnDemandSnd(true);
            stockLocationFactory.save(stockLocation);
        }
        stockLocation = stockLocations.get(0);
        stockLocationInSameODCluster = stockLocations.get(1);

        LocalDate tomorrow = LocalDateTime.now().plusDays(1).toLocalDate();
        slots = slotFactory.createSlots(stockLocations, tomorrow, tomorrow, Slot.Type.ONE_HOUR, user, 1, 1);
        setupShopper();
        setupOnDemandRanger();
    }

    @Test
    public void testSetSlot() throws Exception {
        setOnDemandShopperDriverSlot();

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandShoppingBatches = batchRepository.findByType(Batch.Type.ON_DEMAND_SHOPPING);
            List<Batch> onDemandDeliveryBatches = batchRepository.findByType(Batch.Type.ON_DEMAND_DELIVERY);
            Job shoppingJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_SHOPPER).get(0);
            Job deliveryJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_DRIVER).get(0);
            Assert.assertThat(onDemandShoppingBatches, Matchers.hasSize(1));
            Assert.assertThat(onDemandDeliveryBatches, Matchers.hasSize(1));
            Assert.assertEquals(Job.State.INITIAL, shoppingJob.getState());
            Assert.assertEquals(Job.State.INITIAL, deliveryJob.getState());
        });
    }

    @Test
    public void testShipmentAdjust() throws Exception {
        setOnDemandShopperDriverSlot();
        adjustShipment();

        transactionHelper.withNewTransaction(() -> {
            Job shoppingJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_SHOPPER).get(0);
            Job deliveryJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_DRIVER).get(0);
            Assert.assertEquals(Job.State.STARTED, shoppingJob.getState());
            Assert.assertEquals(Job.State.INITIAL, deliveryJob.getState());

            Slot slot = shoppingJob.getJobSlots().get(0).getSlot();
            ZoneId zoneId = ZoneId.of(slot.getStockLocation().getState().getTimeZone());
            LocalDateTime startTime = ZonedDateTime.of(slot.getStartTime(), ZoneId.of("UTC")).withZoneSameInstant(zoneId).toLocalDateTime();
            LocalDateTime endTime = ZonedDateTime.of(slot.getEndTime(), ZoneId.of("UTC")).withZoneSameInstant(zoneId).toLocalDateTime();
            final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");

            verify(notificationService, times(1))
                    .sendPushNotificationForNewOnDemandShoppingJob(
                            shopper.getId(),
                            "Order-1",
                            startTime.format(formatter),
                            endTime.format(formatter),
                            user.getId()
                    );
        });
    }

    @Test
    public void testFinalizeShopping() throws Exception {
        setOnDemandShopperDriverSlot();
        adjustShipment();
        finalizeShopping();

        transactionHelper.withNewTransaction(() -> {
            Job shoppingJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_SHOPPER).get(0);
            Assert.assertEquals(Job.State.FINALIZING, shoppingJob.getState());
        });
    }

    @Test
    public void testPayShopping() throws Exception {
        setOnDemandShopperDriverSlot();
        adjustShipment();
        finalizeShopping();
        payShopping();

        transactionHelper.withNewTransaction(() -> {
            Job shoppingJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_SHOPPER).get(0);
            Assert.assertEquals(Job.State.FINISHED, shoppingJob.getState());
        });
    }

    @Test
    public void testPickup() throws Exception {
        setOnDemandShopperDriverSlot();
        adjustShipment();
        finalizeShopping();
        payShopping();
        pickupDelivery();

        transactionHelper.withNewTransaction(() -> {
            Job deliveryJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_DRIVER).get(0);
            Assert.assertEquals(Job.State.STARTED, deliveryJob.getState());
        });
    }

    @Test
    public void testAccept() throws Exception {
        setOnDemandShopperDriverSlot();
        adjustShipment();
        finalizeShopping();
        payShopping();
        pickupDelivery();
        acceptDelivery();

        transactionHelper.withNewTransaction(() -> {
            Job deliveryJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_DRIVER).get(0);
            Assert.assertEquals(Job.State.ACCEPTED, deliveryJob.getState());
        });
    }

    @Test
    public void testStartDelivery() throws Exception {
        setOnDemandShopperDriverSlot();
        adjustShipment();
        finalizeShopping();
        payShopping();
        pickupDelivery();
        acceptDelivery();
        startDelivery();

        transactionHelper.withNewTransaction(() -> {
            Job deliveryJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_DRIVER).get(0);
            Assert.assertEquals(Job.State.DELIVERING, deliveryJob.getState());
        });
    }

    @Test
    public void testFoundAddress() throws Exception {
        setOnDemandShopperDriverSlot();
        adjustShipment();
        finalizeShopping();
        payShopping();
        pickupDelivery();
        acceptDelivery();
        startDelivery();
        arriveDelivery();

        transactionHelper.withNewTransaction(() -> {
            Job deliveryJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_DRIVER).get(0);
            Assert.assertEquals(Job.State.FOUND_ADDRESS, deliveryJob.getState());
        });
    }

    @Test
    public void testFinalizeDelivery() throws Exception {
        setOnDemandShopperDriverSlot();
        adjustShipment();
        finalizeShopping();
        payShopping();
        pickupDelivery();
        acceptDelivery();
        startDelivery();
        arriveDelivery();
        finalizeDelivery();

        transactionHelper.withNewTransaction(() -> {
            Job deliveryJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_DRIVER).get(0);
            Assert.assertEquals(Job.State.FINALIZING_DELIVERY, deliveryJob.getState());
        });
    }

    @Test
    public void testCapturePayment() throws Exception {
        setOnDemandShopperDriverSlot();
        adjustShipment();
        finalizeShopping();
        payShopping();
        pickupDelivery();
        acceptDelivery();
        startDelivery();
        arriveDelivery();
        finalizeDelivery();
        capturePayment();

        transactionHelper.withNewTransaction(() -> {
            Shipment s = shipmentRepository.findById(shipment.getId()).orElse(null);
            Assert.assertTrue(s.isPaymentClear());
        });
    }

    @Test
    public void testFinishDelivery() throws Exception {
        setOnDemandShopperDriverSlot();
        adjustShipment();
        finalizeShopping();
        payShopping();
        pickupDelivery();
        acceptDelivery();
        startDelivery();
        arriveDelivery();
        finalizeDelivery();
        capturePayment();
        finishDelivery();

        transactionHelper.withNewTransaction(() -> {
            Job deliveryJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_DRIVER).get(0);
            Assert.assertEquals(Job.State.FINISHED, deliveryJob.getState());
        });
    }

    @Test
    public void testWithRangerMultiJob_shouldStartNewOnDemandRangerJob_afterCurrentOnDemandDeliveryJobFinished() throws Exception {
        OnDemandCluster odCluster = onDemandClusterFactory.create(user);
        Cluster cluster1 = clusterFactory.create(user);
        Cluster cluster2 = clusterFactory.create(user);

        stockLocation.setEnableOnDemandSnd(true);
        stockLocation.setOnDemandCluster(odCluster);
        stockLocation.setCluster(cluster1);
        stockLocationFactory.save(stockLocation);

        stockLocationInSameODCluster.setEnableOnDemandSnd(false);
        stockLocationInSameODCluster.setOnDemandCluster(odCluster);
        stockLocation.setCluster(cluster2);
        stockLocationFactory.save(stockLocationInSameODCluster);

        setOnDemandShopperDriverSlot();
        adjustShipment();
        finalizeShopping();
        payShopping();
        pickupDelivery();
        acceptDelivery();
        startDelivery();

        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocationInSameODCluster, slots.get(0).getId(), 1, "Order-On-Demand-Ranger", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);
        shipmentHelper.adjustShipment(shipmentObj, user);

        arriveDelivery();
        finalizeDelivery();
        capturePayment();
        finishDelivery();

        transactionHelper.withNewTransaction(() -> {
            List<Batch> onDemandRangerBatches = batchRepository.findByType(Batch.Type.ON_DEMAND);
            List<Batch> onDemandShoppingBatches = batchRepository.findByType(Batch.Type.ON_DEMAND_SHOPPING);
            List<Batch> onDemandDeliveryBatches = batchRepository.findByType(Batch.Type.ON_DEMAND_DELIVERY);
            Assert.assertThat(onDemandRangerBatches, Matchers.hasSize(1));
            Assert.assertThat(onDemandShoppingBatches, Matchers.hasSize(1));
            Assert.assertThat(onDemandDeliveryBatches, Matchers.hasSize(1));

            Job shopperJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_SHOPPER).get(0);
            Assert.assertEquals(Job.State.FINISHED, shopperJob.getState());

            Job driverJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_DRIVER).get(0);
            Assert.assertEquals(Job.State.FINISHED, driverJob.getState());

            Job rangerJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_RANGER).get(0);
            Assert.assertEquals(Job.State.STARTED, rangerJob.getState());
        });
    }

    @Test
    public void testCancel() throws Exception {
        setOnDemandShopperDriverSlot();
        adjustShipment();
        finalizeShopping();
        payShopping();
        pickupDelivery();
        acceptDelivery();
        startDelivery();
        cancelShipment();

        transactionHelper.withNewTransaction(() -> {
            Job shoppingJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_SHOPPER).get(0);
            Job deliveryJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_DRIVER).get(0);
            Assert.assertEquals(Job.State.CANCELLED, shoppingJob.getState());
            Assert.assertEquals(Job.State.CANCELLED, deliveryJob.getState());
        });
    }

    @Test
    public void testFailDelivery() throws Exception {
        setOnDemandShopperDriverSlot();
        adjustShipment();
        finalizeShopping();
        payShopping();
        pickupDelivery();
        acceptDelivery();
        startDelivery();
        failDelivery();

        transactionHelper.withNewTransaction(() -> {
            Job deliveryJob = jobRepository.findAllByType(Job.Type.ON_DEMAND_DRIVER).get(0);
            Assert.assertEquals(Job.State.FAILED, deliveryJob.getState());
        });
    }

    private void setupShopper() {
        shopper = userFactory.createUserData(Role.Name.SHOPPER, user.getTenant());
        Agent agent = agentFactory.createAgent(shopper, stockLocation, Agent.State.WORKING);
        agent.setLat(stockLocation.getLat());
        agent.setLon(stockLocation.getLon());
        agentClockInActivityService.createAgentClockInActivity(agent, 8);
    }

    private void setupOnDemandRanger() {
        ranger = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER, user.getTenant());
        Agent agent = agentFactory.createAgent(ranger, stockLocation, Agent.State.WORKING);
        agent.setLat(stockLocation.getLat());
        agent.setLon(stockLocation.getLon());
        agentClockInActivityService.createAgentClockInActivity(agent, 8);
    }

    private void setOnDemandShopperDriverSlot() throws Exception {
        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.298692, 106.7824296), "RS. Fatmawati");
        helper.assertSetOnDemandSlot(true, shipmentObj, user);

        transactionHelper.withNewTransaction(() -> {
            shipment = shipmentRepository.findByOrderNumber("Order-1");
            shoppingBatch = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND_SHOPPING, shopper, Job.getInactiveJobStates()).get(0);
            deliveryBatch = batchRepository.findActiveBatchesByTypeAndUser(Batch.Type.ON_DEMAND_DELIVERY, ranger, Job.getInactiveJobStates()).get(0);
        });
    }

    private void adjustShipment() throws Exception {
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + shipment.getNumber() + "/adjust")
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(shipmentObj.toString()));
    }

    private void finalizeShopping() throws Exception {
        JSONObject payload = createFinalizePayload();
        String url = String.format("/api/batches/%d/shipments/%s/finalize", shoppingBatch.getId(), shipment.getNumber());

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(7500.0);
        mvc.perform(MockMvcRequestBuilders.post(url)
            .header("X-Fulfillment-User-Token", shopper.getToken())
            .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken())
            .content(payload.toString())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    private JSONObject createFinalizePayload() throws Exception {
        List<Item> items = itemRepository.findAllByShipmentId(shipment.getId());
        JSONArray itemsArray = new JSONArray();
        for (Item item : items) {
            item.setFoundQty(item.getRequestedQty());
            itemRepository.save(item);
            JSONObject item1Object = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
            item1Object.put("id", item.getId());
            item1Object.put("found_qty", item.getRequestedQty());
            itemsArray.put(item1Object);
        }
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        return requestBody;
    }

    private void payShopping() throws Exception {
        File file1 = new File("src/test/resources/fixtures/receipt1.jpg");
        FileInputStream input1 = new FileInputStream(file1);
        MockMultipartFile multipartFile1 = new MockMultipartFile("receipts[0].attachments[0]", file1.getName(), "image/jpeg", IOUtils.toByteArray(input1));
        input1.close();

        String url = String.format("/api/batches/%d/shipments/%s/pay", shoppingBatch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.multipart(url)
            .file(multipartFile1)
            .param("receipts[0].number", "SomeNumber")
            .param("receipts[0].total", "10.53")
            .param("receipts[0].tax", "1.66")
            .header("X-Fulfillment-User-Token", shopper.getToken())
            .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void pickupDelivery() throws Exception {
        String url = String.format("/api/batches/%d/pickup", deliveryBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
            .header("X-Fulfillment-User-Token", ranger.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    private void acceptDelivery() throws Exception {
        String url = String.format("/api/batches/%d/shipments/%s/accept", deliveryBatch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
            .header("X-Fulfillment-User-Token", ranger.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    private void startDelivery() throws Exception {
        String url = String.format("/api/batches/%d/shipments/%s/deliver", deliveryBatch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
            .header("X-Fulfillment-User-Token", ranger.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void arriveDelivery() throws Exception {
        String url = String.format("/api/batches/%d/shipments/%s/arrive", deliveryBatch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
            .header("X-Fulfillment-User-Token", ranger.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void finalizeDelivery() throws Exception {
        JSONObject payload = new JSONObject();
        payload.put("items", new JSONArray());
        String url = String.format("/api/v2/batches/%d/shipments/%s/finalize_delivery", deliveryBatch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.put(url)
            .header("X-Fulfillment-User-Token", ranger.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content(payload.toString()))
            .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void capturePayment() throws Exception {
        JsonNode response = paymentMockFactory.createPayment(null, shipment.getOrderTotal(), true);
        Mockito.when(paymentService.capturePayment(shipment.getNumber(), "127.0.0.1", false)).thenReturn(ResponseEntity.ok(response.toString()));
        String url = String.format("/api/v2/batches/%d/shipments/%s/capture", deliveryBatch.getId(), shipment.getNumber());

        mvc.perform(MockMvcRequestBuilders.post(url)
            .header("X-Fulfillment-User-Token", ranger.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void finishDelivery() throws Exception {
        File file = new File("src/test/resources/fixtures/signature.jpg");
        FileInputStream input1 = new FileInputStream(file);
        MockMultipartFile multipartFile = new MockMultipartFile("attachment", file.getName(), "image/jpeg", IOUtils.toByteArray(input1));
        input1.close();

        String url = String.format("/api/batches/%d/shipments/%s/finish", deliveryBatch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.multipart(url)
            .file(multipartFile)
            .param("receiver", "Robert")
            .param("cash_amount", "7500.0")
            .param("lat", "-6.2047739")
            .param("lon", "106.7100399")
            .param("age_consent", "true")
            .header("X-Fulfillment-User-Token", ranger.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    private void cancelShipment() throws Exception {
        JSONObject payload = new JSONObject();
        payload.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());
        String url = String.format("/api/shipments/%s/cancel", shipment.getNumber());

        mvc.perform(MockMvcRequestBuilders.put(url)
            .content(payload.toString())
            .contentType(MediaType.APPLICATION_JSON)
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void failDelivery() throws Exception {
        JSONObject payload = new JSONObject();
        payload.put("reason", Job.Note.FAIL_DELIVERY_CANNOT_REACH_CUSTOMER.toString());
        String url = String.format("/api/batches/%d/shipments/%s/fail_delivery", deliveryBatch.getId(), shipment.getNumber());

        mvc.perform(MockMvcRequestBuilders.put(url)
            .content(payload.toString())
            .contentType(MediaType.APPLICATION_JSON)
            .header("X-Fulfillment-User-Token", ranger.getToken())
            .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().isOk());
    }
}
