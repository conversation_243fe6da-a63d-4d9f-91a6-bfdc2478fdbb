package com.happyfresh.fulfillment.integrationTest.test.slot;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.service.JedisCacheService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.CountryFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.repository.ClusterRepository;
import com.happyfresh.fulfillment.repository.TenantRepository;
import com.happyfresh.fulfillment.slot.presenter.FleetUtilizationAvailabilityPresenter;
import com.happyfresh.fulfillment.slot.presenter.SlotUtilizationDatePresenter;
import com.happyfresh.fulfillment.slot.presenter.SlotUtilizationPresenter;
import com.happyfresh.fulfillment.stockLocation.service.StockLocationService;
import org.hamcrest.Matchers;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

public class SlotUtilizationAvailabilityTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private ClusterRepository clusterRepository;

    @Autowired
    private CountryFactory countryFactory;

    @Autowired
    private JedisCacheService cacheService;

    @SpyBean
    private StockLocationService stockLocationService;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private TenantRepository tenantRepository;

    private List<StockLocation> stockLocations;

    private StockLocation stockLocation;

    private User admin;

    private Country country;

    private LocalDate today;

    @Override
    protected void setupElasticSearchBeforeEachTests() {
        elasticSearchSetupService.setPreFillSimpleRouteEdges(false);
    }

    @Before
    public void setUp() throws InterruptedException {
        admin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);

        stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, admin);
        stockLocation = stockLocations.get(0);
        Cluster cluster = stockLocation.getCluster();
        cluster.setSlotType(Slot.Type.LONGER_DELIVERY);
        clusterRepository.save(cluster);

        country = stockLocation.getState().getCountry();
        Map<String, String> preferences = country.getPreferences();
        preferences.put("day_full_threshold", "100.0");
        preferences.put("utilization_number_of_days", "3");
        country.setPreferences(preferences);
        countryFactory.save(country);

        today = LocalDate.now();
    }

    @Test
    public void shouldSuccess_fromCache_NonDDS() throws Exception {
        // Arrange: direct fill Redis
        FleetUtilizationAvailabilityPresenter presenter = new FleetUtilizationAvailabilityPresenter(false, "Grand Lucky SCBD", false, 3);
        String cacheKey = "V2:SLOT_UTILIZATION_AVAILABILITY:stock_location:999:date:" + LocalDate.now();
        String cacheValue = mapper.writeValueAsString(presenter);
        cacheService.setWithExpiry(cacheKey, cacheValue, 10);

        mvc.perform(MockMvcRequestBuilders.get("/api/stock_locations/999/fleet_utilization_availability")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.fleet_utilization_availability", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.days_count", Matchers.equalTo(3)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.stock_location_name", Matchers.equalTo("Grand Lucky SCBD")));
    }

    @Test
    public void shouldSuccess_fromCache_DDS() throws Exception {
        // Arrange: direct fill Redis
        FleetUtilizationAvailabilityPresenter presenter = new FleetUtilizationAvailabilityPresenter(true, "Grand Lucky SCBD", false, 3);
        presenter.setEarliestAvailableDate(LocalDate.now());
        presenter.setHighDemand(false);
        presenter.setOnDemandAvailability(false);
        presenter.setUtilization(50.0);

        String cacheKey = "V2:SLOT_UTILIZATION_AVAILABILITY:stock_location:999:date:" + LocalDate.now();
        String cacheValue = mapper.writeValueAsString(presenter);
        cacheService.setWithExpiry(cacheKey, cacheValue, 10);

        mvc.perform(MockMvcRequestBuilders.get("/api/stock_locations/999/fleet_utilization_availability")
                        .header("X-Fulfillment-User-Token", admin.getToken())
                        .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.fleet_utilization_availability", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.days_count", Matchers.equalTo(3)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.stock_location_name", Matchers.equalTo("Grand Lucky SCBD")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.on_demand_availability", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.earliest_available_date", Matchers.equalTo(LocalDate.now().toString())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.high_demand", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.utilization", Matchers.equalTo(50.0)));
    }

    @Test
    public void should404_onUnknownExternalStockLocationId() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/api/stock_locations/999/fleet_utilization_availability")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isNotFound());
    }

    @Test
    public void shouldAlwaysReturnTrue_onNonDDSStores_evenIfUnavailable() throws Exception {
        Cluster cluster = stockLocation.getCluster();
        cluster.setSlotType(Slot.Type.ONE_HOUR);
        clusterRepository.save(cluster);

        runTest(true, false, Arrays.asList(120.0, 120.0, 120.0), null, false, null, 0);
    }

    @Test
    public void shouldAlwaysReturnTrue_onFleetUtilizationDeactivated_evenIfUnavailable() throws Exception {
        Tenant tenant = country.getTenant();
        Map<String, String> preferences = tenant.getPreferences();
        preferences.put("enable_fleet_utilization_availability", "false");
        tenant.setPreferences(preferences);
        tenantRepository.save(tenant);

        runTest(true, false, Arrays.asList(120.0, 120.0, 120.0), null, false, null, 0);
    }

    @Test
    public void shouldTrue_idleOdRangerAvailable_utilizationEmpty() throws Exception {
        runTest(true, true, Arrays.asList(0.0, 0.0, 0.0), LocalDate.now(), false, 0.0);
    }

    @Test
    public void shouldCalculate_withWrongCacheValue() throws Exception {
        String expectedCacheKey = "V2:SLOT_UTILIZATION_AVAILABILITY:stock_location:" + stockLocation.getExternalId() + ":date:" + LocalDate.now();
        cacheService.setWithExpiry(expectedCacheKey, "random_string", 10);

        List<SlotUtilizationDatePresenter> slotsUtilization = new ArrayList<>();
        slotsUtilization.add(new SlotUtilizationDatePresenter(today.plusDays(1), 100.0));
        slotsUtilization.add(new SlotUtilizationDatePresenter(today.plusDays(2), 100.0));
        slotsUtilization.add(new SlotUtilizationDatePresenter(today.plusDays(3), 100.0));

        SlotUtilizationPresenter presenter = new SlotUtilizationPresenter();
        presenter.setSlotsUtilization(slotsUtilization);
        presenter.setIdleOnDemandRanger(false);

        Mockito.when(stockLocationService.getFleetUtilization(stockLocation.getExternalId(), country.getUtilizationNumberOfDays()))
                .thenReturn(presenter);

        mvc.perform(MockMvcRequestBuilders.get("/api/stock_locations/" + stockLocation.getExternalId() + "/fleet_utilization_availability")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.fleet_utilization_availability", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.days_count", Matchers.equalTo(3)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.stock_location_name", Matchers.equalTo(stockLocation.getName())));

        verify(stockLocationService, times(1))
                .getFleetUtilization(stockLocation.getExternalId(), country.getUtilizationNumberOfDays());
    }

    @Test
    public void shouldReturnDaysCount_basedOnSlotsUtilizationDays() throws Exception {
        Map<String, String> preferences = country.getPreferences();
        preferences.put("day_full_threshold", "100.0");
        preferences.put("utilization_number_of_days", "2");
        country.setPreferences(preferences);
        countryFactory.save(country);

        List<SlotUtilizationDatePresenter> slotsUtilization = new ArrayList<>();
        slotsUtilization.add(new SlotUtilizationDatePresenter(today.plusDays(1), 100.0));
        slotsUtilization.add(new SlotUtilizationDatePresenter(today.plusDays(2), 100.0));

        SlotUtilizationPresenter presenter = new SlotUtilizationPresenter();
        presenter.setSlotsUtilization(slotsUtilization);
        presenter.setIdleOnDemandRanger(false);

        Mockito.when(stockLocationService.getFleetUtilization(stockLocation.getExternalId(), country.getUtilizationNumberOfDays()))
                .thenReturn(presenter);

        mvc.perform(MockMvcRequestBuilders.get("/api/stock_locations/" + stockLocation.getExternalId() + "/fleet_utilization_availability")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.days_count", Matchers.equalTo(2)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.stock_location_name", Matchers.equalTo(stockLocation.getName())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.fleet_utilization_availability", Matchers.equalTo(false)));

        verify(stockLocationService, times(1))
                .getFleetUtilization(stockLocation.getExternalId(), country.getUtilizationNumberOfDays());
    }

    @Test
    public void shouldReturnDaysCount0_onEmptyUtilization() throws Exception {
        Map<String, String> preferences = country.getPreferences();
        preferences.put("day_full_threshold", "100.0");
        preferences.put("utilization_number_of_days", "3");
        country.setPreferences(preferences);
        countryFactory.save(country);

        SlotUtilizationPresenter presenter = new SlotUtilizationPresenter();
        presenter.setSlotsUtilization(new ArrayList<>());
        presenter.setIdleOnDemandRanger(false);

        Mockito.when(stockLocationService.getFleetUtilization(stockLocation.getExternalId(), country.getUtilizationNumberOfDays()))
                .thenReturn(presenter);

        mvc.perform(MockMvcRequestBuilders.get("/api/stock_locations/" + stockLocation.getExternalId() + "/fleet_utilization_availability")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.days_count", Matchers.equalTo(0)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.stock_location_name", Matchers.equalTo(stockLocation.getName())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.fleet_utilization_availability", Matchers.equalTo(false)));

        verify(stockLocationService, times(1))
                .getFleetUtilization(stockLocation.getExternalId(), country.getUtilizationNumberOfDays());
    }

    @Test
    public void shouldTrue_idleOdRangerAvailable_utilizationPartiallyFull() throws Exception {
        runTest(true, true, Arrays.asList(80.0, 100.0, 100.0), LocalDate.now(), false, 80.0);
    }

    @Test
    public void shouldTrue_idleOdRangerNotAvailable_utilizationPartiallyFull() throws Exception {
        runTest(true, false, Arrays.asList(80.0, 100.0, 100.0), LocalDate.now(), false, 80.0);
    }

    @Test
    public void shouldTrue_idleOdRangerAvailable_utilizationFull() throws Exception {
        runTest(true, true, Arrays.asList(100.0, 100.0, 100.0), LocalDate.now(), true, 100.0);
    }

    @Test
    public void shouldFalse_idleOdRangerNotAvailable_utilizationFull() throws Exception {
        runTest(false, false, Arrays.asList(100.0, 100.0, 100.0), null, true, 100.0);
    }

    private void runTest(boolean result, boolean idleOnDemand, List<Double> utilizations, LocalDate earliestAvailableDate, boolean highDemand, Double utilization) throws Exception {
        runTest(result, idleOnDemand, utilizations, earliestAvailableDate, highDemand, utilization, 1);
    }

    private void runTest(boolean result, boolean idleOnDemand, List<Double> utilizations, LocalDate earliestAvailableDate, boolean highDemand, Double utilization, int mockedMethodCalledCount) throws Exception {
        LocalDate today = LocalDate.now();

        SlotUtilizationPresenter presenter = new SlotUtilizationPresenter();
        List<SlotUtilizationDatePresenter> slotsUtilization = new ArrayList<>();
        int utilizationDaysSize = utilizations.size();
        for (int i = 0; i < utilizationDaysSize; i++) {
            Double utilizationPerDay = utilizations.get(i);
            slotsUtilization.add(new SlotUtilizationDatePresenter(today.plusDays(i), utilizationPerDay));
        }
        presenter.setSlotsUtilization(slotsUtilization);
        presenter.setIdleOnDemandRanger(idleOnDemand);

        Mockito.when(stockLocationService.getFleetUtilization(stockLocation.getExternalId(), country.getUtilizationNumberOfDays()))
                .thenReturn(presenter);

        mvc.perform(MockMvcRequestBuilders.get("/api/stock_locations/" + stockLocation.getExternalId() + "/fleet_utilization_availability")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.days_count", Matchers.equalTo(utilizationDaysSize)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.stock_location_name", Matchers.equalTo(stockLocation.getName())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.fleet_utilization_availability", Matchers.equalTo(result)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.on_demand_availability", Matchers.equalTo(idleOnDemand)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.earliest_available_date", Matchers.equalTo(earliestAvailableDate != null ? earliestAvailableDate.toString() : null)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.high_demand", Matchers.equalTo(highDemand)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.utilization", Matchers.equalTo(utilization)));

        verify(stockLocationService, times(mockedMethodCalledCount))
                .getFleetUtilization(stockLocation.getExternalId(), country.getUtilizationNumberOfDays());
    }

}
