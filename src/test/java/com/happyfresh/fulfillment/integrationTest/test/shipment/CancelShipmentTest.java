package com.happyfresh.fulfillment.integrationTest.test.shipment;

import com.happyfresh.fulfillment.batch.service.BatchSndService;
import com.happyfresh.fulfillment.batch.service.DriverAutoAssignmentService;
import com.happyfresh.fulfillment.batch.service.ShopperAutoAssignmentService;
import com.happyfresh.fulfillment.enabler.service.api.StratoApiService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.MockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.ClusterRepository;
import com.happyfresh.fulfillment.repository.JobRepository;
import com.happyfresh.fulfillment.slot.bean.SlotOptimizationContext;
import com.happyfresh.fulfillment.slot.presenter.SlotOptimizationEvent;
import com.happyfresh.fulfillment.slot.service.SlotOptimizationService;
import com.happyfresh.fulfillment.stockLocation.service.StockLocationService;
import org.assertj.core.util.Lists;
import org.hamcrest.Matchers;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

public class CancelShipmentTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private ClusterRepository clusterRepository;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private MockServerHelper mockServerHelper;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private BatchSndService batchSndService;

    @MockBean
    private StockLocationService stockLocationService;

    @Autowired
    private AgentFactory agentFactory;

    @Autowired
    private StratoApiService stratoApiService;

    @Autowired
    private ShiftFactory shiftFactory;

    @SpyBean
    private DriverAutoAssignmentService driverAutoAssignmentService;

    @SpyBean
    private ShopperAutoAssignmentService shopperAutoAssignmentService;

    @SpyBean
    private SlotOptimizationService slotOptimizationService;

    private User systemAdmin;
    private StockLocation stockLocation;
    private List<Slot> slots;
    private List<StockLocation> stockLocations;

    @BeforeClass
    public static void initProperty() {
        System.setProperty("kafka.listener.enabled","true");
    }

    @AfterClass
    public static void resetProperty() {
        System.setProperty("kafka.listener.enabled","false");
    }

    @Before
    public void setup() {
        systemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);

        stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, systemAdmin);
        stockLocation = stockLocations.get(0);
        slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.LONGER_DELIVERY, systemAdmin);
    }

    @Test
    public void return200WithShipmentBeingCancelledIfFulfillmentHasNotStarted() throws Exception {
        Shipment shipment = shipmentFactory.createShipment(slots.get(0), systemAdmin, "H234567", "H234567", Shipment.State.PENDING);
        Batch shoppingBatch = batchFactory.createBatch(systemAdmin, shipment, slots.get(0), Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(systemAdmin, shipment, slots.get(0), Batch.Type.DELIVERY);

        Shift sShift = shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);
        Shift dShift = shiftFactory.createDriverShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);

        shoppingBatch.setShift(sShift);
        deliveryBatch.setShift(dShift);

        batchFactory.saveAll(List.of(shoppingBatch, deliveryBatch));

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/H234567/cancel")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.order_number", Matchers.equalTo("H234567")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.state", Matchers.equalTo("CANCELLED")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.shopping_job", Matchers.nullValue()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.delivery_job", Matchers.nullValue()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.number", Matchers.equalTo("H234567")));

    }

    @Test
    public void shouldTriggerInvalidateCacheFleetUtilization() throws Exception {
        doNothing().when(stockLocationService).refreshCache(anyLong());
        return200WithShipmentBeingCancelledIfFulfillmentHasNotStarted();
        verify(stockLocationService, times(1)).refreshCache(anyLong());
    }

    @Test
    public void return200WithShipmentBeingCancelledIfFulfillmentHasStarted() throws Exception {
        Shipment shipment = shipmentFactory.createShipment(slots.get(0), systemAdmin, "H234567", "H234567", Shipment.State.PENDING);
        Batch shoppingBatch = batchFactory.createBatch(systemAdmin, shipment, slots.get(0), Batch.Type.SHOPPING, Job.State.FINALIZING);
        Batch deliveryBatch = batchFactory.createBatch(systemAdmin, shipment, slots.get(0), Batch.Type.DELIVERY, Job.State.DELIVERING);

        Shift sShift = shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);
        Shift dShift = shiftFactory.createDriverShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);

        shoppingBatch.setShift(sShift);
        deliveryBatch.setShift(dShift);

        batchFactory.saveAll(List.of(shoppingBatch, deliveryBatch));

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/H234567/cancel")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.order_number", Matchers.equalTo("H234567")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.state", Matchers.equalTo("CANCELLED")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.shopping_job.state", Matchers.equalTo("cancelled")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.delivery_job.state", Matchers.equalTo("cancelled")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.number", Matchers.equalTo("H234567")));
    }

    @Test
    public void shouldTriggerRemoveLocusTask() throws Exception {
        Cluster cluster = stockLocation.getCluster();
        HashMap<String, String> preferences = new HashMap<>();
        preferences.put("enable_locus", "true");
        cluster.setPreferences(preferences);
        clusterRepository.save(cluster);
        Slot slot = slots.get(0);

        Shipment shipment = shipmentFactory.createShipment(slot, systemAdmin, "H234567", "H234567", Shipment.State.PENDING);
        Batch shoppingBatch = batchFactory.createBatch(systemAdmin, shipment, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(systemAdmin, shipment, slot, Batch.Type.DELIVERY);

        Shift sShift = shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);
        Shift dShift = shiftFactory.createDriverShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);

        shoppingBatch.setShift(sShift);
        deliveryBatch.setShift(dShift);

        batchFactory.saveAll(List.of(shoppingBatch, deliveryBatch));

        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        String slotStartDate = slot.getStartTime().format(DateTimeFormatter.BASIC_ISO_DATE);
        String batchId = "batch_1_" + slotStartDate;
        String planId = "plan_1_" + slotStartDate;
        String url = "https://locusapitest.com/v1/client/happyfresh/batch/" + batchId + "/plan/" + planId + "/task";
        mockServer.expect(ExpectedCount.once(), requestTo(url))
                .andExpect(method(HttpMethod.DELETE))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(""));

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/H234567/cancel")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.order_number", Matchers.equalTo("H234567")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.state", Matchers.equalTo("CANCELLED")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.shopping_job", Matchers.nullValue()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.delivery_job", Matchers.nullValue()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.number", Matchers.equalTo("H234567")));

        mockServer.verify();
    }

    @Test
    public void return200WithShipmentBeingCancelledAndShouldNotTriggerFleetSyncMessage() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER, systemAdmin.getTenant());
        agentFactory.createAgent(driver, stockLocation, Agent.State.WORKING);

        Shipment shipment = shipmentFactory.createShipment(slots.get(0), systemAdmin, "H234567", "H234567", Shipment.State.READY);
        Batch shoppingBatch = batchFactory.createBatch(systemAdmin, shipment, slots.get(0), Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(systemAdmin, driver, Lists.newArrayList(shipment), slots.get(0), Batch.Type.DELIVERY);

        Shift sShift = shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);
        Shift dShift = shiftFactory.createDriverShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);

        shoppingBatch.setShift(sShift);
        deliveryBatch.setShift(dShift);

        batchFactory.saveAll(List.of(shoppingBatch, deliveryBatch));

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/H234567/cancel")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.order_number", Matchers.equalTo("H234567")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.state", Matchers.equalTo("CANCELLED")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.shopping_job", Matchers.nullValue()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.delivery_job", Matchers.nullValue()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.number", Matchers.equalTo("H234567")));
    }

    @Test
    public void return200WithShipmentBeingCancelledAndShouldTriggerFleetSyncMessage() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER, systemAdmin.getTenant());
        agentFactory.createAgent(driver, stockLocation, Agent.State.WORKING);

        Shipment shipment = shipmentFactory.createShipment(slots.get(0), systemAdmin, "H234567", "H234567", Shipment.State.READY);
        Batch shoppingBatch = batchFactory.createBatch(systemAdmin, shipment, slots.get(0), Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(systemAdmin, driver, Lists.newArrayList(shipment), slots.get(0), Batch.Type.DELIVERY);

        Shift sShift = shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);
        Shift dShift = shiftFactory.createDriverShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);

        shoppingBatch.setShift(sShift);
        deliveryBatch.setShift(dShift);

        batchFactory.saveAll(List.of(shoppingBatch, deliveryBatch));

        shoppingBatch.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        batchSndService.pickup(deliveryBatch.getId());
        batchSndService.accept(shipment.getNumber());
        batchSndService.deliver(shipment.getNumber());

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/H234567/cancel")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.order_number", Matchers.equalTo("H234567")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.state", Matchers.equalTo("CANCELLED")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.shopping_job.state", Matchers.equalTo("cancelled")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.delivery_job.state", Matchers.equalTo("cancelled")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.number", Matchers.equalTo("H234567")));

    }

    @Test
    public void shouldTriggerStratoReleaseCapcity() throws Exception {
        stockLocation.setEnabler(StockLocation.Enabler.HFC);
        stockLocation.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);
        stockLocationFactory.save(stockLocation);
        Slot slot = slots.get(0);

        Shipment shipment = shipmentFactory.createShipment(slot, systemAdmin, "H234567", "H234567", Shipment.State.PENDING);
        Batch shoppingBatch = batchFactory.createBatch(systemAdmin, shipment, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(systemAdmin, shipment, slot, Batch.Type.DELIVERY);

        Shift sShift = shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);
        Shift dShift = shiftFactory.createDriverShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);

        shoppingBatch.setShift(sShift);
        deliveryBatch.setShift(dShift);

        batchFactory.saveAll(List.of(shoppingBatch, deliveryBatch));

        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        String response = "{" +
                "\"delivery_slot\":\"2021-07-18T22:00:00Z\"," +
                "\"book_available\":false," +
                "\"total_line_item_capacity\":90," +
                "\"total_line_item_available\":73}";
        String url = "https://localhost:3000/store/" + stockLocation.getExternalId() + "/capacity/release";
        mockServer.expect(ExpectedCount.once(), requestTo(url))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(response));

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/H234567/cancel")
            .header("X-Fulfillment-User-Token", systemAdmin.getToken())
            .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken()))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.order_number", Matchers.equalTo("H234567")))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.state", Matchers.equalTo("CANCELLED")))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.shopping_job", Matchers.nullValue()))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.delivery_job", Matchers.nullValue()))
            .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.number", Matchers.equalTo("H234567")));
        mockServer.verify();
    }

    @Test
    public void shouldTriggerStratoExpressReleaseCapcity_expressHFS() throws Exception {
        stockLocation.setEnabler(StockLocation.Enabler.HFC);
        stockLocation.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);
        stockLocationFactory.save(stockLocation);
        Slot slot = slotFactory.createOnDemandSlot(stockLocation, systemAdmin);

        Shipment shipment = shipmentFactory.createShipment(slot, systemAdmin, "H234567", "H234567", Shipment.State.PENDING);
        batchFactory.createBatch(systemAdmin, shipment, slot, Batch.Type.SHOPPING);
        batchFactory.createBatch(systemAdmin, shipment, slot, Batch.Type.ON_DEMAND_DELIVERY);

        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        String url = "https://localhost:3000/store/" + stockLocation.getExternalId() + "/express_capacity/release";
        mockServer.expect(ExpectedCount.once(), requestTo(url))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON));

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/H234567/cancel")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.order_number", Matchers.equalTo("H234567")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.state", Matchers.equalTo("CANCELLED")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.shopping_job", Matchers.nullValue()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.delivery_job", Matchers.nullValue()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.number", Matchers.equalTo("H234567")));
        mockServer.verify();
    }

    @Test
    public void test_cancel_shouldTriggerDriverAutoAssignment() throws Exception {
        Cluster cluster = stockLocation.getCluster();
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");
        clusterRepository.save(cluster);

        slotFactory.deleteAll(slots);
        slots = slotFactory.createLongerDeliverySlots(stockLocation, LocalDateTime.now(), 5, 1, 3, 3, systemAdmin);

        Shipment shipment = shipmentFactory.createShipment(slots.get(0), systemAdmin, "H234567", "H234567", Shipment.State.PENDING);
        Batch sBatch = batchFactory.createBatch(systemAdmin, shipment, slots.get(0), Batch.Type.SHOPPING, Job.State.FINALIZING);
        Batch dBatch = batchFactory.createBatch(systemAdmin, shipment, slots.get(0), Batch.Type.DELIVERY, Job.State.DELIVERING);

        Shift sShift = shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);
        Shift dShift = shiftFactory.createDriverShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);

        sBatch.setShift(sShift);
        dBatch.setShift(dShift);

        batchFactory.saveAll(List.of(sBatch, dBatch));

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/H234567/cancel")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.order_number", Matchers.equalTo("H234567")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.state", Matchers.equalTo("CANCELLED")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.shopping_job.state", Matchers.equalTo("cancelled")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.delivery_job.state", Matchers.equalTo("cancelled")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.number", Matchers.equalTo("H234567")));

        Mockito.verify(driverAutoAssignmentService).publishAutoAssignmentEvent(anyString(), anyString(), eq(SlotOptimizationEvent.AutoAssignmentTriggerEvent.SLOT_OPTIMIZATION));
    }

    @Test
    public void test_cancel_whenNotLastShipmentInBatch_shouldNotTriggerDriverAutoAssignment() throws Exception {
        Cluster cluster = stockLocation.getCluster();
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");
        clusterRepository.save(cluster);

        Shipment shipment = shipmentFactory.createShipment(slots.get(0), systemAdmin, "H234567", "H234567", Shipment.State.PENDING);
        Shipment shipment2 = shipmentFactory.createShipment(slots.get(0), systemAdmin, "H234568", "H234568", Shipment.State.PENDING);
        Batch sBatch = batchFactory.createBatch(systemAdmin, List.of(shipment, shipment2), slots.get(0), Batch.Type.SHOPPING);
        Batch dBatch = batchFactory.createBatch(systemAdmin, List.of(shipment, shipment2), slots.get(0), Batch.Type.DELIVERY);

        Shift sShift = shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);
        Shift dShift = shiftFactory.createDriverShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);

        sBatch.setShift(sShift);
        dBatch.setShift(dShift);

        batchFactory.saveAll(List.of(sBatch, dBatch));

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/H234567/cancel")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.order_number", Matchers.equalTo("H234567")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.state", Matchers.equalTo("CANCELLED")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.number", Matchers.equalTo("H234567")));

        Mockito.verify(driverAutoAssignmentService, never()).publishAutoAssignmentEvent(anyString(), anyString(), eq(SlotOptimizationEvent.AutoAssignmentTriggerEvent.SLOT_OPTIMIZATION));
    }

    @Test
    public void test_cancel_withBatchShiftIsNull_shouldSetShiftIdToZeroBeforePublishAutoAssignmentEvent() throws Exception {
        Cluster cluster = stockLocation.getCluster();
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");
        clusterRepository.save(cluster);

        slotFactory.deleteAll(slots);
        slots = slotFactory.createLongerDeliverySlots(stockLocation, LocalDateTime.now(), 5, 1, 3, 3, systemAdmin);

        Shipment shipment = shipmentFactory.createShipment(slots.get(0), systemAdmin, "H234567", "H234567", Shipment.State.PENDING);
        Batch sBatch = batchFactory.createBatch(systemAdmin, shipment, slots.get(0), Batch.Type.SHOPPING, Job.State.FINALIZING);
        Batch dBatch = batchFactory.createBatch(systemAdmin, shipment, slots.get(0), Batch.Type.DELIVERY, Job.State.DELIVERING);

        Shift sShift = shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);
        Shift dShift = shiftFactory.createDriverShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);

        sBatch.setShift(sShift);
        dBatch.setShift(null);

        batchFactory.saveAll(List.of(sBatch, dBatch));

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/H234567/cancel")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.order_number", Matchers.equalTo("H234567")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.state", Matchers.equalTo("CANCELLED")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.shopping_job.state", Matchers.equalTo("cancelled")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.delivery_job.state", Matchers.equalTo("cancelled")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.number", Matchers.equalTo("H234567")));

        Mockito.verify(driverAutoAssignmentService).publishAutoAssignmentEvent(anyString(), eq("0"), eq(SlotOptimizationEvent.AutoAssignmentTriggerEvent.SLOT_OPTIMIZATION));
    }

    @Test
    public void test_cancel_forTodaySlot_shouldTriggerDriverAndShopperAutoAssignment() throws Exception {
        Cluster cluster = stockLocation.getCluster();
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");
        clusterRepository.save(cluster);
        Map<String, String> stockLocationPreferences = stockLocation.getPreferences();
        stockLocationPreferences.put("enable_shopper_auto_assignment", "true");
        stockLocationFactory.save(stockLocation);

        slotFactory.deleteAll(slots);
        slots = slotFactory.createLongerDeliverySlots(stockLocation, LocalDateTime.now(), 5, 1, 3, 3, systemAdmin);

        Slot firstSlot = slots.get(0);
        Shipment shipment = shipmentFactory.createShipment(firstSlot, systemAdmin, "H234567", "H234567", Shipment.State.PENDING);
        Batch sBatch = batchFactory.createBatch(systemAdmin, shipment, firstSlot, Batch.Type.SHOPPING, Job.State.FINALIZING);
        Batch dBatch = batchFactory.createBatch(systemAdmin, shipment, firstSlot, Batch.Type.DELIVERY, Job.State.DELIVERING);

        Shift sShift = shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);
        Shift dShift = shiftFactory.createDriverShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);

        sBatch.setShift(sShift);
        dBatch.setShift(dShift);

        batchFactory.saveAll(List.of(sBatch, dBatch));

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/H234567/cancel")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.order_number", Matchers.equalTo("H234567")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.state", Matchers.equalTo("CANCELLED")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.shopping_job.state", Matchers.equalTo("cancelled")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.delivery_job.state", Matchers.equalTo("cancelled")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.number", Matchers.equalTo("H234567")));

        Mockito.verify(driverAutoAssignmentService).publishAutoAssignmentEvent(anyString(), anyString(), eq(SlotOptimizationEvent.AutoAssignmentTriggerEvent.SLOT_OPTIMIZATION));
        Mockito.verify(shopperAutoAssignmentService).publishAutoAssignmentEvent(anyString(), anyString(), isNull(), eq(SlotOptimizationEvent.AutoAssignmentTriggerEvent.SLOT_OPTIMIZATION));
        Mockito.verify(slotOptimizationService).publishSlotOptimization(any(SlotOptimizationContext.class));
    }

    @Test
    public void test_cancel_forTomorrowSlot_shouldNotTriggerDriverAndShopperAutoAssignment() throws Exception {
        Cluster cluster = stockLocation.getCluster();
        Map<String, String> preferences = cluster.getPreferences();
        preferences.put("enable_driver_auto_assignment", "true");
        clusterRepository.save(cluster);
        Map<String, String> stockLocationPreferences = stockLocation.getPreferences();
        stockLocationPreferences.put("enable_shopper_auto_assignment", "true");
        stockLocationFactory.save(stockLocation);

        slotFactory.deleteAll(slots);
        slots = slotFactory.createLongerDeliverySlots(stockLocation, LocalDateTime.now().plusDays(1L), 5, 1, 3, 3, systemAdmin);

        Slot firstSlot = slots.get(0);
        Shipment shipment = shipmentFactory.createShipment(firstSlot, systemAdmin, "H234567", "H234567", Shipment.State.PENDING);
        Batch sBatch = batchFactory.createBatch(systemAdmin, shipment, firstSlot, Batch.Type.SHOPPING, Job.State.FINALIZING);
        Batch dBatch = batchFactory.createBatch(systemAdmin, shipment, firstSlot, Batch.Type.DELIVERY, Job.State.DELIVERING);

        Shift sShift = shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);
        Shift dShift = shiftFactory.createDriverShifts(stockLocation, LocalDateTime.now(), 1, 8, 1, systemAdmin).get(0);

        sBatch.setShift(sShift);
        dBatch.setShift(dShift);

        batchFactory.saveAll(List.of(sBatch, dBatch));

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/H234567/cancel")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.order_number", Matchers.equalTo("H234567")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.state", Matchers.equalTo("CANCELLED")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.shopping_job.state", Matchers.equalTo("cancelled")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.delivery_job.state", Matchers.equalTo("cancelled")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.number", Matchers.equalTo("H234567")));

        Mockito.verify(driverAutoAssignmentService, never()).publishAutoAssignmentEvent(anyString(), anyString(), eq(SlotOptimizationEvent.AutoAssignmentTriggerEvent.SLOT_OPTIMIZATION));
        Mockito.verify(shopperAutoAssignmentService, never()).publishAutoAssignmentEvent(anyString(), anyString(), isNull(), eq(SlotOptimizationEvent.AutoAssignmentTriggerEvent.SLOT_OPTIMIZATION));
        Mockito.verify(slotOptimizationService).publishSlotOptimization(any(SlotOptimizationContext.class));
    }

    @Test
    public void test_cancel_whenShipmentIsNotFound_shouldReturn404() throws Exception {
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/H111111/cancel")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isNotFound());

    }

    @Test
    public void test_cancel_whenJobNotFoundDueToShipmentIsAlreadyCancelled_shouldReturnCorrectError() throws Exception {
        Shipment shipment = shipmentFactory.createShipment(slots.get(0), systemAdmin, "H234567", "H234567", Shipment.State.CANCELLED);

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/H234567/cancel")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is4xxClientError())
                .andExpect(jsonPath("$.errors", Matchers.notNullValue()))
                .andExpect(jsonPath("$.errors[0].type", Matchers.is("ShipmentCancelledException")))
                .andExpect(jsonPath("$.errors[0].message", Matchers.is("Shipment is already cancelled.")));

    }

}
