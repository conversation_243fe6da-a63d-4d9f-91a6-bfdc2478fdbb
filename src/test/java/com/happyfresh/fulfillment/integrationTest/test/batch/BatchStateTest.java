package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.JobRepository;
import com.happyfresh.fulfillment.repository.PaymentRepository;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import org.apache.commons.io.IOUtils;
import org.hamcrest.Matchers;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.io.File;
import java.io.FileInputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

public class BatchStateTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private AgentFactory agentFactory;

    @Autowired
    private PaymentRepository paymentRepository;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private TransactionHelper transactionHelper;

    @MockBean
    private OrderService orderService;

    User systemAdmin;

    User externalSystemAdmin;

    User shopper;

    User driver;

    @Before
    public void setUp() throws InterruptedException {
        systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN);
        externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, systemAdmin.getTenant());
        shopper = userFactory.createUserData(Role.Name.SHOPPER, systemAdmin.getTenant());
        driver = userFactory.createUserData(Role.Name.DRIVER, systemAdmin.getTenant());
    }

    private void getBatchAvailable(User user, String stateName, int progress, int total) throws Exception {
        if (total < 0) {
            mvc.perform(MockMvcRequestBuilders.get("/api/v2/batches/available")
                    .header("X-Fulfillment-User-Token", user.getToken())
                    .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                    .contentType(MediaType.APPLICATION_JSON))
                    .andDo(MockMvcResultHandlers.print())
                    .andExpect(MockMvcResultMatchers.jsonPath("$.batches", Matchers.hasSize(0)))
                    .andExpect(MockMvcResultMatchers.status().isOk());
        } else {
            mvc.perform(MockMvcRequestBuilders.get("/api/v2/batches/available")
                    .header("X-Fulfillment-User-Token", user.getToken())
                    .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                    .contentType(MediaType.APPLICATION_JSON))
                    .andDo(MockMvcResultHandlers.print())
                    .andExpect(MockMvcResultMatchers.jsonPath("$.batches[0].state.state_name", Matchers.equalTo(stateName)))
                    .andExpect(MockMvcResultMatchers.jsonPath("$.batches[0].state.total", Matchers.equalTo(total)))
                    .andExpect(MockMvcResultMatchers.jsonPath("$.batches[0].state.progress", Matchers.equalTo(progress)))
                    .andExpect(MockMvcResultMatchers.status().isOk());
        }
    }

    private void getBatchActive(User user, String stateName, int progress, int total) throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/api/batches/active")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.batches[0].state.state_name", Matchers.equalTo(stateName)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batches[0].state.total", Matchers.equalTo(total)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batches[0].state.progress", Matchers.equalTo(progress)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void startBatch(Long batchId, User user, String stateName, int progress, int total) throws Exception {
        String url = String.format("/api/batches/%d/start", batchId);
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.state_name", Matchers.equalTo(stateName)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.total", Matchers.equalTo(total)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.progress", Matchers.equalTo(progress)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void finalizeShipment(Long batchId, String shipmentNumber, User user, JSONObject requestBody) throws Exception {
        String url = String.format("/api/batches/%d/shipments/%s/finalize", batchId, shipmentNumber);
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    private void pay(Long batchId, String shipmentNumber, User user, String stateName, int progress, int total) throws Exception {
        File file1 = new File("src/test/resources/fixtures/receipt1.jpg");
        FileInputStream input1 = new FileInputStream(file1);
        MockMultipartFile multipartFile1 = new MockMultipartFile("receipts[0].attachments[0]", file1.getName(), "image/jpeg", IOUtils.toByteArray(input1));
        input1.close();

        String url = String.format("/api/batches/%d/shipments/%s/pay", batchId, shipmentNumber);
        mvc.perform(MockMvcRequestBuilders.multipart(url)
                .file(multipartFile1)
                .param("receipts[0].number", "SomeNumber")
                .param("receipts[0].total", "10.53")
                .param("receipts[0].tax", "1.66")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.state_name", Matchers.equalTo(stateName)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.total", Matchers.equalTo(total)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.progress", Matchers.equalTo(progress)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void book(Long batchId, User user, String stateName, int progress, int total) throws Exception {
        String url = String.format("/api/batches/%d/book", batchId);
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.state_name", Matchers.equalTo(stateName)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.total", Matchers.equalTo(total)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.progress", Matchers.equalTo(progress)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void pickUp(Long batchId, User user, String stateName, int progress, int total) throws Exception {
        String url = String.format("/api/batches/%d/pickup", batchId);
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.state_name", Matchers.equalTo(stateName)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.total", Matchers.equalTo(total)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.progress", Matchers.equalTo(progress)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void accept(Long batchId, String shipmentNumber, User user, String stateName, int progress, int total) throws Exception {
        String url = String.format("/api/batches/%d/shipments/%s/accept", batchId, shipmentNumber);
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.state_name", Matchers.equalTo(stateName)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.total", Matchers.equalTo(total)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.progress", Matchers.equalTo(progress)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void deliver(Long batchId, String shipmentNumber, User user, String stateName, int progress, int total) throws Exception {
        String url = String.format("/api/batches/%d/shipments/%s/deliver", batchId, shipmentNumber);
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.state_name", Matchers.equalTo(stateName)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.total", Matchers.equalTo(total)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.progress", Matchers.equalTo(progress)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void arrive(Long batchId, String shipmentNumber, User user, String stateName, int progress, int total) throws Exception {
        String url = String.format("/api/batches/%d/shipments/%s/arrive", batchId, shipmentNumber);
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.state_name", Matchers.equalTo(stateName)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.total", Matchers.equalTo(total)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.progress", Matchers.equalTo(progress)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void finalizingDelivery(Long batchId, String shipmentNumber, User user) throws Exception {
        JSONArray itemsArray = new JSONArray();
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);
        String url = String.format("/api/v2/batches/%d/shipments/%s/finalize_delivery", batchId, shipmentNumber);
        mvc.perform(MockMvcRequestBuilders.put(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    private void finish(Long batchId, String shipmentNumber, User user, String stateName, int progress, int total) throws Exception {
        File file1 = new File("src/test/resources/fixtures/signature.jpg");
        FileInputStream input1 = new FileInputStream(file1);
        MockMultipartFile multipartFile = new MockMultipartFile("attachment", file1.getName(), "image/jpeg", IOUtils.toByteArray(input1));
        input1.close();

        String url = String.format("/api/batches/%d/shipments/%s/finish", batchId, shipmentNumber);
        mvc.perform(MockMvcRequestBuilders.multipart(url)
                .file(multipartFile)
                .param("receiver", "Robert")
                .param("cash_amount", "7500.0")
                .param("lat", "-6.2047739")
                .param("lon", "106.7100399")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.state_name", Matchers.equalTo(stateName)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.total", Matchers.equalTo(total)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.state.progress", Matchers.equalTo(progress)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void return200IfSuccessForRangerSingleShipment() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver).get(0);
        Slot anHourAgoSlot = slotFactory.createSlot(stockLocation, driver, 0, -1);
        agentFactory.createAgent(shopper, stockLocation, Agent.State.WORKING);
        agentFactory.createAgent(driver, stockLocation, Agent.State.WORKING);

        Batch rangerBatch = batchFactory.createBatch(driver, anHourAgoSlot, Batch.Type.RANGER);
        Shipment shipment = rangerBatch.getJobs().get(0).getShipment();

        getBatchAvailable(shopper,null, -1, -1);
        getBatchAvailable(driver,"available", 0, 0);

        startBatch(rangerBatch.getId(), driver, "started", 1, 1);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(7500.0);
        Item item = shipment.getItems().get(0);
        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty());
        Item item2 = shipment.getItems().get(1);
        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        finalizeShipment(rangerBatch.getId(), shipment.getNumber(), driver, requestBody);

        pay(rangerBatch.getId(), shipment.getNumber(), driver, "delivering", 0,1 );

        deliver(rangerBatch.getId(), shipment.getNumber(), driver, "delivering", 0, 1);

        arrive(rangerBatch.getId(), shipment.getNumber(), driver, "delivering", 0, 1);

        finish(rangerBatch.getId(), shipment.getNumber(), driver, "completed", 1, 1);
    }

    @Test
    public void return200IfSuccessForRangerSingleShipmentWithPaymentCOD() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver).get(0);
        Slot anHourAgoSlot = slotFactory.createSlot(stockLocation, driver, 0, -1);
        agentFactory.createAgent(shopper, stockLocation, Agent.State.WORKING);
        agentFactory.createAgent(driver, stockLocation, Agent.State.WORKING);

        Batch rangerBatch = batchFactory.createBatch(driver, anHourAgoSlot, Batch.Type.RANGER);
        Shipment shipment = rangerBatch.getJobs().get(0).getShipment();

        getBatchAvailable(shopper,null, -1, -1);
        getBatchAvailable(driver,"available", 0, 0);
        startBatch(rangerBatch.getId(), driver, "started", 1, 1);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(7500.0);
        Item item = shipment.getItems().get(0);
        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty());
        Item item2 = shipment.getItems().get(1);
        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        finalizeShipment(rangerBatch.getId(), shipment.getNumber(), driver, requestBody);

        pay(rangerBatch.getId(), shipment.getNumber(), driver, "delivering", 0,1 );

        deliver(rangerBatch.getId(), shipment.getNumber(), driver, "delivering", 0, 1);

        arrive(rangerBatch.getId(), shipment.getNumber(), driver, "delivering", 0, 1);

        finalizingDelivery(rangerBatch.getId(), shipment.getNumber(), driver);

        getBatchActive(driver, "capture_payment", 0, 1);

        finish(rangerBatch.getId(), shipment.getNumber(), driver, "completed", 1, 1);
    }

    @Test
    public void return200IfSuccessForRangerSingleShipmentWithPaymentCC() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver).get(0);
        Slot anHourAgoSlot = slotFactory.createSlot(stockLocation, driver, 0, -1);
        agentFactory.createAgent(shopper, stockLocation, Agent.State.WORKING);
        agentFactory.createAgent(driver, stockLocation, Agent.State.WORKING);

        Batch rangerBatch = batchFactory.createBatch(driver, anHourAgoSlot, Batch.Type.RANGER);
        Shipment shipment = rangerBatch.getJobs().get(0).getShipment();
        List<Payment> payments = paymentRepository.findAll();
        for (Payment payment : payments) {
            payment.setType(Payment.Type.CREDIT_CARD);
            paymentRepository.save(payment);
        }
        getBatchAvailable(shopper,null, -1, -1);
        getBatchAvailable(driver,"available", 0, 0);

        startBatch(rangerBatch.getId(), driver, "started", 1, 1);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(7500.0);
        Item item = shipment.getItems().get(0);
        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty());
        Item item2 = shipment.getItems().get(1);
        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        finalizeShipment(rangerBatch.getId(), shipment.getNumber(), driver, requestBody);

        pay(rangerBatch.getId(), shipment.getNumber(), driver, "delivering", 0,1 );

        deliver(rangerBatch.getId(), shipment.getNumber(), driver, "delivering", 0, 1);

        arrive(rangerBatch.getId(), shipment.getNumber(), driver, "delivering", 0, 1);

        finalizingDelivery(rangerBatch.getId(), shipment.getNumber(), driver);

        getBatchActive(driver, "capture_payment", 0, 1);

        finish(rangerBatch.getId(), shipment.getNumber(), driver, "completed", 1, 1);
    }

    @Test
    public void return200IfSuccessForRangerMultipleShipmentWithPaymentCOD() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver).get(0);
        Slot anHourAgoSlot = slotFactory.createSlot(stockLocation, driver, 0, -1);
        agentFactory.createAgent(driver, stockLocation, Agent.State.WORKING);


        Shipment shipment1 = shipmentFactory.createShipment(anHourAgoSlot, driver, "xxx1", "xxx1", Shipment.State.READY, 1);
        Shipment shipment2 = shipmentFactory.createShipment(anHourAgoSlot, driver, "xxx2", "xxx2", Shipment.State.READY, 1);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch rangerBatch = batchFactory.createBatch(driver, shipments, anHourAgoSlot, Batch.Type.RANGER);

        getBatchAvailable(driver,"available", 0, 0);

        startBatch(rangerBatch.getId(), driver, "started", 2, 2);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(7500.0);
        Item item = shipment1.getItems().get(0);
        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty());
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        finalizeShipment(rangerBatch.getId(), shipment1.getNumber(), driver, requestBody);

        Mockito.when(orderService.getOrderTotal(shipment2.getNumber())).thenReturn(7500.0);
        Item item2 = shipment2.getItems().get(0);
        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());
        JSONArray itemsArray2 = new JSONArray();
        itemsArray2.put(object2);
        JSONObject requestBody2 = new JSONObject();
        requestBody2.put("items", itemsArray2);

        finalizeShipment(rangerBatch.getId(), shipment2.getNumber(), driver, requestBody2);

        pay(rangerBatch.getId(), shipment1.getNumber(), driver, "payment", 1,2 );

        pay(rangerBatch.getId(), shipment2.getNumber(), driver, "delivering", 0,2 );

        deliver(rangerBatch.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        arrive(rangerBatch.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        finalizingDelivery(rangerBatch.getId(), shipment1.getNumber(), driver);

        getBatchActive(driver, "capture_payment", 0, 2);

        finish(rangerBatch.getId(), shipment1.getNumber(), driver, "delivering", 1, 2);

        deliver(rangerBatch.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        arrive(rangerBatch.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        finalizingDelivery(rangerBatch.getId(), shipment2.getNumber(), driver);

        getBatchActive(driver, "capture_payment", 1, 2);

        finish(rangerBatch.getId(), shipment2.getNumber(), driver, "completed", 2, 2);
    }

    @Test
    public void return200IfSuccessForRangerMultipleShipmentWithPaymentCC() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver).get(0);
        Slot anHourAgoSlot = slotFactory.createSlot(stockLocation, driver, 0, -1);
        agentFactory.createAgent(driver, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(anHourAgoSlot, driver, "xxx1", "xxx1", Shipment.State.READY, 1);
        Shipment shipment2 = shipmentFactory.createShipment(anHourAgoSlot, driver, "xxx2", "xxx2", Shipment.State.READY, 1);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);
        List<Payment> payments = paymentRepository.findAll();
        for (Payment payment : payments) {
            payment.setType(Payment.Type.CREDIT_CARD);
            paymentRepository.save(payment);
        }

        Batch rangerBatch = batchFactory.createBatch(driver, shipments, anHourAgoSlot, Batch.Type.RANGER);

        getBatchAvailable(driver,"available", 0, 0);

        startBatch(rangerBatch.getId(), driver, "started", 2, 2);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(7500.0);
        Item item = shipment1.getItems().get(0);
        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty());
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        finalizeShipment(rangerBatch.getId(), shipment1.getNumber(), driver, requestBody);

        Mockito.when(orderService.getOrderTotal(shipment2.getNumber())).thenReturn(7500.0);
        Item item2 = shipment2.getItems().get(0);
        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());
        JSONArray itemsArray2 = new JSONArray();
        itemsArray2.put(object2);
        JSONObject requestBody2 = new JSONObject();
        requestBody2.put("items", itemsArray2);

        finalizeShipment(rangerBatch.getId(), shipment2.getNumber(), driver, requestBody2);

        pay(rangerBatch.getId(), shipment1.getNumber(), driver, "payment", 1,2 );

        pay(rangerBatch.getId(), shipment2.getNumber(), driver, "delivering", 0,2 );

        deliver(rangerBatch.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        arrive(rangerBatch.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        finalizingDelivery(rangerBatch.getId(), shipment1.getNumber(), driver);

        getBatchActive(driver, "capture_payment", 0, 2);

        finish(rangerBatch.getId(), shipment1.getNumber(), driver, "delivering", 1, 2);

        deliver(rangerBatch.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        arrive(rangerBatch.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        finalizingDelivery(rangerBatch.getId(), shipment2.getNumber(), driver);

        getBatchActive(driver, "capture_payment", 1, 2);

        finish(rangerBatch.getId(), shipment2.getNumber(), driver, "completed", 2, 2);
    }

    @Test
    public void return200IfSuccessForRangerMultipleShipment() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver).get(0);
        Slot anHourAgoSlot = slotFactory.createSlot(stockLocation, driver, 0, -1);
        agentFactory.createAgent(driver, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(anHourAgoSlot, driver, "xxx1", "xxx1", Shipment.State.READY, 1);
        Shipment shipment2 = shipmentFactory.createShipment(anHourAgoSlot, driver, "xxx2", "xxx2", Shipment.State.READY, 1);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch rangerBatch = batchFactory.createBatch(driver, shipments, anHourAgoSlot, Batch.Type.RANGER);

        getBatchAvailable(driver,"available", 0, 0);

        startBatch(rangerBatch.getId(), driver, "started", 2, 2);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(7500.0);
        Item item = shipment1.getItems().get(0);
        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty());
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        finalizeShipment(rangerBatch.getId(), shipment1.getNumber(), driver, requestBody);

        Mockito.when(orderService.getOrderTotal(shipment2.getNumber())).thenReturn(7500.0);
        Item item2 = shipment2.getItems().get(0);
        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());
        JSONArray itemsArray2 = new JSONArray();
        itemsArray2.put(object2);
        JSONObject requestBody2 = new JSONObject();
        requestBody2.put("items", itemsArray2);

        finalizeShipment(rangerBatch.getId(), shipment2.getNumber(), driver, requestBody2);

        pay(rangerBatch.getId(), shipment1.getNumber(), driver, "payment", 1,2 );

        pay(rangerBatch.getId(), shipment2.getNumber(), driver, "delivering", 0,2 );

        deliver(rangerBatch.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        arrive(rangerBatch.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        finish(rangerBatch.getId(), shipment1.getNumber(), driver, "delivering", 1, 2);

        deliver(rangerBatch.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        arrive(rangerBatch.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        finish(rangerBatch.getId(), shipment2.getNumber(), driver, "completed", 2, 2);
    }

    @Test
    public void return200IfSuccessForSingleShipment() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver).get(0);
        Slot anHourAgoSlot = slotFactory.createSlot(stockLocation, driver, 0, -1);
        agentFactory.createAgent(driver, stockLocation, Agent.State.WORKING);

        Batch shoppingBatch = batchFactory.createBatch(driver, anHourAgoSlot, Batch.Type.SHOPPING);
        shoppingBatch.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Shipment shipment = shoppingBatch.getJobs().get(0).getShipment();
        Batch deliveryBatch = batchFactory.createBatch(driver, shipment, anHourAgoSlot, Batch.Type.DELIVERY);

        getBatchAvailable(driver,"available", 0, 0);

        book(deliveryBatch.getId(), driver, "booked" , 1 ,1 );

        pickUp(deliveryBatch.getId(), driver, "picked_up", 1 ,1 );

        accept(deliveryBatch.getId(), shipment.getNumber(), driver, "delivering", 0, 1);

        deliver(deliveryBatch.getId(), shipment.getNumber(), driver, "delivering", 0, 1);

        arrive(deliveryBatch.getId(), shipment.getNumber(), driver, "delivering", 0, 1);

        finish(deliveryBatch.getId(), shipment.getNumber(), driver, "completed", 1, 1);
    }

    @Test
    public void return200IfSuccessForMultipleShipment() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver).get(0);
        Slot anHourAgoSlot = slotFactory.createSlot(stockLocation, driver, 0, -1);
        agentFactory.createAgent(driver, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(anHourAgoSlot, driver);
        Shipment shipment2 = shipmentFactory.createShipment(anHourAgoSlot, driver);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch shoppingBatch = batchFactory.createBatch(driver, shipments, anHourAgoSlot, Batch.Type.SHOPPING);
        shoppingBatch.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch = batchFactory.createBatch(driver, shipments, anHourAgoSlot, Batch.Type.DELIVERY);

        getBatchAvailable(driver,"available", 0, 0);

        book(deliveryBatch.getId(), driver, "booked" , 2 ,2 );

        pickUp(deliveryBatch.getId(), driver, "picked_up", 2, 2);

        accept(deliveryBatch.getId(), shipment1.getNumber(), driver, "accepted", 1, 2);

        accept(deliveryBatch.getId(), shipment2.getNumber(), driver, "delivering", 0, 2);

        deliver(deliveryBatch.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        arrive(deliveryBatch.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        finish(deliveryBatch.getId(), shipment1.getNumber(), driver, "delivering", 1, 2);

        deliver(deliveryBatch.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        arrive(deliveryBatch.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        finish(deliveryBatch.getId(), shipment2.getNumber(), driver, "completed", 2, 2);
    }

    @Test
    public void return200IfSuccessForOnDemandRangerSingleShipment() throws Exception {
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, externalSystemAdmin);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setOpenAt(LocalTime.of(03, 00));
        stockLocation.setCloseAt(LocalTime.of(13, 00));
        stockLocation.setShopperAveragePickingTimePerUniqItem(5);
        stockLocation.setType(StockLocation.Type.SPECIAL);
        stockLocation.setEnableOnDemandDelivery(true);
        stockLocationFactory.save(stockLocation);

        LocalDate tomorrow = LocalDateTime.now().plusDays(1).toLocalDate();
        Slot slot = slotFactory.createOnDemandSlot(stockLocation,externalSystemAdmin);


        User onDemandRanger1 = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER,externalSystemAdmin.getTenant());
        Agent agent1 = agentFactory.createAgent(onDemandRanger1,stockLocation,Agent.State.WORKING);

        Batch batch = batchFactory.createBatch(externalSystemAdmin, slot, Batch.Type.ON_DEMAND);
        batch.setUser(onDemandRanger1);
        Job job = batch.getJobs().get(0);
        job.setState(Job.State.STARTED);
        transactionHelper.withNewTransaction(() -> {
                batchRepository.save(batch);
                jobRepository.save(job);
        });
        Shipment shipment = batch.getJobs().get(0).getShipment();

        getBatchActive(onDemandRanger1, "started", 1, 1);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(7500.0);
        Item item = shipment.getItems().get(0);
        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty());
        Item item2 = shipment.getItems().get(1);
        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        finalizeShipment(batch.getId(), shipment.getNumber(), onDemandRanger1, requestBody);

        pay(batch.getId(), shipment.getNumber(), onDemandRanger1, "delivering", 0,1);

        deliver(batch.getId(), shipment.getNumber(), onDemandRanger1, "delivering", 0, 1);

        arrive(batch.getId(), shipment.getNumber(), onDemandRanger1, "delivering", 0, 1);

        finish(batch.getId(), shipment.getNumber(), onDemandRanger1, "completed", 1, 1);
    }

    @Test
    public void return200IfSuccessForRangerMultipleShipmentWithPaymentEWallet() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver).get(0);
        Slot anHourAgoSlot = slotFactory.createSlot(stockLocation, driver, 0, -1);
        agentFactory.createAgent(driver, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(anHourAgoSlot, driver, "xxx1", "xxx1", Shipment.State.READY, 1);
        Shipment shipment2 = shipmentFactory.createShipment(anHourAgoSlot, driver, "xxx2", "xxx2", Shipment.State.READY, 1);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);
        List<Payment> payments = paymentRepository.findAll();
        for (Payment payment : payments) {
            payment.setType(Payment.Type.E_WALLET);
            paymentRepository.save(payment);
        }

        Batch rangerBatch = batchFactory.createBatch(driver, shipments, anHourAgoSlot, Batch.Type.RANGER);

        getBatchAvailable(driver,"available", 0, 0);

        startBatch(rangerBatch.getId(), driver, "started", 2, 2);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(7500.0);
        Item item = shipment1.getItems().get(0);
        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty());
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        finalizeShipment(rangerBatch.getId(), shipment1.getNumber(), driver, requestBody);

        Mockito.when(orderService.getOrderTotal(shipment2.getNumber())).thenReturn(7500.0);
        Item item2 = shipment2.getItems().get(0);
        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());
        JSONArray itemsArray2 = new JSONArray();
        itemsArray2.put(object2);
        JSONObject requestBody2 = new JSONObject();
        requestBody2.put("items", itemsArray2);

        finalizeShipment(rangerBatch.getId(), shipment2.getNumber(), driver, requestBody2);

        pay(rangerBatch.getId(), shipment1.getNumber(), driver, "payment", 1,2 );

        pay(rangerBatch.getId(), shipment2.getNumber(), driver, "delivering", 0,2 );

        deliver(rangerBatch.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        arrive(rangerBatch.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        finalizingDelivery(rangerBatch.getId(), shipment1.getNumber(), driver);

        getBatchActive(driver, "capture_payment", 0, 2);

        finish(rangerBatch.getId(), shipment1.getNumber(), driver, "delivering", 1, 2);

        deliver(rangerBatch.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        arrive(rangerBatch.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        finalizingDelivery(rangerBatch.getId(), shipment2.getNumber(), driver);

        getBatchActive(driver, "capture_payment", 1, 2);

        finish(rangerBatch.getId(), shipment2.getNumber(), driver, "completed", 2, 2);
    }

    @Test
    public void return200IfSuccessForMultipleShipmentInMultipleBatchesCase1() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver).get(0);
        Slot anHourAgoSlot = slotFactory.createSlot(stockLocation, driver, 0, -1);
        agentFactory.createAgent(driver, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(anHourAgoSlot, driver);
        Shipment shipment2 = shipmentFactory.createShipment(anHourAgoSlot, driver);
        List<Shipment> shipmentsBatch1 = Lists.newArrayList(shipment1, shipment2);

        Batch shoppingBatch1 = batchFactory.createBatch(driver, shipmentsBatch1, anHourAgoSlot, Batch.Type.SHOPPING);
        shoppingBatch1.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch1 = batchFactory.createBatch(driver, shipmentsBatch1, anHourAgoSlot, Batch.Type.DELIVERY);

        Shipment shipment3 = shipmentFactory.createShipment(anHourAgoSlot, driver);
        Shipment shipment4 = shipmentFactory.createShipment(anHourAgoSlot, driver);
        List<Shipment> shipmentsBatch2 = Lists.newArrayList(shipment3, shipment4);

        Batch shoppingBatch2 = batchFactory.createBatch(driver, shipmentsBatch2, anHourAgoSlot, Batch.Type.SHOPPING);
        shoppingBatch2.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch2 = batchFactory.createBatch(driver, shipmentsBatch2, anHourAgoSlot, Batch.Type.DELIVERY);

        book(deliveryBatch1.getId(), driver, "booked" , 2 ,2 );

        pickUp(deliveryBatch1.getId(), driver, "picked_up", 2, 2);

        accept(deliveryBatch1.getId(), shipment1.getNumber(), driver, "accepted", 1, 2);

        accept(deliveryBatch1.getId(), shipment2.getNumber(), driver, "delivering", 0, 2);

        book(deliveryBatch2.getId(), driver, "booked" , 2 ,2 );

        pickUp(deliveryBatch2.getId(), driver, "picked_up", 2, 2);

        accept(deliveryBatch2.getId(), shipment3.getNumber(), driver, "accepted", 1, 2);

        accept(deliveryBatch2.getId(), shipment4.getNumber(), driver, "delivering", 0, 2);

        // Deliver shipment 1

        deliver(deliveryBatch1.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        arrive(deliveryBatch1.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        finish(deliveryBatch1.getId(), shipment1.getNumber(), driver, "delivering", 1, 2);

        // Deliver shipment 3

        deliver(deliveryBatch2.getId(), shipment3.getNumber(), driver, "delivering", 0, 2);

        arrive(deliveryBatch2.getId(), shipment3.getNumber(), driver, "delivering", 0, 2);

        finish(deliveryBatch2.getId(), shipment3.getNumber(), driver, "delivering", 1, 2);

        // Deliver shipment 2

        deliver(deliveryBatch1.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        arrive(deliveryBatch1.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        finish(deliveryBatch1.getId(), shipment2.getNumber(), driver, "completed", 2, 2);

        // Deliver shipment 2

        deliver(deliveryBatch2.getId(), shipment4.getNumber(), driver, "delivering", 1, 2);

        arrive(deliveryBatch2.getId(), shipment4.getNumber(), driver, "delivering", 1, 2);

        finish(deliveryBatch2.getId(), shipment4.getNumber(), driver, "completed", 2, 2);
    }

    @Test
    public void return200IfSuccessForMultipleShipmentInMultipleBatchesCase2() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver).get(0);
        Slot anHourAgoSlot = slotFactory.createSlot(stockLocation, driver, 0, -1);
        agentFactory.createAgent(driver, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(anHourAgoSlot, driver);
        Shipment shipment2 = shipmentFactory.createShipment(anHourAgoSlot, driver);
        List<Shipment> shipmentsBatch1 = Lists.newArrayList(shipment1, shipment2);

        Batch shoppingBatch1 = batchFactory.createBatch(driver, shipmentsBatch1, anHourAgoSlot, Batch.Type.SHOPPING);
        shoppingBatch1.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch1 = batchFactory.createBatch(driver, shipmentsBatch1, anHourAgoSlot, Batch.Type.DELIVERY);

        Shipment shipment3 = shipmentFactory.createShipment(anHourAgoSlot, driver);
        Shipment shipment4 = shipmentFactory.createShipment(anHourAgoSlot, driver);
        List<Shipment> shipmentsBatch2 = Lists.newArrayList(shipment3, shipment4);

        Batch shoppingBatch2 = batchFactory.createBatch(driver, shipmentsBatch2, anHourAgoSlot, Batch.Type.SHOPPING);
        shoppingBatch2.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch2 = batchFactory.createBatch(driver, shipmentsBatch2, anHourAgoSlot, Batch.Type.DELIVERY);

        book(deliveryBatch1.getId(), driver, "booked" , 2 ,2 );

        pickUp(deliveryBatch1.getId(), driver, "picked_up", 2, 2);

        accept(deliveryBatch1.getId(), shipment1.getNumber(), driver, "accepted", 1, 2);

        accept(deliveryBatch1.getId(), shipment2.getNumber(), driver, "delivering", 0, 2);

        // Deliver shipment 1

        deliver(deliveryBatch1.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        arrive(deliveryBatch1.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        finish(deliveryBatch1.getId(), shipment1.getNumber(), driver, "delivering", 1, 2);

        book(deliveryBatch2.getId(), driver, "booked" , 2 ,2 );

        pickUp(deliveryBatch2.getId(), driver, "picked_up", 2, 2);

        accept(deliveryBatch2.getId(), shipment3.getNumber(), driver, "accepted", 1, 2);

        accept(deliveryBatch2.getId(), shipment4.getNumber(), driver, "delivering", 0, 2);

        // Deliver shipment 3

        deliver(deliveryBatch2.getId(), shipment3.getNumber(), driver, "delivering", 0, 2);

        arrive(deliveryBatch2.getId(), shipment3.getNumber(), driver, "delivering", 0, 2);

        finish(deliveryBatch2.getId(), shipment3.getNumber(), driver, "delivering", 1, 2);

        // Deliver shipment 2

        deliver(deliveryBatch1.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        arrive(deliveryBatch1.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        finish(deliveryBatch1.getId(), shipment2.getNumber(), driver, "completed", 2, 2);

        // Deliver shipment 4

        deliver(deliveryBatch2.getId(), shipment4.getNumber(), driver, "delivering", 1, 2);

        arrive(deliveryBatch2.getId(), shipment4.getNumber(), driver, "delivering", 1, 2);

        finish(deliveryBatch2.getId(), shipment4.getNumber(), driver, "completed", 2, 2);
    }

    @Test
    public void return200IfSuccessForMultipleShipmentInMultipleBatchesCase3() throws Exception {
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver).get(0);
        Slot anHourAgoSlot = slotFactory.createSlot(stockLocation, driver, 0, -1);
        agentFactory.createAgent(driver, stockLocation, Agent.State.WORKING);

        Shipment shipment1 = shipmentFactory.createShipment(anHourAgoSlot, driver);
        Shipment shipment2 = shipmentFactory.createShipment(anHourAgoSlot, driver);
        List<Shipment> shipmentsBatch1 = Lists.newArrayList(shipment1, shipment2);

        Batch shoppingBatch1 = batchFactory.createBatch(driver, shipmentsBatch1, anHourAgoSlot, Batch.Type.SHOPPING);
        shoppingBatch1.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch1 = batchFactory.createBatch(driver, shipmentsBatch1, anHourAgoSlot, Batch.Type.DELIVERY);

        Shipment shipment3 = shipmentFactory.createShipment(anHourAgoSlot, driver);
        Shipment shipment4 = shipmentFactory.createShipment(anHourAgoSlot, driver);
        List<Shipment> shipmentsBatch2 = Lists.newArrayList(shipment3, shipment4);

        Batch shoppingBatch2 = batchFactory.createBatch(driver, shipmentsBatch2, anHourAgoSlot, Batch.Type.SHOPPING);
        shoppingBatch2.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch2 = batchFactory.createBatch(driver, shipmentsBatch2, anHourAgoSlot, Batch.Type.DELIVERY);

        book(deliveryBatch1.getId(), driver, "booked" , 2 ,2 );

        pickUp(deliveryBatch1.getId(), driver, "picked_up", 2, 2);

        accept(deliveryBatch1.getId(), shipment1.getNumber(), driver, "accepted", 1, 2);

        accept(deliveryBatch1.getId(), shipment2.getNumber(), driver, "delivering", 0, 2);

        // Deliver shipment 1

        deliver(deliveryBatch1.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        arrive(deliveryBatch1.getId(), shipment1.getNumber(), driver, "delivering", 0, 2);

        finish(deliveryBatch1.getId(), shipment1.getNumber(), driver, "delivering", 1, 2);

        book(deliveryBatch2.getId(), driver, "booked" , 2 ,2 );

        // Deliver shipment 2

        deliver(deliveryBatch1.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        arrive(deliveryBatch1.getId(), shipment2.getNumber(), driver, "delivering", 1, 2);

        finish(deliveryBatch1.getId(), shipment2.getNumber(), driver, "completed", 2, 2);


        pickUp(deliveryBatch2.getId(), driver, "picked_up", 2, 2);

        accept(deliveryBatch2.getId(), shipment3.getNumber(), driver, "accepted", 1, 2);

        accept(deliveryBatch2.getId(), shipment4.getNumber(), driver, "delivering", 0, 2);

        // Deliver shipment 3

        deliver(deliveryBatch2.getId(), shipment3.getNumber(), driver, "delivering", 0, 2);

        arrive(deliveryBatch2.getId(), shipment3.getNumber(), driver, "delivering", 0, 2);

        finish(deliveryBatch2.getId(), shipment3.getNumber(), driver, "delivering", 1, 2);

        // Deliver shipment 4

        deliver(deliveryBatch2.getId(), shipment4.getNumber(), driver, "delivering", 1, 2);

        arrive(deliveryBatch2.getId(), shipment4.getNumber(), driver, "delivering", 1, 2);

        finish(deliveryBatch2.getId(), shipment4.getNumber(), driver, "completed", 2, 2);
    }
}
