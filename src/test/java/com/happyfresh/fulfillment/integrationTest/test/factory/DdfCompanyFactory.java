package com.happyfresh.fulfillment.integrationTest.test.factory;

import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.repository.ClusterRepository;
import com.happyfresh.fulfillment.repository.DdfCompanyRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Service
@Profile("it")
public class DdfCompanyFactory {

    @Autowired
    private DdfCompanyRepository repo;

    public DdfCompany create(Company company, boolean usingStoreDdf, User creator) {
        return create(
            company,
            0,
            1000,
            usingStoreDdf,
            BigDecimal.valueOf(1000),
            5D,
            BigDecimal.valueOf(1000),
            BigDecimal.valueOf(1000),
            creator);
    }

    public DdfCompany create(Company company, int lowerVolumeLimit, int upperVolumeLimit, boolean usingStoreDdf, User creator) {
        return create(
            company,
            lowerVolumeLimit,
            upperVolumeLimit,
            usingStoreDdf,
            BigDecimal.valueOf(0),
            0D,
            BigDecimal.valueOf(0),
            BigDecimal.valueOf(0),
            creator);
    }

    public DdfCompany create(Company company,
                             int lowerVolumeLimit,
                             int upperVolumeLimit,
                             boolean usingStoreDdf,
                             BigDecimal minimumFee,
                             double minimumFeeKm,
                             BigDecimal fixedFee,
                             BigDecimal costPerKm,
                             User creator) {
        DdfCompany ddfCompany = new DdfCompany();
        ddfCompany.setCompany(company);
        ddfCompany.setLowerVolumeLimit(lowerVolumeLimit);
        ddfCompany.setUpperVolumeLimit(upperVolumeLimit);
        ddfCompany.setStoreDdf(usingStoreDdf);
        ddfCompany.setMinimumFee(minimumFee);
        ddfCompany.setMinimumFeeKm(minimumFeeKm);
        ddfCompany.setFixedFee(fixedFee);
        ddfCompany.setCostPerKm(costPerKm);

        ddfCompany.setTenant(creator.getTenant());
        ddfCompany.setCreatedBy(creator.getId());
        if (repo != null)
            repo.save(ddfCompany);

        return ddfCompany;
    }

    public List<DdfCompany> createList(Company company, User creator) {
        List<DdfCompany> ddfCompanyList = new ArrayList<>();

        DdfCompany ddfCompany = new DdfCompany();
        ddfCompany.setCompany(company);
        ddfCompany.setStoreDdf(false);
        ddfCompany.setLowerVolumeLimit(1001);
        ddfCompany.setUpperVolumeLimit(10000);
        ddfCompany.setMinimumFee(BigDecimal.valueOf(5000));
        ddfCompany.setMinimumFeeKm(5D);
        ddfCompany.setFixedFee(BigDecimal.valueOf(12000));
        ddfCompany.setCostPerKm(BigDecimal.valueOf(10000));
        ddfCompany.setTenant(creator.getTenant());
        ddfCompany.setCreatedBy(creator.getId());
        if (repo != null)
            repo.save(ddfCompany);
        ddfCompanyList.add(ddfCompany);

        DdfCompany ddfCompany2 = new DdfCompany();
        ddfCompany2.setCompany(company);
        ddfCompany2.setStoreDdf(true);
        ddfCompany2.setLowerVolumeLimit(0);
        ddfCompany2.setUpperVolumeLimit(1000);
        ddfCompany2.setMinimumFee(BigDecimal.valueOf(0));
        ddfCompany2.setMinimumFeeKm(0D);
        ddfCompany2.setFixedFee(BigDecimal.valueOf(0));
        ddfCompany2.setCostPerKm(BigDecimal.valueOf(0));
        ddfCompany2.setTenant(creator.getTenant());
        ddfCompany2.setCreatedBy(creator.getId());
        if (repo != null)
            repo.save(ddfCompany2);
        ddfCompanyList.add(ddfCompany2);

        return ddfCompanyList;
    }

    public DdfCompany save(DdfCompany ddfCompany) {
        return repo.save(ddfCompany);
    }
}
