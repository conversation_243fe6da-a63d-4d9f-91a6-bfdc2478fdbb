package com.happyfresh.fulfillment.integrationTest.test.receipt;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.batch.mapper.ReceiptMapper;
import com.happyfresh.fulfillment.common.service.JedisCacheService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.receipt.presenter.PendingReceiptPresenter;
import com.happyfresh.fulfillment.receipt.service.ReceiptService;
import org.hamcrest.Matchers;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

public class GetPendingReceiptTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private ReceiptFactory receiptFactory;

    @Autowired
    private JobFactory jobFactory;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private JedisCacheService cacheService;

    @SpyBean
    private ReceiptService receiptService;

    @Autowired
    private ReceiptMapper receiptMapper;

    @Test
    public void returnPendingReceipt() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);

        //Create 1 unfinished receipt
        Slot slot1 = slotFactory.createSlot(stockLocation, user, 0, 1);
        Shipment shipment1 = shipmentFactory.createShipment(slot1, user);
        Batch batch1 = batchFactory.createBatch(user, Arrays.asList(shipment1), slot1, Batch.Type.SHOPPING);
        batch1.setUser(user);
        batchFactory.save(batch1);
        receiptFactory.createReceipt(shipment1, "Receipt-1");

        //Create 1 finished receipt
        Slot slot2 = slotFactory.createSlot(stockLocation, user, 0, 2);
        Shipment shipment2 = shipmentFactory.createShipment(slot2, user);
        Batch batch2 = batchFactory.createBatch(user, Arrays.asList(shipment2), slot2, Batch.Type.SHOPPING);
        batch2.setUser(user);
        batchFactory.save(batch2);
        receiptFactory.createReceipt(shipment2, "Receipt-2", true);

        mvc.perform(MockMvcRequestBuilders.get("/api/receipts/pending")
                        .header("X-Fulfillment-User-Token", user.getToken())
                        .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.receipts", Matchers.hasSize(1)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].number", Matchers.equalTo("Receipt-1")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].stock_location_name", Matchers.equalTo(stockLocation.getName())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].order_number", Matchers.equalTo(shipment1.getOrderNumber())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].shipment_number", Matchers.equalTo(shipment1.getNumber())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].batch_id", Matchers.equalTo(1)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].tax_required", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].date", Matchers.equalTo(slot1.getStartTime().toLocalDate().toString())));
    }

    @Test
    public void getPendingReceiptCached_shouldGetFromCache() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);

        // Arrange: direct fill Redis
        Slot slot = slotFactory.createSlot(stockLocation, user, 0, 1);
        Shipment shipment = shipmentFactory.createShipment(slot, user);
        Receipt receipt = receiptFactory.createReceipt(shipment, "Receipt-1");
        PendingReceiptPresenter presenter = receiptMapper.receiptToPendingReceiptPresenter(receipt);

        Assert.assertNotNull(receipt.getShipment().getSlot().getStartTime());
        Assert.assertEquals(receipt.getShipment().getSlot().getStartTime(), presenter.getDateTime());

        String cacheKey = "V2:PENDING_RECEIPT:user_id:" + user.getId();
        String cacheValue = mapper.writeValueAsString(List.of(presenter));
        cacheService.setWithExpiry(cacheKey, cacheValue, 10);

        mvc.perform(MockMvcRequestBuilders.get("/api/receipts/pending")
                        .header("X-Fulfillment-User-Token", user.getToken())
                        .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andDo(MockMvcResultHandlers.print())
                .andExpect(jsonPath("$.receipts", Matchers.hasSize(1)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].number", Matchers.equalTo("Receipt-1")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].stock_location_name", Matchers.equalTo(stockLocation.getName())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].order_number", Matchers.equalTo(presenter.getOrderNumber())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].date", Matchers.equalTo(slot.getStartTime().toLocalDate().toString())));
    }

    @Test
    public void getPendingReceiptCached_after1stTime_shouldGetFromCache() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);

        //Create 1 unfinished receipt
        Slot slot1 = slotFactory.createSlot(stockLocation, user, 0, 1);
        Shipment shipment1 = shipmentFactory.createShipment(slot1, user);
        Batch batch1 = batchFactory.createBatch(user, Arrays.asList(shipment1), slot1, Batch.Type.SHOPPING);
        batch1.setUser(user);
        batchFactory.save(batch1);
        receiptFactory.createReceipt(shipment1, "Receipt-1");

        mvc.perform(MockMvcRequestBuilders.get("/api/receipts/pending")
                        .header("X-Fulfillment-User-Token", user.getToken())
                        .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.receipts", Matchers.hasSize(1)));

        mvc.perform(MockMvcRequestBuilders.get("/api/receipts/pending")
                        .header("X-Fulfillment-User-Token", user.getToken())
                        .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.receipts", Matchers.hasSize(1)));

        mvc.perform(MockMvcRequestBuilders.get("/api/receipts/pending")
                        .header("X-Fulfillment-User-Token", user.getToken())
                        .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.receipts", Matchers.hasSize(1)));

        verify(receiptService, times(1)).getPendingReceipt(any(User.class));
    }

    @Test
    public void getPendingReceiptCached_onAddReceipt_shouldInvalidate() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);

        // Create 1 unfinished receipt
        Slot slot1 = slotFactory.createSlot(stockLocation, user, 0, 1);
        Shipment shipment1 = shipmentFactory.createShipment(slot1, user);
        Batch batch1 = batchFactory.createBatch(user, List.of(shipment1), slot1, Batch.Type.SHOPPING);
        batch1.setUser(user);
        batchFactory.save(batch1);
        shipment1.setJobs(batch1.getJobs());
        shipmentFactory.save(shipment1);
        receiptFactory.createReceipt(shipment1, "Receipt-1");

        mvc.perform(MockMvcRequestBuilders.get("/api/receipts/pending")
                        .header("X-Fulfillment-User-Token", user.getToken())
                        .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.receipts", Matchers.hasSize(1)));

        String cacheKey = "V2:PENDING_RECEIPT:user_id:" + user.getId();
        Assert.assertNotNull(cacheService.get(cacheKey));

        // Create 1 unfinished receipt
        Shipment shipment2 = shipmentFactory.createShipment(slot1, user);
        Batch batch2 = batchFactory.createBatch(user, List.of(shipment2), slot1, Batch.Type.SHOPPING);
        batch2.setUser(user);
        batchFactory.save(batch2);
        shipment2.setJobs(batch1.getJobs());
        shipmentFactory.save(shipment2);
        receiptFactory.createReceipt(shipment2, "Receipt-2");

        Assert.assertTrue(cacheService.get(cacheKey).isEmpty());
    }

    @Test
    public void returnPendingReceiptForRanger() throws Exception {
        User user = userFactory.createUserData(Role.Name.DRIVER);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);
        stockLocation.setType(StockLocation.Type.SPECIAL);

        //Create 1 finished jobs, receipt not finished
        Slot slot1 = slotFactory.createSlot(stockLocation, user, 0, 1);
        Shipment shipment1 = shipmentFactory.createShipment(slot1, user);
        Batch batch1 = batchFactory.createBatch(user, Arrays.asList(shipment1), slot1, Batch.Type.RANGER);
        batch1.setUser(user);
        batchFactory.save(batch1);
        receiptFactory.createReceipt(shipment1, "Receipt-1");

        //Create 1 finished jobs, receipt finished
        Slot slot2 = slotFactory.createSlot(stockLocation, user, 0, 2);
        Shipment shipment2 = shipmentFactory.createShipment(slot2, user);
        Batch batch2 = batchFactory.createBatch(user, Arrays.asList(shipment2), slot2, Batch.Type.RANGER);
        batch2.setUser(user);
        batchFactory.save(batch2);
        receiptFactory.createReceipt(shipment2, "Receipt-2", true);

        mvc.perform(MockMvcRequestBuilders.get("/api/receipts/pending")
                        .header("X-Fulfillment-User-Token", user.getToken())
                        .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.receipts", Matchers.hasSize(1)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].number", Matchers.equalTo("Receipt-1")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].stock_location_name", Matchers.equalTo(stockLocation.getName())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].order_number", Matchers.equalTo(shipment1.getOrderNumber())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].shipment_number", Matchers.equalTo(shipment1.getNumber())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].batch_id", Matchers.equalTo(1)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].tax_required", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].date", Matchers.equalTo(slot1.getStartTime().toLocalDate().toString())));
    }

    @Test
    public void returnPendingReceipts_forOnDemandRanger() throws Exception {
        User user = userFactory.createUserData(Role.Name.ON_DEMAND_RANGER);

        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);
        stockLocation.setType(StockLocation.Type.SPECIAL);
        stockLocationFactory.save(stockLocation);

        //Create 1 finished jobs, receipt not finished
        Slot slot1 = slotFactory.createSlot(stockLocation, user, 0, 1);
        Shipment shipment1 = shipmentFactory.createShipment(slot1, user);
        Batch batch1 = batchFactory.createBatch(user, Arrays.asList(shipment1), slot1, Batch.Type.ON_DEMAND);
        batch1.setUser(user);
        batchFactory.save(batch1);
        receiptFactory.createReceipt(shipment1, "Receipt-1");

        //Create 1 finished jobs, receipt finished
        Slot slot2 = slotFactory.createSlot(stockLocation, user, 0, 2);
        Shipment shipment2 = shipmentFactory.createShipment(slot2, user);
        Batch batch2 = batchFactory.createBatch(user, Arrays.asList(shipment2), slot2, Batch.Type.ON_DEMAND);
        batch2.setUser(user);
        batchFactory.save(batch2);
        receiptFactory.createReceipt(shipment2, "Receipt-2", true);

        mvc.perform(MockMvcRequestBuilders.get("/api/receipts/pending")
                        .header("X-Fulfillment-User-Token", user.getToken())
                        .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.receipts", Matchers.hasSize(1)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].number", Matchers.equalTo("Receipt-1")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].stock_location_name", Matchers.equalTo(stockLocation.getName())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].order_number", Matchers.equalTo(shipment1.getOrderNumber())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].shipment_number", Matchers.equalTo(shipment1.getNumber())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].batch_id", Matchers.equalTo(1)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].tax_required", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].date", Matchers.equalTo(slot1.getStartTime().toLocalDate().toString())));
    }

    @Test
    public void returnPendingReceipts_forOnDemandShopper() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);

        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);
        stockLocation.setType(StockLocation.Type.SPECIAL);
        stockLocationFactory.save(stockLocation);

        //Create 1 finished jobs, receipt not finished
        Slot slot1 = slotFactory.createSlot(stockLocation, user, 0, 1);
        Shipment shipment1 = shipmentFactory.createShipment(slot1, user);
        Batch batch1 = batchFactory.createBatch(user, Collections.singletonList(shipment1), slot1, Batch.Type.ON_DEMAND_SHOPPING);
        batch1.setUser(user);
        batchFactory.save(batch1);
        receiptFactory.createReceipt(shipment1, "Receipt-1");

        //Create 1 finished jobs, receipt finished
        Slot slot2 = slotFactory.createSlot(stockLocation, user, 0, 2);
        Shipment shipment2 = shipmentFactory.createShipment(slot2, user);
        Batch batch2 = batchFactory.createBatch(user, Collections.singletonList(shipment2), slot2, Batch.Type.ON_DEMAND_SHOPPING);
        batch2.setUser(user);
        batchFactory.save(batch2);
        receiptFactory.createReceipt(shipment2, "Receipt-2", true);

        mvc.perform(MockMvcRequestBuilders.get("/api/receipts/pending")
                        .header("X-Fulfillment-User-Token", user.getToken())
                        .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.receipts", Matchers.hasSize(1)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].number", Matchers.equalTo("Receipt-1")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].stock_location_name", Matchers.equalTo(stockLocation.getName())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].order_number", Matchers.equalTo(shipment1.getOrderNumber())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].shipment_number", Matchers.equalTo(shipment1.getNumber())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].batch_id", Matchers.equalTo(1)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].tax_required", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].date", Matchers.equalTo(slot1.getStartTime().toLocalDate().toString())));
    }

    @Test
    public void returnPendingReceipt_maxOneWeekAgo() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);

        //Create 1 unfinished receipt
        Slot slot1 = slotFactory.createSlot(stockLocation, user, 0, 1);
        Shipment shipment1 = shipmentFactory.createShipment(slot1, user);
        Batch batch1 = batchFactory.createBatch(user, Arrays.asList(shipment1), slot1, Batch.Type.SHOPPING);
        batch1.setUser(user);
        batchFactory.save(batch1);
        receiptFactory.createReceipt(shipment1, "Receipt-1");

        //Create 1 unfinished receipt one week ago
        Slot slot2 = slotFactory.createSlot(stockLocation, user, 0, 2);
        Shipment shipment2 = shipmentFactory.createShipment(slot2, user);
        Batch batch2 = batchFactory.createBatch(user, Arrays.asList(shipment2), slot2, Batch.Type.SHOPPING);
        batch2.setUser(user);
        batch2.setStartTime(batch2.getStartTime().minusDays(8L));
        batch2.setEndTime(batch2.getEndTime().minusDays(8L));
        batchFactory.save(batch2);
        receiptFactory.createReceipt(shipment2, "Receipt-2");

        mvc.perform(MockMvcRequestBuilders.get("/api/receipts/pending")
                        .header("X-Fulfillment-User-Token", user.getToken())
                        .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.receipts", Matchers.hasSize(1)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].number", Matchers.equalTo("Receipt-1")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].stock_location_name", Matchers.equalTo(stockLocation.getName())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].order_number", Matchers.equalTo(shipment1.getOrderNumber())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].shipment_number", Matchers.equalTo(shipment1.getNumber())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].batch_id", Matchers.equalTo(1)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].tax_required", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.receipts[0].date", Matchers.equalTo(slot1.getStartTime().toLocalDate().toString())));
    }

}
