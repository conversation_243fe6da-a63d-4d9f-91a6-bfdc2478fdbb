package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.BatchFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.SlotFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.List;

public class BatchDetailTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Test
    public void testBatchDetail_return404IfNotExists() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);
        mvc.perform(MockMvcRequestBuilders.get("/api/batches/543")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isNotFound());
    }

    @Test
    public void testBatchDetail_return200IfExists() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, user);

        Batch batch = batchFactory.createBatch(user, slots.get(0), Batch.Type.SHOPPING);

        mvc.perform(MockMvcRequestBuilders.get("/api/batches/" + batch.getId())
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON));
    }

}
