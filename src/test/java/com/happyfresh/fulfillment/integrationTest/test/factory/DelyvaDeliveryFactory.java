package com.happyfresh.fulfillment.integrationTest.test.factory;

import com.happyfresh.fulfillment.entity.DelyvaDelivery;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.repository.DelyvaDeliveryRepository;
import com.happyfresh.fulfillment.tpl.delyva.model.DelyvaStatusCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@Profile("it")
public class DelyvaDeliveryFactory {

    @Autowired
    private DelyvaDeliveryRepository repository;

    @Transactional
    public DelyvaDelivery save(DelyvaDelivery delivery) {
        return repository.save(delivery);
    }

    public DelyvaDelivery createInitial(Shipment shipment, User user) {
        return create(shipment, user, DelyvaStatusCode.INITIAL);
    }

    public DelyvaDelivery create(Shipment shipment, User user) {
        return create(shipment, user, DelyvaStatusCode.INITIAL);
    }

    public DelyvaDelivery create(Shipment shipment, User user, DelyvaStatusCode status) {
        return create(shipment, user, status, LocalDateTime.now());
    }

    public DelyvaDelivery create(Shipment shipment, User user, DelyvaStatusCode status, LocalDateTime scheduledAt) {
        return create(shipment, user, status, scheduledAt, null);
    }

    public DelyvaDelivery create(Shipment shipment, User user, DelyvaStatusCode status, LocalDateTime scheduledAt, String externalOrderId) {
        DelyvaDelivery delivery = new DelyvaDelivery();
        delivery.setTenant(user.getTenant());
        delivery.setCreatedBy(user.getId());
        delivery.setShipment(shipment);
        delivery.setStatus(status);
        delivery.setServiceCode("PICKUPP");
        delivery.setPickUpScheduledAt(scheduledAt);
        delivery.setLatestStatusChangedAt(LocalDateTime.now());
        delivery.setExternalId(externalOrderId);
        delivery.setBookingAttemptCount(0);

        if (repository != null)
            repository.save(delivery);

        return delivery;
    }
}
