package com.happyfresh.fulfillment.integrationTest.test.enabler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.presenter.StratoEvent;
import com.happyfresh.fulfillment.common.service.EnablerEventPublisherService;
import com.happyfresh.fulfillment.enabler.form.StratoItemForm;
import com.happyfresh.fulfillment.enabler.form.StratoPackagingForm;
import com.happyfresh.fulfillment.enabler.form.StratoWebhookForm;
import com.happyfresh.fulfillment.enabler.service.StratoWebhookService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.ItemRepository;
import org.hamcrest.Matchers;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.io.IOException;
import java.util.List;

import static java.nio.file.Files.readAllBytes;
import static java.nio.file.Paths.get;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

public class StratoWebhookTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StratoWebhookService webhookService;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private ItemFactory itemFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private PackagingFactory packagingFactory;

    @Autowired
    private ItemRepository itemRepository;

    @MockBean
    private EnablerEventPublisherService enablerEventPublisherService;

    private User creator;

    private User shopper;

    private User systemAdmin;

    private Shipment shipment;

    private byte[] webhookPayload;

    private List<String> itemSkus = Lists.newArrayList("SKU1", "SKU2");

    private List<StockLocation> stockLocations;

    protected void setupElasticSearchBeforeEachTests() {
        elasticSearchSetupService.setPreFillSimpleRouteEdges(false); // prevent pre-fill ES
    }

    @Before
    public void init() throws IOException {
        if (this.creator == null) {
            this.creator = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
            this.systemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, creator.getTenant());
            this.shopper = userFactory.createUserData(Role.Name.STRATO_SHOPPER, creator.getTenant());
        }
        if (this.shipment == null) {
            stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, creator, Slot.Type.LONGER_DELIVERY);
            StockLocation stockLocation = stockLocations.get(0);
            stockLocation.setEnabler(StockLocation.Enabler.HFC);
            stockLocation.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);
            stockLocationFactory.save(stockLocation);
            List<Slot> slots1 = slotFactory.createLongerDeliverySlots(stockLocation, creator, 2, 4);

            this.shipment = shipmentFactory.createShipment(slots1.get(0), this.creator);
            int itemCounts = 2;
            List<Item> items = itemFactory.createItems(shipment, shopper, itemCounts);
            for (int i=0; i < itemCounts; i++) {
                Item item = items.get(i);
                item.setSku(itemSkus.get(i));
                itemFactory.save(item);
            }
            List<Shipment> shipments = Lists.newArrayList(shipment);
            Batch batch = batchFactory.createBatch(shopper, shipments, slots1.get(0), Batch.Type.SHOPPING);
        }
        if (this.webhookPayload == null) {
            this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_picked_payload.json"));
        }
    }

    @Test
    public void testWebhookCaseStockLocationFulfilledByStrato() throws Exception {
        String body = mapper.readTree(this.webhookPayload).toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PICKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);
        packagingFactory.createPackagingList(1, stockLocations.get(0), this.creator);

        mvc.perform(MockMvcRequestBuilders.post("/api/strato/webhook")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(body))
                .andExpect(jsonPath("$.status", Matchers.equalTo("ok")))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Thread.sleep(200);
        verify(enablerEventPublisherService, atLeastOnce()).publish(any(StratoEvent.class), anyString(), anyString());
    }

    @Test
    public void testWebhookCaseStockLocationNotFulfilledByStrato() throws Exception {
        String body = mapper.readTree(this.webhookPayload).toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PICKED.toString(), form.getStatus());

        List<Slot> slots1 = slotFactory.createLongerDeliverySlots(stockLocations.get(1), creator, 2, 4);

        Shipment shipment1 = shipmentFactory.createShipment(slots1.get(0), this.creator);
        shipment1.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(shipment1);

        mvc.perform(MockMvcRequestBuilders.post("/api/strato/webhook")
                .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(body))
                .andExpect(jsonPath("$.status", Matchers.equalTo("ok")))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        Thread.sleep(200);
        verify(enablerEventPublisherService, never()).publish(any(StratoEvent.class), anyString(), anyString());
    }

    @Test
    public void testWebhookCaseWithoutAuth() throws Exception {
        String body = mapper.readTree(this.webhookPayload).toString();

        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        Assert.assertEquals(StratoEvent.Status.ORDER_ITEM_PICKED.toString(), form.getStatus());
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        mvc.perform(MockMvcRequestBuilders.post("/api/strato/webhook")
                .contentType(MediaType.APPLICATION_JSON)
                .content(body))
                .andExpect(MockMvcResultMatchers.status().is(401));
    }

    @Test
    public void testWebhook_withInvalidStatusAndOrderNumber_shouldReturn400() throws Exception {
        String body = mapper.readTree(this.webhookPayload).toString();
        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);
        packagingFactory.createPackagingList(1, stockLocations.get(0), this.creator);

        form.setStatus("");
        body = mapper.writeValueAsString(form);
        validate400SingleInvalidField(body, "must not be blank", "status");

        form.setStatus(null);
        body = mapper.writeValueAsString(form);
        validate400SingleInvalidField(body, "must not be blank", "status");

        form.setStatus("order_item_xxx");
        body = mapper.writeValueAsString(form);
        validate400SingleInvalidField(body, "Unknown status", "status");

        form.setStatus("order_item_packed");

        form.setOrderNumber("");
        body = mapper.writeValueAsString(form);
        validate400SingleInvalidField(body, "must not be blank", "orderNumber");

        form.setOrderNumber(null);
        body = mapper.writeValueAsString(form);
        validate400SingleInvalidField(body, "must not be blank", "orderNumber");
    }

    @Test
    public void testWebhook_withNegativeItemQty_withInvalidSku_shouldReturn400() throws Exception {
        String body = mapper.readTree(this.webhookPayload).toString();
        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        List<StratoItemForm> items = form.getItems();
        StratoItemForm item = items.get(0);

        item.setSku(null);
        body = mapper.writeValueAsString(form);
        validate400SingleInvalidField(body, "must not be empty", "items[0].sku");

        item.setSku("");
        body = mapper.writeValueAsString(form);
        validate400SingleInvalidField(body, "must not be empty", "items[0].sku");

        item.setSku("12312312-ID");

        item.setFoundQty(-12);
        body = mapper.writeValueAsString(form);
        validate400SingleInvalidField(body, "must be greater than or equal to 0", "items[0].foundQty");
        item.setFoundQty(1);

        item.setOosQty(-12);
        body = mapper.writeValueAsString(form);
        validate400SingleInvalidField(body, "must be greater than or equal to 0", "items[0].oosQty");
        item.setOosQty(1);

        item.setFoundQty(null);
        body = mapper.writeValueAsString(form);
        validate400SingleInvalidField(body, "must not be null", "items[0].foundQty");
        item.setFoundQty(1);
    }

    @Test
    public void testWebhook_withNullPackagingId_shouldReturn400() throws Exception {
        String body = mapper.readTree(this.webhookPayload).toString();
        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        List<StratoPackagingForm> packagings = form.getPackagings();
        StratoPackagingForm packaging = packagings.get(0);

        packaging.setPackagingId(null);
        body = mapper.writeValueAsString(form);
        validate400SingleInvalidField(body, "must not be empty", "packagings[0].packagingId");

        packaging.setPackagingId("");
        body = mapper.writeValueAsString(form);
        validate400SingleInvalidField(body, "must not be empty", "packagings[0].packagingId");
    }

    @Test
    public void testWebhook_withEmptyPackagings_shouldReturn200() throws Exception {
        String body = mapper.readTree(this.webhookPayload).toString();
        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        this.shipment.setOrderNumber(form.getOrderNumber());
        shipmentFactory.save(this.shipment);

        form.getPackagings().clear();
        Assert.assertEquals(0, form.getPackagings().size());

        body = mapper.writeValueAsString(form);
        mvc.perform(MockMvcRequestBuilders.post("/api/strato/webhook")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void testWebhook_withInvalidQty_shouldReturn409() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_case_1_payload.json"));
        String body = mapper.readTree(this.webhookPayload).toString();
        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        List<StratoItemForm> formItems = form.getItems();

        itemRepository.deleteAll();
        List<Item> items = itemFactory.createItems(shipment, shopper, formItems.size());
        for (int i = 0; i < items.size(); i++) {
            StratoItemForm _formItem = formItems.get(i);
            items.get(i).setSku(_formItem.getSku());
            items.get(i).setRequestedQty(_formItem.getFoundQty() + _formItem.getOosQty());
        }
        itemRepository.saveAll(items);
        packagingFactory.createPackagingList(1, stockLocations.get(0), this.creator);

        shipment.setOrderNumber(form.getOrderNumber());
        shipment.setItems(items);
        shipmentFactory.save(shipment);

        formItems.get(0).setFoundQty(99); // deliberately set found qty > requested qty
        body = mapper.writeValueAsString(form);
        validate409SingleInvalidField(body, "InvalidItemQtyException", "Found quantity or OOS quantity is not valid.", formItems.get(0).getSku());
    }

    @Test
    public void testWebhook_withInvalidPackagingId_shouldReturn409() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_case_1_payload.json"));
        String body = mapper.readTree(this.webhookPayload).toString();
        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        List<StratoItemForm> formItems = form.getItems();

        itemRepository.deleteAll();
        List<Item> items = itemFactory.createItems(shipment, shopper, formItems.size());
        for (int i = 0; i < items.size(); i++) {
            StratoItemForm _formItem = formItems.get(i);
            items.get(i).setSku(_formItem.getSku());
            items.get(i).setRequestedQty(_formItem.getFoundQty() + _formItem.getOosQty());
        }
        itemRepository.saveAll(items);
        packagingFactory.createPackagingList(1, stockLocations.get(0), this.creator);

        shipment.setOrderNumber(form.getOrderNumber());
        shipment.setItems(items);
        shipmentFactory.save(shipment);

        form.getPackagings().get(0).setPackagingId("99");
        body = mapper.writeValueAsString(form);
        validate409SingleInvalidField(body, "InvalidPackagingException", "Unknown packaging id.", "99");
    }

    @Test
    public void testWebhook_withMultipleExceptions_shouldReturn409() throws Exception {
        this.webhookPayload = readAllBytes(get("src", "test", "resources", "fixtures", "strato_webhook_order_item_packed_case_1_payload.json"));
        String body = mapper.readTree(this.webhookPayload).toString();
        StratoWebhookForm form = mapper.readValue(body, StratoWebhookForm.class);
        List<StratoItemForm> formItems = form.getItems();

        itemRepository.deleteAll();
        List<Item> items = itemFactory.createItems(shipment, shopper, formItems.size());
        for (int i = 0; i < items.size(); i++) {
            StratoItemForm _formItem = formItems.get(i);
            items.get(i).setSku(_formItem.getSku());
            items.get(i).setRequestedQty(_formItem.getFoundQty() + _formItem.getOosQty());
        }
        itemRepository.saveAll(items);
        packagingFactory.createPackagingList(1, stockLocations.get(0), this.creator);

        shipment.setOrderNumber(form.getOrderNumber());
        shipment.setItems(items);
        shipmentFactory.save(shipment);

        // 1. invalid found qty
        formItems.get(0).setFoundQty(99);
        // 2. missing item
        String removedSku = formItems.get(1).getSku();
        formItems.remove(1);
        // 3. unknown item
        StratoItemForm unknownItemForm = new StratoItemForm();
        unknownItemForm.setSku("11111-ID");
        unknownItemForm.setFoundQty(1);
        unknownItemForm.setOosQty(0);
        formItems.add(unknownItemForm);

        body = mapper.writeValueAsString(form);

        mvc.perform(MockMvcRequestBuilders.post("/api/strato/webhook")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is(409))
                .andExpect(jsonPath("$.errors", Matchers.hasSize(3)))
                .andExpect(jsonPath("$.errors[0].type", Matchers.equalTo("InvalidItemQtyException")))
                .andExpect(jsonPath("$.errors[0].message", Matchers.equalTo("Found quantity or OOS quantity is not valid.")))
                .andExpect(jsonPath("$.errors[0].field", Matchers.equalTo(formItems.get(0).getSku())))
                .andExpect(jsonPath("$.errors[1].type", Matchers.equalTo("InvalidItemQtyException")))
                .andExpect(jsonPath("$.errors[1].message", Matchers.equalTo("Item not found.")))
                .andExpect(jsonPath("$.errors[1].field", Matchers.equalTo(unknownItemForm.getSku())))
                .andExpect(jsonPath("$.errors[2].type", Matchers.equalTo("InvalidItemQtyException")))
                .andExpect(jsonPath("$.errors[2].message", Matchers.equalTo("Missing finalize item(s).")))
                .andExpect(jsonPath("$.errors[2].field", Matchers.equalTo(removedSku)));
    }

    private void validate400SingleInvalidField(String body, String validationMessage, String invalidField) throws Exception {
        mvc.perform(MockMvcRequestBuilders.post("/api/strato/webhook")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is(400))
                .andExpect(jsonPath("$.errors", Matchers.hasSize(1)))
                .andExpect(jsonPath("$.errors[0].type", Matchers.equalTo("MethodArgumentNotValidException")))
                .andExpect(jsonPath("$.errors[0].message", Matchers.equalTo(validationMessage)))
                .andExpect(jsonPath("$.errors[0].field", Matchers.equalTo(invalidField)));
    }

    private void validate409SingleInvalidField(String body, String exceptionName, String validationMessage, String invalidField) throws Exception {
        mvc.perform(MockMvcRequestBuilders.post("/api/strato/webhook")
                        .header("X-Fulfillment-User-Token", systemAdmin.getToken())
                        .header("X-Fulfillment-Tenant-Token", systemAdmin.getTenant().getToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is(409))
                .andExpect(jsonPath("$.errors", Matchers.hasSize(1)))
                .andExpect(jsonPath("$.errors[0].type", Matchers.equalTo(exceptionName)))
                .andExpect(jsonPath("$.errors[0].message", Matchers.equalTo(validationMessage)))
                .andExpect(jsonPath("$.errors[0].field", Matchers.equalTo(invalidField)));
    }

}
