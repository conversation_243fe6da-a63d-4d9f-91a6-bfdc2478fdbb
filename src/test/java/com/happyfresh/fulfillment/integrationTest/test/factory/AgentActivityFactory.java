package com.happyfresh.fulfillment.integrationTest.test.factory;

import com.happyfresh.fulfillment.entity.Agent;
import com.happyfresh.fulfillment.entity.AgentActivity;
import com.happyfresh.fulfillment.repository.AgentActivityRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Profile("it")
public class AgentActivityFactory {

    @Autowired
    AgentActivityRepository agentActivityRepository;

    @Transactional
    public AgentActivity createAgentActivity(Agent agent) {
        AgentActivity agentActivity = new AgentActivity();
        agentActivity.setUserId(agent.getUser().getId());
        agentActivity.setAgentId(agent.getId());
        agentActivity.setState(agent.getState());
        agentActivity.setStockLocationId(agent.getStockLocation().getId());
        agentActivity.setLat(agent.getLat());
        agentActivity.setLon(agent.getLon());
        agentActivity.setAccuracy(agent.getAccuracy());
        agentActivity.setCreatedBy(agent.getCreatedBy());
        agentActivity.setUpdatedBy(agent.getUpdatedBy());
        agentActivity.setTenant(agent.getTenant());

        if(agentActivityRepository!= null)
            agentActivityRepository.save(agentActivity);

        return agentActivity;
    }
}
