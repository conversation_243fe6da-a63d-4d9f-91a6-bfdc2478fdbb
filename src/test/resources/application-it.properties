spring.datasource.url=${FSRV_DATABASE_URL_KEY_TEST:*********************************************************************}
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.hikari.maximum-pool-size=3
spring.jpa.show-sql=false

# Data Migration Configuration Variables #
spring.flyway.baseline-on-migrate=true
spring.flyway.sql-migration-prefix=v
spring.flyway.sql-migration-suffixes=.sql
spring.flyway.out-of-order=true

redis.url=${FSRV_REDIS_URL_TEST:redis://localhost:6379}

spring.cache.cache-names=default,expireAfterAccess10m
spring.cache.caffeine.spec=expireAfterWrite=3600s,expireAfterAccess=600s

# AWS S3 Bucket Name for storing receipts receipts and signatures
s3.bucket-name=hf-fulfillment.v2-test
s3.receipt-key-prefix-name=receipt
s3.signature-key-prefix-name=signature
s3.url=https://s3-ap-southeast-1.amazonaws.com
s3.packaging-key-prefix-name=packaging
s3.attachment-key-prefix-name=receipt
s3.cdn-url=${FSRV_S3_CDN_URL:https://cdn.carbon.com}

spring.elasticsearch.jest.uris=${FSRV_ES_CLUSTER_NODES_TEST:http://localhost:9200}
spring.elasticsearch.jest.prefix=${FSRV_ES_PREFIX_TEST:test}

# Google
google.service.api-key=${FSRV_GOOGLE_SERVICE_API_KEY_TEST:}

# Graphhopper
graphhopper.api-key=${GRAPHHOPPER_API_KEY_TEST:}

segment.analytics.android-write-key:${SEGMENT_ANDROID_WRITE_KEY:aaa}
segment.analytics.ios-write-key:${SEGMENT_IOS_WRITE_KEY:aaa}
segment.analytics.web-write-key:${SEGMENT_WEB_WRITE_KEY:aaa}
segment.analytics.mobileweb-write-key:${SEGMENT_MOBILEWEB_WRITE_KEY:aaa}
segment.analytics.grabfresh-write-key:${SEGMENT_GRABFRESH_WRITE_KEY:aaa}
segment.analytics.sprinkles-write-key:${SEGMENT_SPRINKLES_WRITE_KEY:aaa}

# AES CBC-128 symmetric encryption
encryption.secret-key=${FSRV_ENCRYPTION_SECRET_KEY:fulfillment-jaya}
encryption.initialization-vector=${FSRV_ENCRYPTION_INITIALIZATION_VECTOR:key-5678--123456}

# ResiId credentials
resi.email=${FSRV_RESI_EMAIL_TEST:}
resi.password=${FSRV_RESI_PASSWORD_TEST:}
resi.apiKey=${FSRV_RESI_API_KEY_TEST:}

# Jubelio
jubelio.source-id=${FSRV_JUBELIO_SOURCE_ID:2097152}

# Grab Express
GRAB_API_URL=${FSRV_GRAB_API_URL_KEY:https://sandbox.grabexpress.com}
ID_GRAB_PARTNER_ID=${FSRV_ID_GRAB_PARTNER_ID_KEY:1234asdf}
ID_GRAB_PARTNER_SECRET=${FSRV_ID_GRAB_PARTNER_SECRET_KEY:6789hjkl}
TH_GRAB_PARTNER_ID=${FSRV_TH_GRAB_PARTNER_ID_KEY:1234asdf}
TH_GRAB_PARTNER_SECRET=${FSRV_TH_GRAB_PARTNER_SECRET_KEY:6789hjkl}
MY_GRAB_PARTNER_ID=${FSRV_MY_GRAB_PARTNER_ID_KEY:1234asdf}
MY_GRAB_PARTNER_SECRET=${FSRV_MY_GRAB_PARTNER_SECRET_KEY:6789hjkl}

# API Documentation
API_DOCUMENTATION_USERNAME=${FSRV_API_DOCUMENTATION_USERNAME:user}
API_DOCUMENTATION_PASSWORD=${FSRV_API_DOCUMENTATION_PASSWORD:password}

# Kafka
spring.kafka.producer.bootstrap-servers=${FSRV_KAFKA_PRODUCER_SERVER_URL:localhost:9092}
spring.kafka.consumer.bootstrap-servers=${FSRV_KAFKA_SERVER_URL:localhost:9092}
spring.kafka.consumer.group-id=${FSRV_KAFKA_CONSUMER_GROUP:internal_processing}
kafka.consumer.jubelio-group-id=${FSRV_KAFKA_JUBELIO_CONSUMER_GROUP:jubelio_processing_local}
kafka.consumer.resi-group-id=${FSRV_KAFKA_RESI_CONSUMER_GROUP:resi_processing_local}
kafka.consumer.slot-optimization-group-id=${FSRV_KAFKA_SLOT_OPTIMIZATION_CONSUMER_GROUP:slot_optimization_processing_local}
kafka.consumer.slot-optimization-tomorrow-group-id=${FSRV_KAFKA_SLOT_OPTIMIZATION_TOMORROW_CONSUMER_GROUP:slot_optimization_tomorrow_processing}
kafka.consumer.strato-group-id=${FSRV_KAFKA_STRATO_CONSUMER_GROUP:strato_processing_local}
kafka.consumer.enabler-group-id=${FSRV_KAFKA_ENABLER_CONSUMER_GROUP:enabler_processing_local}
kafka.consumer.payment-group-id=${FSRV_KAFKA_PAYMENT_CONSUMER_GROUP:fulfillment_payment_processing}
kafka.consumer.radar-group-id=${FSRV_KAFKA_RADAR_CONSUMER_GROUP:fulfillment_radar_processing}
kafka.topic.payment-event-topic=${FSRV_KAFKA_PAYMENT_EVENT_TOPIC:happyfresh_payment_sandbox}
kafka.consumer.spree-group-id=${FSRV_KAFKA_SPREE_CONSUMER_GROUP:fulfillment_spree_processing}
kafka.topic.user-event-topic=${FSRV_KAFKA_USER_EVENT_TOPIC:happyfresh_user}
kafka.consumer.delyva-group-id=${FSRV_KAFKA_DELYVA_CONSUMER_GROUP:fulfillment_delyva_processing}
kafka.authentication.username=${FSRV_KAFKA_USERNAME:}
kafka.authentication.password=${FSRV_KAFKA_PASSWORD:}
kafka.provider=${FSRV_KAFKA_PROVIDER:}
kafka.producer.authentication.username=${FSRV_KAFKA_PRODUCER_USERNAME:}
kafka.producer.authentication.password=${FSRV_KAFKA_PRODUCER_PASSWORD:}
kafka.producer.provider=${FSRV_KAFKA_PRODUCER_PROVIDER:}

# Locus
locus.client-id=${FSRV_LOCUS_CLIENT_ID_TEST:happyfresh}
locus.api-key=${FSRV_LOCUS_API_KEY_TEST:12345}
locus.base-url=${FSRV_LOCUS_BASE_URL_TEST:https://locusapitest.com/v1/client/happyfresh}

# Lalamove
lalamove.base-url=${FSRV_LALAMOVE_BASE_URL:https://rest.sandbox.lalamove.com}
lalamove.api-keys.id=${FSRV_LALAMOVE_ID_API_KEY:1234asdf}
lalamove.api-keys.my=${FSRV_LALAMOVE_MY_API_KEY:1234asdf}
lalamove.api-keys.th=${FSRV_LALAMOVE_TH_API_KEY:1234asdf}
lalamove.api-secrets.id=${FSRV_LALAMOVE_ID_API_SECRET:6789hjkl}
lalamove.api-secrets.my=${FSRV_LALAMOVE_MY_API_SECRET:6789hjkl}
lalamove.api-secrets.th=${FSRV_LALAMOVE_TH_API_SECRET:6789hjkl}
lalamove.updater-delay=5
lalamove.retry-timeout-offset=10
lalamove.max-try-count=3

# Login Attempt
login-attempt.max-attempt=${FSRV_LOGIN_ATTEMPT_MAX:5}
login-attempt.decay-in-seconds=${FSRV_LOGIN_ATTEMPT_DECAY:600}
login-attempt.lockout-duration-in-seconds=${FSRV_LOGIN_ATTEMPT_LOCKOUT_DURATION:900}

# Hypertrack
hypertrack.secret=${FSRV_HYPERTRACK_SECRET_KEY:secret}

# Strato
strato.base-url=${FSRV_STRATO_BASE_URL:https://localhost:3000}
strato.max-attempt=${FSRV_STRATO_MAX_ATTEMPT:3}
strato.backoff-ms=${FSRV_STRATO_BACKOFF:500}

# Radar
radar.base-url=${FSRV_RADAR_BASE_URL:}
radar.secret-key=${FSRV_RADAR_SECRET_KEY:}
radar.webhook-token=${FSRV_RADAR_WEBHOOK_TOKEN:b4024836c7b2c9d17db7e79211e691a4835569ff}

#As Worker
# Disable Kafka Listener
kafka.listener.enabled=${FSRV_KAFKA_LISTENER_ENABLED:false}
# Disable Active MQ Consumer
jms.listener.enabled=${FSRV_JMS_LISTENER_ENABLED:false}
# Disable Scheduler
scheduler.enabled=${FSRV_SCHEDULER_ENABLED:false}

# Delyva
delyva.base-url=${FSRV_DELYVA_BASE_URL:}
delyva.api-keys.my=${FSRV_DELYVA_MY_API_KEY:}
delyva.customer-ids.my=${FSRV_DELYVA_MY_CUSTOMER_ID:}
delyva.updater-delay=${FSRV_DELYVA_UPDATER_DELAY:5}
delyva.retry-timeout-offset=${FSRV_DELYVA_TIMEOUT_OFFSET:10}
delyva.max-try-count=${FSRV_DELYVA_MAX_TRY_COUNT:3}
delyva.api-secret=${FSRV_DELYVA_API_SECRET:}

# Hypermart
hypermart.base-url=${FSRV_HYPERMART_BASE_URL:}
hypermart.user=${FSRV_HYPERMART_USER:}
hypermart.password=${FSRV_HYPERMART_PASSWORD:}
hypermart.updater-delay=${FSRV_HYPERMART_UPDATER_DELAY:5}
hypermart.retry-timeout-offset=${FSRV_HYPERMART_TIMEOUT_OFFSET:10}
hypermart.max-try-count=${FSRV_HYPERMART_MAX_TRY_COUNT:3}

# MoEngage
moengage.push-max-recipient=${FSRV_MOENGAGE_PUSH_MAX_RECIPIENT:50}

# LezCash
lezcash.base-url=${FSRV_LEZCASH_BASE_URL:}
lezcash.api-key=${FSRV_LEZCASH_API_KEY:}
lezcash.error-code-above-to-enable-retry=${FSRV_LEZCASH_ERROR_CODE_ABOVE_TO_ENABLE_RETRY:500}
lezcash.enable-debugger=${FSRV_LEZCASH_ENABLE_DEBUGGER:false}

# App
app.min-snd-version-code-to-take-job=${FSRV_MIN_SND_VERSION_CODE_TO_TAKE_JOB:600}

# jobrunr - sidekiq alike
org.jobrunr.job-scheduler.enabled=${FSRV_KAFKA_LISTENER_ENABLED:false}
org.jobrunr.background-job-server.enabled=${FSRV_KAFKA_LISTENER_ENABLED:false}
org.jobrunr.dashboard.enabled=${FSRV_KAFKA_LISTENER_ENABLED:false}
