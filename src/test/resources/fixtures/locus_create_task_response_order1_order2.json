{"tours": [{"visits": [{"id": "shift_start_visit", "chosenLocationId": "loc1", "eta": {"COMPLETED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "ACCEPTED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "ARRIVED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "STARTED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "TRANSACTING": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}}, "visitSource": "USER", "sourceId": "vehicle-7", "clientId": "happyfresh-devo", "summary": {"totalTravelDistance": 0, "totalTravelDuration": 0}, "violations": [], "visitTravel": {"timeDistancePair": {"distance": 0, "duration": 0}, "polyline": null}, "chosenSlot": {"start": "2020-03-06T14:00:00.000+0000", "end": "2020-03-06T15:00:00.000+0000"}, "chosenHomebaseId": null, "chosenResources": null}, {"id": "homebase", "chosenLocationId": "Loc1", "eta": {"COMPLETED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "ACCEPTED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "ARRIVED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "STARTED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "TRANSACTING": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}}, "visitSource": "TASK", "sourceId": "Order-1", "clientId": "happyfresh-devo", "summary": {"totalTravelDistance": 0, "totalTravelDuration": 0}, "violations": [], "visitTravel": {"timeDistancePair": {"distance": 0, "duration": 0}, "polyline": null}, "chosenSlot": {"start": "2020-03-05T17:00:00.000+0000", "end": "2020-03-06T16:59:59.999+0000"}, "chosenHomebaseId": null, "chosenResources": null}, {"id": "homebase", "chosenLocationId": "Loc1", "eta": {"COMPLETED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "ACCEPTED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "ARRIVED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "STARTED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "TRANSACTING": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}}, "visitSource": "TASK", "sourceId": "Order-2", "clientId": "happyfresh-devo", "summary": {"totalTravelDistance": 0, "totalTravelDuration": 0}, "violations": [], "visitTravel": {"timeDistancePair": {"distance": 0, "duration": 0}, "polyline": null}, "chosenSlot": {"start": "2020-03-05T17:00:00.000+0000", "end": "2020-03-06T16:59:59.999+0000"}, "chosenHomebaseId": null, "chosenResources": null}, {"id": "customer", "chosenLocationId": "Loc1", "eta": {"COMPLETED": {"initialEta": {"arrivalTime": "2020-03-06T14:25:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:25:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "ACCEPTED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "ARRIVED": {"initialEta": {"arrivalTime": "2020-03-06T14:15:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:15:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "STARTED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "TRANSACTING": {"initialEta": {"arrivalTime": "2020-03-06T14:15:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:15:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}}, "visitSource": "TASK", "sourceId": "Order-1", "clientId": "happyfresh-devo", "summary": {"totalTravelDistance": 4768, "totalTravelDuration": 795}, "violations": [], "visitTravel": {"timeDistancePair": {"distance": 4767, "duration": 677}, "polyline": "dp~d@c{bkSiBLsBH]CCMDKd@GnCUfBS|CUG_@Mq@Y_AUWKUi@kAw@cBG_@[yCE]BU@IBELEP@JLZxCHd@h@hBd@lAb@`A^lAXxB\\nEN~BJzCJ`Ff@fFPfBVrE`@`JVhEd@vHxK[vAG~@KX?`AKXEj@@bBJrAPGRtBb@`ATXH~@ZtB~@~@n@r@h@hAlAj@p@h@r@bAlBjA|Cj@dBTf@LRRV`AdAnA`AfAj@fAh@JTEPMHS?aAm@WCOGoAq@w@q@_@_@i@w@a@q@Ue@cBv@cAh@JRhAnBv@|@TTp@l@`A~@h@XaBfCJHa@h@e@W_AhB"}, "chosenSlot": {"start": "2020-03-06T14:00:00.000+0000", "end": "2020-03-06T15:00:00.000+0000"}, "chosenHomebaseId": null, "chosenResources": null}, {"id": "customer", "chosenLocationId": "Loc1", "eta": {"COMPLETED": {"initialEta": {"arrivalTime": "2020-03-06T14:25:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:25:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "ACCEPTED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "ARRIVED": {"initialEta": {"arrivalTime": "2020-03-06T14:15:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:15:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "STARTED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "TRANSACTING": {"initialEta": {"arrivalTime": "2020-03-06T14:15:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:15:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}}, "visitSource": "TASK", "sourceId": "Order-2", "clientId": "happyfresh-devo", "summary": {"totalTravelDistance": 4768, "totalTravelDuration": 795}, "violations": [], "visitTravel": {"timeDistancePair": {"distance": 4767, "duration": 677}, "polyline": "dp~d@c{bkSiBLsBH]CCMDKd@GnCUfBS|CUG_@Mq@Y_AUWKUi@kAw@cBG_@[yCE]BU@IBELEP@JLZxCHd@h@hBd@lAb@`A^lAXxB\\nEN~BJzCJ`Ff@fFPfBVrE`@`JVhEd@vHxK[vAG~@KX?`AKXEj@@bBJrAPGRtBb@`ATXH~@ZtB~@~@n@r@h@hAlAj@p@h@r@bAlBjA|Cj@dBTf@LRRV`AdAnA`AfAj@fAh@JTEPMHS?aAm@WCOGoAq@w@q@_@_@i@w@a@q@Ue@cBv@cAh@JRhAnBv@|@TTp@l@`A~@h@XaBfCJHa@h@e@W_AhB"}, "chosenSlot": {"start": "2020-03-06T14:00:00.000+0000", "end": "2020-03-06T15:00:00.000+0000"}, "chosenHomebaseId": null, "chosenResources": null}, {"id": "shift_end_visit", "chosenLocationId": "loc1", "eta": {"COMPLETED": {"initialEta": {"arrivalTime": "2020-03-06T14:37:53.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:37:53.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "ACCEPTED": {"initialEta": {"arrivalTime": "2020-03-06T14:25:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:25:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "ARRIVED": {"initialEta": {"arrivalTime": "2020-03-06T14:37:53.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:37:53.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "STARTED": {"initialEta": {"arrivalTime": "2020-03-06T14:25:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:25:15.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}, "TRANSACTING": {"initialEta": {"arrivalTime": "2020-03-06T14:37:53.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:37:53.000+0000", "estimatedOn": "2020-03-03T16:11:56.045+0000"}}}, "visitSource": "USER", "sourceId": "vehicle-7", "clientId": "happyfresh-devo", "summary": {"totalTravelDistance": 9194, "totalTravelDuration": 1553}, "violations": [], "visitTravel": {"timeDistancePair": {"distance": 4425, "duration": 641}, "polyline": "lr`e@ej_kSMRE?A@_@n@VCH?RH]j@bCrAV]v@sAZy@hCwEpBwDiEsC{@q@SUY_@q@eA_@_AcAcDe@mAS_@}@_ByB_CcAy@oAu@{Aq@k@S{@UyDw@aBSyACoN\\ATq@?_CHDTEdAGz@YnCkA`Hw@xEc@xBKFC@G?ICIGIUtBmKh@}C\\s@BC?GDgB?mCUwGC}As@sLi@uIOsC?sAK{AMoBGeCCi@GgBKCECMe@KUIo@IyBImACGe@?cAFaAF"}, "chosenSlot": {"start": "2020-03-06T14:00:00.000+0000", "end": "2020-03-06T15:00:00.000+0000"}, "chosenHomebaseId": null, "chosenResources": null}], "summary": {"volume": {"used": 0.0005, "total": 0.0005, "available": 0.01, "required": null, "current": 0.0, "definition": null}, "time": {"used": 1395.0, "total": 1395.0, "available": 3600.0, "required": null, "current": null, "definition": null}, "resources": {}, "totalTravelDistance": 9194, "totalTravelDuration": 1553, "totalTardiness": 0, "totalIdleDuration": 0, "totalTransactionDuration": 600, "vehiclesOperationSlot": {"start": "2020-03-06T14:00:00.000+0000", "end": "2020-03-06T14:37:53.000+0000"}, "customMetrics": {}, "startTime": "2020-03-06T14:00:00.000+0000", "endTime": "2020-03-06T14:37:53.000+0000", "taskCount": 1, "visitCount": 4, "freightCost": null, "uniqueAddressCount": 0, "totalSlaBreachedTasks": 0, "usedSkills": {"tags": []}, "usedRoutes": [], "customFieldMetrics": []}, "tourId": {"clientId": "happyfresh-devo", "tourId": "tour-7"}, "userVisitsData": [{"visit": {"clientId": "happyfresh-devo", "taskId": "vehicle-7", "id": "shift_start_visit", "task": false, "volumes": {"volumes": []}, "resources": {"resources": []}, "visitStatus": {"status": "RECEIVED", "triggerTime": "2020-03-03T16:02:16.844+0000", "checklistValues": {}, "receiveTime": "2020-03-03T16:02:16.844+0000", "location": null, "actor": null, "assignedUser": null, "vehicleStatus": null}, "trackLink": null, "tripId": null, "statusUpdates": [{"status": "RECEIVED", "triggerTime": "2020-03-03T16:02:16.844+0000", "checklistValues": {}, "receiveTime": "2020-03-03T16:02:16.844+0000", "location": null, "actor": null, "assignedUser": null, "vehicleStatus": null}], "locationIds": [], "locationOptions": [{"id": "loc1", "geometry": {"latLng": {"lat": -6.223642, "lng": 106.843181, "accuracy": 0}}, "timeWindow": {"slot": {"start": "2020-03-06T14:00:00.000+0000", "end": "2020-03-06T15:00:00.000+0000"}, "strictness": "STRICT", "canTransactAfterSlot": false, "treatEtaAsSla": false, "transactionDuration": 0, "readinessDuration": 0, "slotBuffer": 0, "slots": [{"start": "2020-03-06T14:00:00.000+0000", "end": "2020-03-06T15:00:00.000+0000"}]}, "nonAvailableWindows": [], "locationAddress": {"id": null, "placeName": null, "localityName": null, "formattedAddress": null, "subLocalityName": null, "pincode": null, "city": null, "state": null, "countryCode": null, "locationType": null, "placeHash": null}, "contact": null, "geocodingMetadata": {"center": null, "radius": null, "provider": null, "archive": [], "goodness": null, "confidence": null, "requestId": null, "placeNameProvider": null, "localityProvider": null, "placeNameArchive": [], "localityArchive": []}, "customerId": null, "addressId": null}], "chosenLocation": {"id": "loc1", "geometry": {"latLng": {"lat": -6.223642, "lng": 106.843181, "accuracy": 0}}, "timeWindow": {"slot": {"start": "2020-03-06T14:00:00.000+0000", "end": "2020-03-06T15:00:00.000+0000"}, "strictness": "STRICT", "canTransactAfterSlot": false, "treatEtaAsSla": false, "transactionDuration": 0, "readinessDuration": 0, "slotBuffer": 0, "slots": [{"start": "2020-03-06T14:00:00.000+0000", "end": "2020-03-06T15:00:00.000+0000"}]}, "nonAvailableWindows": [], "locationAddress": {"id": null, "placeName": null, "localityName": null, "formattedAddress": null, "subLocalityName": null, "pincode": null, "city": null, "state": null, "countryCode": null, "locationType": null, "placeHash": null}, "contact": null, "geocodingMetadata": {"center": null, "radius": null, "provider": null, "archive": [], "goodness": null, "confidence": null, "requestId": null, "placeNameProvider": null, "localityProvider": null, "placeNameArchive": [], "localityArchive": []}, "customerId": null, "addressId": null}, "eta": {"COMPLETED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}}, "ACCEPTED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}}, "ARRIVED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}}, "STARTED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}}, "TRANSACTING": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}}}, "checklists": [], "amountTransaction": null, "payments": {"totalAmount": null, "pendingAmount": null, "paymentInstruments": [], "payments": [], "fullAmountRequired": false, "paymentRequired": null}, "visitMetadata": null, "userVisitType": "SHIFT_START", "breakType": null, "slotEdits": [], "triggeredGeofences": [], "summary": {"tardiness": null, "actualTravelPair": null}, "orderDetail": {"lineItems": [], "transactionDetail": null, "cratingInfo": null}, "appFields": {"items": []}, "visitName": null, "visitAppConfig": {"skipStatuses": [], "allowVisitReschedule": null, "blockCompletion": null, "allowZeroItemVisitCompletion": null, "enforceSingleTransaction": null}, "routeId": null, "distanceFromHomebase": null}, "userId": {"clientId": "happyfresh-devo", "userId": "vehicle-7"}, "positionPreference": "FIRST", "skip": false}, {"visit": {"clientId": "happyfresh-devo", "taskId": "vehicle-7", "id": "shift_end_visit", "task": false, "volumes": {"volumes": []}, "resources": {"resources": []}, "visitStatus": {"status": "RECEIVED", "triggerTime": "2020-03-03T16:02:16.844+0000", "checklistValues": {}, "receiveTime": "2020-03-03T16:02:16.844+0000", "location": null, "actor": null, "assignedUser": null, "vehicleStatus": null}, "trackLink": null, "tripId": null, "statusUpdates": [{"status": "RECEIVED", "triggerTime": "2020-03-03T16:02:16.844+0000", "checklistValues": {}, "receiveTime": "2020-03-03T16:02:16.844+0000", "location": null, "actor": null, "assignedUser": null, "vehicleStatus": null}], "locationIds": [], "locationOptions": [{"id": "loc1", "geometry": {"latLng": {"lat": -6.223642, "lng": 106.843181, "accuracy": 0}}, "timeWindow": {"slot": {"start": "2020-03-06T14:00:00.000+0000", "end": "2020-03-06T15:00:00.000+0000"}, "strictness": "STRICT", "canTransactAfterSlot": false, "treatEtaAsSla": false, "transactionDuration": 0, "readinessDuration": 0, "slotBuffer": 0, "slots": [{"start": "2020-03-06T14:00:00.000+0000", "end": "2020-03-06T15:00:00.000+0000"}]}, "nonAvailableWindows": [], "locationAddress": {"id": null, "placeName": null, "localityName": null, "formattedAddress": null, "subLocalityName": null, "pincode": null, "city": null, "state": null, "countryCode": null, "locationType": null, "placeHash": null}, "contact": null, "geocodingMetadata": {"center": null, "radius": null, "provider": null, "archive": [], "goodness": null, "confidence": null, "requestId": null, "placeNameProvider": null, "localityProvider": null, "placeNameArchive": [], "localityArchive": []}, "customerId": null, "addressId": null}], "chosenLocation": {"id": "loc1", "geometry": {"latLng": {"lat": -6.223642, "lng": 106.843181, "accuracy": 0}}, "timeWindow": {"slot": {"start": "2020-03-06T14:00:00.000+0000", "end": "2020-03-06T15:00:00.000+0000"}, "strictness": "STRICT", "canTransactAfterSlot": false, "treatEtaAsSla": false, "transactionDuration": 0, "readinessDuration": 0, "slotBuffer": 0, "slots": [{"start": "2020-03-06T14:00:00.000+0000", "end": "2020-03-06T15:00:00.000+0000"}]}, "nonAvailableWindows": [], "locationAddress": {"id": null, "placeName": null, "localityName": null, "formattedAddress": null, "subLocalityName": null, "pincode": null, "city": null, "state": null, "countryCode": null, "locationType": null, "placeHash": null}, "contact": null, "geocodingMetadata": {"center": null, "radius": null, "provider": null, "archive": [], "goodness": null, "confidence": null, "requestId": null, "placeNameProvider": null, "localityProvider": null, "placeNameArchive": [], "localityArchive": []}, "customerId": null, "addressId": null}, "eta": {"COMPLETED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}}, "ACCEPTED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}}, "ARRIVED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}}, "STARTED": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}}, "TRANSACTING": {"initialEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}, "currentEta": {"arrivalTime": "2020-03-06T14:02:00.000+0000", "estimatedOn": "2020-03-03T16:11:56.019+0000"}}}, "checklists": [], "amountTransaction": null, "payments": {"totalAmount": null, "pendingAmount": null, "paymentInstruments": [], "payments": [], "fullAmountRequired": false, "paymentRequired": null}, "visitMetadata": null, "userVisitType": "SHIFT_END", "breakType": null, "slotEdits": [], "triggeredGeofences": [], "summary": {"tardiness": null, "actualTravelPair": null}, "orderDetail": {"lineItems": [], "transactionDetail": null, "cratingInfo": null}, "appFields": {"items": []}, "visitName": null, "visitAppConfig": {"skipStatuses": [], "allowVisitReschedule": null, "blockCompletion": null, "allowZeroItemVisitCompletion": null, "enforceSingleTransaction": null}, "routeId": null, "distanceFromHomebase": null}, "userId": {"clientId": "happyfresh-devo", "userId": "vehicle-7"}, "positionPreference": "LAST", "skip": false}], "hasDirections": false, "vehicle": {"id": {"clientId": "happyfresh-devo", "version": null, "id": "vehicle-7"}, "vehicleModel": {"name": "Vehicle Stock Location 1", "volumeLimit": {"unit": "METERS_CUBIC", "higherThresh": "0.01"}, "resourceLimits": [], "travelParams": null, "vehicleType": "BIKE", "costModel": null, "additionalProperties": null, "loadingParams": null, "allowMultiTrip": true, "skills": {"tags": []}, "id": {"clientId": "happyfresh-devo", "version": null, "modelId": "vehicle_1"}}, "operationShift": {"startTime": "2020-03-06T14:00:00.000+0000", "startLocation": {"lat": -6.223642, "lng": 106.843181, "accuracy": 0}, "endTime": "2020-03-06T15:00:00.000+0000", "endLocation": {"lat": -6.223642, "lng": 106.843181, "accuracy": 0}, "breaks": []}, "resourceLimits": [], "vehicleCostModel": null, "userCount": 1, "skills": {"tags": []}, "additionalProperties": null, "documents": null, "userAlias": null, "rating": null, "userName": null}, "assignedUser": null, "tourName": "Tour 7", "tourDay": null, "tourDaysInfo": [], "tags": [], "tourTrips": [], "customProperties": {}}]}