.PHONY: docker-build-local docker-run-prod docker-run-dev docker-build-bitbucket

# coramil added
REPOSITORY_NAME ?= fulfillmentservice
IMAGE_NAME ?= core
ENVIRONMENT ?= prod
DEPLOY_DOCKER_IMAGE_URL ?= fulfillmentservice/core

# for use on local environment development
docker-build-local:
ifeq ($(ENVIRONMENT),$(filter $(ENVIRONMENT), base dev test pre-prod prod))
	docker build -t ${REPOSITORY_NAME}/${IMAGE_NAME}:${ENVIRONMENT} --target ${ENVIRONMENT} .
else 
	@echo "Error: valid values for ENVIRONMENT is : base, dev, test, pre-prod, or prod"
endif

docker-run-prod:
	docker run --rm -it -p 8080:8080 -p 8000:8000 ${REPOSITORY_NAME}/${IMAGE_NAME}:prod

docker-run-pre-prod:
	docker run --rm -it -p 8080:8080 -p 8000:8000 ${REPOSITORY_NAME}/${IMAGE_NAME}:pre-prod

# Automatically restarts the program when a change is detected in the directory.
docker-run-dev:
	docker run --rm -it -p 8080:8080 -p 8000:8000 -v ${PWD}:/opt/app ${REPOSITORY_NAME}/${IMAGE_NAME}:dev

# for use with the bitbucket pipeline
docker-build-bitbucket:
	docker build --target prod --build-arg VERSION=${VERSION} -t ${DEPLOY_DOCKER_IMAGE_URL} .