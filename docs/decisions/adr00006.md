# ADR00006: Fleet Clock-In to Shift

* Created at: 2022-03-09
* Created by: Hardiansyah

## Status

Accepted

## Context

Based on Ops analysis on On-Time Performance (OTP) we believe that having the system distribute jobs to the fleet instead of having the fleet choose jobs for themselves will lead to better distribution of workloads.
In case of driver and ranger assignment, the method we choose is we assign batch based on Fleet Plan.
Thus, each fleet that clock-in need to attach to a available shift.

## Decision

Current Clock-In Endpoint doesn't provide fleet clocking-in to a Shift. Because of that, we need to change current Clock-In Endpoint that it can handle that requirement.  
In this change, SnD Client can choose either a fleet clock-in based on clock-in duration or shift. 
And Clock-In Endpoint will save the shift_id to that fleet if the shift_id included in the payload.
Then we return Shift Id, Start Time, and End Time in response payload.

## Consequences
In this Clock-In Endpoint, there will be changes in endpoint request and response payloads. Thus, SnD need to make changes to adapt with this new changes.