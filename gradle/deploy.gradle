ext {
    deployApplicationName = "FulfillmentService-CoreAPI"
    deployJarName = "fulfillment.v2.jar"
}

task copyDeployFiles {
    inputs.files bootJar
    outputs.dir "$buildDir/deployments"
    doLast {
        copy {
            from bootJar
            into "$buildDir/deployments"
            rename { String fileName ->
                deployJarName
            }
        }

        copy {
            from "Procfile"
            into "$buildDir/deployments"
        }

        copy {
            from ".ebextensions"
            into "$buildDir/deployments/.ebextensions"
        }

    }
}

task createDeployBundle(type: Zip) {
    from copyDeployFiles
    archiveName "deploy.zip"
}

beanstalk {
    s3Endpoint = "s3-ap-southeast-1.amazonaws.com"
    beanstalkEndpoint = "elasticbeanstalk.ap-southeast-1.amazonaws.com"

    deployments {
        staging {
            file = createDeployBundle.archivePath
            application = deployApplicationName
            environment = "fsrv-staging"
            versionPrefix = "${project.name}-staging-"
        }

        replica {
            file = createDeployBundle.archivePath
            application = deployApplicationName
            environment = "fsrv-replica"
            versionPrefix = "${project.name}-replica-"
        }

        sandbox {
            file = createDeployBundle.archivePath
            application = deployApplicationName
            environment = "fsrv-sandbox"
            versionPrefix = "${project.name}-sandbox-"
        }

        production {
            file = createDeployBundle.archivePath
            application = deployApplicationName
            environment = "fsrv-production"
            versionPrefix = "${project.name}-production-"
        }
    }
}

deployProduction.dependsOn createDeployBundle
deployStaging.dependsOn createDeployBundle
deployReplica.dependsOn createDeployBundle
deploySandbox.dependsOn createDeployBundle