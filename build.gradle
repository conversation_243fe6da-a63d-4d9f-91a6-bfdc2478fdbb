import java.time.Instant

buildscript {
    ext {
        springBootVersion = '2.7.18'
    }
    ext['httpcore.version'] = '4.4.16'
    ext['log4j2.version'] = '2.17.2'

    repositories {
        mavenCentral()
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
    }

}

plugins {
    id 'net.ltgt.apt' version '0.21'
    id "fi.evident.beanstalk" version "0.1.3"
    id "org.sonarqube" version "3.4.0.2513"
    id "jacoco"
}

apply plugin: 'java'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply from: "gradle/deploy.gradle"
apply plugin: 'jacoco'


group = 'com.happyfresh.fulfillment'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = 11

repositories {
    mavenCentral()
    maven {
        url 'https://s3.amazonaws.com/download.elasticsearch.org/lucenesnapshots/00142c9'
    }
}

configurations {
    compile.exclude module: 'spring-boot-starter-logging'
}

dependencies {
    implementation('org.springframework.boot:spring-boot-starter-actuator')
    implementation('org.springframework.boot:spring-boot-starter-cache')
    implementation('org.springframework.boot:spring-boot-starter-data-jpa')
    implementation('org.springframework.boot:spring-boot-starter-security')
    implementation('org.springframework.boot:spring-boot-starter-web-services') {
        exclude module: 'spring-boot-starter-tomcat'
    }
    implementation('org.springframework.boot:spring-boot-starter-undertow') {
        exclude group: 'io.undertow', module: 'undertow-websockets-jsr'
    }
    implementation('org.springframework.boot:spring-boot-starter-log4j2')
    implementation('org.springframework.boot:spring-boot-starter-cache')
    implementation('org.springframework.boot:spring-boot-starter-activemq')
    implementation('org.springframework.kafka:spring-kafka')

    implementation('io.searchbox:jest:6.3.1')
    implementation('org.elasticsearch:elasticsearch:7.17.9')
    implementation('org.mapstruct:mapstruct-processor:1.5.3.Final')
    implementation('org.mapstruct:mapstruct:1.5.3.Final')
    implementation('org.freemarker:freemarker:2.3.32')
    implementation('io.logz.log4j2:logzio-log4j2-appender:1.0.17')
    implementation('com.google.guava:guava:31.1-jre')
    implementation('io.sentry:sentry-log4j2:6.18.1')
    implementation('redis.clients:jedis:4.2.3')
    implementation('org.flywaydb:flyway-core:8.5.13')
    implementation('org.apache.commons:commons-lang3:3.12.0')
    implementation('org.apache.commons:commons-csv:1.9.0')
    implementation('com.amazonaws:aws-java-sdk-sns:1.12.470')
    implementation('com.amazonaws:aws-java-sdk-s3:1.12.470')
    implementation('org.postgresql:postgresql')
    implementation('com.google.maps:google-maps-services:2.1.2')
    implementation('com.graphhopper:jsprit-core:1.8.1')
    implementation('com.github.ben-manes.caffeine:caffeine:2.9.3')
    implementation('org.javers:javers-spring-boot-starter-sql:6.13.0')
    implementation('com.segment.analytics.java:analytics:3.4.0')
    implementation('com.graphhopper:directions-api-client-hc:1.0-pre3')
    implementation('net.javacrumbs.shedlock:shedlock-spring:4.42.0')
    implementation('net.javacrumbs.shedlock:shedlock-provider-jdbc-template:4.42.0')
    implementation files('library/jedis-lock/jedis-lock.jar')
    implementation('com.googlecode.libphonenumber:libphonenumber:8.13.10')
    implementation('io.github.resilience4j:resilience4j-circuitbreaker:1.7.1')
    implementation('org.jetbrains.kotlin:kotlin-stdlib:1.7.22')
    implementation('com.fasterxml.jackson.core:jackson-databind:2.13.5')
    implementation('com.fasterxml.jackson.core:jackson-core:2.13.5')
    implementation('org.jobrunr:jobrunr-spring-boot-2-starter:7.2.2')

    compileOnly('org.projectlombok:lombok:1.18.28')

    annotationProcessor('org.mapstruct:mapstruct-processor:1.5.3.Final')
    annotationProcessor('org.projectlombok:lombok:1.18.28')

    testImplementation('org.springframework.boot:spring-boot-starter-test')
    testImplementation('org.springframework.security:spring-security-test')
    testImplementation('org.powermock:powermock-module-junit4:2.0.9')
    testImplementation('org.powermock:powermock-api-mockito2:2.0.9')
}

task createMigrationFile {
    doLast {
        def defaultSuffix = "rename_this"
        def suffix = (project.hasProperty('f') && f?.trim()) ? f : defaultSuffix
        suffix = (project.hasProperty('c') && c?.trim()) ? "create_table_${c}" : suffix

        def ddl = (project.hasProperty('c') && c?.trim()) ? "CREATE TABLE hff_${c} (\n);" : "--Write the SQL script here"

        Instant instant = Instant.now()
        Long millisecond = instant.toEpochMilli()

        String path = "${projectDir}/src/main/resources/db/migration/"
        String filename = "v${millisecond}__${suffix}.sql"

        file(path + filename).text = ddl
        println "Done creating ${filename}"
    }
}

test {
    ignoreFailures = false
    maxHeapSize = "1024m"

    filter {
        includeTestsMatching "com.happyfresh.fulfillment.unit.*"
        includeTestsMatching "com.happyfresh.fulfillment.integrationTest.repositoryTest.*"
    }
}

jacocoTestReport {
    reports {
        xml.enabled true
    }
}

test.finalizedBy jacocoTestReport

bootJar {
    mainClassName = 'com.happyfresh.fulfillment.MainApp'
}

sonarqube {
    properties {
        property "sonar.tests", "src/test/java/com/happyfresh/fulfillment/unit"
    }
}